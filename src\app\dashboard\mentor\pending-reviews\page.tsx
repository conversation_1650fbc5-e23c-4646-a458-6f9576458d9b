import { redirect } from 'next/navigation'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import payloadConfig from '@/payload.config'
import PendingReviewsClient from '@/components/dashboard/PendingReviewsClient'
import DashboardLayout from '@/components/dashboard/DashboardLayout'

export default async function PendingReviewsPage() {
  // Get the current user
  const cookieStore = await cookies()
  const token = cookieStore.get('payload-token')?.value

  if (!token) {
    redirect('/login')
  }

  const user = await extractUserFromToken(token)

  if (!user || user.role !== 'mentor') {
    redirect('/dashboard')
  }

  return (
    <DashboardLayout>
      <div className="p-6" dir="rtl">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">مراجعات المعلمين المنتظرة للتقييم</h1>
          <p className="mt-2 text-gray-600">قم بتقييم مراجعات المعلمين للحفاظ على معايير الجودة</p>
        </div>

        <PendingReviewsClient />
      </div>
    </DashboardLayout>
  )
}
