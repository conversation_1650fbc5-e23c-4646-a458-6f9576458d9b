'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2, ExternalLink, Eye } from 'lucide-react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { EnhancedEditor, textToLexical, lexicalToText } from '@/components/ui/EnhancedEditor'
import { extractPlainText, createRichTextFromPlain } from '@/utils/richTextUtils'
import { ImageUpload } from '@/components/ui/ImageUpload'
import { getImageUrl } from '@/utils/imageUtils'

export default function NewsCreateClient() {
  const [isLoading, setIsLoading] = useState(true)
  const [userRole, setUserRole] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    content: '',
    featuredImage: '',
    status: 'draft'
  })

  useEffect(() => {
    async function checkUserRole() {
      try {
        // Fetch user info to check role
        const response = await fetch('/api/auth/me', {
          credentials: 'include',
        })

        if (!response.ok) {
          if (response.status === 401) {
            setError('يجب تسجيل الدخول لإنشاء الأخبار')
          } else {
            setError('حدث خطأ أثناء التحقق من صلاحياتك')
          }
          setIsLoading(false)
          return
        }

        const data = await response.json()
        const role = typeof data.user?.role === 'object'
          ? data.user?.role?.slug
          : data.user?.role

        setUserRole(role)

        // Check if user is a mentor
        if (role !== 'mentor' && role !== 'super-admin' && role !== 'school-admin') {
          setError('ليس لديك صلاحية إنشاء الأخبار')
        }

        setIsLoading(false)
      } catch (err) {
        console.error('Error checking user role:', err)
        setError('حدث خطأ غير متوقع')
        setIsLoading(false)
      }
    }

    checkUserRole()
  }, [])

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Handle rich text editor changes
  const handleRichTextChange = (value: string) => {
    // Update the content without triggering form submission
    // This prevents the form from submitting while still editing
    setFormData(prev => ({ ...prev, content: value }))
  }

  // Handle image upload
  const handleImageChange = (url: string) => {
    setFormData(prev => ({ ...prev, featuredImage: url }))
  }

  // Toggle preview mode
  const togglePreview = () => {
    setIsPreviewMode(prev => !prev)
  }

  if (isLoading) {
    return (
      <div className="p-6" dir="rtl">
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-center text-muted-foreground">جاري التحقق من الصلاحيات...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6" dir="rtl">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-500">خطأ</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button variant="outline" className="mt-4" asChild>
              <Link href="/dashboard/news">العودة إلى الأخبار</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Get the button that was clicked
      const submitter = (e as any).nativeEvent.submitter
      const status = submitter.value || 'draft'

      // Make a direct API call to Payload
      const response = await fetch('/api/collections/news', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          slug: formData.slug,
          content: formData.content,
          featuredImage: formData.featuredImage || undefined,
          status,
        }),
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'فشل في إنشاء الخبر')
      }

      // Redirect to the news page
      window.location.href = '/dashboard/news'
    } catch (err) {
      console.error('Error creating news:', err)
      setError('فشل في إنشاء الخبر. يرجى المحاولة مرة أخرى.')
      setIsLoading(false)
    }
  }

  return (
    <div className="p-6" dir="rtl">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>إنشاء خبر جديد</CardTitle>
          <Button
            variant="outline"
            onClick={togglePreview}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4 ml-2" />
            {isPreviewMode ? 'تحرير' : 'معاينة'}
          </Button>
        </CardHeader>
        <CardContent>
          {isPreviewMode ? (
            <div className="preview-container">
              <h1 className="text-2xl font-bold mb-4">{formData.title}</h1>
              {formData.featuredImage && (
                <div className="mb-4 relative aspect-video w-full overflow-hidden rounded-md bg-gray-100">
                  <img
                    src={formData.featuredImage}
                    alt={formData.title}
                    className="object-cover w-full h-full"
                  />
                </div>
              )}
              <div 
                className="prose max-w-full"
                dangerouslySetInnerHTML={{ 
                  __html: formData.content
                }}
              />
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">العنوان</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="عنوان الخبر"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="slug">الرابط المختصر (slug)</Label>
                <Input
                  id="slug"
                  name="slug"
                  value={formData.slug}
                  onChange={handleInputChange}
                  placeholder="الرابط-المختصر"
                  required
                />
                <p className="text-sm text-muted-foreground">
                  مسار URL المختصر المستخدم في رابط الخبر (فقط أحرف وأرقام وشرطات)
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="featuredImage">صورة الغلاف</Label>
                <ImageUpload
                  value={formData.featuredImage}
                  onChange={handleImageChange}
                />
                {formData.featuredImage && (
                  <div className="mt-2 text-sm flex items-center">
                    <Button
                      type="button"
                      variant="link"
                      className="h-auto p-0 text-blue-500"
                      onClick={() => window.open(formData.featuredImage, '_blank')}
                    >
                      <ExternalLink className="h-3 w-3 ml-1" />
                      عرض الصورة كاملة
                    </Button>
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="content">محتوى الخبر</Label>
                <EnhancedEditor
                  value={formData.content}
                  onChange={handleRichTextChange}
                  className="min-h-[300px] border p-4 rounded-md"
                />
              </div>
              
              <div className="flex justify-between pt-4">
                <Button
                  type="button"
                  variant="outline"
                  asChild
                >
                  <Link href="/dashboard/news">إلغاء</Link>
                </Button>
                <div className="flex gap-2">
                  <Button 
                    type="submit" 
                    name="status"
                    value="draft"
                    variant="outline"
                  >
                    حفظ كمسودة
                  </Button>
                  <Button 
                    type="submit"
                    name="status"
                    value="published"
                  >
                    نشر
                  </Button>
                </div>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
