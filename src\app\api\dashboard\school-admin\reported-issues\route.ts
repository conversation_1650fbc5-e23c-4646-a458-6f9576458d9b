import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

// Helper function to check if a string is a valid MongoDB ObjectId
const isValidObjectId = (id: string): boolean => {
  if (!id) return false
  return ObjectId.isValid(id) && new ObjectId(id).toString() === id
}

// Helper function to safely convert to ObjectId
const safeObjectId = (id: string): ObjectId | string => {
  if (isValidObjectId(id)) {
    return new ObjectId(id)
  }
  // Return the original string if it's not a valid ObjectId
  // This allows for mock IDs in testing/development
  return id
}

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token and get the user ID
      const { userId } = await verifyJWT(token)

      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const querySchoolId = url.searchParams.get('schoolId')
      const status = url.searchParams.get('status') || 'all'
      const limit = parseInt(url.searchParams.get('limit') || '50')

      // Try to use MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the user from MongoDB, safely handling the ID
        let mongoUser;
        if (isValidObjectId(userId)) {
          mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) });
        } else {
          // Handle mock IDs (for testing/development)
          mongoUser = await db.collection('users').findOne({ _id: userId as any });
          
          // If not found with string ID, try alternative lookup methods
          if (!mongoUser) {
            mongoUser = await db.collection('users').findOne({ id: userId });
          }
        }

        if (mongoUser) {
          // Get user role slug
          let userRoleSlug: string | null = null
          console.log('User object:', mongoUser)
          console.log('User role property:', mongoUser.role)

          if (mongoUser.role instanceof ObjectId) {
            // If role is an ObjectId, use it to lookup the role
            console.log('User role is ObjectId, looking up role by ID:', mongoUser.role)
            const role = await db.collection('roles').findOne({ _id: mongoUser.role })
            console.log('Role document found:', role)
            userRoleSlug = role?.slug || null
          } else if (typeof mongoUser.role === 'string') {
            if (isValidObjectId(mongoUser.role)) {
              // If role is a string ID and is a valid ObjectId, fetch the role and use its slug
              console.log('User role is string ID, looking up role by ID:', mongoUser.role)
              try {
                const role = await db.collection('roles').findOne({ _id: new ObjectId(mongoUser.role) })
                userRoleSlug = role?.slug || null
              } catch (error) {
                console.error('Error fetching role for school admin dashboard:', error)
                userRoleSlug = null // Ensure slug is null on error
              }
            } else {
              // If role is a string but not a valid ObjectId, it might be the role slug itself
              userRoleSlug = mongoUser.role
            }
          } else if (
            typeof mongoUser.role === 'object' &&
            mongoUser.role !== null &&
            'slug' in mongoUser.role
          ) {
            // If role is a populated object, use its slug
            userRoleSlug = mongoUser.role.slug
          }

          console.log('Checking access for userRoleSlug:', userRoleSlug)
          // Check if user is a school admin
          if (userRoleSlug !== 'school-admin' && userRoleSlug !== 'super-admin') {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get school ID
          let schoolId = querySchoolId;
          
          if (!schoolId) {
            if (typeof mongoUser.school === 'object' && mongoUser.school !== null) {
              schoolId = mongoUser.school.id || mongoUser.school._id?.toString();
            } else if (mongoUser.school) {
              schoolId = mongoUser.school;
            }
          }

          if (!schoolId) {
            return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
          }

          // Build query for issues
          const query: any = { }
          
          // Handle the schoolId field in different formats
          if (isValidObjectId(schoolId)) {
            query.$or = [
              { schoolId },  // String format
              { schoolId: new ObjectId(schoolId) },  // ObjectId format
              { 'school.id': schoolId },  // Nested object with string id
              { 'school._id': new ObjectId(schoolId) }  // Nested object with ObjectId
            ]
          } else {
            // For non-ObjectId school IDs (mock data or testing)
            query.$or = [
              { schoolId },
              { 'school.id': schoolId },
              { school: schoolId }
            ]
          }

          // Filter by status if specified
          if (status !== 'all') {
            query.status = status
          }

          // Check if the collection exists
          const collections = await db.listCollections({ name: 'systemicIssues' }).toArray()

          if (collections.length === 0) {
            // Collection doesn't exist, return empty array
            return NextResponse.json({ issues: [] })
          }

          // Get issues from MongoDB
          const issues = await db
            .collection('systemicIssues')
            .find(query)
            .sort({ reportedAt: -1 })
            .limit(limit)
            .toArray()

          // Format issues
          const formattedIssues = await Promise.all(
            issues.map(async (issue) => {
              // Get reporter details
              let reporterName = 'Unknown User'
              let reporterRole = 'Unknown'

              if (issue.reportedBy) {
                try {
                  let reporter;
                  if (isValidObjectId(issue.reportedBy)) {
                    reporter = await db.collection('users').findOne({ _id: new ObjectId(issue.reportedBy) });
                  } else {
                    // Try with string ID for mock data
                    reporter = await db.collection('users').findOne({ 
                      $or: [
                        { _id: issue.reportedBy },
                        { id: issue.reportedBy }
                      ] 
                    });
                  }
                  
                  if (reporter) {
                    reporterName = `${reporter.firstName || ''} ${reporter.lastName || ''}`.trim()
                    reporterRole =
                      typeof reporter.role === 'object' ? reporter.role.slug : reporter.role
                  }
                } catch (error) {
                  console.warn('Error fetching reporter details:', error)
                }
              }

              // Get target details if available
              let targetName = issue.targetName || undefined

              if (issue.targetId && issue.targetType && !targetName) {
                try {
                  if (issue.targetType === 'user') {
                    let target;
                    if (isValidObjectId(issue.targetId)) {
                      target = await db.collection('users').findOne({ _id: new ObjectId(issue.targetId) });
                    } else {
                      // Try with string ID for mock data
                      target = await db.collection('users').findOne({ 
                        $or: [
                          { _id: issue.targetId },
                          { id: issue.targetId }
                        ]
                      });
                    }
                    
                    if (target) {
                      targetName = `${target.firstName || ''} ${target.lastName || ''}`.trim()
                    }
                  } else if (issue.targetType === 'article') {
                    let target;
                    if (isValidObjectId(issue.targetId)) {
                      target = await db.collection('articles').findOne({ _id: new ObjectId(issue.targetId) });
                    } else {
                      // Try with string ID for mock data
                      target = await db.collection('articles').findOne({ 
                        $or: [
                          { _id: issue.targetId },
                          { id: issue.targetId }
                        ]
                      });
                    }
                    
                    if (target) {
                      targetName = target.title || `Article #${issue.targetId}`
                    }
                  }
                } catch (error) {
                  console.warn('Error fetching target details:', error)
                }
              }

              return {
                id: issue._id.toString ? issue._id.toString() : issue._id,
                title: issue.title || 'Untitled Issue',
                description: issue.description || '',
                reportedBy: {
                  id: issue.reportedBy || '',
                  name: reporterName,
                  role: reporterRole,
                },
                reportedAt: issue.reportedAt || new Date().toISOString(),
                status: issue.status || 'pending',
                severity: issue.severity || 'medium',
                targetType: issue.targetType || 'other',
                targetId: issue.targetId,
                targetName,
                resolution: issue.resolution,
                resolvedBy: issue.resolvedBy,
                resolvedAt: issue.resolvedAt,
              }
            }),
          )

          return NextResponse.json({ issues: formattedIssues })
        }
      } catch (mongoError) {
        console.warn('Error fetching issues from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a school admin or super admin
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
      if (userRole !== 'school-admin' && userRole !== 'super-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get school ID
      const schoolId =
        querySchoolId || (typeof user.school === 'object' ? user.school?.id : user.school)

      if (!schoolId) {
        return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
      }

      // Build query for issues
      const query: any = {
        schoolId: { equals: schoolId },
      }

      // Filter by status if specified
      if (status !== 'all') {
        query.status = { equals: status }
      }

      // Check if the collection exists
      let hasSystemicIssues = false
      try {
        const payloadAny = payload as any
        const schema = payloadAny.collections?.systemicIssues?.config?.fields
        hasSystemicIssues = !!schema
      } catch (error) {
        console.log('Error checking for systemicIssues collection:', error)
        hasSystemicIssues = false
      }

      if (!hasSystemicIssues) {
        // Collection doesn't exist, return empty array
        return NextResponse.json({ issues: [] })
      }

      // Get issues from Payload
      const payloadAny = payload as any

      // Fix the query to avoid the schoolId path error
      const fixedQuery = {
        ...(query.schoolId ? { 
          or: [
            { 'school.id': { equals: schoolId } },
            { schoolId: { equals: schoolId } }
          ]
        } : {}),
        ...(status !== 'all' ? { status: { equals: status } } : {}),
      }

      // Use the default API as fallback
      try {
        const issuesResult = await payloadAny.find({
          collection: 'systemicIssues',
          where: fixedQuery,
          sort: '-reportedAt',
          limit,
          depth: 2,
        })

        // Format issues
        const formattedIssues = issuesResult.docs.map((issue: any) => ({
          id: issue.id,
          title: issue.title || 'Untitled Issue',
          description: issue.description || '',
          reportedBy: {
            id: typeof issue.reportedBy === 'object' ? issue.reportedBy.id : issue.reportedBy,
            name: 
              typeof issue.reportedBy === 'object'
                ? `${issue.reportedBy.firstName || ''} ${issue.reportedBy.lastName || ''}`.trim()
                : 'Unknown User',
            role:
              typeof issue.reportedBy === 'object' && issue.reportedBy.role
                ? typeof issue.reportedBy.role === 'object'
                  ? issue.reportedBy.role.slug
                  : issue.reportedBy.role
                : 'Unknown',
          },
          reportedAt: issue.reportedAt || issue.createdAt,
          status: issue.status || 'pending',
          severity: issue.severity || 'medium',
          targetType: issue.targetType || 'other',
          targetId:
            issue.targetId ||
            (issue.target && typeof issue.target === 'object' ? issue.target.id : undefined),
          targetName:
            issue.targetName ||
            (issue.target && typeof issue.target === 'object'
              ? issue.target.title ||
                `${issue.target.firstName || ''} ${issue.target.lastName || ''}`.trim()
              : undefined),
          resolution: issue.resolution,
          resolvedBy: issue.resolvedBy,
          resolvedAt: issue.resolvedAt,
        }))

        return NextResponse.json({ issues: formattedIssues })
      } catch (error: any) {
        console.error('Error querying systemicIssues collection:', error)
        return NextResponse.json(
          { error: 'Failed to fetch reported issues', details: error.message },
          { status: 500 }
        )
      }
    } catch (error) {
      console.error('Authentication error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Server error:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
