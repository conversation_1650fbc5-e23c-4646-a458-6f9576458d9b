import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'

export async function GET(req: NextRequest) {
  try {
    console.log('Updating student school...')
    
    // Initialize Payload
    const payload = await getPayload({ config })
    
    // Find the teacher
    const teachers = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: '<EMAIL>',
        },
      },
    })
    
    if (teachers.docs.length === 0) {
      console.log('Teacher not found')
      return NextResponse.json({ error: 'Teacher not found' }, { status: 404 })
    }
    
    const teacher = teachers.docs[0]
    const teacherSchool = typeof teacher.school === 'object' ? teacher.school.id : teacher.school
    
    console.log('Teacher found with school:', teacherSchool)
    
    // Find the student
    const students = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: '<EMAIL>',
        },
      },
    })
    
    if (students.docs.length === 0) {
      console.log('Student not found')
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }
    
    const student = students.docs[0]
    
    // Update the student
    const updatedStudent = await payload.update({
      collection: 'users',
      id: student.id,
      data: {
        school: teacherSchool,
      },
    })
    
    console.log('Student updated with school:', teacherSchool)
    
    return NextResponse.json({
      success: true,
      message: 'Student school updated successfully',
      student: {
        id: updatedStudent.id,
        email: updatedStudent.email,
        school: updatedStudent.school,
      },
    })
  } catch (error) {
    console.error('Error updating student school:', error)
    return NextResponse.json({ error: 'Failed to update student school' }, { status: 500 })
  }
}
