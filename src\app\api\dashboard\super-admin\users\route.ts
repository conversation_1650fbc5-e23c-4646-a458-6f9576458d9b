import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      const userId = typeof decoded === 'object' ? decoded.id : null
      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const schoolId = url.searchParams.get('schoolId')
      const role = url.searchParams.get('role')
      const status = url.searchParams.get('status')
      const search = url.searchParams.get('search')
      const limit = parseInt(url.searchParams.get('limit') || '50')
      const page = parseInt(url.searchParams.get('page') || '1')

      // Get the payload instance
      const payload = await getPayload({ config })
      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })
      // Check if user is a super-admin
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
      if (userRole !== 'super-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()
        let query: any = {}
        if (schoolId) {
          query.$or = [
            { 'school.id': schoolId },
            { school: schoolId },
            { school: new ObjectId(schoolId) },
            { 'school.$oid': schoolId },
          ]
        }
        if (role) {
          query.$and = query.$and || [];
          query.$and.push({
            $or: [
              { role: role },
              { 'role.slug': role },
              { role: new ObjectId(role) },
              { 'role.$oid': role },
            ]
          });
        }
        if (status) query['status'] = status
        if (search) {
          query.$or = [
            { firstName: { $regex: search, $options: 'i' } },
            { lastName: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { name: { $regex: search, $options: 'i' } },
          ]
        }
        const users = await db.collection('users').find(query).skip((page-1)*limit).limit(limit).toArray()
        if (users.length > 0) {
          return NextResponse.json({
            users: users.map((item: any) => ({
              id: item._id?.toString() || item.id,
              name: item.name || `${item.firstName || ''} ${item.lastName || ''}`.trim(),
              firstName: item.firstName || '',
              lastName: item.lastName || '',
              email: item.email || '',
              status: item.status || '',
              role: typeof item.role === 'object' ? item.role?.slug : item.role,
              roleId: typeof item.role === 'object' ? item.role?._id?.toString() || item.role?.id : item.role,
              schoolName: typeof item.school === 'object' ? item.school?.name : '',
              schoolId: typeof item.school === 'object' ? item.school?.id : item.school,
              grade: item.grade || '',
              createdAt: item.createdAt || '',
              updatedAt: item.updatedAt || '',
            })),
          })
        }
      } catch (mongoError) {
        console.warn('Error fetching users from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails or is empty
      try {
        let where: any = {}
        if (schoolId) where['school'] = { equals: schoolId }
        if (role) where['role'] = { equals: role }
        if (status) where['status'] = { equals: status }
        if (search) {
          where['or'] = [
            { firstName: { like: search } },
            { lastName: { like: search } },
            { email: { like: search } },
            { name: { like: search } },
          ]
        }
        const result = await payload.find({
          collection: 'users',
          where,
          limit,
          page,
          depth: 2,
        })
        if (result.docs.length > 0) {
          return NextResponse.json({
            users: result.docs.map((item: any) => ({
              id: item.id,
              name: item.name || `${item.firstName || ''} ${item.lastName || ''}`.trim(),
              firstName: item.firstName || '',
              lastName: item.lastName || '',
              email: item.email || '',
              status: item.status || '',
              role: typeof item.role === 'object' ? item.role?.slug : item.role,
              schoolName: typeof item.school === 'object' ? item.school?.name : '',
              schoolId: typeof item.school === 'object' ? item.school?.id : item.school,
              createdAt: item.createdAt || '',
              updatedAt: item.updatedAt || '',
            })),
          })
        }
      } catch (payloadError) {
        console.warn('Error fetching users from Payload CMS:', payloadError)
      }

      // If no data, return empty array
      return NextResponse.json({ users: [] })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 