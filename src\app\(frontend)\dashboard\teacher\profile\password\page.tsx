import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { redirect } from 'next/navigation'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'

export default async function TeacherChangePasswordPage() {
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // If not logged in, redirect to login
  if (!user) {
    redirect('/login')
  }

  // Check if user is a teacher
  const isTeacher = typeof user.role === 'object' 
    ? user.role?.slug === 'teacher' 
    : user.role === 'teacher'

  if (!isTeacher) {
    // Redirect to appropriate dashboard based on role
    const userRole = typeof user.role === 'object' 
      ? user.role?.slug 
      : user.role

    if (userRole === 'student') {
      redirect('/dashboard/student/profile/password')
    } else if (userRole === 'mentor') {
      redirect('/dashboard/mentor/profile/password')
    } else {
      redirect('/')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-blue-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <div className="text-xl font-bold">Young Reporter</div>
          <div className="flex space-x-6">
            <Link href="/">Home</Link>
            <Link href="/articles">Articles</Link>
            <Link href="/news">News</Link>
            <Link href="/statistics">Statistics</Link>
            <Link href="/about">About</Link>
            <Link href="/contact">Contact</Link>
            <Link href="/api/logout">Logout</Link>
          </div>
        </div>
      </nav>

      {/* Profile Header */}
      <div className="bg-blue-700 text-white py-12 px-4">
        <div className="container mx-auto">
          <h1 className="text-4xl font-bold mb-4">Change Password</h1>
          <p className="text-xl">
            Update your account password
          </p>
        </div>
      </div>

      {/* Password Change Content */}
      <div className="py-12 px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Sidebar */}
            <div className="md:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 className="text-xl font-bold mb-4">Navigation</h2>
                <div className="space-y-2">
                  <Link
                    href="/dashboard/teacher"
                    className="block text-blue-600 hover:text-blue-800"
                  >
                    Dashboard
                  </Link>
                  <Link
                    href="/dashboard/teacher/profile"
                    className="block text-blue-600 hover:text-blue-800"
                  >
                    Profile
                  </Link>
                  <Link
                    href="/dashboard/teacher/profile/password"
                    className="block font-bold text-blue-800"
                  >
                    Change Password
                  </Link>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="md:col-span-2">
              <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Change Your Password</h2>
                  <form action="/api/profile/password" method="post" className="space-y-6">
                    <div>
                      <label htmlFor="currentPassword" className="block text-gray-700 font-bold mb-2">
                        Current Password
                      </label>
                      <input
                        type="password"
                        id="currentPassword"
                        name="currentPassword"
                        className="w-full p-3 border border-gray-300 rounded"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="newPassword" className="block text-gray-700 font-bold mb-2">
                        New Password
                      </label>
                      <input
                        type="password"
                        id="newPassword"
                        name="newPassword"
                        className="w-full p-3 border border-gray-300 rounded"
                        required
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        Password must be at least 8 characters long and include a number and a special character.
                      </p>
                    </div>

                    <div>
                      <label htmlFor="confirmPassword" className="block text-gray-700 font-bold mb-2">
                        Confirm New Password
                      </label>
                      <input
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        className="w-full p-3 border border-gray-300 rounded"
                        required
                      />
                    </div>

                    <div className="pt-4">
                      <button
                        type="submit"
                        className="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition"
                      >
                        Change Password
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 px-4 mt-auto">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <h3 className="text-xl font-bold">Young Reporter</h3>
              <p>Empowering student journalists since 2023</p>
            </div>
            <div className="flex space-x-6">
              <Link href="/">Home</Link>
              <Link href="/about">About</Link>
              <Link href="/contact">Contact</Link>
              <Link href="/privacy">Privacy Policy</Link>
            </div>
          </div>
          <div className="mt-8 text-center text-gray-400">
            <p>© {new Date().getFullYear()} Young Reporter. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
