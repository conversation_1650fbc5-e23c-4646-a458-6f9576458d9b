import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

// Helper function for standardized error responses
function errorResponse(error: string, status: number) {
  return NextResponse.json(
    {
      success: false,
      error,
    },
    { status },
  )
}

// Helper function for standardized success responses
function successResponse(data: any) {
  return NextResponse.json({
    success: true,
    data,
  })
}

// GET a single user
export async function GET(req: NextRequest, context: { params: { id: string } }) {
  // Await params before accessing its properties
  const params = await context.params
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return errorResponse('Unauthorized', 401)
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return errorResponse('Unauthorized', 401)
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is an admin or mentor
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'
      const isMentor = role === 'mentor'

      if (!isAdmin && !isMentor) {
        return errorResponse('Forbidden', 403)
      }

      // If user is a mentor, they can only access teachers from their school
      if (isMentor) {
        // Get the requested user
        const { id } = params
        const requestedUser = await payload.findByID({
          collection: 'users',
          id,
          depth: 1,
        })

        // Check if the requested user is a teacher
        const requestedUserRole = typeof requestedUser.role === 'object'
          ? requestedUser.role?.slug
          : requestedUser.role

        const isTeacher = requestedUserRole === 'teacher'

        if (!isTeacher) {
          return errorResponse('Forbidden: Mentors can only access teacher profiles', 403)
        }

        // Check if the teacher belongs to the mentor's school
        const mentorSchoolId = typeof currentUser.school === 'object'
          ? currentUser.school?.id
          : currentUser.school

        const teacherSchoolId = typeof requestedUser.school === 'object'
          ? requestedUser.school?.id
          : requestedUser.school

        if (mentorSchoolId !== teacherSchoolId) {
          return errorResponse('Forbidden: Teacher does not belong to your school', 403)
        }
      }

      // Get the requested user
      const { id } = params
      const user = await payload.findByID({
        collection: 'users',
        id,
        depth: 1,
      })

      return successResponse(user)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// UPDATE a user
export async function PATCH(req: NextRequest, context: { params: { id: string } }) {
  // Await params before accessing its properties
  const params = await context.params
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is an admin
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'

      if (!isAdmin) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get the request body
      const body = await req.json()

      // Update the user
      const { id } = context.params
      const updatedUser = await payload.update({
        collection: 'users',
        id,
        data: body,
      })

      return NextResponse.json(updatedUser)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// DELETE a user
export async function DELETE(req: NextRequest, context: { params: { id: string } }) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is an admin
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'

      if (!isAdmin) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get the ID from params
      const { id } = context.params

      // Prevent deleting yourself
      if (id === userId) {
        return NextResponse.json({ error: 'Cannot delete yourself' }, { status: 400 })
      }

      // Delete the user
      const deletedUser = await payload.delete({
        collection: 'users',
        id,
      })

      return NextResponse.json({ success: true, message: 'User deleted successfully' })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
