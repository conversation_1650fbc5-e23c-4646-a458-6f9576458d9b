import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the token and get the user ID
    const { userId } = await verifyJWT(token)

    if (!userId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get URL parameters
    const url = new URL(req.url)
    const querySchoolId = url.searchParams.get('schoolId')
    const limit = parseInt(url.searchParams.get('limit') || '50')
    const page = parseInt(url.searchParams.get('page') || '1')

    // Initialize Payload
    const payload = await getPayload({ config })

    // Get the current user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })

    // Verify user is a school admin
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'school-admin' && role !== 'super-admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get school ID
    const schoolId =
      querySchoolId || (typeof user.school === 'object' ? user.school?.id : user.school)

    if (!schoolId) {
      return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
    }

    // Get student role ID
    const studentRole = await payload.find({
      collection: 'roles',
      where: {
        slug: { equals: 'student' },
      },
    })

    if (!studentRole || studentRole.docs.length === 0) {
      return NextResponse.json({ error: 'Student role not found' }, { status: 404 })
    }

    const studentRoleId = studentRole.docs[0].id

    // Get students from the school
    const studentsResponse = await payload.find({
      collection: 'users',
      where: {
        or: [
          {
            role: {
              equals: studentRoleId,
            },
          },
          {
            'role.slug': {
              equals: 'student',
            },
          },
        ],
        school: {
          equals: schoolId,
        },
      },
      limit,
      page,
      depth: 1,
    })

    // Add additional information about each student
    const studentsWithDetails = await Promise.all(
      studentsResponse.docs.map(async (student) => {
        try {
          // Get count of articles by this student
          const articlesCount = await payload.find({
            collection: 'articles',
            where: {
              'author.id': {
                equals: student.id,
              },
            },
            limit: 0, // Only get count
          })

          return {
            id: student.id,
            firstName: student.firstName || '',
            lastName: student.lastName || '',
            email: student.email || '',
            status: student.status || 'active',
            articlesCount: articlesCount.totalDocs,
            points: typeof (student as any).points === 'number' ? (student as any).points : 0,
            anonymizedId: `Student ${student.id.substring(0, 4)}`,
          }
        } catch (err) {
          console.error(`Error getting stats for student ${student.id}:`, err)
          return {
            id: student.id,
            firstName: student.firstName || '',
            lastName: student.lastName || '',
            email: student.email || '',
            status: student.status || 'active',
            articlesCount: 0,
            points: typeof (student as any).points === 'number' ? (student as any).points : 0,
            anonymizedId: `Student ${student.id.substring(0, 4)}`,
          }
        }
      }),
    )

    return NextResponse.json({
      students: studentsWithDetails,
      totalStudents: studentsResponse.totalDocs,
      totalPages: studentsResponse.totalPages,
      page: studentsResponse.page,
    })
  } catch (error) {
    console.error('Error fetching students:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
