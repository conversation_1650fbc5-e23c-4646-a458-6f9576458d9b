import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized: No token provided' },
        { status: 401 }
      )
    }

    // Verify token to get user info
    let decodedToken
    try {
      const JWT_SECRET = process.env.PAYLOAD_SECRET || 'default-secret'
      decodedToken = jwt.verify(token, JWT_SECRET) as { id: string; email: string; role: string }
    } catch (err) {
      console.error('Error verifying token:', err)
      return NextResponse.json(
        { success: false, error: 'Unauthorized: Invalid token' },
        { status: 401 }
      )
    }

    const { id: userId, role: userRole } = decodedToken

    // Only allow school admin and super admin
    if (userRole !== 'school-admin' && userRole !== 'super-admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized: Insufficient permissions' },
        { status: 403 }
      )
    }

    const reportId = params.id

    // Initialize PayloadCMS
    const payload = await getPayload({ config })

    try {
      // First, get the report details
      const report = await payload.findByID({
        collection: 'reports' as any,
        id: reportId,
      }) as any

      if (!report) {
        return NextResponse.json(
          { success: false, error: 'Report not found' },
          { status: 404 }
        )
      }

      // Get the article ID from the report
      const articleId = report.article

      try {
        // Delete the article
        await payload.delete({
          collection: 'articles',
          id: articleId as string,
        })
        
        // Update the report status
        await payload.update({
          collection: 'reports' as any,
          id: reportId,
          data: {
            status: 'reviewed',
            resolved: true,
            resolvedBy: userId,
            resolvedAt: new Date().toISOString(),
            actions: [
              {
                action: 'article-removed',
                actionBy: userId,
                notes: 'Article was removed due to policy violation',
                actionDate: new Date().toISOString(),
              },
            ],
          },
        })
        
        // Create a notification for the article author
        if (report.author) {
          try {
            await payload.create({
              collection: 'notifications',
              data: {
                user: report.author,
                type: 'warning',
                message: `Your article "${report.articleTitle}" has been removed due to a policy violation.`,
                read: false,
                createdAt: new Date().toISOString(),
              },
            })
          } catch (notificationError) {
            // Log error but continue
            console.error('Failed to create notification for author:', notificationError)
          }
        }

        return NextResponse.json({
          success: true,
          message: 'Article removed successfully',
        })
      } catch (articleError) {
        console.error('Error removing article:', articleError)
        return NextResponse.json(
          { success: false, error: 'Failed to remove article' },
          { status: 500 }
        )
      }
    } catch (reportError) {
      console.error('Error fetching report details:', reportError)
      return NextResponse.json(
        { success: false, error: 'Failed to process report' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error processing report action:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
} 