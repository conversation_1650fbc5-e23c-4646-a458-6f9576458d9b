import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

// GET endpoint to retrieve feedback (for authorized users)
export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      console.log('[Feedback API - GET] User ID from token:', userId)

      // Get URL parameters
      const url = new URL(req.url)
      const schoolId = url.searchParams.get('schoolId')

      // Get the payload instance
      const payload = await getPayload({ config })
      
      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      // Extract user role
      let userRole = typeof user.role === 'object' ? user.role?.slug : user.role
      console.log('[Feedback API - GET] User role:', userRole)

      // Only super-admins, school-admins and teachers can view feedback
      if (userRole !== 'super-admin' && userRole !== 'school-admin' && userRole !== 'teacher') {
        return NextResponse.json(
          { error: 'Forbidden - Insufficient permissions to view feedback' }, 
          { status: 403 }
        )
      }

      // Connect to the database
      const { db } = await connectToDatabase()
      
      // Set up query based on role and parameters
      const query: any = {}
      
      // Filter by school if user is school-admin or teacher
      if (userRole !== 'super-admin' && user.school) {
        let schoolIdValue = user.school
        
        if (typeof user.school === 'object') {
          schoolIdValue = (user.school as any)._id || (user.school as any).id
        }
        
        if (schoolIdValue) {
          query.schoolId = schoolIdValue
        }
      } else if (schoolId && userRole === 'super-admin') {
        // Super admin can filter by school
        query.schoolId = schoolId
      }

      // Try to get data from MongoDB first
      try {
        const feedbackItems = await db.collection('feedback').find(query).sort({ lastUpdated: -1 }).toArray()
        
        if (feedbackItems.length > 0) {
          const formattedFeedback = await Promise.all(feedbackItems.map(async (item) => {
            // Get submitter details if needed
            let submitterName = item.submitterName || 'Unknown User'
            let submitterRole = item.submitterRole || 'unknown'
            let schoolName = item.schoolName || 'Unknown School'
            
            if (!item.submitterName && item.submitterId) {
              try {
                const submitter = await db.collection('users').findOne({
                  _id: typeof item.submitterId === 'string' ? new ObjectId(item.submitterId) : item.submitterId
                })
                
                if (submitter) {
                  submitterName = `${submitter.firstName || ''} ${submitter.lastName || ''}`.trim() || submitter.email
                  submitterRole = typeof submitter.role === 'object' ? submitter.role?.slug : submitter.role
                }
              } catch (e) {
                console.error('[Feedback API] Error fetching submitter:', e)
              }
            }
            
            if (!item.schoolName && item.schoolId) {
              try {
                const school = await db.collection('schools').findOne({
                  _id: typeof item.schoolId === 'string' ? new ObjectId(item.schoolId) : item.schoolId
                })
                
                if (school) {
                  schoolName = school.name
                }
              } catch (e) {
                console.error('[Feedback API] Error fetching school:', e)
              }
            }
            
            return {
              id: item._id.toString(),
              type: item.type || 'issue',
              subject: item.subject || item.title || 'Feedback',
              message: item.message || item.comment || item.description || '',
              status: item.status || 'new',
              priority: item.priority || 'medium',
              schoolId: item.schoolId?.toString() || 'unknown',
              schoolName,
              submitterId: item.submitterId?.toString() || item.userId?.toString() || 'unknown',
              submitterName,
              submitterRole,
              dateSubmitted: item.dateSubmitted || item.createdAt || new Date().toISOString(),
              lastUpdated: item.lastUpdated || item.updatedAt || new Date().toISOString(),
              response: item.response || null,
            }
          }))
          
          // Return in the format expected by the FeedbackManager component
          return NextResponse.json({
            feedback: formattedFeedback
          })
        }
      } catch (mongoError) {
        console.warn('[Feedback API] Error fetching feedback from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails or is empty
      try {
        const payloadWhere: any = {}
        
        if (userRole !== 'super-admin' && user.school) {
          let schoolIdValue = user.school
          
          if (typeof user.school === 'object') {
            schoolIdValue = (user.school as any)._id || (user.school as any).id
          }
          
          if (schoolIdValue) {
            payloadWhere['school'] = { equals: schoolIdValue }
          }
        } else if (schoolId && userRole === 'super-admin') {
          payloadWhere['school'] = { equals: schoolId }
        }
        
        const result = await payload.find({
          collection: 'systemicIssues',
          where: payloadWhere,
          limit: 100,
        })
        
        if (result.docs.length > 0) {
          return NextResponse.json({
            feedback: result.docs.map((item: any) => ({
              id: item.id,
              type: item.type,
              subject: item.title,
              message: item.description,
              status: item.status,
              priority: item.severity,
              schoolId: typeof item.school === 'object' ? item.school?.id : item.school,
              schoolName: typeof item.school === 'object' ? item.school?.name : '',
              submitterId: typeof item.reporter === 'object' ? item.reporter?.id : item.reporter,
              submitterName: typeof item.reporter === 'object' ? `${item.reporter?.firstName || ''} ${item.reporter?.lastName || ''}`.trim() : '',
              submitterRole: typeof item.reporter === 'object' ? (item.reporter?.role?.slug || item.reporter?.role || '') : '',
              dateSubmitted: item.createdAt,
              lastUpdated: item.updatedAt,
              response: item.resolution,
            })),
          })
        }
      } catch (payloadError) {
        console.warn('[Feedback API] Error fetching feedback from Payload CMS:', payloadError)
      }

      // If no data, return empty array
      return NextResponse.json({ feedback: [] })
    } catch (error) {
      console.error('[Feedback API - GET] Token verification error:', error)
      return NextResponse.json(
        { error: 'Unauthorized', details: error instanceof Error ? error.message : String(error) },
        { status: 401 },
      )
    }
  } catch (error) {
    console.error('[Feedback API - GET] Error retrieving feedback:', error)
    return NextResponse.json(
      {
        error: 'Internal Server Error',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}

// POST endpoint to submit feedback
export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json().catch(() => ({}))
    const { rating, comment, type, subject, message } = body

    // For backward compatibility, allow both old and new formats
    const isFeedbackFormat = typeof rating === 'number' && (rating >= 1 && rating <= 5)
    const isIssueFormat = type && subject && message

    if (!isFeedbackFormat && !isIssueFormat) {
      return NextResponse.json({ 
        error: 'Invalid request format. Must include either rating (1-5) or type/subject/message fields.' 
      }, { status: 400 })
    }

    // Get the token from cookies (optional - anonymous feedback is allowed)
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    let userId = null
    let schoolId = null
    let userEmail = null
    let userName = null

    // If token exists, get the user ID and school
    if (token) {
      try {
        // Verify the token
        const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
        userId = typeof decoded === 'object' ? decoded.id : null
        
        if (userId) {
          // Get the payload instance
          const payload = await getPayload({ config })
          
          // Get the current user
          const user = await payload.findByID({
            collection: 'users',
            id: userId,
            depth: 1,
          })
          
          if (user) {
            userName = `${user.firstName || ''} ${user.lastName || ''}`.trim()
            userEmail = user.email
            
            if (user.school) {
              // Extract school ID
              if (typeof user.school === 'string') {
                schoolId = user.school
              } else if (typeof user.school === 'object') {
                schoolId = user.school.id
              }
            }
          }
        }
      } catch (e) {
        console.error('[Feedback API - POST] Error processing token:', e)
        // Continue without user association
      }
    }

    // Connect to the database
    const { db } = await connectToDatabase()

    // Create the feedback document based on the format
    let feedbackDoc
    
    if (isFeedbackFormat) {
      // Old format - rating-based feedback
      feedbackDoc = {
        rating,
        comment: comment || null,
        userId: userId ? new ObjectId(userId) : null,
        submitterId: userId,
        submitterName: userName,
        submitterEmail: userEmail,
        schoolId: schoolId ? new ObjectId(schoolId) : null,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastUpdated: new Date(),
        status: 'new'
      }
    } else {
      // New format - issue-based feedback
      feedbackDoc = {
        type: type || 'issue',
        subject: subject || 'Feedback',
        message: message || '',
        status: 'new',
        priority: body.priority || 'medium',
        userId: userId ? new ObjectId(userId) : null,
        submitterId: userId,
        submitterName: userName,
        submitterEmail: userEmail,
        schoolId: schoolId ? new ObjectId(schoolId) : null,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastUpdated: new Date(),
        dateSubmitted: new Date()
      }
    }

    // Insert the feedback
    const result = await db.collection('feedback').insertOne(feedbackDoc)

    if (!result.insertedId) {
      return NextResponse.json({ error: 'Failed to save feedback' }, { status: 500 })
    }

    // Log the feedback activity
    if (userId) {
      try {
        await db.collection('activities').insertOne({
          userId: new ObjectId(userId),
          activityType: 'feedback',
          details: {
            description: isFeedbackFormat ? 'Submitted platform feedback' : 'Reported an issue',
            feedbackId: result.insertedId,
          },
          school: schoolId ? new ObjectId(schoolId) : null,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
      } catch (e) {
        console.error('[Feedback API - POST] Error logging activity:', e)
        // Continue without activity logging
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Feedback submitted successfully',
      feedbackId: result.insertedId,
    })
  } catch (error) {
    console.error('[Feedback API - POST] Error submitting feedback:', error)
    return NextResponse.json(
      {
        error: 'Internal Server Error',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}

// Helper function to handle Payload CMS feedback updates
async function handlePayloadFeedback(payload: any, feedbackId: string, action: string, body: any) {
  try {
    const existingPayloadFeedback = await payload.findByID({
      collection: 'systemicIssues',
      id: feedbackId,
    })
    
    if (!existingPayloadFeedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 })
    }
    
    // Update the feedback in Payload CMS
    if (action === 'status') {
      await payload.update({
        collection: 'systemicIssues',
        id: feedbackId,
        data: {
          status: body.status,
        },
      })
    } else if (action === 'response') {
      await payload.update({
        collection: 'systemicIssues',
        id: feedbackId,
        data: {
          resolution: body.response,
          status: body.status,
        },
      })
    }
    
    // Get the updated feedback item
    const updatedFeedback = await payload.findByID({
      collection: 'systemicIssues',
      id: feedbackId,
    })
    
    if (!updatedFeedback) {
      return NextResponse.json({ error: 'Failed to retrieve updated feedback' }, { status: 500 })
    }
    
    // Format the response
    const formattedFeedback = {
      id: updatedFeedback.id,
      type: updatedFeedback.type,
      subject: updatedFeedback.title,
      message: updatedFeedback.description,
      status: updatedFeedback.status,
      priority: updatedFeedback.severity,
      schoolId: typeof updatedFeedback.school === 'object' ? updatedFeedback.school?.id : updatedFeedback.school,
      schoolName: typeof updatedFeedback.school === 'object' ? updatedFeedback.school?.name : '',
      submitterId: typeof updatedFeedback.reporter === 'object' ? updatedFeedback.reporter?.id : updatedFeedback.reporter,
      submitterName: typeof updatedFeedback.reporter === 'object' ? 
        `${updatedFeedback.reporter?.firstName || ''} ${updatedFeedback.reporter?.lastName || ''}`.trim() : '',
      submitterRole: typeof updatedFeedback.reporter === 'object' && updatedFeedback.reporter.role ? 
        (typeof updatedFeedback.reporter.role === 'object' ? updatedFeedback.reporter.role.slug : updatedFeedback.reporter.role) : '',
      dateSubmitted: updatedFeedback.createdAt,
      lastUpdated: updatedFeedback.updatedAt,
      response: updatedFeedback.resolution,
    }
    
    return NextResponse.json({ feedback: formattedFeedback })
  } catch (payloadError) {
    console.error('[Feedback API] Error updating feedback in Payload CMS:', payloadError)
    return NextResponse.json({ error: 'Feedback not found' }, { status: 404 })
  }
}

// PATCH endpoint for feedback status and response updates
export async function PATCH(req: NextRequest, { params }: { params: { slug: string[] } }) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      const userId = typeof decoded === 'object' ? decoded.id : null
      
      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      console.log('[Feedback API - PATCH] User ID from token:', userId)

      // Get the payload instance
      const payload = await getPayload({ config })
      
      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })
      
      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      // Extract user role
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
      console.log('[Feedback API - PATCH] User role:', userRole)

      // Only specific roles can update feedback
      const canUpdateFeedback = userRole === 'super-admin' || userRole === 'school-admin'
      if (!canUpdateFeedback) {
        return NextResponse.json({ error: 'Forbidden - Insufficient permissions to update feedback' }, { status: 403 })
      }

      // Extract feedback ID and action from the URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      
      // Properly extract the feedbackId and action from the path
      // The path format is /api/dashboard/feedback/[feedbackId]/[action]
      let feedbackId = '';
      let action = '';
      
      // Find the index of "feedback" in the path and get the next two components
      const feedbackIndex = pathParts.findIndex(part => part === 'feedback');
      if (feedbackIndex !== -1 && feedbackIndex + 2 < pathParts.length) {
        feedbackId = pathParts[feedbackIndex + 1];
        action = pathParts[feedbackIndex + 2];
      } else {
        return NextResponse.json({ error: 'Invalid URL format' }, { status: 400 })
      }
      
      console.log(`[Feedback API - PATCH] Processing: feedbackId=${feedbackId}, action=${action}`);

      // Parse request body
      const body = await req.json()

      // Connect to the database
      const { db } = await connectToDatabase()
      const feedbackCollection = db.collection('feedback')
      
      try {
        // Try to convert feedbackId to ObjectId
        let objectId: ObjectId
        try {
          objectId = new ObjectId(feedbackId)
        } catch (e) {
          // If feedbackId is not a valid ObjectId, try to find it in Payload CMS
          return handlePayloadFeedback(payload, feedbackId, action, body)
        }
        
        // Check if feedback exists in MongoDB
        const existingFeedback = await feedbackCollection.findOne({ _id: objectId })
        
        if (!existingFeedback) {
          // Try to find it in Payload CMS if not in MongoDB
          return handlePayloadFeedback(payload, feedbackId, action, body)
        }
        
        // Update fields based on action
        const updateData: Record<string, any> = {
          updatedAt: new Date(),
          lastUpdated: new Date(),
        }
        
        if (action === 'status') {
          updateData.status = body.status
        } else if (action === 'response') {
          updateData.response = body.response
          if (body.status) {
            updateData.status = body.status
          }
        } else {
          return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
        }
        
        // Update the feedback in MongoDB
        const result = await feedbackCollection.updateOne(
          { _id: objectId },
          { $set: updateData }
        )
        
        if (result.modifiedCount === 0) {
          return NextResponse.json({ error: 'Failed to update feedback' }, { status: 500 })
        }
        
        // Get the updated feedback item
        const updatedFeedback = await feedbackCollection.findOne({ _id: objectId })
        
        if (!updatedFeedback) {
          return NextResponse.json({ error: 'Failed to retrieve updated feedback' }, { status: 500 })
        }
        
        // Format the response
        const formattedFeedback = {
          id: updatedFeedback._id.toString(),
          type: updatedFeedback.type || 'issue',
          subject: updatedFeedback.subject || updatedFeedback.title || 'Feedback',
          message: updatedFeedback.message || updatedFeedback.comment || updatedFeedback.description || '',
          status: updatedFeedback.status || 'new',
          priority: updatedFeedback.priority || 'medium',
          schoolId: updatedFeedback.schoolId?.toString() || 'unknown',
          schoolName: updatedFeedback.schoolName || 'Unknown School',
          submitterId: updatedFeedback.submitterId?.toString() || updatedFeedback.userId?.toString() || 'unknown',
          submitterName: updatedFeedback.submitterName || 'Unknown User',
          submitterRole: updatedFeedback.submitterRole || 'unknown',
          dateSubmitted: updatedFeedback.dateSubmitted || updatedFeedback.createdAt || new Date().toISOString(),
          lastUpdated: updatedFeedback.updatedAt?.toISOString() || new Date().toISOString(),
          response: updatedFeedback.response || null,
        }
        
        return NextResponse.json({ feedback: formattedFeedback })
      } catch (dbError) {
        console.error('[Feedback API - PATCH] Database error:', dbError)
        return NextResponse.json({ error: 'Database error' }, { status: 500 })
      }
    } catch (error) {
      console.error('[Feedback API - PATCH] Token verification error:', error)
      return NextResponse.json(
        { error: 'Unauthorized', details: error instanceof Error ? error.message : String(error) },
        { status: 401 },
      )
    }
  } catch (error) {
    console.error('[Feedback API - PATCH] Error updating feedback:', error)
    return NextResponse.json(
      {
        error: 'Internal Server Error',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
} 