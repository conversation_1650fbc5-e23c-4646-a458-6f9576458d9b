'use client'

import { useEffe<PERSON>, useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>List,
  <PERSON>bsTrig<PERSON>,
} from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/components/ui/use-toast'
import { MoreHorizontal, SearchIcon, PenIcon, EyeIcon, TrashIcon, CheckCircle2Icon } from 'lucide-react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ReviewsManager } from './reviews-manager'

interface ContentItem {
  id: string
  title: string
  author: {
    id: string
    name: string
  }
  status: string
  schoolName: string
  schoolId: string
  createdAt: string
  updatedAt: string
  type: 'article' | 'news' | 'comment'
}

interface ContentManagerProps {
  schoolId?: string
  type?: 'article' | 'news' | 'comment'
}

// Ensure the correct singular type is used for API requests
const typeMap: Record<string, string> = {
  articles: 'article',
  news: 'news',
  comments: 'comment',
  article: 'article',
  comment: 'comment',
};

export function ContentManager({ schoolId, type }: ContentManagerProps) {
  const [loading, setLoading] = useState(true)
  const [content, setContent] = useState<ContentItem[]>([])
  const [filteredContent, setFilteredContent] = useState<ContentItem[]>([])
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string>(type || 'article')
  const [filters, setFilters] = useState({
    search: '',
    school: 'all',
    status: 'all',
  })
  
  const fetchContent = async (tabType: string) => {
    try {
      setLoading(true)
      const apiType = typeMap[tabType] || tabType
      let url = `/api/dashboard/super-admin/content?type=${apiType}`
      if (schoolId) url += `&schoolId=${schoolId}`
      // Add pagination and other filters as needed
      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to fetch content')
      const data = await response.json()
      setContent(data.content || [])
      setFilteredContent(data.content || [])
      setLoading(false)
    } catch (error) {
      console.error('Error fetching content:', error)
      setError('Failed to load content data')
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchContent(activeTab)
  }, [activeTab])
  
  useEffect(() => {
    // Filter content based on current filters
    let filtered = [...content]
    
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(searchLower) || 
        item.author.name.toLowerCase().includes(searchLower) ||
        item.schoolName.toLowerCase().includes(searchLower)
      )
    }
    
    if (filters.school !== 'all') {
      filtered = filtered.filter(item => item.schoolId === filters.school)
    }
    
    if (filters.status !== 'all') {
      filtered = filtered.filter(item => item.status === filters.status)
    }
    
    setFilteredContent(filtered)
  }, [filters, content])

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    setFilters({
      search: '',
      school: 'all',
      status: 'all',
    })
  }

  const handleForcePublish = async (id: string) => {
    try {
      // This will be replaced with an actual API call when implemented
      // const response = await fetch(`/api/dashboard/super-admin/content/${id}/publish`, {
      //   method: 'POST',
      // })
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // Update status in state
      setContent(content.map(item => 
        item.id === id 
          ? { ...item, status: 'published', updatedAt: new Date().toISOString() }
          : item
      ))
      
      toast({
        title: "Content published",
        description: "The content has been force-published successfully",
      })
    } catch (error) {
      console.error('Error publishing content:', error)
      toast({
        title: "Error publishing content",
        description: "There was a problem publishing the content",
        variant: "destructive",
      })
    }
  }

  const handleDeleteContent = async (id: string) => {
    try {
      // This will be replaced with an actual API call when implemented
      // const response = await fetch(`/api/dashboard/super-admin/content/${id}`, {
      //   method: 'DELETE',
      // })
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // Remove content from state
      setContent(content.filter(item => item.id !== id))
      
      toast({
        title: "Content deleted",
        description: "The content has been deleted successfully",
      })
    } catch (error) {
      console.error('Error deleting content:', error)
      toast({
        title: "Error deleting content",
        description: "There was a problem deleting the content",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('ar-EG', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getContentUrl = (item: ContentItem) => {
    switch (item.type) {
      case 'article':
        return `/articles/${item.id}`
      case 'news':
        return `/news/${item.id}`
      case 'comment':
        return `/comments/${item.id}`
      default:
        return '#'
    }
  }

  if (loading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-[400px] w-full" />
      </div>
    )
  }

  if (error) {
    return <div className="text-red-500">خطأ: {error}</div>
  }

  // Get unique schools for filter
  const schools = [
    { id: 'all', name: 'جميع المدارس' },
    ...Array.from(new Set(content.map(item => item.schoolId))).map(schoolId => {
      const schoolItem = content.find(item => item.schoolId === schoolId)
      return {
        id: schoolId,
        name: schoolItem?.schoolName || 'مدرسة غير معروفة',
      }
    })
  ]

  return (
    <div className="space-y-4" dir="rtl">
      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList>
          <TabsTrigger value="articles">المقالات</TabsTrigger>
          <TabsTrigger value="news">الأخبار</TabsTrigger>
          <TabsTrigger value="reviews">المراجعات</TabsTrigger>
        </TabsList>
        
        <div className="flex flex-col sm:flex-row justify-between gap-4 mt-6">
          <div className="flex items-center gap-2 w-full sm:w-auto">
            <SearchIcon className="w-4 h-4 text-muted-foreground" />
            <Input
              placeholder={`البحث في ${activeTab === 'articles' ? 'المقالات' : activeTab === 'news' ? 'الأخبار' : 'المراجعات'}...`}
              className="max-w-xs"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            />
          </div>
          
          <div className="flex flex-wrap items-center gap-2">
            <Select 
              value={filters.school} 
              onValueChange={(value) => setFilters(prev => ({ ...prev, school: value }))}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="اختر المدرسة" />
              </SelectTrigger>
              <SelectContent>
                {schools.map(school => (
                  <SelectItem key={school.id} value={school.id}>{school.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select 
              value={filters.status} 
              onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="published">منشور</SelectItem>
                <SelectItem value="pending">قيد الانتظار</SelectItem>
                <SelectItem value="draft">مسودة</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <TabsContent value="articles" className="mt-4">
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>العنوان</TableHead>
                  <TableHead>الكاتب</TableHead>
                  <TableHead>المدرسة</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead className="text-left">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContent.length > 0 ? (
                  filteredContent.filter(item => item.type === 'article').map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium max-w-xs truncate" title={item.title}>
                        {item.title}
                      </TableCell>
                      <TableCell>{item.author.name}</TableCell>
                      <TableCell>{item.schoolName}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={`${getStatusColor(item.status)}`}>
                          {item.status === 'published' ? 'منشور' : 
                           item.status === 'pending' ? 'قيد الانتظار' : 
                           item.status === 'draft' ? 'مسودة' : item.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatDate(item.updatedAt)}</TableCell>
                      <TableCell className="text-left">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">فتح القائمة</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <a href={getContentUrl(item)} target="_blank" rel="noopener noreferrer">
                                <EyeIcon className="h-4 w-4 ml-2" /> عرض
                              </a>
                            </DropdownMenuItem>
                            {item.type === 'news' && (
                              <DropdownMenuItem asChild>
                                <a href={`/dashboard/news/edit/${item.id}`}>
                                  <PenIcon className="h-4 w-4 ml-2" /> تعديل
                                </a>
                              </DropdownMenuItem>
                            )}
                            {item.type === 'article' && (
                              <DropdownMenuItem asChild>
                                <a href={`/dashboard/articles/edit/${item.id}`}>
                                  <PenIcon className="h-4 w-4 ml-2" /> تعديل
                                </a>
                              </DropdownMenuItem>
                            )}
                            {item.status !== 'published' && (
                              <DropdownMenuItem onClick={() => handleForcePublish(item.id)}>
                                <CheckCircle2Icon className="h-4 w-4 ml-2" /> نشر إجباري
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteContent(item.id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <TrashIcon className="h-4 w-4 ml-2" /> حذف
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                      لم يتم العثور على مقالات مطابقة للفلاتر
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <div className="text-xs text-muted-foreground mt-4">
              عرض {filteredContent.filter(item => item.type === 'article').length} من {content.filter(item => item.type === 'article').length} مقال
            </div>
        </TabsContent>

        <TabsContent value="news" className="mt-4">
        <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>العنوان</TableHead>
                  <TableHead>الكاتب</TableHead>
                  <TableHead>المدرسة</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead className="text-left">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContent.length > 0 ? (
                  filteredContent.filter(item => item.type === 'news').map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium max-w-xs truncate" title={item.title}>
                        {item.title}
                      </TableCell>
                      <TableCell>{item.author.name}</TableCell>
                      <TableCell>{item.schoolName}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={`${getStatusColor(item.status)}`}>
                          {item.status === 'published' ? 'منشور' : 
                           item.status === 'pending' ? 'قيد الانتظار' : 
                           item.status === 'draft' ? 'مسودة' : item.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatDate(item.updatedAt)}</TableCell>
                      <TableCell className="text-left">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">فتح القائمة</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <a href={getContentUrl(item)} target="_blank" rel="noopener noreferrer">
                                <EyeIcon className="h-4 w-4 ml-2" /> عرض
                              </a>
                            </DropdownMenuItem>
                            {item.type === 'news' && (
                              <DropdownMenuItem asChild>
                                <a href={`/dashboard/news/edit/${item.id}`}>
                                  <PenIcon className="h-4 w-4 ml-2" /> تعديل
                                </a>
                              </DropdownMenuItem>
                            )}
                            {item.type === 'article' && (
                              <DropdownMenuItem asChild>
                                <a href={`/dashboard/articles/edit/${item.id}`}>
                                  <PenIcon className="h-4 w-4 ml-2" /> تعديل
                                </a>
                              </DropdownMenuItem>
                            )}
                            {item.status !== 'published' && (
                              <DropdownMenuItem onClick={() => handleForcePublish(item.id)}>
                                <CheckCircle2Icon className="h-4 w-4 ml-2" /> نشر إجباري
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteContent(item.id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <TrashIcon className="h-4 w-4 ml-2" /> حذف
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                      لم يتم العثور على أخبار مطابقة للفلاتر
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <div className="text-xs text-muted-foreground mt-4">
              عرض {filteredContent.filter(item => item.type === 'news').length} من {content.filter(item => item.type === 'news').length} خبر
            </div>
        </TabsContent>

        <TabsContent value="reviews" className="mt-4">
          <ReviewsManager schoolId={schoolId} />
        </TabsContent>
      </Tabs>
    </div>
  )
} 