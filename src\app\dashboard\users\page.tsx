'use client'

import { useEffect, useState } from 'react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { PlusCircle, Pencil, Trash2, Eye } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string | { slug: string };
  school?: { id: string, name: string } | string;
}

export default function UsersPage() {
  const { toast } = useToast()
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showViewDialog, setShowViewDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [editForm, setEditForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    role: '',
  })

  useEffect(() => {
    fetchUsers()
  }, [])

  async function fetchUsers() {
    try {
      setIsLoading(true)
      const response = await fetch('/api/dashboard/users', {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }

      const data = await response.json()
      setUsers(data.users || [])
      setIsLoading(false)
    } catch (err) {
      console.error('Error fetching users:', err)
      setError('Failed to load users. Please try again.')
      setIsLoading(false)
    }
  }

  const handleViewUser = (user: User) => {
    setSelectedUser(user)
    setShowViewDialog(true)
  }

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setEditForm({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: typeof user.role === 'object' ? user.role.slug : user.role,
    })
    setShowEditDialog(true)
  }

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user)
    setShowDeleteDialog(true)
  }

  const confirmDeleteUser = async () => {
    if (!selectedUser) return
    
    try {
      setIsSubmitting(true)
      const response = await fetch(`/api/dashboard/users/${selectedUser.id}`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete user')
      }

      toast({
        title: 'User deleted',
        description: `${selectedUser.firstName} ${selectedUser.lastName} has been deleted successfully.`,
      })

      // Refresh the user list
      fetchUsers()
      setShowDeleteDialog(false)
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to delete user. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const submitEditForm = async () => {
    if (!selectedUser) return
    
    try {
      setIsSubmitting(true)
      const response = await fetch(`/api/dashboard/users/${selectedUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(editForm),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user')
      }

      toast({
        title: 'User updated',
        description: `${editForm.firstName} ${editForm.lastName}'s information has been updated successfully.`,
      })

      // Refresh the user list
      fetchUsers()
      setShowEditDialog(false)
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to update user. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div dir="rtl" className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6" dir="rtl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">إدارة المستخدمين</h1>
          <Button>
            <PlusCircle className="ml-2 h-4 w-4" />
            إضافة مستخدم
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>جميع المستخدمين</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الاسم</TableHead>
                  <TableHead>البريد الإلكتروني</TableHead>
                  <TableHead>الدور</TableHead>
                  <TableHead>المدرسة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.length > 0 ? (
                  users.map((user: User) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        {user.firstName} {user.lastName}
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        {typeof user.role === 'object' ? 
                          (user.role?.slug === 'teacher' ? 'معلم' : 
                           user.role?.slug === 'student' ? 'طالب' : 
                           user.role?.slug === 'mentor' ? 'موجه' : 
                           user.role?.slug === 'super-admin' ? 'مشرف أعلى' : 
                           user.role?.slug === 'school-admin' ? 'مدير مدرسة' : 
                           user.role?.slug) : 
                          (user.role === 'teacher' ? 'معلم' : 
                           user.role === 'student' ? 'طالب' : 
                           user.role === 'mentor' ? 'موجه' : 
                           user.role === 'super-admin' ? 'مشرف أعلى' : 
                           user.role === 'school-admin' ? 'مدير مدرسة' : 
                           user.role)
                        }
                      </TableCell>
                      <TableCell>
                        {typeof user.school === 'object' ? user.school?.name : 'غير متوفر'}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleViewUser(user)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEditUser(user)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="text-red-500 hover:text-red-700"
                            onClick={() => handleDeleteUser(user)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      لم يتم العثور على مستخدمين
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* View User Dialog */}
        <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>عرض المستخدم</DialogTitle>
            </DialogHeader>
            {selectedUser && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">الاسم الأول</Label>
                    <p className="font-medium">{selectedUser.firstName}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">اسم العائلة</Label>
                    <p className="font-medium">{selectedUser.lastName}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">البريد الإلكتروني</Label>
                    <p className="font-medium">{selectedUser.email}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">الدور</Label>
                    <p className="font-medium">
                      {typeof selectedUser.role === 'object' ? 
                        (selectedUser.role?.slug === 'teacher' ? 'معلم' : 
                         selectedUser.role?.slug === 'student' ? 'طالب' : 
                         selectedUser.role?.slug === 'mentor' ? 'موجه' : 
                         selectedUser.role?.slug === 'super-admin' ? 'مشرف أعلى' : 
                         selectedUser.role?.slug === 'school-admin' ? 'مدير مدرسة' : 
                         selectedUser.role?.slug) : 
                        (selectedUser.role === 'teacher' ? 'معلم' : 
                         selectedUser.role === 'student' ? 'طالب' : 
                         selectedUser.role === 'mentor' ? 'موجه' : 
                         selectedUser.role === 'super-admin' ? 'مشرف أعلى' : 
                         selectedUser.role === 'school-admin' ? 'مدير مدرسة' : 
                         selectedUser.role)
                      }
                    </p>
                  </div>
                </div>
                <div>
                  <Label className="text-muted-foreground">المدرسة</Label>
                  <p className="font-medium">
                    {typeof selectedUser.school === 'object' ? selectedUser.school?.name : 'غير متوفر'}
                  </p>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowViewDialog(false)}>
                إغلاق
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit User Dialog */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تعديل المستخدم</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">الاسم الأول</Label>
                  <Input
                    id="firstName"
                    value={editForm.firstName}
                    onChange={(e) => setEditForm({ ...editForm, firstName: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">اسم العائلة</Label>
                  <Input
                    id="lastName"
                    value={editForm.lastName}
                    onChange={(e) => setEditForm({ ...editForm, lastName: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input
                  id="email"
                  value={editForm.email}
                  onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">الدور</Label>
                <Select
                  value={editForm.role}
                  onValueChange={(value) => setEditForm({ ...editForm, role: value })}
                >
                  <SelectTrigger id="role">
                    <SelectValue placeholder="اختر الدور" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="teacher">معلم</SelectItem>
                    <SelectItem value="student">طالب</SelectItem>
                    <SelectItem value="mentor">موجه</SelectItem>
                    <SelectItem value="school-admin">مدير مدرسة</SelectItem>
                    <SelectItem value="super-admin">مشرف أعلى</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowEditDialog(false)} disabled={isSubmitting}>
                إلغاء
              </Button>
              <Button onClick={submitEditForm} disabled={isSubmitting}>
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete User Dialog */}
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تأكيد الحذف</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p>
                هل أنت متأكد من رغبتك في حذف المستخدم{' '}
                <span className="font-medium">
                  {selectedUser?.firstName} {selectedUser?.lastName}
                </span>؟
              </p>
              <p className="text-sm text-gray-500 mt-2">
                هذا الإجراء لا يمكن التراجع عنه وسيؤدي إلى إزالة جميع بيانات المستخدم.
              </p>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowDeleteDialog(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDeleteUser}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'جاري الحذف...' : 'تأكيد الحذف'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
