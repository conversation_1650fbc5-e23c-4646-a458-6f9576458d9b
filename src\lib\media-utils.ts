/**
 * Media Utilities
 *
 * Utility functions for handling media files and paths
 */

/**
 * Check if a string is a media path
 *
 * @param path The path to check
 * @returns True if the path is a media path, false otherwise
 */
export function isMediaPath(path: string | any): boolean {
  // Handle non-string values
  if (!path) return false

  // Convert to string if it's not already
  const pathStr = typeof path === 'string' ? path : String(path)

  // Check if the path is empty after conversion
  if (!pathStr || pathStr.trim() === '') return false

  // Check for common media path patterns
  return (
    // Check for API paths
    pathStr.includes('/api/media') ||
    pathStr.startsWith('/media') ||

    // Check for file paths
    pathStr.includes('/file/') ||

    // Check for file extensions
    /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(pathStr) ||

    // Check for specific patterns we've seen in the logs
    /\/file\/\d+-\d+/.test(pathStr) || // Pattern like /file/1156104-5

    // Check for URL-encoded paths
    pathStr.includes('%2Fapi%2Fmedia') ||
    pathStr.includes('%2Fmedia') ||
    pathStr.includes('%2Ffile%2F') ||

    // Check for quoted paths (often seen in error messages)
    pathStr.includes('"/api/media') ||
    pathStr.includes('"/media') ||
    pathStr.includes('"/file/')
  )
}

/**
 * Safely handle a potential media path in a query
 *
 * @param query The query object to check
 * @returns A safe query object with media paths handled
 */
export function safeMediaQuery(query: Record<string, any>): Record<string, any> {
  // Handle null or undefined
  if (!query) return {}

  // Handle arrays
  if (Array.isArray(query)) {
    const result = query.map(item =>
      typeof item === 'object' ? safeMediaQuery(item) :
      isMediaPath(item) ? null : item
    ).filter(Boolean) // Remove null values
    
    // Return empty object if result is empty to avoid Payload issues
    return result.length > 0 ? result : {}
  }

  // Create a copy of the query
  const safeQuery = { ...query }
  
  // Track if all properties were removed
  let hasProperties = false

  // Check each property for media paths
  for (const key in safeQuery) {
    if (!safeQuery.hasOwnProperty(key)) continue

    const value = safeQuery[key]

    if (value === null || value === undefined) {
      // Keep null/undefined values
      hasProperties = true
      continue
    } else if (typeof value === 'string' && isMediaPath(value)) {
      // Remove media paths from queries to prevent ObjectId cast errors
      delete safeQuery[key]
    } else if (Array.isArray(value)) {
      // Handle arrays
      const cleanedArray = value
        .map(item => typeof item === 'object' ? safeMediaQuery(item) : isMediaPath(item) ? null : item)
        .filter(Boolean); // Remove null values
      
      if (cleanedArray.length > 0) {
        safeQuery[key] = cleanedArray;
        hasProperties = true;
      } else {
        delete safeQuery[key];
      }
    } else if (typeof value === 'object') {
      // Recursively check nested objects
      const cleanedObj = safeMediaQuery(value);
      
      // Only keep the object if it has properties
      if (Object.keys(cleanedObj).length > 0) {
        safeQuery[key] = cleanedObj;
        hasProperties = true;
      } else {
        delete safeQuery[key];
      }
    } else {
      // Regular value
      hasProperties = true;
    }
  }

  // If all properties were removed, ensure we have a valid default query
  if (!hasProperties || Object.keys(safeQuery).length === 0) {
    // Return a query that will match everything
    return {};
  }

  return safeQuery;
}

/**
 * Create a safe MongoDB query that excludes media paths
 *
 * @param queries Array of query objects
 * @returns Array of safe query objects
 */
export function createSafeQueries(queries: Record<string, any>[]): Record<string, any>[] {
  return queries.map((query) => safeMediaQuery(query))
}

/**
 * Handle errors related to media paths
 *
 * @param error The error to check
 * @returns True if the error is related to media paths, false otherwise
 */
export function isMediaPathError(error: any): boolean {
  if (!error) return false

  try {
    // Check if the error message contains media path indicators
    const errorString = error.toString()

    // Check for common media path patterns in the error
    if (
      errorString.includes('/api/media') ||
      errorString.includes('/media') ||
      errorString.includes('/file/') ||
      errorString.includes('.jpg') ||
      errorString.includes('.jpeg') ||
      errorString.includes('.png') ||
      errorString.includes('.gif') ||
      errorString.includes('.webp') ||
      errorString.includes('.svg') ||
      (error.value && isMediaPath(error.value))
    ) {
      return true
    }

    // Check for CastError with ObjectId failures
    if (
      (error.name === 'CastError' || errorString.includes('CastError')) &&
      (error.kind === 'ObjectId' || errorString.includes('ObjectId')) &&
      error.value
    ) {
      // Check if the value looks like a media path
      return isMediaPath(error.value)
    }

    // Check for specific error patterns we've seen in the logs
    if (
      errorString.includes('Cast to ObjectId failed') ||
      errorString.includes('at path "_id" for model "media"') ||
      errorString.includes('BSONError: input must be a 24 character hex string')
    ) {
      return true
    }

    // Check for stringValue property which often contains the problematic value
    if (error.stringValue && isMediaPath(error.stringValue)) {
      return true
    }

    // Check for nested errors
    if (error.reason) {
      return isMediaPathError(error.reason)
    }

    // Check for errors in the 'errors' property (common in validation errors)
    if (error.errors) {
      for (const key in error.errors) {
        if (isMediaPathError(error.errors[key])) {
          return true
        }
      }
    }
  } catch (e) {
    // If we get an error while checking, it's safer to return false
    console.error('Error in isMediaPathError:', e)
  }

  return false
}

/**
 * Log a media path error with helpful information
 *
 * @param error The error to log
 * @param context Additional context information
 */
export function logMediaPathError(error: any, context: string = ''): void {
  console.warn(`Media path error detected ${context ? `in ${context}` : ''}:`)
  console.warn('This error is likely caused by a media path being treated as an ObjectId.')
  console.warn('The operation will continue with alternative queries.')

  // Log the error details for debugging
  if (error.value) {
    console.warn(`Problematic value: ${error.value}`)
  }
  if (error.path) {
    console.warn(`At path: ${error.path}`)
  }
}

/**
 * Safely filter an array of articles to remove any with media path IDs
 * and clean up any media paths in the article properties
 *
 * @param articles Array of articles to filter
 * @returns Filtered array of articles with media paths cleaned up
 */
export function filterMediaPathArticles(articles: any[]): any[] {
  if (!articles || !Array.isArray(articles)) return []

  return articles
    .filter((article) => {
      // Skip null or undefined articles
      if (!article) return false

      // Skip articles with media path IDs
      if (article.id && typeof article.id === 'string' && isMediaPath(article.id)) {
        console.log('Filtering out article with media path ID:', article.id)
        return false
      }

      // Skip articles with media path _ids
      if (article._id && typeof article._id === 'string' && isMediaPath(article._id)) {
        console.log('Filtering out article with media path _id:', article._id)
        return false
      }

      return true
    })
    .map((article) => {
      // Create a copy of the article to avoid modifying the original
      const cleanedArticle = { ...article }

      // Clean up featuredImage if it's a media path
      if (cleanedArticle.featuredImage && typeof cleanedArticle.featuredImage === 'string' && isMediaPath(cleanedArticle.featuredImage)) {
        console.log('Preserving media path in featuredImage:', cleanedArticle.featuredImage)
        // Don't nullify the featuredImage, we need it for displaying images
        // cleanedArticle.featuredImage = null
      }

      // Clean up image if it's a media path
      if (cleanedArticle.image && typeof cleanedArticle.image === 'string' && isMediaPath(cleanedArticle.image)) {
        console.log('Preserving media path in image:', cleanedArticle.image)
        // Don't nullify the image, we need it for displaying images
        // cleanedArticle.image = null
      }

      return cleanedArticle
    })
}

/**
 * Safely handle Payload CMS response to filter out media paths
 *
 * @param response Payload CMS response
 * @returns Safe response with media paths filtered out
 */
export function safePayloadResponse(response: any): any {
  if (!response) return { docs: [] }

  // If response has docs property, filter the docs
  if (response.docs && Array.isArray(response.docs)) {
    return {
      ...response,
      docs: filterMediaPathArticles(response.docs),
    }
  }

  // If response is an array, filter it directly
  if (Array.isArray(response)) {
    return filterMediaPathArticles(response)
  }

  // If response is a single object, check if it's a media path
  if (typeof response === 'object') {
    if (response.id && typeof response.id === 'string' && isMediaPath(response.id)) {
      return null
    }
  }

  return response
}
