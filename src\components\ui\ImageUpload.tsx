'use client'

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Upload, X, Image as ImageIcon } from 'lucide-react'

interface ImageUploadProps {
  value: string
  onChange: (url: string) => void
  disabled?: boolean
}

export function ImageUpload({ value, onChange, disabled = false }: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Check file type
    if (!file.type.startsWith('image/')) {
      setError('يرجى اختيار ملف صورة')
      return
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('يجب أن تكون الصورة أقل من 5 ميجابايت')
      return
    }

    setIsUploading(true)
    setError('')

    try {
      // Create FormData
      const formData = new FormData()
      formData.append('file', file)
      formData.append('alt', 'صورة مميزة للمقال')

      // Upload to Payload CMS media collection
      const response = await fetch('/api/media', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('فشل في تحميل الصورة')
      }

      const data = await response.json()
      console.log('Media upload response:', data);
      
      // Ensure the URL is in the correct format
      let mediaUrl = data.url;
      
      // If it's an ID or filename without the proper path
      if (data.id && !mediaUrl.includes('/api/media/file/')) {
        // Format as /api/media/file/[filename]
        if (data.filename) {
          mediaUrl = `/api/media/file/${data.filename}`;
        } else {
          mediaUrl = `/api/media/file/${data.id}`;
        }
      }
      
      console.log('Using media URL:', mediaUrl);
      onChange(mediaUrl);
    } catch (err) {
      console.error('Error uploading image:', err)
      setError('فشل في تحميل الصورة. يرجى المحاولة مرة أخرى.')
    } finally {
      setIsUploading(false)
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemoveImage = () => {
    onChange('')
    // Clear the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="space-y-2" dir="rtl">
      <Label>الصورة المميزة</Label>
      
      <div className="border border-gray-300 rounded-md p-4">
        {value ? (
          <div className="space-y-2">
            <div className="relative aspect-video w-full overflow-hidden rounded-md bg-gray-100">
              <img 
                src={value} 
                alt="صورة مميزة" 
                className="object-cover w-full h-full"
              />
              {!disabled && (
                <button
                  type="button"
                  onClick={handleRemoveImage}
                  className="absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                  aria-label="إزالة الصورة"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-4">
            <div className="mb-2 rounded-full bg-gray-100 p-2">
              <ImageIcon className="h-6 w-6 text-gray-500" />
            </div>
            <p className="mb-2 text-sm text-gray-500">
              {isUploading ? 'جاري التحميل...' : 'لم يتم اختيار صورة'}
            </p>
            {!disabled && (
              <div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                  disabled={isUploading || disabled}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading || disabled}
                >
                  <Upload className="ml-2 h-4 w-4" />
                  {isUploading ? 'جاري التحميل...' : 'تحميل صورة'}
                </Button>
              </div>
            )}
          </div>
        )}
        
        {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
        
        <p className="text-xs text-gray-500 mt-2">
          الموصى به: 1920×1080 بكسل أو أكبر، بنسبة عرض إلى ارتفاع 16:9، بتنسيق JPG أو PNG، بحد أقصى 5 ميجابايت
        </p>
      </div>
    </div>
  )
}
