import { NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(request: Request) {
  try {
    const { db } = await connectToDatabase()

    // Get query parameters
    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')
    const userRole = url.searchParams.get('userRole')
    const schoolId = url.searchParams.get('schoolId')

    console.log('Fetching notifications with params:', { userId, userRole, schoolId })

    // Validate required parameters
    if (!userId) {
      return NextResponse.json(
        { error: 'userId is a required parameter' },
        { status: 400 },
      )
    }

    // Build query - We'll search for notifications where:
    const userIdConditions = []
    
    // 1. Try both string ID and ObjectId formats
    userIdConditions.push({ userId: userId })
    userIdConditions.push({ user: userId })
    userIdConditions.push({ targetUserId: userId })
    
    // 2. Check the recipient field (used in new notifications)
    userIdConditions.push({ recipient: userId })
    
    // 3. Try with ObjectId if the userId is a valid ObjectId
    try {
      if (userId.length === 24) {
        const userObjectId = new ObjectId(userId)
        userIdConditions.push({ recipient: userObjectId })
        userIdConditions.push({ user: userObjectId })
        userIdConditions.push({ userId: userObjectId })
      }
    } catch (e) {
      console.log('userId is not a valid ObjectId')
    }
    
    // Basic query structure
    const query: any = {
      $or: userIdConditions
    }
    
    // Add global notifications
    const globalCondition = { global: true }
    
    // Add role-specific notifications if role is provided
    const roleConditions = []
    if (userRole) {
      roleConditions.push({ userRole })
    }
    
    // Add school-specific notifications if schoolId is provided
    const schoolConditions = []
    if (schoolId) {
      schoolConditions.push({ schoolId })
      
      // Try with ObjectId if the schoolId is a valid ObjectId
      try {
        if (schoolId.length === 24) {
          const schoolObjectId = new ObjectId(schoolId)
          schoolConditions.push({ schoolId: schoolObjectId })
        }
      } catch (e) {
        console.log('schoolId is not a valid ObjectId')
      }
    }

    // Combine all conditions
    const finalQuery = {
      $or: [
        ...userIdConditions,
        globalCondition,
        ...(roleConditions.length > 0 ? roleConditions : []),
        ...(schoolConditions.length > 0 ? schoolConditions : [])
      ]
    }
    
    console.log('Notification query:', JSON.stringify(finalQuery))

    // Get notifications
    let dbNotifications = await db
      .collection('notifications')
      .find(finalQuery)
      .sort({ createdAt: -1, date: -1 })
      .limit(100)
      .toArray()

    console.log(`Found ${dbNotifications.length} notifications`)

    // Process notifications to ensure they have a consistent format
    let processedNotifications = dbNotifications.map((notification: any) => {
      // Ensure each notification has a unique id property
      const processed = { ...notification }
      if (!processed.id && processed._id) {
        processed.id = typeof processed._id === 'string' ? 
          processed._id : 
          processed._id.toString()
      }
      
      // Ensure createdAt is a string (in case it's a Date object)
      if (processed.createdAt instanceof Date) {
        processed.createdAt = processed.createdAt.toISOString()
      }
      
      return processed
    })

    // If no notifications found, create some test notifications
    if (processedNotifications.length === 0) {
      console.log('No notifications found, creating test data')

      // Create test notifications
      const testNotifications = [
        {
          id: `notification-${Date.now()}-1`,
          title: 'مرحبًا بك في Young Reporter',
          message: 'شكرًا لانضمامك إلى منصة Young Reporter.',
          type: 'system',
          recipient: userId,
          userRole,
          schoolId,
          global: true,
          read: false,
          createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
        },
        {
          id: `notification-${Date.now()}-2`,
          title: 'ميزات جديدة متاحة',
          message: 'تحقق من ميزات لوحة المعلومات الجديدة!',
          type: 'system',
          recipient: userId,
          userRole,
          global: true,
          read: true,
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        },
      ]

      // Insert test notifications into database
      await db.collection('notifications').insertMany(testNotifications)

      // Return the test notifications
      processedNotifications = testNotifications.map(n => ({
        ...n,
        createdAt: n.createdAt instanceof Date ? n.createdAt.toISOString() : n.createdAt
      }))
    }

    return NextResponse.json({
      success: true,
      notifications: processedNotifications,
      unreadCount: processedNotifications.filter((n) => !n.read).length,
    })
  } catch (error) {
    console.error('Error fetching notifications:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch notifications',
      details: error instanceof Error ? error.message : 'Unknown error'  
    }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const { db } = await connectToDatabase()

    // Get request body
    const body = await request.json()
    const {
      title,
      message,
      type,
      userId,
      userRole,
      schoolId,
      link,
      pointsChange,
      targetUserId,
      global,
      recipient,
      data
    } = body

    // Validate required fields - either recipient or userId is required
    if (!title || !message || !type || !(userId || recipient)) {
      return NextResponse.json({ 
        error: 'Missing required fields (title, message, type, and either userId or recipient)',
        receivedFields: Object.keys(body)
      }, { status: 400 })
    }

    // Create notification
    const notification = {
      id: Date.now().toString(),
      title,
      message,
      type,
      userId: userId || recipient, // Use recipient as fallback
      recipient: recipient || userId, // Use userId as fallback
      userRole,
      schoolId,
      link,
      pointsChange,
      targetUserId,
      global: global || false,
      read: false,
      createdAt: new Date(),
      date: new Date().toISOString(),
      data // Additional custom data
    }

    // Save to database
    await db.collection('notifications').insertOne(notification)

    return NextResponse.json({
      success: true,
      notification: {
        ...notification,
        createdAt: notification.createdAt.toISOString()
      },
    })
  } catch (error) {
    console.error('Error creating notification:', error)
    return NextResponse.json({ error: 'Failed to create notification' }, { status: 500 })
  }
}

export async function PUT(request: Request) {
  try {
    const { db } = await connectToDatabase()

    // Get request body
    const body = await request.json()
    const { id, read } = body

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'Notification ID is required' }, { status: 400 })
    }

    // Try to update notification by id field first
    let result = await db.collection('notifications').updateOne({ id }, { $set: { read } })

    // If no document was matched, try to update using MongoDB _id (if it's a valid ObjectId)
    if (result.matchedCount === 0) {
      // Check if the ID could be a valid MongoDB ObjectId
      try {
        const objectId = new ObjectId(id)
        result = await db.collection('notifications').updateOne({ _id: objectId }, { $set: { read } })
      } catch (err) {
        // Not a valid ObjectId, so do nothing
        console.log('ID is not a valid MongoDB ObjectId:', id)
      }
    }

    if (result.matchedCount === 0) {
      return NextResponse.json({ error: 'Notification not found' }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'Notification updated successfully',
    })
  } catch (error) {
    console.error('Error updating notification:', error)
    return NextResponse.json({ error: 'Failed to update notification' }, { status: 500 })
  }
}
