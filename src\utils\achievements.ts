import { Payload } from 'payload'

/**
 * Check if a user has earned any new achievements and award them
 */
export const checkAndAwardAchievements = async ({
  payload,
  userId,
}: {
  payload: Payload
  userId: string
}): Promise<void> => {
  try {
    // Get the user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
      depth: 0,
    })

    if (!user) {
      console.error(`User not found: ${userId}`)
      return
    }

    // Get all achievements
    const achievements = await payload.find({
      collection: 'achievements',
      limit: 100,
    })

    // Get user's existing achievements
    const userAchievements = await payload.find({
      collection: 'user-achievements',
      where: {
        'user.id': {
          equals: userId,
        },
      },
      depth: 1,
    })

    // Get the IDs of achievements the user already has
    const existingAchievementIds = userAchievements.docs.map(
      (ua) => typeof ua.achievement === 'object' ? ua.achievement.id : ua.achievement
    )

    // Check each achievement to see if the user has earned it
    for (const achievement of achievements.docs) {
      // Skip if user already has this achievement
      if (existingAchievementIds.includes(achievement.id)) {
        continue
      }

      // Check if user meets the criteria for this achievement
      const earned = await checkAchievementCriteria({
        payload,
        userId,
        achievement,
      })

      if (earned) {
        // Award the achievement
        await awardAchievement({
          payload,
          userId,
          achievementId: achievement.id,
          metadata: earned.metadata,
        })
      }
    }
  } catch (error) {
    console.error('Error checking achievements:', error)
  }
}

/**
 * Check if a user meets the criteria for an achievement
 */
const checkAchievementCriteria = async ({
  payload,
  userId,
  achievement,
}: {
  payload: Payload
  userId: string
  achievement: any
}): Promise<{ earned: boolean; metadata?: any }> => {
  try {
    const criteria = achievement.criteria || {}
    const type = achievement.type

    // Get user stats based on achievement type
    switch (type) {
      case 'article': {
        // Check article-related achievements
        const articles = await payload.find({
          collection: 'articles',
          where: {
            'author.id': {
              equals: userId,
            },
          },
        })

        const publishedArticles = articles.docs.filter(
          (article) => article.status === 'published'
        )

        // Check specific criteria
        if (criteria.articlesPublished && publishedArticles.length >= criteria.articlesPublished) {
          return {
            earned: true,
            metadata: {
              articlesPublished: publishedArticles.length,
              articleIds: publishedArticles.map((a) => a.id),
            },
          }
        }

        if (criteria.articlesCreated && articles.docs.length >= criteria.articlesCreated) {
          return {
            earned: true,
            metadata: {
              articlesCreated: articles.docs.length,
              articleIds: articles.docs.map((a) => a.id),
            },
          }
        }
        break
      }

      case 'review': {
        // Check review-related achievements
        // For teachers who review articles
        const user = await payload.findByID({
          collection: 'users',
          id: userId,
          depth: 0,
        })

        const userRole = typeof user.role === 'object' ? user.role.slug : user.role

        if (userRole === 'teacher') {
          const articles = await payload.find({
            collection: 'articles',
            where: {
              'reviewedBy.id': {
                equals: userId,
              },
            },
          })

          if (criteria.articlesReviewed && articles.docs.length >= criteria.articlesReviewed) {
            return {
              earned: true,
              metadata: {
                articlesReviewed: articles.docs.length,
                articleIds: articles.docs.map((a) => a.id),
              },
            }
          }
        }
        break
      }

      case 'engagement': {
        // This could be for commenting, liking, etc.
        // For now, we'll just check if they've logged in a certain number of times
        // This would require tracking login counts in the user document
        break
      }

      case 'special': {
        // Special achievements can be manually awarded
        // or based on specific events
        break
      }
    }

    return { earned: false }
  } catch (error) {
    console.error('Error checking achievement criteria:', error)
    return { earned: false }
  }
}

/**
 * Award an achievement to a user
 */
export const awardAchievement = async ({
  payload,
  userId,
  achievementId,
  metadata,
}: {
  payload: Payload
  userId: string
  achievementId: string
  metadata?: any
}): Promise<void> => {
  try {
    // Create the user achievement record
    await payload.create({
      collection: 'user-achievements',
      data: {
        user: userId,
        achievement: achievementId,
        earnedAt: new Date().toISOString(),
        metadata: metadata || {},
      },
    })

    // Get the achievement details for the notification
    const achievement = await payload.findByID({
      collection: 'achievements',
      id: achievementId,
    })

    // Create a notification for the user
    await payload.create({
      collection: 'notifications',
      data: {
        user: userId,
        message: `You've earned the "${achievement.name}" achievement!`,
        type: 'success',
        read: false,
        link: '/profile/achievements',
      },
    })
  } catch (error) {
    console.error('Error awarding achievement:', error)
  }
}
