import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { MongoClient } from 'mongodb'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

export async function POST(req: NextRequest) {
  // Define isDev at the top level of the function
  const isDev =
    !process.env.NODE_ENV ||
    process.env.NODE_ENV === 'development' ||
    process.env.NODE_ENV === 'test'

  try {
    const body = await req.json()
    const { email, password } = body

    if (!email || !password) {
      return NextResponse.json({ error: 'Email and password are required' }, { status: 400 })
    }

    // Connect directly to MongoDB to avoid Payload's login mechanism
    console.log('Attempting to connect to MongoDB...')
    const dbUri = process.env.DATABASE_URI || ''
    console.log('Database URI:', dbUri ? `${dbUri.substring(0, 20)}...` : 'Not set')

    const client = new MongoClient(dbUri)

    try {
      console.log('Connecting to MongoDB...')
      await client.connect()
      console.log('Connected to MongoDB successfully')

      const db = client.db()
      const usersCollection = db.collection('users')
      console.log('Accessing users collection')

      // Find the user by email
      console.log(`Looking up user with email: ${email}`)
      const user = await usersCollection.findOne({ email })
      console.log('User found:', !!user)

      // List all users in the collection (for debugging)
      console.log('Listing all users in the database:')
      const allUsers = await usersCollection.find({}).limit(10).toArray()
      console.log(`Found ${allUsers.length} users:`)
      allUsers.forEach((u) => console.log(`- ${u.email} (ID: ${u._id})`))

      if (!user) {
        console.log('User not found with email:', email)

        // Create a test user if in development mode
        if (isDev) {
          console.log('Creating a test user for development...')
          try {
            // Hash the password
            const salt = await bcrypt.genSalt(10)
            const hashedPassword = await bcrypt.hash('password123', salt)

            // Find the student role
            const rolesCollection = db.collection('roles')
            console.log('Looking for student role...')
            const roles = await rolesCollection.find({}).toArray()
            console.log('Available roles:', roles.map((r) => `${r.name || r.slug}`).join(', '))

            const studentRole = await rolesCollection.findOne({ slug: 'student' })
            console.log('Student role found:', studentRole ? 'yes' : 'no')

            // If no student role exists, create one
            let studentRoleId = studentRole?._id
            if (!studentRole) {
              console.log('Creating student role...')
              const newRole = {
                name: 'Student',
                slug: 'student',
                permissions: ['submit-articles'],
                createdAt: new Date(),
                updatedAt: new Date(),
              }
              const roleResult = await rolesCollection.insertOne(newRole)
              studentRoleId = roleResult.insertedId
              console.log('Student role created with ID:', studentRoleId)
            }

            // Create the user
            const newUser = {
              email: '<EMAIL>',
              hash: hashedPassword,
              salt,
              firstName: 'Test',
              lastName: 'User',
              role: studentRoleId,
              createdAt: new Date(),
              updatedAt: new Date(),
              loginAttempts: 0,
            }

            // Check if user already exists
            const existingUser = await usersCollection.findOne({ email: '<EMAIL>' })
            if (existingUser) {
              console.log('Test user already exists, updating password...')
              await usersCollection.updateOne(
                { email: '<EMAIL>' },
                {
                  $set: {
                    hash: hashedPassword,
                    salt,
                    role: studentRoleId,
                    updatedAt: new Date(),
                  },
                },
              )
              console.log('Test user updated with new password')
            } else {
              const result = await usersCollection.insertOne(newUser)
              console.log('Test user created with ID:', result.insertedId)
            }

            console.log('Please try logging <NAME_EMAIL> / password123')
          } catch (createError) {
            console.error('Error creating test user:', createError)
          }
        }

        return NextResponse.json(
          {
            error: 'Invalid email or password',
            debug:
              process.env.NODE_ENV !== 'production'
                ? 'Try <EMAIL> / password123'
                : undefined,
          },
          { status: 401 },
        )
      }

      console.log('User ID:', user._id.toString())
      console.log('User has password hash:', !!user.hash)

      // Fix loginAttempts if it's NaN
      if (isNaN(user.loginAttempts)) {
        console.log('Fixing NaN loginAttempts')
        await usersCollection.updateOne({ _id: user._id }, { $set: { loginAttempts: 0 } })
      }

      // Verify password
      console.log('Verifying password...')

      // Check if user has a hash
      if (!user.hash) {
        console.log('User has no password hash, creating a default one')
        // Create a default password for development
        const isDev =
          !process.env.NODE_ENV ||
          process.env.NODE_ENV === 'development' ||
          process.env.NODE_ENV === 'test'
        if (isDev) {
          try {
            // Hash the password 'password123'
            const salt = await bcrypt.genSalt(10)
            const hashedPassword = await bcrypt.hash('password123', salt)

            // Update the user with the new password
            await usersCollection.updateOne(
              { _id: user._id },
              {
                $set: {
                  hash: hashedPassword,
                  salt: salt,
                  updatedAt: new Date(),
                },
              },
            )

            console.log('Default password set for user. Try password: password123')

            // Continue with the new password
            const isMatch = await bcrypt.compare('password123', hashedPassword)
            if (isMatch) {
              console.log('Using default password for login')
              // Reset login attempts
              await usersCollection.updateOne({ _id: user._id }, { $set: { loginAttempts: 0 } })
              // Continue with login process
            } else {
              return NextResponse.json(
                {
                  error: 'Password verification failed',
                  debug: isDev ? 'Try password: password123' : undefined,
                },
                { status: 401 },
              )
            }
          } catch (hashError) {
            console.error('Error creating default password:', hashError)
            return NextResponse.json({ error: 'Error setting up user account' }, { status: 500 })
          }
        } else {
          return NextResponse.json({ error: 'Invalid account setup' }, { status: 401 })
        }
      } else {
        // Normal password verification
        try {
          const isMatch = await bcrypt.compare(password, user.hash)
          console.log('Password match:', isMatch)

          if (!isMatch) {
            // Increment login attempts
            await usersCollection.updateOne(
              { _id: user._id },
              { $set: { loginAttempts: (user.loginAttempts || 0) + 1 } },
            )

            return NextResponse.json(
              {
                error: 'Invalid email or password',
                debug: isDev ? 'Try password: password123' : undefined,
              },
              { status: 401 },
            )
          }

          // Reset login attempts on successful login
          await usersCollection.updateOne({ _id: user._id }, { $set: { loginAttempts: 0 } })
        } catch (bcryptError) {
          console.error('Error verifying password:', bcryptError)
          return NextResponse.json({ error: 'Error verifying credentials' }, { status: 500 })
        }
      }

      // Login attempts are already reset in the password verification section

      // Get the payload instance to generate a token
      console.log('Getting Payload instance...')
      const payload = await getPayload({ config })
      console.log('Payload instance obtained')

      // Get the user with relationships populated
      console.log('Getting user with relationships populated...')
      const userId = user._id.toString()
      console.log('User ID for Payload lookup:', userId)

      try {
        const payloadUser = await payload.findByID({
          collection: 'users',
          id: userId,
          depth: 1, // Ensure relationships are populated
        })
        console.log('User found in Payload:', !!payloadUser)

        // Generate a token that matches Payload CMS's format
        console.log('Generating JWT token...')

        // Check if user has pending status
        if (user.status === 'pending') {
          console.log('User has pending status, redirecting to pending-approval page')
          return NextResponse.json({
            success: true,
            redirectUrl: '/pending-approval',
            pendingApproval: true
          })
        }

        // Get the user's role
        const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
        console.log('User role for token:', userRole)

        const token = jwt.sign(
          {
            id: user._id.toString(),
            collection: 'users',
            email: user.email,
            role: userRole, // Include the role in the token
            userStatus: user.status || 'active', // Include the user's status
            // Include these fields to match Payload's token format
            exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 7, // 7 days
            iat: Math.floor(Date.now() / 1000),
            aud: 'payload-cms',
            iss: 'payload-cms',
          },
          process.env.PAYLOAD_SECRET || 'secret',
        )

        console.log('Token generated successfully')

        // Set the payload token as a cookie
        console.log('Setting payload-token cookie with token:', token.substring(0, 20) + '...')

        // Create a response with the cookie
        const response = NextResponse.json({
          user: payloadUser,
          token,
          success: true,
          redirectUrl: '/dashboard',
        })

        // Set the cookie on the response
        response.cookies.set('payload-token', token, {
          httpOnly: true,
          path: '/',
          sameSite: 'lax',
          secure: !isDev, // Only secure in production
          maxAge: 60 * 60 * 24 * 7, // 7 days
        })

        console.log('Cookie set successfully')
        console.log('Login successful, returning response with redirect URL')
        return response
      } catch (payloadError) {
        console.error('Error in Payload operations:', payloadError)
        return NextResponse.json({ error: 'Error processing user data' }, { status: 500 })
      }
    } finally {
      await client.close()
    }
  } catch (error: unknown) {
    console.error('Error in custom login:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return NextResponse.json(
      { error: `An error occurred during login: ${errorMessage}` },
      { status: 500 },
    )
  }
}
