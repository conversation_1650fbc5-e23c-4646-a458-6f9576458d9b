'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { getImageUrl } from '@/utils/imageUtils'

interface News {
  id: string
  title: string
  slug: string
  featuredImage?: any
  publishedAt?: string
  createdAt: string
  tags?: string[]
}

interface ClientMainCarouselProps {
  news: News[]
}

export function ClientMainCarousel({ news }: ClientMainCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)

  // If no news is provided, create a placeholder
  const carouselItems = news.length > 0
    ? news
    : [{
        id: '1',
        title: 'Loading news...',
        slug: '#',
        createdAt: new Date().toISOString(),
        tags: ['News']
      }]

  // Auto-advance the carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % carouselItems.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [carouselItems.length])

  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % carouselItems.length)
  }

  const goToPrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + carouselItems.length) % carouselItems.length)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
  }

  return (
    <div className="relative h-[510px] overflow-hidden ">
      {carouselItems.map((item, index) => {
        // Get featured image
        const newsImage = item.featuredImage
          ? getImageUrl(item.featuredImage)
          : 'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80'

        // Format date
        const publishDate = item.publishedAt
          ? new Date(item.publishedAt)
          : new Date(item.createdAt)

        const formattedDate = publishDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: '2-digit',
        })

        // Get category/tag
        const category = Array.isArray(item.tags) && item.tags.length > 0
          ? item.tags[0]
          : 'News'

        return (
          <div
            key={item.id}
            className={`absolute inset-0 transition-opacity duration-500 ${
              index === currentIndex ? 'opacity-100 z-10' : 'opacity-0 z-0'
            }`}
          >
            <Image
              src={newsImage}
              alt={item.title || 'News image'}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 60vw"
              priority={index === 0}
            />
            <div className="absolute inset-0 overlay flex flex-col justify-end p-6">
              <div className="mb-2 flex items-center">
                <span className="bg-primary text-primary-foreground uppercase font-semibold px-3 py-1 mr-2 text-sm rounded-sm">
                  {category}
                </span>
                <span className="text-white text-sm">{formattedDate}</span>
              </div>
              <Link
                href={`/news/${item.slug}`}
                className="text-xl lg:text-2xl text-white uppercase font-bold hover:text-primary-foreground/80 transition-colors"
              >
                {item.title}
              </Link>
            </div>
          </div>
        )
      })}


    </div>
  )
}
