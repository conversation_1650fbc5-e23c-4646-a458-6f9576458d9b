'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { PlusCircle, Eye, AlertTriangle, Star, Loader2, RefreshCw } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { showToast } from '@/lib/toast-utils'
import Link from 'next/link'

import { Article, ApiResponse, ArticlesResponse, ReviewResponse, ReportResponse } from '@/types'
import ErrorBoundary from '@/components/ErrorBoundary'
import { getImageUrl, getImageAlt } from '@/utils/imageUtils'

export default function ArticlesPage() {
  const router = useRouter()
  const [articles, setArticles] = useState<Article[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [reportReason, setReportReason] = useState('')
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null)
  const [isReporting, setIsReporting] = useState(false)
  const [isReviewOpen, setIsReviewOpen] = useState(false)
  const [reviewData, setReviewData] = useState<{
    comment: string
    rating: number
    approved: boolean
  }>({
    comment: '',
    rating: 5,
    approved: false,
  })
  const [isSubmittingReview, setIsSubmittingReview] = useState(false)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [userRole, setUserRole] = useState<string>('')

  // Fetch user role on component mount
  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        const res = await fetch('/api/auth/me', {
          credentials: 'include',
        })
        const data = await res.json()
        setUserRole(data.role || '')
      } catch (err) {
        console.error('Error fetching user role:', err)
      }
    }
    fetchUserRole()
  }, [])

  // Fetch articles on component mount
  useEffect(() => {
    async function fetchArticles() {
      try {
        setIsLoading(true)
        const response = await fetch('/api/dashboard/articles?depth=2', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new Error('Failed to fetch articles')
        }

        const data = (await response.json()) as ApiResponse<ArticlesResponse>

        if (data.success && data.data) {
          console.log("Articles received:", data.data.articles);
          setArticles(data.data.articles || [])
        } else {
          throw new Error(data.error || 'Failed to fetch articles')
        }
      } catch (err) {
        console.error('Error fetching articles:', err)
        setError('فشل في تحميل المقالات. يرجى المحاولة مرة أخرى.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchArticles()
  }, [])

  // Helper function for handling loading state in async operations
  const withLoading = async <T,>(action: string, fn: () => Promise<T>): Promise<T> => {
    setActionLoading(action)
    try {
      return await fn()
    } finally {
      setActionLoading(null)
    }
  }

  // Function to refresh articles after mutations
  const refreshArticles = async () => {
    try {
      const response = await fetch('/api/dashboard/articles', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch articles')
      }

      const data = (await response.json()) as ApiResponse<ArticlesResponse>

      if (data.success && data.data) {
        setArticles(data.data.articles || [])
      }
    } catch (err) {
      console.error('Error refreshing articles:', err)
    }
  }

  const handleReportArticle = async () => {
    if (!selectedArticle) return

    if (!reportReason.trim()) {
      showToast({
        title: 'خطأ',
        description: 'يرجى تقديم سبب للإبلاغ عن هذا المقال',
        variant: 'destructive',
      })
      return
    }

    await withLoading(`report-${selectedArticle.id}`, async () => {
      try {
        setIsReporting(true)

        const response = await fetch('/api/article/report', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            articleId: selectedArticle.id,
            reason: reportReason,
          }),
        })

        if (!response.ok) {
          throw new Error('Failed to report article')
        }

        const data = (await response.json()) as ApiResponse<ReportResponse>

        if (!data.success) {
          throw new Error(data.error || 'Failed to report article')
        }

        showToast({
          title: 'تم بنجاح',
          description: 'تم الإبلاغ عن المقال بنجاح',
        })

        // Reset form
        setReportReason('')
        setSelectedArticle(null)

        // Refresh articles to get updated data
        await refreshArticles()
      } catch (err) {
        console.error('Error reporting article:', err)
        showToast({
          title: 'خطأ',
          description: 'فشل في الإبلاغ عن المقال. يرجى المحاولة مرة أخرى.',
          variant: 'destructive',
        })
      } finally {
        setIsReporting(false)
      }
    })
  }

  const handleReviewArticle = async () => {
    if (!selectedArticle) return

    // Always require a comment for reviews
    if (!reviewData.comment || !reviewData.comment.trim()) {
      showToast({
        title: 'خطأ',
        description:
          'يرجى تقديم تعليق لمراجعتك. هذه تغذية راجعة مطلوبة للطالب.',
        variant: 'destructive',
      })
      return
    }

    await withLoading(`review-${selectedArticle.id}`, async () => {
      try {
        setIsSubmittingReview(true)

        const response = await fetch('/api/article/review', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            articleId: selectedArticle.id,
            comment: reviewData.comment,
            rating: reviewData.rating,
            approved: reviewData.approved
          }),
        })

        if (!response.ok) {
          throw new Error('Failed to review article')
        }

        const data = (await response.json()) as ApiResponse<ReviewResponse>

        if (!data.success && data.error) {
          throw new Error(data.error)
        }

        const points = data.data?.points || 0
        const message = data.data?.message || 'تمت مراجعة المقال بنجاح'

        showToast({
          title: 'تم بنجاح',
          description: `${message}. لقد ربحت ${points} نقطة!`,
        })

        // Update the article status in the list
        setArticles(
          articles.map((article) =>
            article.id === selectedArticle.id
              ? { ...article, status: reviewData.approved ? 'published' : 'pending-review' }
              : article,
          ),
        )

        // Reset form
        setReviewData({
          comment: '',
          rating: 5,
          approved: false,
        })

        // Close modal
        setSelectedArticle(null)
        setIsReviewOpen(false)

        // Refresh articles to get updated data
        await refreshArticles()
      } catch (err) {
        console.error('Error reviewing article:', err)
        showToast({
          title: 'خطأ',
          description: 'فشل في مراجعة المقال. يرجى المحاولة مرة أخرى.',
          variant: 'destructive',
        })
      } finally {
        setIsSubmittingReview(false)
      }
    })
  }

  const handlePreviewArticle = async (article: Article) => {
    await withLoading(`preview-${article.id}`, async () => {
      router.push(`/dashboard/articles/${article.id}`)
    })
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" dir="rtl">
          {error}
        </div>
      </DashboardLayout>
    )
  }

  // Check if user is a teacher
  const isTeacher = () => {
    // Use the userRole state that we fetch on component mount
    if (userRole) {
      return userRole === 'teacher'
    }

    // If we don't have user role data yet, try to determine from articles
    if (articles && articles.length > 0) {
      // Try to determine from the first article's author role
      const firstArticle = articles[0]
      if (firstArticle && firstArticle.author && typeof firstArticle.author === 'object') {
        const role = firstArticle.author.role
        if (typeof role === 'object' && role?.slug) {
          return role.slug === 'teacher'
        }
      }
    }

    // Default to true to be safe (hide the add button)
    return true
  }

  return (
    <DashboardLayout>
      <div className="p-6" dir="rtl">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <h1 className="text-2xl font-bold">المقالات</h1>
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshArticles}
              className="mr-2"
              title="تحديث المقالات"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          </div>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/dashboard/articles/new">
                <PlusCircle className="ml-2 h-4 w-4" />
                إضافة مقال
              </Link>
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>جميع المقالات</CardTitle>
          </CardHeader>
          <CardContent>
            <ErrorBoundary
              fallback={
                <div className="text-red-500 p-4">
                  فشل في تحميل المقالات. يرجى تحديث الصفحة.
                </div>
              }
            >
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الصورة</TableHead>
                    <TableHead>العنوان</TableHead>
                    <TableHead>{userRole === 'teacher' ? 'الصف' : 'المؤلف'}</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>التاريخ</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {articles.length > 0 ? (
                    articles.map((article, index) => (
                      <TableRow key={article.id || `article-${index}`}>
                        <TableCell>
                          <div className="h-16 w-24 relative overflow-hidden rounded">
                            <Image
                              src={(() => {
                                // If featuredImage is a string, use it directly
                                if (article.featuredImage && typeof article.featuredImage === 'string') {
                                  console.log('Using direct image path for:', article.title, article.featuredImage);
                                  return article.featuredImage;
                                }
                                // Fallback
                                else {
                                  return 'https://images.unsplash.com/photo-1510442650500-93217e634e4c?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
                                }
                              })()}
                              alt={article.title || 'صورة المقال'}
                              fill
                              className="object-cover"
                              sizes="96px"
                              onError={(e) => {
                                // Try alternative URL if the primary one fails
                                const imgElement = e.currentTarget as HTMLImageElement;
                                
                                // Check if we're already using the fallback to prevent loops
                                if (imgElement.src.includes('placeholder')) return;
                                
                                console.log('Image load error for:', article.title, imgElement.src);
                                
                                if (imgElement.src.includes('/api/media/file/')) {
                                  console.log('API path failed, trying media path');
                                  const filename = imgElement.src.split('/api/media/file/')[1];
                                  imgElement.src = `/media/${filename}`;
                                } else if (imgElement.src.includes('/media/')) {
                                  console.log('Media path failed, using placeholder');
                                  imgElement.src = '/placeholder-article.png';
                                } else {
                                  imgElement.src = '/placeholder-article.png';
                                }
                              }}
                            />
                          </div>
                        </TableCell>
                        <TableCell>{article.title}</TableCell>
                        <TableCell>
                          {userRole === 'teacher' ? (
                            // For teachers, show student's academic grade level and NEVER show names
                            <span className="font-medium">
                              {(() => {
                                // Debug logs
                                console.log(`Article ${article.id} grade check:`, 
                                  typeof article.author === 'object' ? article.author?.grade : 'non-object author');
                                
                                if (typeof article.author === 'object' && article.author && article.author.grade) {
                                  return article.author.grade === 'N/A' 
                                    ? 'غير محدد' 
                                    : `الصف ${article.author.grade}`;
                                } else {
                                  return 'غير محدد';
                                }
                              })()}
                            </span>
                          ) : (
                            // For everyone else, show author
                            typeof article.author === 'object' && article.author ? (
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center ml-2">
                                  <span className="text-xs font-medium">
                                    {article.author.firstName?.[0] || ''}
                                    {article.author.lastName?.[0] || ''}
                                  </span>
                                </div>
                                <div>
                                  {`${article.author.firstName || ''} ${article.author.lastName || ''}`.trim() || 'مؤلف غير معروف'}
                                </div>
                              </div>
                            ) : typeof article.author === 'string' ? (
                              // Check if the string is likely a MongoDB ID (24 hex chars)
                              /^[0-9a-fA-F]{24}$/.test(article.author) ? 
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center ml-2">
                                  <span className="text-xs font-medium">؟</span>
                                </div>
                                <div>مؤلف غير معروف</div>
                              </div> : 
                              (article.author === 'Unknown' ? 'مؤلف غير معروف' : article.author)
                            ) : (
                              // Fallback for null/undefined
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center ml-2">
                                  <span className="text-xs font-medium">؟</span>
                                </div>
                                <div>مؤلف غير معروف</div>
                              </div>
                            )
                          )}
                        </TableCell>
                        <TableCell>
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              article.status === 'published'
                                ? 'bg-green-100 text-green-800'
                                : article.status === 'pending-review'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-blue-100 text-blue-800'
                            }`}
                          >
                            {article.status === 'published' ? 'منشور' : 
                             article.status === 'pending-review' ? 'قيد المراجعة' : 
                             article.status === 'draft' ? 'مسودة' : article.status}
                          </span>
                        </TableCell>
                        <TableCell>{new Date(article.createdAt).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2 space-x-reverse">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePreviewArticle(article)}
                              disabled={actionLoading === `preview-${article.id}`}
                            >
                              {actionLoading === `preview-${article.id}` ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </Button>

                            {article.status === 'pending-review' && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedArticle(article)
                                  setIsReviewOpen(true)
                                }}
                                disabled={actionLoading === `review-${article.id}`}
                              >
                                {actionLoading === `review-${article.id}` ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Star className="h-4 w-4" />
                                )}
                              </Button>
                            )}

                            {/* Admin actions: Edit and Remove */}
                            {(userRole === 'super-admin' || userRole === 'school-admin') ? (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => { router.push(`/dashboard/articles/edit/${article.id}`) }}
                                >
                                  تعديل
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={async () => {
                                    if (confirm('هل أنت متأكد أنك تريد حذف هذا المقال؟')) {
                                      console.log('DELETE attempt:', { userId: userRole, userRole, articleAuthor: article.author });
                                      await withLoading(`delete-${article.id}`, async () => {
                                        try {
                                          const response = await fetch(`/api/dashboard/articles/${article.id}`, {
                                            method: 'DELETE',
                                            credentials: 'include',
                                          })
                                          if (!response.ok) throw new Error('Failed to delete article')
                                          showToast({ title: 'تم الحذف', description: 'تمت إزالة المقال.' })
                                          await refreshArticles()
                                        } catch (err) {
                                          showToast({ title: 'خطأ', description: 'فشل في حذف المقال', variant: 'destructive' })
                                        }
                                      })
                                    }
                                  }}
                                  disabled={actionLoading === `delete-${article.id}`}
                                >
                                  {actionLoading === `delete-${article.id}` ? <Loader2 className="h-4 w-4 animate-spin" /> : 'حذف'}
                                </Button>
                              </>
                            ) : (
                              // Non-admins: Report action
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-amber-500"
                                    onClick={() => setSelectedArticle(article)}
                                    disabled={actionLoading === `report-${article.id}`}
                                  >
                                    {actionLoading === `report-${article.id}` ? (
                                      <Loader2 className="h-4 w-4 animate-spin" />
                                    ) : (
                                      <AlertTriangle className="h-4 w-4" />
                                    )}
                                  </Button>
                                </DialogTrigger>
                                <DialogContent dir="rtl">
                                  <DialogHeader>
                                    <DialogTitle>الإبلاغ عن مقال</DialogTitle>
                                    <DialogDescription>
                                      يرجى تقديم سبب للإبلاغ عن هذا المقال. سيتم إرسال هذا إلى المسؤولين للمراجعة.
                                    </DialogDescription>
                                  </DialogHeader>
                                  <div className="py-4">
                                    <Textarea
                                      placeholder="سبب الإبلاغ..."
                                      value={reportReason}
                                      onChange={(e) => setReportReason(e.target.value)}
                                      className="min-h-[100px]"
                                    />
                                  </div>
                                  <DialogFooter className="sm:justify-start flex-row-reverse">
                                    <Button
                                      variant="outline"
                                      onClick={() => {
                                        setReportReason('')
                                        setSelectedArticle(null)
                                      }}
                                    >
                                      إلغاء
                                    </Button>
                                    <Button onClick={handleReportArticle} disabled={isReporting}>
                                      {isReporting ? (
                                        <>
                                          <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                                          جارٍ الإبلاغ...
                                        </>
                                      ) : (
                                        'إبلاغ'
                                      )}
                                    </Button>
                                  </DialogFooter>
                                </DialogContent>
                              </Dialog>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4">
                        لم يتم العثور على مقالات
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </ErrorBoundary>
          </CardContent>
        </Card>

        {/* Review Dialog */}
        <Dialog open={isReviewOpen} onOpenChange={setIsReviewOpen}>
          <DialogContent className="sm:max-w-[500px]" dir="rtl">
            <DialogHeader>
              <DialogTitle>مراجعة المقال</DialogTitle>
              <DialogDescription>
                مراجعة هذا المقال واتخاذ قرار بشأن الموافقة عليه للنشر.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4 space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">التقييم (1-10)</h3>
                <div className="flex items-center space-x-1 space-x-reverse">
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (
                    <Button
                      key={rating}
                      variant={reviewData.rating === rating ? 'default' : 'outline'}
                      size="sm"
                      className="w-8 h-8 p-0"
                      onClick={() => setReviewData({ ...reviewData, rating })}
                    >
                      {rating}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">
                  التعليقات <span className="text-red-500">*</span>
                </h3>
                <Textarea
                  placeholder="قدم تغذية راجعة للطالب (مطلوب)..."
                  value={reviewData.comment}
                  onChange={(e) => setReviewData({ ...reviewData, comment: e.target.value })}
                  className={`min-h-[100px] ${!reviewData.comment?.trim() ? 'border-red-300 focus:border-red-500' : ''}`}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  تعليقاتك مهمة لعملية تعلم الطالب. يرجى تقديم تعليقات بناءة.
                </p>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="checkbox"
                  id="approve"
                  checked={reviewData.approved}
                  onChange={(e) => setReviewData({ ...reviewData, approved: e.target.checked })}
                  className="rounded border-gray-300"
                />
                <label htmlFor="approve" className="text-sm font-medium">
                  الموافقة على النشر
                </label>
              </div>
            </div>
            <DialogFooter className="sm:justify-start flex-row-reverse">
              <Button
                variant="outline"
                onClick={() => {
                  setReviewData({
                    comment: '',
                    rating: 5,
                    approved: false,
                  })
                  setSelectedArticle(null)
                  setIsReviewOpen(false)
                }}
              >
                إلغاء
              </Button>
              <Button onClick={handleReviewArticle} disabled={isSubmittingReview}>
                {isSubmittingReview ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جارٍ الإرسال...
                  </>
                ) : (
                  'إرسال المراجعة'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
