'use client'

import { useState, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MessageSquare, Settings } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { formatDate } from '@/lib/date-utils'

interface Activity {
  id: string
  userId: string
  userName: string
  userRole: string
  schoolId?: string
  schoolName?: string
  activityType: string
  description: string
  date: string
  targetId?: string
  targetType?: string
}

interface RecentActivitiesProps {
  schoolId?: string
  limit?: number
}

export default function RecentActivities({ schoolId, limit = 10 }: RecentActivitiesProps) {
  const router = useRouter()
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function fetchActivities() {
      try {
        setIsLoading(true)
        let url = `/api/activities?limit=${limit}`
        if (schoolId) {
          url += `&schoolId=${schoolId}`
        }
        const response = await fetch(url)
        if (!response.ok) {
          throw new Error('فشل في جلب الأنشطة')
        }
        const data = await response.json()
        setActivities(data.docs || data.activities || [])
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching activities:', err)
        setError('فشل في تحميل الأنشطة')
        setIsLoading(false)
      }
    }
    fetchActivities()
  }, [schoolId, limit])

  const getRoleBadgeClass = (role: string) => {
    switch (role) {
      case 'teacher':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'student':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'mentor':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'school-admin':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'super-admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between" dir="rtl">
          <span>الأنشطة الأخيرة</span>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push('/dashboard/activities')}
            >
              <MessageSquare className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent dir="rtl">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 text-red-500 p-4 rounded-md">{error}</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>المستخدم</TableHead>
                <TableHead>الدور</TableHead>
                <TableHead>النشاط</TableHead>
                <TableHead>التاريخ</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {activities.length > 0 ? (
                activities.map((activity) => (
                  <TableRow key={activity.id}>
                    <TableCell className="font-medium">{activity.userName || 'غير معروف'}</TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${getRoleBadgeClass(activity.userRole)}`}
                      >
                        {activity.userRole || 'غير معروف'}
                      </span>
                    </TableCell>
                    <TableCell>{activity.description || 'لا يوجد وصف'}</TableCell>
                    <TableCell>{formatDate(activity.date, undefined, 'غير معروف')}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                    لم يتم العثور على أي نشاط
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
