'use client'

import { useState, useEffect } from 'react'
import { Bell } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'

interface Notification {
  id: string
  message: string
  date: string
  read: boolean
  link?: string
  type?: string
}

export function NotificationBell() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchNotifications() {
      try {
        // First get the current user data
        const userResponse = await fetch('/api/auth/me', {
          credentials: 'include',
        })

        if (!userResponse.ok) {
          throw new Error('Failed to fetch user data')
        }

        const userData = await userResponse.json()

        if (!userData.user) {
          throw new Error('No user data found')
        }

        // Get user ID and role for notifications
        const userId = userData.user.id
        const userRole =
          typeof userData.user.role === 'object' ? userData.user.role?.slug : userData.user.role
        const schoolId =
          typeof userData.user.school === 'object' ? userData.user.school?.id : userData.user.school

        // Now fetch notifications with the user data
        const notificationsUrl = new URL('/api/notifications', window.location.origin)
        notificationsUrl.searchParams.append('userId', userId)
        notificationsUrl.searchParams.append('userRole', userRole)
        if (schoolId) {
          notificationsUrl.searchParams.append('schoolId', schoolId)
        }

        const response = await fetch(notificationsUrl.toString(), {
          credentials: 'include',
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success && data.notifications) {
            // Map the MongoDB notification format to our component format
            const formattedNotifications = data.notifications.map((notification: any) => ({
              id: notification.id || notification._id,
              message: notification.message || notification.description || notification.title,
              date: notification.date || notification.createdAt,
              read: notification.read || false,
              link: notification.link,
              type: notification.type,
            }))
            setNotifications(formattedNotifications)
            setUnreadCount(
              formattedNotifications.filter((notification: Notification) => !notification.read)
                .length,
            )
          } else {
            throw new Error('Invalid notification data format')
          }
        } else {
          // For development, use mock data if API fails
          console.warn('Using mock notification data due to API error')
          const mockNotifications: Notification[] = [
            {
              id: '1',
              message: 'تم الموافقة على مقالك',
              date: new Date().toISOString(),
              read: false,
            },
            {
              id: '2',
              message: 'تعليق جديد على مقالك',
              date: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
              read: false,
            },
            {
              id: '3',
              message: 'مرحبًا بك في الصحفي الشاب!',
              date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
              read: true,
            },
          ]
          setNotifications(mockNotifications)
          setUnreadCount(mockNotifications.filter((n) => !n.read).length)
        }
      } catch (error) {
        console.error('Error fetching notifications:', error)
        // Use mock data as fallback
        const mockNotifications: Notification[] = [
          {
            id: '1',
            message: 'تم الموافقة على مقالك',
            date: new Date().toISOString(),
            read: false,
          },
          {
            id: '2',
            message: 'تعليق جديد على مقالك',
            date: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            read: false,
          },
          {
            id: '3',
            message: 'مرحبًا بك في الصحفي الشاب!',
            date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
            read: true,
          },
        ]
        setNotifications(mockNotifications)
        setUnreadCount(mockNotifications.filter((n) => !n.read).length)
      } finally {
        setIsLoading(false)
      }
    }

    fetchNotifications()
  }, [])

  const markAsRead = async (id: string) => {
    try {
      // Call the API to mark the notification as read
      const response = await fetch('/api/notifications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          id,
          read: true,
        }),
      })

      if (response.ok) {
        // Update the state
        setNotifications(
          notifications.map((notification) =>
            notification.id === id ? { ...notification, read: true } : notification,
          ),
        )
        setUnreadCount(Math.max(0, unreadCount - 1))
      } else {
        console.error('Failed to mark notification as read:', await response.text())
        // Still update the UI for better user experience
        setNotifications(
          notifications.map((notification) =>
            notification.id === id ? { ...notification, read: true } : notification,
          ),
        )
        setUnreadCount(Math.max(0, unreadCount - 1))
      }
    } catch (error) {
      console.error('Error marking notification as read:', error)
      // Still update the UI for better user experience
      setNotifications(
        notifications.map((notification) =>
          notification.id === id ? { ...notification, read: true } : notification,
        ),
      )
      setUnreadCount(Math.max(0, unreadCount - 1))
    }
  }

  const markAllAsRead = async () => {
    try {
      // Get the current user data for user ID
      const userResponse = await fetch('/api/auth/me', {
        credentials: 'include',
      })

      if (!userResponse.ok) {
        throw new Error('Failed to fetch user data')
      }

      const userData = await userResponse.json()

      if (!userData.user) {
        throw new Error('No user data found')
      }

      // Get user ID for notifications
      const userId = userData.user.id

      // We don't have a bulk update endpoint, so we'll update each unread notification
      const unreadNotifications = notifications.filter((notification) => !notification.read)

      // Create an array of promises for each update
      const updatePromises = unreadNotifications.map((notification) =>
        fetch('/api/notifications', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            id: notification.id,
            read: true,
          }),
        }),
      )

      // Execute all updates in parallel
      await Promise.all(updatePromises)

      // Update the state
      setNotifications(notifications.map((notification) => ({ ...notification, read: true })))
      setUnreadCount(0)
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      // Still update the UI for better user experience
      setNotifications(notifications.map((notification) => ({ ...notification, read: true })))
      setUnreadCount(0)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute top-0 left-0 h-4 w-4 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
              {unreadCount}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex justify-between items-center">
          <span>الإشعارات</span>
          {unreadCount > 0 && (
            <Button variant="ghost" size="sm" className="text-xs" onClick={markAllAsRead}>
              تعيين الكل كمقروء
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {isLoading ? (
          <div className="p-4 text-center">جارٍ تحميل الإشعارات...</div>
        ) : notifications.length > 0 ? (
          <div className="flex flex-col">
            <div className="px-4 py-3 text-sm font-medium border-b border-border">
              الإشعارات
            </div>
            <div className="max-h-80 overflow-auto" dir="rtl">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`px-4 py-3 text-sm border-b border-border hover:bg-accent transition-colors cursor-pointer ${
                    !notification.read ? 'bg-secondary/20' : ''
                  }`}
                  onClick={() => {
                    if (!notification.read) {
                      markAsRead(notification.id)
                    }
                    if (notification.link) {
                      window.location.href = notification.link
                    }
                  }}
                >
                  <div className="flex justify-between items-start gap-2">
                    <span className="font-medium">{notification.message}</span>
                    {!notification.read && (
                      <span className="bg-primary h-2 w-2 rounded-full mt-1.5 flex-shrink-0"></span>
                    )}
                  </div>
                  <div className="text-muted-foreground text-xs mt-1">
                    {new Date(notification.date).toLocaleString('ar-EG', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </div>
                </div>
              ))}
            </div>
            {unreadCount > 0 && (
              <div className="p-2 border-t border-border">
                <Button
                  variant="ghost"
                  className="w-full text-xs justify-center"
                  onClick={markAllAsRead}
                >
                  تعيين الكل كمقروء
                </Button>
              </div>
            )}
            {/* <div className="p-2 border-t border-border">
              <Button
                variant="outline"
                className="w-full text-xs justify-center"
                onClick={() => window.location.href = '/dashboard/notifications'}
              >
                عرض جميع الإشعارات
              </Button>
            </div> */}
          </div>
        ) : (
          <div className="p-4 text-center text-gray-500">لا توجد إشعارات</div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
