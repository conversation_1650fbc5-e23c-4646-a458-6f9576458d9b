import React from 'react'

interface PageLayoutProps {
  children: React.ReactNode
  title: string
  subtitle?: string
  bgColor?: string
  textColor?: string
  bgImage: string
}

export const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  title,
  subtitle,
  bgColor = 'bg-primary',
  textColor = 'text-primary-foreground',
  bgImage = 'bg-[url(https://images.unsplash.com/flagged/photo-1578928534298-9747fc52ec97?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)]',
}) => {
  return (
    <div className="flex flex-col" dir="rtl">
      {/* Page Header */}
      <div
        className={`${bgColor} ${textColor} ${bgImage} bg-blend-multiply bg-cover bg-bottom flex flex-col justify-center py-16 min-h-[70vh] px-4`}
      >
        <div className="container mx-auto p-8 flex flex-col justify-center">
          <h1 className="text-4xl font-bold mb-4">{title}</h1>
          {subtitle && <p className="text-xl">{subtitle}</p>}
        </div>
      </div>

      {/* Page Content */}
      {children}
    </div>
  )
}
