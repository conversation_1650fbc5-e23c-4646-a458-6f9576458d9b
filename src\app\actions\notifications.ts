'use server'

import { headers as getHeaders } from 'next/headers.js'
import { getPayload } from 'payload'
import { revalidatePath } from 'next/cache'

import config from '@/payload.config'

export async function fetchUserNotifications() {
  try {
    const headers = await getHeaders()
    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })
    const { user } = await payload.auth({ headers })

    if (!user) {
      return { notifications: [] }
    }

    const notificationsResult = await payload.find({
      collection: 'notifications',
      where: {
        user: {
          equals: user.id,
        },
      },
      sort: '-createdAt',
      limit: 10,
    })

    return { notifications: notificationsResult.docs }
  } catch (error) {
    console.error('Error fetching notifications:', error)
    return { notifications: [] }
  }
}

export async function markNotificationAsRead(id: string) {
  try {
    const headers = await getHeaders()
    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })
    const { user } = await payload.auth({ headers })

    if (!user) {
      return { success: false, error: 'Not authenticated' }
    }

    // Verify the notification belongs to the user
    const notification = await payload.findByID({
      collection: 'notifications',
      id,
    })

    const notificationUserId = 
      typeof notification.user === 'object' ? notification.user.id : notification.user

    if (notificationUserId !== user.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Update the notification
    await payload.update({
      collection: 'notifications',
      id,
      data: {
        read: true,
      },
    })

    // Revalidate the path to update the UI
    revalidatePath('/')
    revalidatePath('/notifications')

    return { success: true }
  } catch (error) {
    console.error('Error marking notification as read:', error)
    return { success: false, error: 'An error occurred' }
  }
}
