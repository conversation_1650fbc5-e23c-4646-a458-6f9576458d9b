import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

/**
 * Super-Admin Authorization API
 * Checks if the current user is authorized as a super admin
 */
export async function POST(req: NextRequest) {
  try {
    // Get the token from the cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can access this endpoint' },
          { status: 403 },
        )
      }

      // Return success response with user details
      return NextResponse.json({
        authorized: true,
        user: {
          id: user.id,
          name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
          role: role
        }
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error validating super-admin authorization:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
