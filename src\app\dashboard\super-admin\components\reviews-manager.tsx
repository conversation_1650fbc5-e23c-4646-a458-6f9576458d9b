import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Skeleton } from '@/components/ui/skeleton'

interface Review {
  id: string
  reviewer: string
  type: 'article' | 'news'
  title: string
  comment: string
  rating: number
  approved?: boolean | null
  date: string
}

export function ReviewsManager({ schoolId }: { schoolId?: string }) {
  const [loading, setLoading] = useState(true)
  const [reviews, setReviews] = useState<Review[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchReviews() {
      setLoading(true)
      try {
        // Fetch articles and news for the school
        const [articlesRes, newsRes] = await Promise.all([
          fetch(
            `/api/dashboard/super-admin/content?type=article${schoolId ? `&schoolId=${schoolId}` : ''}`,
          ),
          fetch(
            `/api/dashboard/super-admin/content?type=news${schoolId ? `&schoolId=${schoolId}` : ''}`,
          ),
        ])
        const articlesData = await articlesRes.json()
        const newsData = await newsRes.json()
        const articleReviews: Review[] = (articlesData.content || []).flatMap((item: any) =>
          (item.teacherReview || []).map((review: any) => ({
            id: review.id || `${item.id}-review`,
            reviewer: review.reviewer?.name || review.reviewer?.id || 'Unknown',
            type: 'article',
            title: item.title,
            comment: review.comment,
            rating: review.rating,
            approved: review.approved,
            date: review.createdAt || item.updatedAt,
          })),
        )
        const newsReviews: Review[] = (newsData.content || []).flatMap((item: any) =>
          (item.teacherReview || []).map((review: any) => ({
            id: review.id || `${item.id}-review`,
            reviewer: review.reviewer?.name || review.reviewer?.id || 'Unknown',
            type: 'news',
            title: item.title,
            comment: review.comment,
            rating: review.rating,
            approved: review.approved,
            date: review.createdAt || item.updatedAt,
          })),
        )
        setReviews([...articleReviews, ...newsReviews])
        setLoading(false)
      } catch (err) {
        setError('Failed to load reviews')
        setLoading(false)
      }
    }
    fetchReviews()
  }, [schoolId])

  if (loading) return <Skeleton className="h-[400px] w-full" />
  if (error) return <div className="text-red-500">{error}</div>

  return (
    <Card>
      <CardHeader>
        <CardTitle>Teacher & Mentor Reviews</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Type</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Reviewer</TableHead>
              <TableHead>Comment</TableHead>
              <TableHead>Rating</TableHead>
              <TableHead>Approved</TableHead>
              <TableHead>Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {reviews.length > 0 ? (
              reviews.map((review) => (
                <TableRow key={review.id}>
                  <TableCell>{review.type}</TableCell>
                  <TableCell>{review.title}</TableCell>
                  <TableCell>{review.reviewer}</TableCell>
                  <TableCell>{review.comment}</TableCell>
                  <TableCell>{review.rating}</TableCell>
                  <TableCell>{review.approved ? 'Yes' : 'No'}</TableCell>
                  <TableCell>{new Date(review.date).toLocaleDateString()}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4 text-muted-foreground">
                  No reviews found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
