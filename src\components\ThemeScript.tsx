'use client'

import { useEffect, useState } from 'react'

export function ThemeScript() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    // This runs on the client only
    setMounted(true)

    // Apply the theme immediately to prevent flash
    const applyTheme = () => {
      // Apply dark mode if stored
      const isDarkMode = localStorage.getItem('darkMode') === 'true'
      if (isDarkMode) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }

      // Apply theme color if stored
      const themeColor = localStorage.getItem('themeColor') || 'cyan'
      document.documentElement.setAttribute('data-theme', themeColor)
    }

    // Run immediately
    applyTheme()

    // Add listener for storage events (theme changes in other tabs)
    const handleStorageChange = () => {
      applyTheme()
    }
    
    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  // Only render in client-side
  if (!mounted) return null
  
  return null
} 