import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      const userId = typeof decoded === 'object' ? decoded.id : null
      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const reportType = url.searchParams.get('reportType') || 'activity'
      const schoolId = url.searchParams.get('schoolId')
      const startDate = url.searchParams.get('startDate')
      const endDate = url.searchParams.get('endDate')
      const exportFormat = url.searchParams.get('exportFormat') || 'csv'
      const limit = parseInt(url.searchParams.get('limit') || '100')
      const page = parseInt(url.searchParams.get('page') || '1')

      // Get the payload instance
      const payload = await getPayload({ config })
      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })
      // Check if user is a super-admin
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
      if (userRole !== 'super-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Build date filter
      let dateFilter: any = {}
      if (startDate || endDate) {
        dateFilter = {}
        if (startDate) dateFilter.$gte = new Date(startDate)
        if (endDate) dateFilter.$lte = new Date(endDate)
      }

      // Helper function to check if a string is a valid MongoDB ObjectId
      const isValidObjectId = (id: string): boolean => {
        if (!id) return false
        return ObjectId.isValid(id) && new ObjectId(id).toString() === id
      }

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()
        let query: any = {}
        
        if (schoolId) {
          if (isValidObjectId(schoolId)) {
            query.$or = [
              { 'school.id': schoolId },
              { school: schoolId },
              { 'school._id': new ObjectId(schoolId) }
            ]
          } else {
            // Handle non-ObjectId school IDs (for mock data or testing)
            query.$or = [{ 'school.id': schoolId }, { school: schoolId }]
          }
        }
        
        if (dateFilter.$gte || dateFilter.$lte) query.createdAt = dateFilter
        let collectionName = ''
        if (reportType === 'activity') collectionName = 'activities'
        else if (reportType === 'content') collectionName = 'articles'
        else if (reportType === 'users') collectionName = 'users'
        else if (reportType === 'feedback') collectionName = 'systemicIssues'
        else collectionName = reportType
        
        let docs = await db.collection(collectionName).find(query).skip((page-1)*limit).limit(limit).toArray()
        if (docs.length > 0) {
          return NextResponse.json({
            reportType,
            exportFormat,
            data: docs,
            count: docs.length,
          })
        }
      } catch (mongoError) {
        console.warn('Error fetching reports from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails or is empty
      try {
        let where: any = {}
        if (schoolId) where['school'] = { equals: schoolId }
        if (startDate || endDate) {
          where['createdAt'] = {}
          if (startDate) where['createdAt'].greater_than_equal = startDate
          if (endDate) where['createdAt'].less_than_equal = endDate
        }
        let collection: 'activities' | 'articles' | 'news' | 'users' | 'systemicIssues' = 'activities'
        if (reportType === 'activity') collection = 'activities'
        else if (reportType === 'content') collection = 'articles'
        else if (reportType === 'news') collection = 'news'
        else if (reportType === 'users') collection = 'users'
        else if (reportType === 'feedback') collection = 'systemicIssues'
        const result = await payload.find({
          collection,
          where,
          limit,
          page,
          depth: 2,
        })
        if (result.docs.length > 0) {
          return NextResponse.json({
            reportType,
            exportFormat,
            data: result.docs,
            count: result.docs.length,
          })
        }
      } catch (payloadError) {
        console.warn('Error fetching reports from Payload CMS:', payloadError)
      }

      // If no data, return empty array
      return NextResponse.json({ reportType, exportFormat, data: [], count: 0 })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching reports:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 