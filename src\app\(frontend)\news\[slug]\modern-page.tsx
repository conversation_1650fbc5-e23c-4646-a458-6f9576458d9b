import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { RichText } from '@/components/RichText'
import { PayloadRichText } from '@/components/PayloadRichText'
import { ShareButtons } from '@/components/ShareButtons'
import { Calendar, User, Tag } from 'lucide-react'
import { getImageUrl } from '@/utils/imageUtils'
import { isMediaPath } from '@/lib/media-utils'
import { ViewCounter } from '../../articles/[slug]/ViewCounter'

// Import custom styles
import './modern-styles.css'

// Helper to check for valid ObjectId
function isValidObjectId(id: string) {
  return /^[a-f\d]{24}$/i.test(id)
}

// Helper to fetch by slug
async function fetchNewsBySlug(slug: string) {
  try {
    console.log(`Fetching news by slug: ${slug}`);
    // Add depth parameter to ensure we get the complete content structure
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/news/${encodeURIComponent(slug)}?depth=2`, 
      { cache: 'no-store' }
    );
    
    if (!res.ok) {
      console.log(`Failed to fetch news by slug: ${res.status} ${res.statusText}`);
      return null;
    }
    
    const data = await res.json();
    console.log('Received news data by slug:', data ? 'Data received' : 'No data');
    return data;
  } catch (error) {
    console.error('Error in fetchNewsBySlug:', error);
    return null;
  }
}

// Helper to fetch by id
async function fetchNewsById(id: string) {
  try {
    console.log(`Fetching news by ID: ${id}`);
    // Add depth parameter to ensure we get the complete content structure
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/dashboard/news/${id}?depth=2`,
      { cache: 'no-store' }
    );
    
    if (!res.ok) {
      console.log(`Failed to fetch news by ID: ${res.status} ${res.statusText}`);
      return null;
    }
    
    const data = await res.json();
    console.log('Received news data by ID:', data && data.news ? 'Data received' : 'No data');
    return data.news || data;
  } catch (error) {
    console.error('Error in fetchNewsById:', error);
    return null;
  }
}

export default async function NewsSlugModernPage({ params }: { params: { slug: string } }) {
  // Await params before destructuring to comply with Next.js requirements
  const paramsObj = await params
  const { slug } = paramsObj

  console.log('News Slug:', slug)

  // Get headers and payload config for potential future use
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  // We don't need the user for this page, but keeping the auth call for future use
  const { user } = await payload.auth({ headers })

  try {
    // Try by slug first
    let newsItem = await fetchNewsBySlug(slug)
    if (!newsItem && isValidObjectId(slug)) {
      newsItem = await fetchNewsById(slug)
    }
    if (!newsItem) return notFound()

    // Get news ID for view tracking
    const newsId = newsItem.id || (newsItem._id ? newsItem._id.toString() : null)

    console.log('News Item Status:', newsItem?.status)
    console.log('News Item Content Type:', typeof newsItem.content)
    console.log('News Content Structure:', 
      typeof newsItem.content === 'object' 
        ? Object.keys(newsItem.content) 
        : 'Not an object')

    // Get author name
    const authorName =
      typeof newsItem.author === 'object' && newsItem.author
        ? newsItem.author.firstName && newsItem.author.lastName
          ? `${newsItem.author.firstName} ${newsItem.author.lastName}`
          : newsItem.author.email
        : 'Unknown Author'

    // Get featured image
    const featuredImage = newsItem.featuredImage
      ? getImageUrl(newsItem.featuredImage)
      : 'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80'

    // Format date
    const publishDate = newsItem.publishedAt
      ? new Date(newsItem.publishedAt)
      : new Date(newsItem.createdAt)

    // Format the date for display
    publishDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })

    // Time ago
    const timeAgo = getTimeAgo(publishDate)

    return (
      <div className="relative">
        {/* Hidden view counter component to track the view */}
        {newsId && <ViewCounter id={newsId} type="news" />}
        
        {/* Hero Header */}
        <div
          className="relative h-screen w-full flex items-center justify-center text-center bg-cover bg-center"
          style={{ backgroundImage: `url(${featuredImage})` }}
        >
          <div className="absolute top-0 right-0 bottom-0 left-0 bg-primary/75 dark:bg-primary/85"></div>

          <main className="px-4 lg:px-8 z-10 sm:max-w-3xl sm:mx-auto">
            <div className="text-center">
              <h2 className="text-2xl tracking-tight leading-10 font-medium sm:text-3xl text-primary-foreground md:text-4xl">
                {newsItem.title}
              </h2>
              <div className="py-5 text-sm font-regular text-primary-foreground flex items-center justify-center">
                <span className="mr-3 flex flex-row items-center">
                  <Calendar className="text-primary-foreground" size={13} />
                  <span className="ml-1">{timeAgo}</span>
                </span>
                <span className="flex flex-row items-center hover:text-primary-foreground/80 mr-3">
                  <User className="text-primary-foreground" size={16} />
                  <span className="ml-1">{authorName}</span>
                </span>
                {newsItem.tags && (
                  <span className="flex flex-row items-center hover:text-primary-foreground/80">
                    <Tag className="text-primary-foreground" size={16} />
                    <span className="ml-1">
                      {Array.isArray(newsItem.tags) ? newsItem.tags[0] : newsItem.tags}
                    </span>
                  </span>
                )}
              </div>
            </div>
          </main>
        </div>

        {/* Content */}
        <div className="max-w-2xl mx-auto">
          <div className="mt-3 bg-card rounded-b lg:rounded-b-none lg:rounded-r flex flex-col justify-between leading-normal">
            <div className="p-6">
              <div className="prose prose-cyan dark:prose-invert max-w-none">
                {/* Display content with better error handling */}
                {newsItem.content ? (
                  <div className="richtext-wrapper">
                    {(() => {
                      try {
                        // Try to render with PayloadRichText
                        if (typeof newsItem.content === 'string' && newsItem.content.trim().startsWith('{')) {
                          // If it's a JSON string, parse it first
                          return <PayloadRichText content={JSON.parse(newsItem.content)} />;
                        } else {
                          // Otherwise use the content directly
                          return <PayloadRichText content={newsItem.content} />;
                        }
                      } catch (error) {
                        console.error("Error rendering content:", error);
                        
                        // Fallback to direct text rendering if we have string content
                        if (typeof newsItem.content === 'string') {
                          return (
                            <div className="richtext-fallback">
                              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md mb-4">
                                <p className="text-yellow-700 text-sm">
                                  Using fallback content renderer
                                </p>
                              </div>
                              
                              {/* Raw HTML rendering */}
                              <div className="mt-4" dangerouslySetInnerHTML={{ __html: newsItem.content }} />
                              
                              {/* Plain text fallback */}
                              <div className="mt-4">
                                {newsItem.content.split('\n\n').map((paragraph: string, i: number) => (
                                  <p key={i} className="mb-4">{paragraph}</p>
                                ))}
                              </div>
                            </div>
                          );
                        }
                        
                        return (
                          <div className="p-4 border border-red-300 bg-red-50 rounded-md">
                            <p className="text-red-600 font-medium">Error displaying content</p>
                            <p className="text-sm text-red-500 mt-2">
                              There was an error rendering this content. Please contact support.
                            </p>
                            {user && 
                              ((typeof user.role === 'object' && 
                                (user.role?.slug === 'mentor' || user.role?.slug === 'super-admin')) || 
                               (typeof user.role === 'string' && 
                                (user.role === 'mentor' || user.role === 'super-admin'))) && (
                              <details className="mt-4">
                                <summary className="cursor-pointer text-sm">Technical details</summary>
                                <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                                  {String(error)}
                                </pre>
                              </details>
                            )}
                          </div>
                        );
                      }
                    })()}
                  </div>
                ) : (
                  <div className="text-center p-8 border border-dashed border-gray-300 rounded">
                    <p className="text-muted-foreground">No content available for this article.</p>
                  </div>
                )}
                
                {/* Debug information for admins */}
                {user && 
                  ((typeof user.role === 'object' && 
                    (user.role?.slug === 'mentor' || user.role?.slug === 'super-admin')) || 
                   (typeof user.role === 'string' && 
                    (user.role === 'mentor' || user.role === 'super-admin'))) && (
                  <div className="mt-8 pt-4 border-t border-gray-200">
                    <details className="text-sm">
                      <summary className="cursor-pointer text-muted-foreground">Debug Information</summary>
                      <div className="mt-2">
                        <h4 className="text-sm font-medium mb-1">Content Type</h4>
                        <div className="p-2 bg-gray-50 rounded text-xs mb-4">
                          {typeof newsItem.content}
                        </div>
                        
                        <h4 className="text-sm font-medium mb-1">Content Preview</h4>
                        <div className="p-2 bg-gray-50 rounded text-xs overflow-auto max-h-64">
                          <pre>{JSON.stringify(newsItem.content, null, 2)}</pre>
                        </div>
                      </div>
                    </details>
                  </div>
                )}
              </div>

              {/* Tags */}
              <div className="mt-8">
                {Array.isArray(newsItem.tags) &&
                  newsItem.tags.map((tag: string, index: number) => (
                    <a
                      key={index}
                      href="#"
                      className="text-xs text-primary font-medium hover:text-foreground transition duration-500 ease-in-out mr-2"
                    >
                      #{tag}
                    </a>
                  ))}
              </div>

              {/* Share Buttons */}
              <div className="border-t border-border pt-6 mt-8">
                <ShareButtons
                  title={newsItem.title}
                  url={`/news/${encodeURIComponent(newsItem.slug)}`}
                  description={`News from Young Reporter`}
                />
              </div>

              {/* Back to News */}
              <div className="mt-8">
                <Link href="/news" className="text-primary font-semibold hover:text-primary/80">
                  ← Back to All News
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error fetching news by slug:', error)
    return notFound()
  }
}

// Helper function to calculate time ago
function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return `${diffInSeconds} seconds ago`
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes} mins ago`
  }

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours} hours ago`
  }

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 30) {
    return `${diffInDays} days ago`
  }

  const diffInMonths = Math.floor(diffInDays / 30)
  if (diffInMonths < 12) {
    return `${diffInMonths} months ago`
  }

  const diffInYears = Math.floor(diffInMonths / 12)
  return `${diffInYears} years ago`
}
