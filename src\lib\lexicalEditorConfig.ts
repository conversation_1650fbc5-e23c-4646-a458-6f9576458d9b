import { InitialConfigType } from '@lexical/react/LexicalComposer'
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin'
import { ContentEditable } from '@lexical/react/LexicalContentEditable'
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin'
import { ParagraphNode, HeadingNode, ListNode, ListItemNode, QuoteNode } from '@lexical/rich-text'

import { LinkNode } from '@lexical/link'
import { CodeNode } from '@lexical/code'
import { Klass, LexicalNode } from 'lexical'

export const lexicalEditorConfig: InitialConfigType = {
  namespace: 'PayloadRichTextViewer',
  theme: {
    paragraph: 'mb-4',
    heading: {
      h1: 'text-2xl font-bold mb-4',
      h2: 'text-xl font-semibold mb-3',
      h3: 'text-lg font-medium mb-2',
    },
    quote: 'border-l-4 border-gray-300 pl-4 italic mb-4',
    list: {
      ol: 'list-decimal pl-5 mb-4',
      ul: 'list-disc pl-5 mb-4',
      listitem: 'mb-1',
    },
    link: 'text-blue-600 hover:underline',
    text: {
      bold: 'font-bold',
      italic: 'italic',
      underline: 'underline',
      strikethrough: 'line-through',
      code: 'bg-gray-100 px-1 py-0.5 rounded text-sm',
    },
  },
  onError(error) {
    console.error('Lexical error:', error)
  },
  nodes: [
    ParagraphNode,
    HeadingNode,
    ListNode,
    ListItemNode,
    QuoteNode,
    LinkNode,
    CodeNode,
  ] as Klass<LexicalNode>[],
  editorState: null,
}
