'use client'

import Link from 'next/link'
// No need for router since we're using window.location.href
import React, { useState } from 'react'

export const LoginForm: React.FC = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      console.log('Attempting login with email:', email)

      // Use the custom login API
      console.log('Sending login request to /api/custom-login')
      const response = await fetch('/api/custom-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        // Ensure credentials are included
        credentials: 'include',
      })

      console.log('Login response status:', response.status)

      // Custom login API doesn't redirect, it returns a JSON response
      // We'll handle the redirection ourselves

      // Otherwise, handle the JSON response
      let data
      try {
        data = await response.json()
        console.log('Login response data:', data)
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError)
        throw new Error('Invalid response from server')
      }

      if (!response.ok) {
        console.error('Login failed:', data.error || 'Unknown error')
        throw new Error(data.error || 'Login failed')
      }

      console.log('Login successful, redirecting to dashboard')

      // Add a small delay to ensure the cookie is properly set
      setTimeout(() => {
        // Redirect to dashboard
        window.location.href = '/dashboard'
      }, 500)
    } catch (err) {
      console.error('Login error:', err)
      setError(err instanceof Error ? err.message : 'Login failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
      <div className="p-8">
        <h1 className="text-2xl font-bold text-center mb-6">Login to Young Reporter</h1>
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded"
              placeholder="Enter your email"
              required
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-gray-700 mb-2">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded"
              placeholder="Enter your password"
              required
            />
          </div>
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition disabled:opacity-50"
            >
              {isLoading ? 'Logging in...' : 'Login'}
            </button>
          </div>
        </form>
        <div className="mt-6 text-center">
          <p className="text-gray-600">
            Don't have an account?{' '}
            <Link href="/register" className="text-blue-600 hover:text-blue-800">
              Register
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
