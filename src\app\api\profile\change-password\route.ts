import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      const { currentPassword, newPassword } = body

      // Validate required fields
      if (!currentPassword || !newPassword) {
        return NextResponse.json(
          { error: 'Current password and new password are required' },
          { status: 400 },
        )
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      // Verify current password
      const isPasswordValid = await bcrypt.compare(currentPassword, user.password)

      if (!isPasswordValid) {
        return NextResponse.json({ error: 'Current password is incorrect' }, { status: 400 })
      }

      // Hash the new password
      const salt = await bcrypt.genSalt(10)
      const hashedPassword = await bcrypt.hash(newPassword, salt)

      // Try to update in MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Update user in MongoDB
        const result = await db.collection('users').updateOne(
          { id: userId },
          {
            $set: {
              password: hashedPassword,
              updatedAt: new Date().toISOString(),
            },
          },
        )

        if (result.matchedCount > 0) {
          console.log('Password updated in MongoDB')
          return NextResponse.json({ success: true, message: 'Password updated successfully' })
        }
      } catch (mongoError) {
        console.warn('Error updating password in MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS
      await payload.update({
        collection: 'users',
        id: userId,
        data: {
          password: newPassword, // Payload will hash this automatically
        },
      })

      return NextResponse.json({ success: true, message: 'Password updated successfully' })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error changing password:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
