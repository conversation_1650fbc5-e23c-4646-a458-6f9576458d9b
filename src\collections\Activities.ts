import type { CollectionConfig } from 'payload'
// Removed import for Where, relying on global/generated types

export const Activities: CollectionConfig = {
  slug: 'activities',
  admin: {
    useAsTitle: 'activityType',
    defaultColumns: ['activityType', 'userId', 'targetUserId', 'createdAt'],
  },
  access: {
    // Everyone can read activities
    read: ({ req }) => {
      // Not logged in
      if (!req.user) return false

      // Super admins can read all activities
      if (
        req.user.role === 'super-admin' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'super-admin')
      ) {
        return true
      }

      // School admins can read activities from their school
      if (
        req.user.role === 'school-admin' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'school-admin')
      ) {
        const userActivitiesQuery = {
          userId: {
            equals: req.user.id,
          },
        }
        const schoolIdFromUser =
          typeof req.user.school === 'object' ? req.user.school?.id : req.user.school

        if (schoolIdFromUser) {
          return {
            or: [
              userActivitiesQuery,
              {
                school: {
                  equals: schoolIdFromUser,
                },
              },
            ],
          }
        }
        return userActivitiesQuery // Only their own activities if no school ID
      }

      // Mentors can read activities from their school
      if (
        req.user.role === 'mentor' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'mentor')
      ) {
        const userActivitiesQuery = {
          userId: {
            equals: req.user.id,
          },
        }
        const schoolIdFromUser =
          typeof req.user.school === 'object' ? req.user.school?.id : req.user.school

        if (schoolIdFromUser) {
          return {
            or: [
              userActivitiesQuery,
              {
                school: {
                  equals: schoolIdFromUser,
                },
              },
            ],
          }
        }
        return userActivitiesQuery // Only their own activities if no school ID
      }

      // Teachers can read their own activities
      if (
        req.user.role === 'teacher' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'teacher')
      ) {
        return {
          userId: {
            equals: req.user.id,
          },
        }
      }

      // Students can read activities related to them
      if (
        req.user.role === 'student' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'student')
      ) {
        return {
          or: [
            {
              userId: {
                equals: req.user.id,
              },
            },
            {
              targetUserId: {
                equals: req.user.id,
              },
            },
          ],
        }
      }

      // Default deny
      return false
    },
    // Only logged in users can create activities
    create: ({ req }) => !!req.user,
    // Only the user who created the activity can update it
    update: ({ req }) => {
      if (!req.user) return false

      // Super admins can update all activities
      if (
        req.user.role === 'super-admin' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'super-admin')
      ) {
        return true
      }

      // Other users can only update their own activities
      return {
        userId: {
          equals: req.user.id,
        },
      }
    },
    // Only the user who created the activity can delete it
    delete: ({ req }) => {
      if (!req.user) return false

      // Super admins can delete all activities
      if (
        req.user.role === 'super-admin' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'super-admin')
      ) {
        return true
      }

      // Other users can only delete their own activities
      return {
        userId: {
          equals: req.user.id,
        },
      }
    },
  },
  fields: [
    {
      name: 'userId',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'targetUserId',
      type: 'relationship',
      relationTo: 'users',
      required: false,
    },
    {
      name: 'activityType',
      type: 'select',
      required: true,
      options: [
        { label: 'Article Review', value: 'article-review' },
        { label: 'Article Comment', value: 'article-comment' },
        { label: 'Student Approval', value: 'student-approval' },
        { label: 'Profile Image Approval', value: 'profile-image-approval' },
        { label: 'Name Change Approval', value: 'name-change-approval' },
        { label: 'News Post', value: 'news-post' },
        { label: 'Achievement Earned', value: 'achievement-earned' },
        { label: 'Login', value: 'login' },
      ],
    },
    {
      name: 'details',
      type: 'json',
      required: false,
    },
    {
      name: 'school',
      type: 'relationship',
      relationTo: 'schools',
      required: false,
    },
    {
      name: 'points',
      type: 'number',
      required: false,
      defaultValue: 0,
    },
  ],
  timestamps: true,
}
