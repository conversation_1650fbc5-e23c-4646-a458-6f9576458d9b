import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import { ObjectId } from 'mongodb'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Try to get feedback from MongoDB first
      try {
        const { db } = await connectToDatabase()
        
        // Get the user's articles
        const articles = await db.collection('articles')
          .find({ 
            $or: [
              { author: userId },
              { author: new ObjectId(userId) }
            ],
            teacherReview: { $exists: true, $ne: [] }
          })
          .toArray()
        
        // Extract all reviews from the articles
        const reviews = []
        
        for (const article of articles) {
          if (Array.isArray(article.teacherReview)) {
            for (const review of article.teacherReview) {
              // Handle both string and object formats for reviewer
              let reviewerId = review.reviewer;
              
              // Convert ObjectId to string
              if (reviewerId && typeof reviewerId === 'object') {
                if (reviewerId.toString) {
                  reviewerId = reviewerId.toString();
                } else if (reviewerId.id) {
                  reviewerId = reviewerId.id;
                } else if ((reviewerId as any)._id) {
                  const _id = (reviewerId as any)._id;
                  reviewerId = _id.toString ? _id.toString() : _id;
                }
              }
                  
              reviews.push({
                id: review.id || `${article._id ? article._id.toString() : article.id}-${reviews.length}`,
                comment: review.comment || '',
                rating: review.rating || 0,
                approved: review.approved || false,
                reviewDate: review.createdAt || (review as any).reviewDate || article.updatedAt || new Date().toISOString(),
                articleId: article._id ? article._id.toString() : article.id,
                articleTitle: article.title || 'Untitled Article',
                reviewerId: reviewerId
              })
            }
          }
        }
        
        // Sort reviews by date (newest first)
        reviews.sort((a, b) => new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime())
        
        return NextResponse.json({ reviews })
      } catch (mongoError) {
        console.warn('Error fetching feedback from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS
      const payload = await getPayload({ config })
      
      // Get the user's articles with teacher reviews
      const articles = await payload.find({
        collection: 'articles',
        where: {
          author: {
            equals: userId,
          },
          'teacherReview': {
            exists: true,
          },
        },
        depth: 0,
      })
      
      // Extract all reviews from the articles
      const reviews = []
      
      for (const article of articles.docs) {
        if (Array.isArray(article.teacherReview)) {
          for (const review of article.teacherReview) {
            // Handle both string and object formats for reviewer
            let reviewerId = review.reviewer;
            
            // Convert ObjectId to string
            if (reviewerId && typeof reviewerId === 'object') {
              if (reviewerId.toString) {
                reviewerId = reviewerId.toString();
              } else if (reviewerId.id) {
                reviewerId = reviewerId.id;
              } else if ((reviewerId as any)._id) {
                const _id = (reviewerId as any)._id;
                reviewerId = _id.toString ? _id.toString() : _id;
              }
            }
                
            reviews.push({
              id: review.id || `${article.id}-${reviews.length}`,
              comment: review.comment || '',
              rating: review.rating || 0,
              approved: review.approved || false,
              reviewDate: (review as any).createdAt || (review as any).reviewDate || article.updatedAt || new Date().toISOString(),
              articleId: article.id,
              articleTitle: article.title || 'Untitled Article',
              reviewerId: reviewerId
            })
          }
        }
      }
      
      // Sort reviews by date (newest first)
      reviews.sort((a, b) => new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime())
      
      return NextResponse.json({ reviews })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching feedback:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
