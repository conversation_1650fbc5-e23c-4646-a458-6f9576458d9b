import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { redirect } from 'next/navigation'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'

export default async function CreateNewsPage() {
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // If not logged in, redirect to login
  if (!user) {
    redirect('/login')
  }

  // Check if user is a mentor
  const isMentor = typeof user.role === 'object' 
    ? user.role?.slug === 'mentor' 
    : user.role === 'mentor'

  if (!isMentor) {
    // Only mentors can create news posts
    redirect('/dashboard/mentor')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-blue-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <div className="text-xl font-bold">Young Reporter</div>
          <div className="flex space-x-6">
            <Link href="/">Home</Link>
            <Link href="/articles">Articles</Link>
            <Link href="/news">News</Link>
            <Link href="/statistics">Statistics</Link>
            <Link href="/about">About</Link>
            <Link href="/contact">Contact</Link>
            <Link href="/api/logout">Logout</Link>
          </div>
        </div>
      </nav>

      {/* Create News Header */}
      <div className="bg-blue-700 text-white py-12 px-4">
        <div className="container mx-auto">
          <h1 className="text-4xl font-bold mb-4">Create News Post</h1>
          <p className="text-xl">
            Share important news and announcements with the community
          </p>
        </div>
      </div>

      {/* Create News Form */}
      <div className="py-12 px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="p-8">
              <form action="/api/news/create" method="post" className="space-y-6">
                <div>
                  <label htmlFor="title" className="block text-gray-700 font-bold mb-2">
                    News Title
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    className="w-full p-3 border border-gray-300 rounded"
                    placeholder="Enter a title for your news post"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="slug" className="block text-gray-700 font-bold mb-2">
                    URL Slug
                  </label>
                  <input
                    type="text"
                    id="slug"
                    name="slug"
                    className="w-full p-3 border border-gray-300 rounded"
                    placeholder="e.g. summer-writing-contest-2023"
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    This will be used in the URL. Use lowercase letters, numbers, and hyphens only.
                  </p>
                </div>

                <div>
                  <label htmlFor="content" className="block text-gray-700 font-bold mb-2">
                    News Content
                  </label>
                  <textarea
                    id="content"
                    name="content"
                    rows={15}
                    className="w-full p-3 border border-gray-300 rounded"
                    placeholder="Write your news post here..."
                    required
                  ></textarea>
                </div>

                <div className="flex justify-between">
                  <div className="space-x-4">
                    <button
                      type="submit"
                      name="status"
                      value="draft"
                      className="bg-gray-500 text-white px-6 py-3 rounded-lg font-bold hover:bg-gray-600 transition"
                    >
                      Save as Draft
                    </button>
                    <button
                      type="submit"
                      name="status"
                      value="published"
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition"
                    >
                      Publish Now
                    </button>
                  </div>
                  <Link
                    href="/dashboard/mentor"
                    className="text-gray-600 px-6 py-3 rounded-lg font-bold hover:text-gray-800 transition"
                  >
                    Cancel
                  </Link>
                </div>
              </form>
            </div>
          </div>

          <div className="mt-8 bg-blue-50 p-6 rounded-lg">
            <h2 className="text-xl font-bold mb-4">News Post Guidelines</h2>
            <ul className="list-disc pl-5 space-y-2">
              <li>Keep news posts clear, concise, and relevant to the Young Reporter community.</li>
              <li>Use a descriptive title that accurately reflects the content.</li>
              <li>Create a simple, readable slug for the URL.</li>
              <li>Include all necessary details such as dates, times, and locations for events.</li>
              <li>Proofread your post before publishing.</li>
              <li>You can save as a draft if you need to come back and finish later.</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 px-4 mt-auto">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <h3 className="text-xl font-bold">Young Reporter</h3>
              <p>Empowering student journalists since 2023</p>
            </div>
            <div className="flex space-x-6">
              <Link href="/">Home</Link>
              <Link href="/about">About</Link>
              <Link href="/contact">Contact</Link>
              <Link href="/privacy">Privacy Policy</Link>
            </div>
          </div>
          <div className="mt-8 text-center text-gray-400">
            <p>© {new Date().getFullYear()} Young Reporter. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
