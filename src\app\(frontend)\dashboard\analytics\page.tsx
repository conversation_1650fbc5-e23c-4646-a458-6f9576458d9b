import { headers as getHeaders } from 'next/headers.js'
import { redirect } from 'next/navigation'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { ResponsiveLayout } from '@/components/ResponsiveLayout'
import { AnalyticsDashboard } from './analytics-dashboard'

export default async function AnalyticsPage() {
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // Redirect to login if not authenticated
  if (!user) {
    redirect('/login?redirect=/dashboard/analytics')
  }

  // Check if user has appropriate role (teacher, mentor, or admin)
  const userRole = typeof user.role === 'object' ? user.role.slug : null
  if (!userRole || !['teacher', 'mentor', 'super-admin'].includes(userRole)) {
    redirect('/')
  }

  // Fetch statistics
  const summaryStats = await payload.find({
    collection: 'statistics',
    where: {
      type: {
        equals: 'summary',
      },
    },
    limit: 1,
  })

  // Fetch school rankings
  const schoolRankings = await payload.find({
    collection: 'statistics',
    where: {
      type: {
        equals: 'schoolRanking',
      },
    },
    limit: 1,
  })

  // Fetch student rankings
  const studentRankings = await payload.find({
    collection: 'statistics',
    where: {
      type: {
        equals: 'studentRanking',
      },
    },
    limit: 1,
  })

  // Fetch recent articles
  const recentArticles = await payload.find({
    collection: 'articles',
    where: {
      status: {
        equals: 'published',
      },
    },
    sort: '-createdAt',
    limit: 5,
    depth: 1,
  })

  // Extract data
  const summary = summaryStats.docs.length > 0 ? summaryStats.docs[0].data : {
    totalArticles: 0,
    publishedArticles: 0,
    totalStudents: 0,
    totalTeachers: 0,
    totalSchools: 0,
  }

  const schoolData = schoolRankings.docs.length > 0 ? schoolRankings.docs[0].data : []
  const studentData = studentRankings.docs.length > 0 ? studentRankings.docs[0].data : []

  return (
    <ResponsiveLayout>
      <div className="bg-blue-700 text-white py-12 px-4">
        <div className="container mx-auto">
          <h1 className="text-4xl font-bold mb-4">Analytics Dashboard</h1>
          <p className="text-xl">Track platform performance and user engagement</p>
        </div>
      </div>

      <div className="py-12 px-4">
        <div className="container mx-auto">
          <AnalyticsDashboard 
            summary={summary} 
            schoolData={schoolData} 
            studentData={studentData}
            recentArticles={recentArticles.docs}
          />
        </div>
      </div>
    </ResponsiveLayout>
  )
}
