import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    console.log('[SchoolAdminContent] API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      console.log('[SchoolAdminContent] No token found')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('[SchoolAdminContent] Token exists:', !!token)

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        console.log('[SchoolAdminContent] No user ID in token')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      console.log('[SchoolAdminContent] User ID from token:', userId)

      // Get URL parameters
      const url = new URL(req.url)
      const schoolId = url.searchParams.get('schoolId')
      const type = url.searchParams.get('type') || 'article'
      const limit = parseInt(url.searchParams.get('limit') || '50')

      console.log('[SchoolAdminContent] Query school ID:', schoolId)

      if (!schoolId) {
        return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
      }

      if (!['article', 'news', 'comment', 'user'].includes(type)) {
        return NextResponse.json({ error: 'Invalid content type' }, { status: 400 })
      }

      // Use the same auth pattern as other working APIs
      const { db } = await connectToDatabase()

      // Get the user from MongoDB
      const mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })
      console.log('[SchoolAdminContent] MongoDB user found:', mongoUser ? 'yes' : 'no')

      if (!mongoUser) {
        console.log('[SchoolAdminContent] User not found in MongoDB')
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      console.log('[SchoolAdminContent] User role property:', mongoUser.role)
      console.log('[SchoolAdminContent] User school property:', mongoUser.school)

      // Extract user role - handle ObjectId lookup like other working APIs
      let userRole = null
      if (mongoUser.role && typeof mongoUser.role === 'object') {
        try {
          console.log('[SchoolAdminContent] Looking up role by ID:', mongoUser.role)
          const roleDoc = await db
            .collection('roles')
            .findOne({ _id: new ObjectId(mongoUser.role) })
          userRole = roleDoc?.slug
          console.log('[SchoolAdminContent] User role from lookup:', userRole)
        } catch (e) {
          console.error('[SchoolAdminContent] Error looking up role:', e)
        }
      } else if (typeof mongoUser.role === 'string') {
        userRole = mongoUser.role
      }

      console.log('[SchoolAdminContent] Final determined role slug:', userRole)

      if (userRole !== 'school-admin' && userRole !== 'super-admin') {
        console.log('[SchoolAdminContent] Access denied - role not allowed:', userRole)
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Extract school ID - handle ObjectId like other working APIs
      let userSchoolId = null
      if (mongoUser.school && typeof mongoUser.school === 'object') {
        userSchoolId = mongoUser.school.toString()
      } else if (typeof mongoUser.school === 'string') {
        userSchoolId = mongoUser.school
      }

      console.log('[SchoolAdminContent] Using school ID from query:', schoolId)
      console.log('[SchoolAdminContent] Final determined school ID:', userSchoolId)

      // Verify the school ID matches the user's school
      if (userSchoolId !== schoolId) {
        console.log('[SchoolAdminContent] School mismatch:', userSchoolId, 'vs', schoolId)
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      let content: any[] = []

      // Get content based on type
      if (type === 'article') {
        content = await db
          .collection('articles')
          .find({
            $or: [
              { 'author.school.id': schoolId },
              { 'author.school': schoolId },
              { school: schoolId },
            ],
          })
          .limit(limit)
          .toArray()
      } else if (type === 'news') {
        content = await db
          .collection('news')
          .find({
            $or: [
              { 'author.school.id': schoolId },
              { 'author.school': schoolId },
              { school: schoolId },
            ],
          })
          .limit(limit)
          .toArray()
      } else if (type === 'comment') {
        content = await db
          .collection('systemicIssues')
          .find({ schoolId: schoolId })
          .limit(limit)
          .toArray()
      } else if (type === 'user') {
        content = await db
          .collection('users')
          .find({
            $or: [{ 'school.id': schoolId }, { school: schoolId }],
          })
          .limit(limit)
          .toArray()
      }

      console.log('[SchoolAdminContent] Found', content.length, 'items of type', type)

      return NextResponse.json({
        content: content.map((item) => ({
          id: item._id.toString(),
          title: item.title || '',
          author: item.author,
          firstName: item.firstName || '',
          lastName: item.lastName || '',
          email: item.email || '',
          createdAt: item.createdAt || '',
          teacherReview: item.teacherReview || [],
        })),
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching content:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
