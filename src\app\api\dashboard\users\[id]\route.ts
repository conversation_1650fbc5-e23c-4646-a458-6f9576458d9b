import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

// GET a single user by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Try to get user from MongoDB first
    try {
      const { db } = await connectToDatabase()
      
      // Try to convert userId to ObjectId
      let idQuery: any;
      try {
        idQuery = new ObjectId(userId);
      } catch (error) {
        idQuery = userId; // Fall back to string if not a valid ObjectId
      }
      
      // Use any type to bypass strict type checking
      const query: any = {
        $or: [
          { _id: idQuery },
          { id: userId }
        ]
      };
      
      const user = await db.collection('users').findOne(query)

      if (user) {
        return NextResponse.json({ user })
      }
    } catch (mongoError) {
      console.warn('Error fetching user from MongoDB, falling back to Payload:', mongoError)
    }

    // Fallback to Payload CMS if MongoDB fails
    const payload = await getPayload({ config })

    // Get user by ID
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
      depth: 1,
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json({ user })
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 })
  }
}

// UPDATE a user by ID
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get request body
    const body = await req.json()
    
    // Basic validation
    if (!body.firstName || !body.lastName || !body.email || !body.role) {
      return NextResponse.json({ 
        error: 'First name, last name, email, and role are required fields' 
      }, { status: 400 })
    }

    // Try to update the user in MongoDB first
    try {
      const { db } = await connectToDatabase()
      
      // Try to convert userId to ObjectId
      let idQuery: any;
      try {
        idQuery = new ObjectId(userId);
      } catch (error) {
        idQuery = userId; // Fall back to string if not a valid ObjectId
      }
      
      // Use any type to bypass strict type checking
      const query: any = {
        $or: [
          { _id: idQuery },
          { id: userId }
        ]
      };
      
      const updateResult = await db.collection('users').updateOne(
        query,
        {
          $set: {
            firstName: body.firstName,
            lastName: body.lastName,
            email: body.email,
            role: body.role,
            updatedAt: new Date()
          }
        }
      )

      if (updateResult.modifiedCount > 0) {
        return NextResponse.json({ 
          success: true, 
          message: 'User updated successfully'
        })
      }
    } catch (mongoError) {
      console.warn('Error updating user in MongoDB, falling back to Payload:', mongoError)
    }

    // Fallback to Payload CMS if MongoDB fails
    const payload = await getPayload({ config })

    // Update user by ID
    await payload.update({
      collection: 'users',
      id: userId,
      data: {
        firstName: body.firstName,
        lastName: body.lastName,
        email: body.email,
        role: body.role
      },
    })

    return NextResponse.json({ 
      success: true, 
      message: 'User updated successfully'
    })
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 })
  }
}

// DELETE a user by ID
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Try to delete the user from MongoDB first
    try {
      const { db } = await connectToDatabase()
      
      // Try to convert userId to ObjectId
      let idQuery: any;
      try {
        idQuery = new ObjectId(userId);
      } catch (error) {
        idQuery = userId; // Fall back to string if not a valid ObjectId
      }
      
      // Use any type to bypass strict type checking
      const query: any = {
        $or: [
          { _id: idQuery },
          { id: userId }
        ]
      };
      
      const deleteResult = await db.collection('users').deleteOne(query)

      if (deleteResult.deletedCount > 0) {
        return NextResponse.json({ 
          success: true, 
          message: 'User deleted successfully'
        })
      }
    } catch (mongoError) {
      console.warn('Error deleting user from MongoDB, falling back to Payload:', mongoError)
    }

    // Fallback to Payload CMS if MongoDB fails
    const payload = await getPayload({ config })

    // Delete user by ID
    await payload.delete({
      collection: 'users',
      id: userId,
    })

    return NextResponse.json({ 
      success: true, 
      message: 'User deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 })
  }
} 