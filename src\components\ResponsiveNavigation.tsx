'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useState, useEffect } from 'react'
import { Menu, X, Newspaper, Bell } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { NavbarNotificationBell } from './navbar/NavbarNotificationBell'
import { UserMenu } from './UserMenu'

type ResponsiveNavigationProps = {
  user: any
  adminRoute: string
  forceBlackText?: boolean
}

export const ResponsiveNavigation: React.FC<ResponsiveNavigationProps> = ({
  user: _user,
  adminRoute: _,
  forceBlackText = false,
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const pathname = usePathname()

  // Check if user is logged in
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await fetch('/api/auth/me', {
          credentials: 'include',
        })
        const data = await response.json()

        if (response.ok && data.user) {
          setIsLoggedIn(true)
        } else {
          setIsLoggedIn(false)
        }
      } catch (err) {
        console.error('Error checking auth:', err)
        setIsLoggedIn(false)
      }
    }

    checkAuth()
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      // Show navbar when scrolled down, hide when at the top
      if (window.scrollY > 0) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true })

    // Initial check
    handleScroll()

    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const closeMenu = () => {
    setIsMenuOpen(false)
  }

  const isActive = (path: string) => {
    return pathname === path
  }

  return (
    <nav
      className={`  uppercase  fixed top-0 left-0 right-0 z-50 transition-all duration-500 linear ${
        isVisible || forceBlackText
          ? 'bg-secondary p-2 text-secondary-foreground '
          : 'bg-transparent p-4 text-white'
      } ${forceBlackText && !isVisible ? 'text-black p-4' : ''}`}
    >
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center gap-2">
            <Newspaper className={`h-6 w-6 ${forceBlackText && !isVisible ? 'text-black' : ''}`} />
            <Link
              href="/"
              className={`text-xl font-bold ${forceBlackText && !isVisible ? 'text-black' : ''}`}
            >
              الصحفي الصغير
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:space-x-6">
            <Link
              href="/"
              className={`px-3 py-2 rounded-md transition-colors ${
                isActive('/')
                  ? 'text-primary font-medium'
                  : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
              }`}
            >
              الصفحة الرئيسية
            </Link>
            <Link
              href="/articles"
              className={`px-3 py-2 rounded-md transition-colors ${
                isActive('/articles')
                  ? 'text-primary  font-medium'
                  : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
              }`}
            >
              المقالات
            </Link>
            <Link
              href="/news"
              className={`px-3 py-2 rounded-md transition-colors ${
                isActive('/news')
                  ? 'text-primary  font-medium'
                  : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
              }`}
            >
              الاخبار
            </Link>
            <Link
              href="/statistics"
              className={`px-3 py-2 rounded-md transition-colors ${
                isActive('/statistics')
                  ? 'text-primary  font-medium'
                  : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
              }`}
            >
              الاحصائيات
            </Link>
            <Link
              href="/about"
              className={`px-3 py-2 rounded-md transition-colors ${
                isActive('/about')
                  ? 'text-primary  font-medium'
                  : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
              }`}
            >
              عن المنصة
            </Link>
            <Link
              href="/contact"
              className={`px-3 py-2 rounded-md transition-colors ${
                isActive('/contact')
                  ? 'text-primary  font-medium'
                  : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
              }`}
            >
              تواصل معنا
            </Link>

            {/* Notification Bell */}
            {isLoggedIn && <NavbarNotificationBell />}

            <UserMenu />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              onClick={toggleMenu}
              variant="ghost"
              size="icon"
              className={`${forceBlackText && !isVisible ? 'text-black' : 'text-primary-foreground'} hover:text-primary`}
              aria-expanded={isMenuOpen}
            >
              <span className="sr-only">فتح القائمة</span>
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      <div
        className={`${
          isMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
        } md:hidden overflow-hidden transition-all duration-300 ease-in-out ${forceBlackText && !isVisible ? 'text-black' : ''}`}
      >
        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          <Link
            href="/"
            className={`block px-3 py-2 rounded-md transition-colors ${
              isActive('/')
                ? 'text-primary  font-medium'
                : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
            }`}
            onClick={closeMenu}
          >
            الصفحة الرئيسية
          </Link>
          <Link
            href="/articles"
            className={`block px-3 py-2 rounded-md transition-colors ${
              isActive('/articles')
                ? 'text-primary  font-medium'
                : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
            }`}
            onClick={closeMenu}
          >
            المقالات
          </Link>
          <Link
            href="/news"
            className={`block px-3 py-2 rounded-md transition-colors ${
              isActive('/news')
                ? 'text-primary  font-medium'
                : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
            }`}
            onClick={closeMenu}
          >
            الاخبار
          </Link>
          <Link
            href="/statistics"
            className={`block px-3 py-2 rounded-md transition-colors ${
              isActive('/statistics')
                ? 'text-primary  font-medium'
                : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
            }`}
            onClick={closeMenu}
          >
            الاحصائيات
          </Link>
          <Link
            href="/about"
            className={`block px-3 py-2 rounded-md transition-colors ${
              isActive('/about')
                ? 'text-primary  font-medium'
                : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
            }`}
            onClick={closeMenu}
          >
            عن المنصة
          </Link>
          <Link
            href="/contact"
            className={`block px-3 py-2 rounded-md transition-colors ${
              isActive('/contact')
                ? 'text-primary  font-medium'
                : `hover:text-primary ${forceBlackText && !isVisible ? 'text-black' : ''}`
            }`}
            onClick={closeMenu}
          >
            تواصل معنا
          </Link>
          {/* Notification Bell */}
          {isLoggedIn && (
            <div className="px-3 py-2">
              <div
                className="w-full flex items-center justify-start gap-2 p-2 cursor-pointer hover:bg-accent rounded-md"
                onClick={closeMenu}
              >
                <Bell className="h-5 w-5" />
                <span>الإشعارات</span>
              </div>
            </div>
          )}

          <div className="px-3 border-2 border-primary py-2">
            <UserMenu />
          </div>
        </div>
      </div>
    </nav>
  )
}
