'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

import { fetchUserNotifications, markNotificationAsRead } from '@/app/actions/notifications'

// Define the shape of our context
interface NotificationContextType {
  notifications: any[]
  loading: boolean
  error: string | null
  markAsRead: (id: string) => Promise<void>
  refreshNotifications: () => Promise<void>
}

// Create the context with a default value
const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  loading: false,
  error: null,
  markAsRead: async () => {},
  refreshNotifications: async () => {},
})

// Hook to use the notification context
export const useNotifications = () => useContext(NotificationContext)

// Provider component
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<any[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // Function to fetch notifications
  const refreshNotifications = async () => {
    try {
      setLoading(true)
      const { notifications: newNotifications } = await fetchUserNotifications()
      setNotifications(newNotifications)
      setError(null)
    } catch (err) {
      setError('Failed to fetch notifications')
      console.error('Error fetching notifications:', err)
    } finally {
      setLoading(false)
    }
  }

  // Function to mark a notification as read
  const markAsRead = async (id: string) => {
    try {
      const result = await markNotificationAsRead(id)
      if (result.success) {
        // Update the local state
        setNotifications((prev) =>
          prev.map((notification) =>
            notification.id === id ? { ...notification, read: true } : notification
          )
        )
      } else {
        setError(result.error || 'Failed to mark notification as read')
      }
    } catch (err) {
      setError('Failed to mark notification as read')
      console.error('Error marking notification as read:', err)
    }
  }

  // Fetch notifications on mount
  useEffect(() => {
    refreshNotifications()
  }, [])

  // Set up a refresh interval (every 5 minutes)
  useEffect(() => {
    const interval = setInterval(() => {
      refreshNotifications()
    }, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [])

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        loading,
        error,
        markAsRead,
        refreshNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  )
}
