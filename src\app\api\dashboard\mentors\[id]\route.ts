import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { id } = await context.params
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    let userId: string | null = null
    try {
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      userId = typeof decoded === 'object' ? decoded.id : null
    } catch {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    // Try MongoDB first
    try {
      const { db } = await connectToDatabase()
      const user = await db.collection('users').findOne({ _id: new ObjectId(userId) })
      if (!user) return NextResponse.json({ error: 'User not found' }, { status: 404 })
      let userRoleSlug: string | null = null
      if (user.role instanceof ObjectId) {
        const role = await db.collection('roles').findOne({ _id: user.role })
        userRoleSlug = role?.slug || null
      } else if (typeof user.role === 'string' && ObjectId.isValid(user.role)) {
        const role = await db.collection('roles').findOne({ _id: new ObjectId(user.role) })
        userRoleSlug = role?.slug || null
      } else if (typeof user.role === 'object' && user.role !== null && 'slug' in user.role) {
        userRoleSlug = user.role.slug
      }
      if (!userRoleSlug || !['super-admin', 'school-admin'].includes(userRoleSlug)) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }
      // School admin: only allow access to mentors in their school
      let schoolFilter = {}
      if (userRoleSlug === 'school-admin') {
        const schoolId = typeof user.school === 'object' ? user.school._id : user.school
        if (schoolId) {
          schoolFilter = {
            school:
              typeof schoolId === 'string' && ObjectId.isValid(schoolId)
                ? new ObjectId(schoolId)
                : schoolId,
          }
        }
      }
      const mentor = await db.collection('users').findOne({
        _id: new ObjectId(id),
        ...schoolFilter,
      })
      if (!mentor) return NextResponse.json({ error: 'Mentor not found' }, { status: 404 })
      // Get school name
      let schoolName = 'Unknown School'
      if (userRoleSlug === 'school-admin') {
        let adminSchoolId = null
        if (typeof user.school === 'object' && user.school && user.school._id) {
          adminSchoolId = user.school._id.toString()
        } else if (user.school) {
          adminSchoolId = user.school.toString()
        }
        if (adminSchoolId && mentor.school && mentor.school.toString() === adminSchoolId) {
          const school = await db
            .collection('schools')
            .findOne({ _id: new ObjectId(adminSchoolId) })
          if (school) schoolName = school.name
        }
      } else if (mentor.school) {
        const schoolId = typeof mentor.school === 'object' ? mentor.school._id : mentor.school
        const school = await db.collection('schools').findOne({ _id: new ObjectId(schoolId) })
        if (school) schoolName = school.name
      }
      // Get review stats
      const reviews = await db
        .collection('activities')
        .find({
          userId: mentor._id.toString(),
          activityType: 'teacher-review',
        })
        .toArray()
      const reviewCount = reviews.length
      const totalRating = reviews.reduce((sum, review) => sum + (review.details?.rating || 0), 0)
      const averageRating = reviewCount > 0 ? totalRating / reviewCount : 0
      const lastActivity = await db
        .collection('activities')
        .find({
          userId: mentor._id.toString(),
        })
        .sort({ date: -1 })
        .limit(1)
        .toArray()
      let mentorName = `${mentor.firstName || ''} ${mentor.lastName || ''}`.trim()
      if (!mentorName) mentorName = 'Unknown'
      // Get mentor activities (limit 10, sorted by date desc)
      const activities = await db
        .collection('activities')
        .find({
          userId: mentor._id.toString(),
        })
        .sort({ date: -1 })
        .limit(10)
        .toArray()
      return NextResponse.json({
        id: mentor._id.toString(),
        name: mentorName,
        email: mentor.email,
        school: schoolName,
        reviewCount,
        averageRating,
        lastActivity: lastActivity[0]?.date || mentor.updatedAt || mentor.createdAt,
        activities,
      })
    } catch (mongoError) {
      // Fallback to Payload CMS
    }
    // Fallback to Payload CMS
    const payload = await getPayload({ config })
    const user = await payload.findByID({ collection: 'users', id: userId, depth: 2 })
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (!['super-admin', 'school-admin'].includes(role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }
    let where = {}
    if (role === 'school-admin') {
      const schoolId = typeof user.school === 'object' ? user.school?.id : user.school
      if (schoolId) {
        where = { school: { equals: schoolId } }
      }
    }
    const mentor = await payload.findByID({ collection: 'users', id: id, depth: 2 })
    if (
      !mentor ||
      (role === 'school-admin' &&
        mentor.school &&
        user.school &&
        (typeof mentor.school === 'object' ? mentor.school.id : mentor.school) !==
          (typeof user.school === 'object' ? user.school.id : user.school))
    ) {
      return NextResponse.json({ error: 'Mentor not found' }, { status: 404 })
    }
    let schoolName = 'Unknown School'
    if (role === 'school-admin') {
      if (typeof user.school === 'object' && user.school && user.school.name)
        schoolName = user.school.name
    } else if (typeof mentor.school === 'object' && mentor.school && mentor.school.name) {
      schoolName = mentor.school.name
    }
    // Get review stats from activities
    const reviews = await payload.find({
      collection: 'activities',
      where: {
        userId: { equals: mentor.id },
        activityType: { equals: 'teacher-review' },
      },
    })
    const reviewCount = reviews.totalDocs
    let totalRating = 0
    reviews.docs.forEach((review) => {
      if (
        review.details &&
        typeof review.details === 'object' &&
        'rating' in review.details &&
        typeof review.details.rating === 'number'
      ) {
        totalRating += review.details.rating
      }
    })
    const averageRating = reviewCount > 0 ? totalRating / reviewCount : 0
    const lastActivity = await payload.find({
      collection: 'activities',
      where: { userId: { equals: mentor.id } },
      sort: '-createdAt',
      limit: 1,
    })
    let mentorName = `${mentor.firstName || ''} ${mentor.lastName || ''}`.trim()
    if (!mentorName) mentorName = 'Unknown'
    // Get mentor activities (limit 10, sorted by createdAt desc)
    const activitiesRes = await payload.find({
      collection: 'activities',
      where: { userId: { equals: mentor.id } },
      sort: '-createdAt',
      limit: 10,
    })
    return NextResponse.json({
      id: mentor.id,
      name: mentorName,
      email: mentor.email,
      school: schoolName,
      reviewCount,
      averageRating,
      lastActivity: lastActivity.docs[0]?.createdAt || mentor.updatedAt || mentor.createdAt,
      activities: activitiesRes.docs,
    })
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
