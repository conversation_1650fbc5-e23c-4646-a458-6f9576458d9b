# Database Synchronization Scripts

These scripts help synchronize data between MongoDB and Payload CMS to fix issues with data not being properly reflected in the UI.

## Available Scripts

### Enforce Database Consistency (Recommended)

This script checks and updates all database connection code in the project to ensure consistent database usage. It prevents the issue where different parts of the application connect to different databases.

```bash
npm run enforce-db
```

This script will:

1. Check all database connection code in the project
2. Update MongoDB connection code to always use the same database name
3. Update environment variables to include the correct database name
4. Add validation checks to ensure consistent database usage

### Fix Database Connection

This script fixes the database connection issue by ensuring all connections go to the same database. It copies data from the "test" database to the "young-reporter" database and updates the .env files to use the correct database.

```bash
npm run fix-db
```

This script will:

1. Connect to both the "test" and "young-reporter" databases
2. Copy all collections from "test" to "young-reporter"
3. Update the .env and .env.local files to use the "young-reporter" database
4. Ensure all connections go to the same database

### Reset MongoDB Users

This script removes all users from MongoDB and then imports them from Payload CMS. This is useful when the users in MongoDB are not the same as the ones in Payload CMS.

```bash
npm run reset-users
```

This script will:

1. Remove all users from MongoDB
2. Ask if you want to import users from Payload CMS
3. If yes, it will run the `export-users` script to import users from Payload CMS

### Export Users from Payload CMS

This script exports users from Payload CMS and imports them into MongoDB.

```bash
npm run export-users
```

This script will:

1. Ask for your Payload CMS credentials
2. Get all users from Payload CMS
3. Import them into MongoDB

### Direct Fix for Profile Update Issue

This script directly updates the MongoDB database to fix profile update issues without requiring Payload CMS API access.

```bash
npm run direct-fix <userId>
```

Example:

```bash
npm run direct-fix 64f7e1234567890abcdef123
```

### Synchronize a Specific User

This script synchronizes a specific user between MongoDB and Payload CMS.

```bash
npm run sync-user <userId>
```

Example:

```bash
npm run sync-user 64f7e1234567890abcdef123
```

### Synchronize All Articles for a User

This script synchronizes all articles for a specific user between MongoDB and Payload CMS.

```bash
npm run sync-user-articles <userId>
```

Example:

```bash
npm run sync-user-articles 64f7e1234567890abcdef123
```

### Synchronize a Specific Article

This script synchronizes a specific article between MongoDB and Payload CMS.

```bash
npm run sync-article <articleId>
```

Example:

```bash
npm run sync-article 64f7e1234567890abcdef123
```

## Finding User IDs

You can find your user ID in several ways:

1. **From the Payload CMS Admin Panel**:
   - Log in to the Payload CMS admin panel
   - Navigate to the Users collection
   - Click on your user
   - The ID will be in the URL: `/admin/collections/users/<userId>`

2. **From the MongoDB Database**:
   - Connect to your MongoDB database
   - Query the `users` collection: `db.users.find({email: "<EMAIL>"})`
   - The `id` field contains your user ID

3. **From the Browser Console**:
   - Log in to the application
   - Open the browser developer tools (F12)
   - Go to the Network tab
   - Look for requests to `/api/auth/me`
   - In the response, find the `user.id` field

## Troubleshooting

### MongoDB Connection Issues

If you encounter MongoDB connection issues, make sure your `.env` file contains the correct `MONGODB_URI` variable. The scripts will try to read this variable from the environment or directly from the `.env` file.

### Database Mismatch Issues

If you're experiencing issues with data not being properly reflected in the UI, it's likely because your application is connecting to two different databases: "test" and "young-reporter".

To fix this issue:

1. First, run the `fix-db` script to copy data between databases:

   ```bash
   npm run fix-db
   ```

2. Then, run the `enforce-db` script to ensure all code uses the same database:

   ```bash
   npm run enforce-db
   ```

3. Restart your application for the changes to take effect.

### Cache Issues

If you're still experiencing issues with data not being reflected in the UI after running the synchronization scripts, try clearing your browser cache or using a private/incognito window.

## How It Works

These scripts ensure that data is properly synchronized between MongoDB and Payload CMS by:

1. Ensuring all connections go to the same database
2. Copying data between databases if needed
3. Updating environment variables to use the correct database
4. Adding cache-busting mechanisms to force the UI to refresh
5. Enforcing consistent database usage across the codebase

The key to preventing synchronization issues is to ensure that all parts of the application connect to the same database. The `enforce-db` script checks and updates all database connection code in the project to ensure consistent database usage.
