'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import FeedbackInbox from '@/components/feedback/FeedbackInbox'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function MyFeedbackPage() {
  const router = useRouter()

  return (
    <DashboardLayout>
      <div className="p-6" dir="rtl">
        <div className="flex items-center mb-6">
          <Link href="/dashboard" className="ml-4">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة إلى لوحة التحكم
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">التعليقات والملاحظات</h1>
        </div>

        <div className="mb-6">
          <p className="text-gray-500 dark:text-gray-400">
            اطلع على الملاحظات المجهولة من المعلمين على مقالاتك المقدمة. تظل هويات المعلمين خاصة، تمامًا كما تكون هويات الطلاب مجهولة عند نشر المقالات. هذه قناة اتصال في اتجاه واحد - لا يمكنك الرد مباشرة على الملاحظات. إذا كانت لديك أسئلة، يرجى التحدث مع منسق المعلمين.
          </p>
        </div>

        <FeedbackInbox />
      </div>
    </DashboardLayout>
  )
}
