'use server'

import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import jwt from 'jsonwebtoken'
import { connectToDatabase } from '@/lib/mongodb'

export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const payload = await getPayload({ config })

    // Verify the token
    const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

    // Get the user ID from the token
    const userId = typeof decoded === 'object' ? decoded.id : null

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
      depth: 2,
    })

    // Verify user role - handle both string ID and populated object cases
    let roleSlug: string
    if (typeof user.role === 'string') {
      const role = await payload.findByID({
        collection: 'roles',
        id: user.role,
        depth: 0,
      })
      roleSlug = role?.slug || ''
    } else if (user.role && typeof user.role === 'object') {
      roleSlug = user.role.slug || ''
    } else {
      return NextResponse.json({ error: 'Invalid user role configuration' }, { status: 400 })
    }

    // Check allowed roles
    if (!['mentor', 'super-admin', 'school-admin'].includes(roleSlug)) {
      return NextResponse.json(
        { error: 'Forbidden', message: 'Only mentors and admins can flag systemic issues' },
        { status: 403 },
      )
    }

    // Get school ID - handle both string ID and populated object cases
    let schoolId: string | undefined
    if (typeof user.school === 'string') {
      schoolId = user.school
    } else if (user.school && typeof user.school === 'object') {
      schoolId = user.school.id
    }

    // Only require school for non-super-admins
    if (roleSlug !== 'super-admin' && !schoolId) {
      return NextResponse.json(
        { error: 'Mentor must be associated with a school' },
        { status: 400 },
      )
    }

    // Parse the request body
    const body = await req.json()
    const { title, description, type, severity, relatedTeacherIds, relatedArticleIds } = body

    // Validate required fields
    if (!title || !description || !type || !severity) {
      return NextResponse.json(
        { error: 'Missing required fields: title, description, type, and severity are required' },
        { status: 400 },
      )
    }

    // Check if the systemicIssues collection exists
    let issue;
    try {
      // Create a new issue
      issue = await payload.create({
        collection: 'systemicIssues',
        data: {
          title,
          description,
          type,
          severity,
          status: 'open',
          reporter: userId,
          school: schoolId,
          relatedTeachers: relatedTeacherIds || [],
          relatedArticles: relatedArticleIds || [],
        },
      });
    } catch (error) {
      console.error('Error creating systemic issue:', error);

      // If the collection doesn't exist, return a more helpful error
      if (error.message && error.message.includes('Collection not found')) {
        return NextResponse.json(
          {
            error: 'SystemicIssues collection not found. Please make sure the collection is properly registered in payload.config.ts.'
          },
          { status: 500 }
        );
      }

      throw error;
    }

    // Create an activity record if issue was created successfully
    if (issue) {
      try {
        await payload.create({
          collection: 'activities',
          data: {
            userId,
            activityType: 'flag-systemic-issue',
            details: {
              issueId: issue.id,
              issueTitle: issue.title,
              issueType: issue.type,
            },
            school: schoolId,
          },
        });
      } catch (error) {
        console.error('Error creating activity record:', error);
        // Continue even if activity creation fails
      }
    }

    return NextResponse.json({ success: true, issue })
  } catch (error) {
    console.error('Error flagging systemic issue:', error)
    return NextResponse.json({ error: 'Failed to flag systemic issue' }, { status: 500 })
  }
}
