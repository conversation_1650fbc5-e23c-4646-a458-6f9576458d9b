import { CollectionAfterChangeHook } from 'payload'
import { updatePointsAndRankings } from '../utils/points'

// Helper function to update school rankings
const updateSchoolRankings = async ({ req }: { req: any }) => {
  const { payload } = req

  try {
    // Get all articles with populated author and school
    const articles = await payload.find({
      collection: 'articles',
      where: {
        status: {
          equals: 'published',
        },
      },
      depth: 2, // Populate relationships
    })

    // Count published articles per school
    const schoolCounts: Record<string, { count: number; name: string }> = {}

    articles.docs.forEach((article: { author: { school: { id: any; name: any } } }) => {
      if (article.author && article.author.school) {
        const schoolId =
          typeof article.author.school === 'object'
            ? article.author.school.id
            : article.author.school

        if (!schoolCounts[schoolId]) {
          schoolCounts[schoolId] = {
            count: 0,
            name:
              typeof article.author.school === 'object'
                ? article.author.school.name
                : 'Unknown School',
          }
        }

        schoolCounts[schoolId].count++
      }
    })

    // Convert to array and sort
    const rankings = Object.entries(schoolCounts)
      .map(([id, data]) => ({
        id,
        name: data.name,
        count: data.count,
      }))
      .sort((a, b) => b.count - a.count)

    // Find existing school ranking or create new one
    const existingStats = await payload.find({
      collection: 'statistics',
      where: {
        type: {
          equals: 'schoolRanking',
        },
      },
    })

    if (existingStats.docs.length > 0) {
      // Update existing
      await payload.update({
        collection: 'statistics',
        id: existingStats.docs[0].id,
        data: {
          data: rankings,
        },
      })
    } else {
      // Create new
      await payload.create({
        collection: 'statistics',
        data: {
          name: 'School Rankings',
          type: 'schoolRanking',
          data: rankings,
        },
      })
    }
  } catch (error) {
    console.error('Error updating school rankings:', error)
  }
}

// Helper function to update student rankings
const updateStudentRankings = async ({ req }: { req: any }) => {
  const { payload } = req

  try {
    // Get all articles with populated author and reviews
    const articles = await payload.find({
      collection: 'articles',
      where: {
        status: {
          equals: 'published',
        },
      },
      depth: 2, // Populate relationships
    })

    // Calculate average rating per student
    const studentStats: Record<
      string,
      { totalRating: number; articleCount: number; name: string }
    > = {}

    articles.docs.forEach(
      (article: { author: { id: any; email: string }; teacherReview: any[] }) => {
        if (article.author && article.teacherReview && article.teacherReview.length > 0) {
          const studentId = typeof article.author === 'object' ? article.author.id : article.author

          if (!studentStats[studentId]) {
            studentStats[studentId] = {
              totalRating: 0,
              articleCount: 0,
              name: typeof article.author === 'object' ? article.author.email : 'Unknown Student',
            }
          }

          // Calculate average rating for this article
          const ratings = article.teacherReview.map((review) => review.rating)
          const avgRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length

          studentStats[studentId].totalRating += avgRating
          studentStats[studentId].articleCount++
        }
      },
    )

    // Calculate average rating and convert to array
    const rankings = Object.entries(studentStats)
      .map(([id, data]) => ({
        id,
        name: data.name,
        articleCount: data.articleCount,
        averageRating: data.totalRating / data.articleCount,
      }))
      .sort((a, b) => b.averageRating - a.averageRating)

    // Find existing student ranking or create new one
    const existingStats = await payload.find({
      collection: 'statistics',
      where: {
        type: {
          equals: 'studentRanking',
        },
      },
    })

    if (existingStats.docs.length > 0) {
      // Update existing
      await payload.update({
        collection: 'statistics',
        id: existingStats.docs[0].id,
        data: {
          data: rankings,
        },
      })
    } else {
      // Create new
      await payload.create({
        collection: 'statistics',
        data: {
          name: 'Student Rankings',
          type: 'studentRanking',
          data: rankings,
        },
      })
    }
  } catch (error) {
    console.error('Error updating student rankings:', error)
  }
}

// Helper function to update teacher rankings
const updateTeacherRankings = async ({ req }: { req: any }) => {
  const { payload } = req

  try {
    // Get all articles with populated teacher reviews
    const articles = await payload.find({
      collection: 'articles',
      where: {
        status: {
          equals: 'published',
        },
      },
      depth: 2, // Populate relationships
    })

    // Count reviews per teacher
    const teacherStats: Record<string, { reviewCount: number; name: string }> = {}

    articles.docs.forEach((article: { teacherReview: any[] }) => {
      if (article.teacherReview && article.teacherReview.length > 0) {
        article.teacherReview.forEach((review: { reviewer: { id: any; email: string } }) => {
          if (review.reviewer) {
            const teacherId =
              typeof review.reviewer === 'object' ? review.reviewer.id : review.reviewer

            if (!teacherStats[teacherId]) {
              teacherStats[teacherId] = {
                reviewCount: 0,
                name:
                  typeof review.reviewer === 'object' ? review.reviewer.email : 'Unknown Teacher',
              }
            }

            teacherStats[teacherId].reviewCount++
          }
        })
      }
    })

    // Convert to array and sort
    const rankings = Object.entries(teacherStats)
      .map(([id, data]) => ({
        id,
        name: data.name,
        reviewCount: data.reviewCount,
      }))
      .sort((a, b) => b.reviewCount - a.reviewCount)

    // Find existing teacher ranking or create new one
    const existingStats = await payload.find({
      collection: 'statistics',
      where: {
        type: {
          equals: 'teacherRanking',
        },
      },
    })

    if (existingStats.docs.length > 0) {
      // Update existing
      await payload.update({
        collection: 'statistics',
        id: existingStats.docs[0].id,
        data: {
          data: rankings,
        },
      })
    } else {
      // Create new
      await payload.create({
        collection: 'statistics',
        data: {
          name: 'Teacher Rankings',
          type: 'teacherRanking',
          data: rankings,
        },
      })
    }
  } catch (error) {
    console.error('Error updating teacher rankings:', error)
  }
}

// Helper function to update mentor rankings
const updateMentorRankings = async ({ req }: { req: any }) => {
  const { payload } = req

  try {
    // Get all news posts with populated author
    const news = await payload.find({
      collection: 'news',
      where: {
        status: {
          equals: 'published',
        },
      },
      depth: 1, // Populate relationships
    })

    // Count published news per mentor
    const mentorCounts: Record<string, { count: number; name: string }> = {}

    news.docs.forEach((newsItem: { author: { id: any; email: any } }) => {
      if (newsItem.author) {
        const mentorId = typeof newsItem.author === 'object' ? newsItem.author.id : newsItem.author

        if (!mentorCounts[mentorId]) {
          mentorCounts[mentorId] = {
            count: 0,
            name: typeof newsItem.author === 'object' ? newsItem.author.email : 'Unknown Mentor',
          }
        }

        mentorCounts[mentorId].count++
      }
    })

    // Convert to array and sort
    const rankings = Object.entries(mentorCounts)
      .map(([id, data]) => ({
        id,
        name: data.name,
        count: data.count,
      }))
      .sort((a, b) => b.count - a.count)

    // Find existing mentor ranking or create new one
    const existingStats = await payload.find({
      collection: 'statistics',
      where: {
        type: {
          equals: 'mentorRanking',
        },
      },
    })

    if (existingStats.docs.length > 0) {
      // Update existing
      await payload.update({
        collection: 'statistics',
        id: existingStats.docs[0].id,
        data: {
          data: rankings,
        },
      })
    } else {
      // Create new
      await payload.create({
        collection: 'statistics',
        data: {
          name: 'Mentor Rankings',
          type: 'mentorRanking',
          data: rankings,
        },
      })
    }
  } catch (error) {
    console.error('Error updating mentor rankings:', error)
  }
}

// Hook to update all statistics
export const updateAllStatistics = async (args: { req: any }) => {
  await updateSchoolRankings(args)
  await updateStudentRankings(args)
  await updateTeacherRankings(args)
  await updateMentorRankings(args)
  
  // Add our new points-based rankings
  await updatePointsAndRankings({ payload: args.req.payload })
}

// Hook for article changes
export const articleAfterChange: CollectionAfterChangeHook = async (args) => {
  // Only update stats for published articles
  if (args.doc.status === 'published') {
    await updateSchoolRankings(args)
    await updateStudentRankings(args)
    await updateTeacherRankings(args)
  }
}

// Hook for news changes
export const newsAfterChange: CollectionAfterChangeHook = async (args) => {
  // Only update stats for published news
  if (args.doc.status === 'published') {
    await updateMentorRankings(args)
  }
}
