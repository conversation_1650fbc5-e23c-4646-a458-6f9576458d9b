import { use } from 'react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import ArticleEditClient from './client'

interface Params {
  id: string
}

export interface ArticleEditPageProps {
  params: Promise<Params> | Params
}

export default function ArticleEditPage({ params }: ArticleEditPageProps) {
  // Properly unwrap params using React.use()
  const resolvedParams = use(params as Promise<Params>) as Params
  const id = resolvedParams.id

  return (
    <DashboardLayout>
      <ArticleEditClient id={id} />
    </DashboardLayout>
  )
} 