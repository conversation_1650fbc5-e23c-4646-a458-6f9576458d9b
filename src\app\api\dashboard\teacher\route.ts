import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

// GET teacher dashboard data
export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 0, // Don't populate relationships to avoid ObjectId errors
      })

      // Verify user is a teacher
      console.log('User object:', JSON.stringify(user))

      // The role is stored as an ID, so we need to look it up
      const userRole = await payload.findByID({
        collection: 'roles',
        id: user.role,
        depth: 0,
      })

      console.log('User role object:', JSON.stringify(userRole))

      // Check if the user is a teacher, super-admin, or school-admin
      if (
        !userRole ||
        (userRole.slug !== 'teacher' &&
          userRole.slug !== 'super-admin' &&
          userRole.slug !== 'school-admin')
      ) {
        return NextResponse.json(
          {
            error: 'Forbidden',
            message: 'Only teachers can access this dashboard',
            userRole: userRole?.slug || 'unknown',
            userObject: user,
          },
          { status: 403 },
        )
      }

      console.log('User role confirmed as:', userRole.slug)

      // Get school ID
      const schoolId = typeof user.school === 'object' ? user.school?.id : user.school

      if (!schoolId) {
        return NextResponse.json(
          { error: 'Teacher must be associated with a school' },
          { status: 400 },
        )
      }

      // Verify the school exists
      try {
        const school = await payload.findByID({
          collection: 'schools',
          id: schoolId,
        })

        if (!school) {
          console.error(`School with ID ${schoolId} not found`)
          return NextResponse.json({ error: 'Associated school not found' }, { status: 404 })
        }

        console.log(`Teacher from school: ${school.name}`)
      } catch (schoolError) {
        console.error('Error finding school:', schoolError)
        return NextResponse.json({ error: 'Error finding associated school' }, { status: 500 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const limit = parseInt(url.searchParams.get('limit') || '5')

      // Get pending student approvals
      const pendingStudents = await payload.find({
        collection: 'users',
        where: {
          'role.slug': { equals: 'student' },
          school: { equals: schoolId },
          status: { equals: 'pending' },
        },
        limit,
      })

      // Get pending article reviews from all schools
      const pendingArticles = await payload.find({
        collection: 'articles',
        where: {
          status: { equals: 'pending-review' },
        },
        limit,
        depth: 0, // Don't populate relationships to avoid ObjectId errors
      })

      // Manually populate necessary fields to avoid CastError
      const populatedArticles = {
        ...pendingArticles,
        docs: await Promise.all(
          pendingArticles.docs.map(async (article) => {
            try {
              // Get author info if available
              let authorInfo = {
                id: 'unknown',
                firstName: 'Unknown',
                lastName: 'User',
                email: '',
              }

              if (article.author) {
                try {
                  // Check if author is already an object
                  if (typeof article.author === 'object' && article.author !== null) {
                    authorInfo = {
                      id: article.author.id || 'unknown',
                      firstName: article.author.firstName || 'Unknown',
                      lastName: article.author.lastName || 'User',
                      email: article.author.email || '',
                    }
                  } else if (typeof article.author === 'string') {
                    // Author is an ID, try to fetch it
                    try {
                      const author = await payload.findByID({
                        collection: 'users',
                        id: article.author,
                        depth: 0,
                      })

                      authorInfo = {
                        id: author.id,
                        firstName: author.firstName || 'Unknown',
                        lastName: author.lastName || 'User',
                        email: author.email || '',
                      }
                    } catch (fetchError) {
                      console.error('Error fetching author by ID:', fetchError)
                    }
                  }
                } catch (authorError) {
                  console.error('Error processing author:', authorError)
                }
              }

              return {
                ...article,
                author: authorInfo,
              }
            } catch (error) {
              console.error('Error populating article:', error)
              return article
            }
          }),
        ),
      }

      // Get teacher stats
      const completedReviews = await payload.find({
        collection: 'activities',
        where: {
          userId: { equals: userId },
          activityType: { equals: 'article-review' },
        },
        limit: 0,
      })

      const totalStudents = await payload.find({
        collection: 'users',
        where: {
          'role.slug': { equals: 'student' },
          school: { equals: schoolId },
          status: { equals: 'active' },
        },
        limit: 0,
      })

      const dashboardData = {
        stats: {
          pendingApprovals: pendingStudents.totalDocs,
          pendingReviews: pendingArticles.totalDocs,
          completedReviews: completedReviews.totalDocs,
          totalStudents: totalStudents.totalDocs,
          points: user.points || 0,
        },
        pendingStudents: pendingStudents.docs,
        pendingArticles: populatedArticles.docs,
      }

      return NextResponse.json(dashboardData)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching teacher dashboard data:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
