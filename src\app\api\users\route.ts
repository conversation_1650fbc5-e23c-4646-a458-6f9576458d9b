import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check user role
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'
      const isMentor = role === 'mentor'
      const isTeacher = role === 'teacher'

      if (!isAdmin && !isMentor && !isTeacher) {
        return NextResponse.json({ error: 'Forbidden', message: 'Only mentors, teachers, and admins can access this endpoint' }, { status: 403 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const approvedBy = url.searchParams.get('approvedBy')
      const roleFilter = url.searchParams.get('role')
      const schoolId = url.searchParams.get('schoolId')
      const limit = parseInt(url.searchParams.get('limit') || '10')
      const page = parseInt(url.searchParams.get('page') || '1')

      // Build the query
      const query: any = {}

      if (approvedBy) {
        // For approvedBy, we need to use the relationship field
        query['approvedBy.id'] = { equals: approvedBy }
      }

      if (roleFilter) {
        // Get role ID from slug
        const roleDoc = await payload.find({
          collection: 'roles',
          where: {
            slug: { equals: roleFilter },
          },
        })

        if (roleDoc.docs.length > 0) {
          query.role = { equals: roleDoc.docs[0].id }
        }
      }

      // Different roles have different access
      if (isTeacher) {
        // Teachers can only see students from their school
        const teacherSchoolId = typeof currentUser.school === 'object' ? currentUser.school?.id : currentUser.school

        if (teacherSchoolId) {
          query.school = { equals: teacherSchoolId }
        }

        // Teachers can only see students
        const studentRole = await payload.find({
          collection: 'roles',
          where: {
            slug: { equals: 'student' },
          },
        })

        if (studentRole.docs.length > 0) {
          query.role = { equals: studentRole.docs[0].id }
        }
      } else if (isMentor) {
        // Mentors can see users from their school
        const mentorSchoolId = typeof currentUser.school === 'object' ? currentUser.school?.id : currentUser.school

        if (schoolId) {
          // If schoolId is provided, check if it matches the mentor's school
          if (mentorSchoolId !== schoolId && role !== 'super-admin') {
            return NextResponse.json(
              { error: 'Forbidden', message: 'You can only access users from your school' },
              { status: 403 },
            )
          }

          query.school = { equals: schoolId }
        } else if (mentorSchoolId) {
          query.school = { equals: mentorSchoolId }
        }
      } else if (role === 'school-admin') {
        // School admins can only see users from their school
        const adminSchoolId = typeof currentUser.school === 'object' ? currentUser.school?.id : currentUser.school

        if (schoolId) {
          // If schoolId is provided, check if it matches the admin's school
          if (adminSchoolId !== schoolId && role !== 'super-admin') {
            return NextResponse.json(
              { error: 'Forbidden', message: 'You can only access users from your school' },
              { status: 403 },
            )
          }

          query.school = { equals: schoolId }
        } else if (adminSchoolId) {
          query.school = { equals: adminSchoolId }
        }
      }
      // Super admins can see all users

      // Get users
      const users = await payload.find({
        collection: 'users',
        where: query,
        sort: '-createdAt', // Sort by creation date, newest first
        limit,
        page,
        depth: 2,
      })

      // Remove sensitive information
      const sanitizedUsers = users.docs.map(user => {
        const { password, resetPasswordToken, resetPasswordExpiration, ...sanitizedUser } = user
        return sanitizedUser
      })

      return NextResponse.json({
        ...users,
        docs: sanitizedUsers,
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
