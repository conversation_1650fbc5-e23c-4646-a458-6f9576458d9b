'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Loader2, CheckCircle, XCircle, User, Image } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'

interface Student {
  id: string
  firstName: string
  lastName: string
  email: string
  status: string
  pendingFirstName?: string
  pendingLastName?: string
  profileImage?: string | { url?: string, id?: string } | null
  pendingProfileImage?: string | { url?: string, id?: string } | null
  profileImageStatus?: string
  nameChangeStatus?: string
}

export default function StudentApprovals() {
  const [pendingApprovals, setPendingApprovals] = useState<Student[]>([])
  const [pendingProfileChanges, setPendingProfileChanges] = useState<Student[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState<Record<string, boolean>>({})
  const [error, setError] = useState('')

  useEffect(() => {
    fetchStudentApprovals()
  }, [])

  const fetchStudentApprovals = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/dashboard/student-approvals', {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to fetch student approvals')
      }

      const data = await response.json()
      
      // Add debugging for profile changes
      console.log('Received pendingProfileChanges:', data.pendingProfileChanges)
      const nameChanges = data.pendingProfileChanges.filter((s: Student) => s.nameChangeStatus === 'pending')
      console.log('Students with pending name changes:', nameChanges)
      
      setPendingApprovals(data.pendingApprovals || [])
      setPendingProfileChanges(data.pendingProfileChanges || [])
      setIsLoading(false)
    } catch (err) {
      console.error('Error fetching student approvals:', err)
      setError('فشل في تحميل موافقات الطلاب. يرجى المحاولة مرة أخرى.')
      setIsLoading(false)
    }
  }

  const handleApproval = async (studentId: string, action: 'approve' | 'reject', approvalType: 'account' | 'profile-image' | 'name-change') => {
    try {
      setIsProcessing({ ...isProcessing, [studentId + approvalType]: true })
      
      const response = await fetch('/api/dashboard/student-approvals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          studentId,
          action,
          approvalType,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to process approval')
      }

      const data = await response.json()
      
      toast({
        title: 'نجاح',
        description: data.message,
      })

      // Refresh the list
      fetchStudentApprovals()
    } catch (err) {
      console.error('Error processing approval:', err)
      toast({
        title: 'خطأ',
        description: 'فشل في معالجة الموافقة. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      })
    } finally {
      setIsProcessing({ ...isProcessing, [studentId + approvalType]: false })
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4 rounded-md text-red-800 dark:text-red-300">
        {error}
      </div>
    )
  }

  return (
    <Tabs defaultValue="account" dir="rtl">
      <TabsList className="mb-4">
        <TabsTrigger value="account">
          موافقات الحسابات
          {pendingApprovals.length > 0 && (
            <Badge variant="secondary" className="mr-2">
              {pendingApprovals.length}
            </Badge>
          )}
        </TabsTrigger>
        <TabsTrigger value="profile">
          تغييرات الملف الشخصي
          {pendingProfileChanges.length > 0 && (
            <Badge variant="secondary" className="mr-2">
              {pendingProfileChanges.length}
            </Badge>
          )}
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="account">
        <Card>
          <CardHeader>
            <CardTitle>موافقات الحسابات المعلقة</CardTitle>
            <CardDescription>
              الموافقة على أو رفض حسابات الطلاب الجديدة من مدرستك
            </CardDescription>
          </CardHeader>
          <CardContent>
            {pendingApprovals.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                لا توجد موافقات حسابات معلقة
              </div>
            ) : (
              <div className="space-y-4">
                {pendingApprovals.map((student) => (
                  <div
                    key={student.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <Avatar>
                        <AvatarFallback>
                          {student.firstName?.[0]}
                          {student.lastName?.[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h4 className="font-medium">
                          {student.firstName} {student.lastName}
                        </h4>
                        <p className="text-sm text-gray-500">{student.email}</p>
                      </div>
                    </div>
                    <div className="flex space-x-2 space-x-reverse">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-500"
                        onClick={() => handleApproval(student.id, 'reject', 'account')}
                        disabled={isProcessing[student.id + 'account']}
                      >
                        {isProcessing[student.id + 'account'] ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <XCircle className="h-4 w-4 ml-1" />
                        )}
                        رفض
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleApproval(student.id, 'approve', 'account')}
                        disabled={isProcessing[student.id + 'account']}
                      >
                        {isProcessing[student.id + 'account'] ? (
                          <Loader2 className="h-4 w-4 animate-spin ml-1" />
                        ) : (
                          <CheckCircle className="h-4 w-4 ml-1" />
                        )}
                        قبول
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="profile">
        <Card>
          <CardHeader>
            <CardTitle>تغييرات الملف الشخصي المعلقة</CardTitle>
            <CardDescription>
              الموافقة على أو رفض صور الملف الشخصي للطلاب وتغييرات الاسم
            </CardDescription>
          </CardHeader>
          <CardContent>
            {pendingProfileChanges.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                لا توجد تغييرات معلقة للملف الشخصي
              </div>
            ) : (
              <div className="space-y-6">
                {pendingProfileChanges.map((student) => (
                  <div
                    key={student.id}
                    className="border rounded-lg overflow-hidden"
                  >
                    <div className="p-4 bg-gray-50 dark:bg-gray-800 flex items-center justify-between">
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <Avatar>
                          {student.profileImage ? (
                            <AvatarImage src={typeof student.profileImage === 'string' 
                              ? student.profileImage 
                              : typeof student.profileImage === 'object' && student.profileImage?.url
                              ? student.profileImage.url
                              : ''}
                              alt={`${student.firstName} ${student.lastName}`}
                            />
                          ) : (
                            <AvatarFallback>
                              {student.firstName?.[0]}
                              {student.lastName?.[0]}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <div>
                          <h4 className="font-medium">
                            {student.firstName} {student.lastName}
                          </h4>
                          <p className="text-sm text-gray-500">{student.email}</p>
                        </div>
                      </div>
                    </div>
                    
                    {student.profileImageStatus === 'pending' && (
                      <div className="p-4 border-t">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center">
                            <Image className="h-5 w-5 ml-2 text-blue-500" />
                            <h5 className="font-medium">تغيير صورة الملف الشخصي</h5>
                          </div>
                          <Badge>معلق</Badge>
                        </div>
                        
                        <div className="flex space-x-4 space-x-reverse mb-4">
                          <div className="text-center">
                            <p className="text-xs text-gray-500 mb-1">الحالية</p>
                            <div className="w-20 h-20 rounded-md bg-gray-100 dark:bg-gray-700 overflow-hidden">
                              {student.profileImage ? (
                                <img
                                  src={typeof student.profileImage === 'string' 
                                    ? student.profileImage 
                                    : typeof student.profileImage === 'object' && student.profileImage?.url
                                    ? student.profileImage.url
                                    : ''}
                                  alt="Current profile"
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="flex items-center justify-center h-full">
                                  <User className="h-8 w-8 text-gray-400" />
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <div className="text-center">
                            <p className="text-xs text-gray-500 mb-1">الجديدة</p>
                            <div className="w-20 h-20 rounded-md bg-gray-100 dark:bg-gray-700 overflow-hidden">
                              {student.pendingProfileImage ? (
                                <img
                                  src={typeof student.pendingProfileImage === 'string' 
                                    ? student.pendingProfileImage 
                                    : typeof student.pendingProfileImage === 'object' && student.pendingProfileImage?.url
                                    ? student.pendingProfileImage.url
                                    : ''}
                                  alt="New profile"
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="flex items-center justify-center h-full">
                                  <User className="h-8 w-8 text-gray-400" />
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        {(!student.pendingProfileImage || student.pendingProfileImage === student.profileImage) && (
                          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800 text-sm">
                            ملاحظة: لا يبدو أن هناك صورة جديدة للملف الشخصي أو أنها مطابقة للصورة الحالية.
                          </div>
                        )}
                        
                        <div className="flex justify-end space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-500"
                            onClick={() => handleApproval(student.id, 'reject', 'profile-image')}
                            disabled={isProcessing[student.id + 'profile-image']}
                          >
                            {isProcessing[student.id + 'profile-image'] ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <XCircle className="h-4 w-4 ml-1" />
                            )}
                            رفض
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleApproval(student.id, 'approve', 'profile-image')}
                            disabled={isProcessing[student.id + 'profile-image']}
                          >
                            {isProcessing[student.id + 'profile-image'] ? (
                              <Loader2 className="h-4 w-4 animate-spin ml-1" />
                            ) : (
                              <CheckCircle className="h-4 w-4 ml-1" />
                            )}
                            قبول
                          </Button>
                        </div>
                      </div>
                    )}
                    
                    {student.nameChangeStatus === 'pending' && (
                      <div className="p-4 border-t">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center">
                            <User className="h-5 w-5 ml-2 text-blue-500" />
                            <h5 className="font-medium">تغيير الاسم</h5>
                          </div>
                          <Badge>معلق</Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <p className="text-xs text-gray-500 mb-1">الاسم الحالي</p>
                            <p className="font-medium">
                              {student.firstName} {student.lastName}
                            </p>
                          </div>
                          
                          <div>
                            <p className="text-xs text-gray-500 mb-1">الاسم الجديد</p>
                            <p className="font-medium">
                              {student.pendingFirstName} {student.pendingLastName}
                            </p>
                          </div>
                        </div>
                        
                        {student.firstName === student.pendingFirstName && 
                         student.lastName === student.pendingLastName && (
                          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800 text-sm">
                            ملاحظة: الاسم المعلق مطابق للاسم الحالي. قد لا يكون هناك تغيير فعلي للموافقة عليه.
                          </div>
                        )}
                        
                        <div className="flex justify-end space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-500"
                            onClick={() => handleApproval(student.id, 'reject', 'name-change')}
                            disabled={isProcessing[student.id + 'name-change']}
                          >
                            {isProcessing[student.id + 'name-change'] ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <XCircle className="h-4 w-4 ml-1" />
                            )}
                            رفض
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleApproval(student.id, 'approve', 'name-change')}
                            disabled={isProcessing[student.id + 'name-change']}
                          >
                            {isProcessing[student.id + 'name-change'] ? (
                              <Loader2 className="h-4 w-4 animate-spin ml-1" />
                            ) : (
                              <CheckCircle className="h-4 w-4 ml-1" />
                            )}
                            قبول
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
