import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies for authentication
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized', success: false }, { status: 401 })
    }

    // Verify the token and get user info
    let userId: string | null = null
    let userEmail: string | null = null

    try {
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      userId = typeof decoded === 'object' && 'id' in decoded ? decoded.id : null
      userEmail = typeof decoded === 'object' && 'email' in decoded ? decoded.email : null
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Invalid token', success: false }, { status: 401 })
    }

    // Extract report data from request body
    const { articleId, reason } = await req.json()

    if (!articleId || !reason) {
      return NextResponse.json(
        { error: 'Article ID and reason are required', success: false },
        { status: 400 }
      )
    }

    // Initialize Payload
    const payload = await getPayload({ config })

    // Fetch the article to ensure it exists
    let article
    try {
      article = await payload.findByID({
        collection: 'articles',
        id: articleId,
      })
    } catch (articleError) {
      console.error('Error fetching article:', articleError)
      return NextResponse.json(
        { error: 'Article not found', success: false },
        { status: 404 }
      )
    }

    // Get the article author information
    const authorId = article.author ? 
      (typeof article.author === 'object' ? article.author.id : article.author) : 
      null
      
    // Get the author's school for easier filtering
    let authorSchool = null;
    if (typeof article.author === 'object' && article.author && article.author.school) {
      // Author has school info in the object
      authorSchool = typeof article.author.school === 'object' 
        ? article.author.school.id 
        : article.author.school;
    } else if (article.author) {
      // Need to look up the author to get their school
      try {
        const authorUser = await payload.findByID({
          collection: 'users',
          id: typeof article.author === 'object' ? article.author.id : article.author,
        });
        
        if (authorUser && authorUser.school) {
          authorSchool = typeof authorUser.school === 'object'
            ? authorUser.school.id
            : authorUser.school;
        }
      } catch (error) {
        console.error('Failed to fetch author school:', error);
      }
    }

    // Create the report using PayloadCMS
    try {
      const reportResult = await payload.create({
        collection: 'reports' as any,
        data: {
          articleTitle: article.title,
          article: articleId,
          author: authorId,
          authorSchool: authorSchool,
          reportedBy: userId,
          reason: reason,
          status: 'pending',
          resolved: false,
        } as any,
      })

      // Create activity log for this report
      try {
        await payload.create({
          collection: 'activities',
          data: {
            userId: userId as string,
            targetUserId: authorId as string,
            activityType: 'article-report' as any,
            details: {
              articleId: articleId,
              articleTitle: article.title,
              reason: reason,
              reportId: reportResult.id,
            },
            createdAt: new Date().toISOString(),
          } as any,
        })
      } catch (activityError) {
        console.error('Error creating activity record:', activityError)
        // Continue even if activity creation fails
      }

      // Create notifications for school admins
      try {
        // Find school based on the article author
        let schoolId = null
        
        if (typeof article.author === 'object' && article.author) {
          schoolId = typeof article.author.school === 'object' 
            ? article.author.school?.id 
            : article.author.school
        } else if (article.author) {
          // Try to find the user
          const authorUser = await payload.findByID({
            collection: 'users',
            id: article.author,
          })
          
          if (authorUser) {
            schoolId = typeof authorUser.school === 'object'
              ? authorUser.school?.id
              : authorUser.school
          }
        }
        
        if (schoolId) {
          // Find school admins for this school
          const schoolAdmins = await payload.find({
            collection: 'users',
            where: {
              role: { equals: 'school-admin' },
              school: { equals: schoolId },
            },
          })
          
          // Create notification for each school admin
          for (const admin of schoolAdmins.docs) {
            await payload.create({
              collection: 'notifications',
              data: {
                user: admin.id,
                type: 'warning' as any,
                message: `Article "${article.title}" has been reported by a user.`,
                read: false,
                data: {
                  articleId: articleId,
                  reportId: reportResult.id,
                },
                createdAt: new Date().toISOString(),
              } as any,
            })
          }
        }
      } catch (notificationError) {
        console.error('Error creating notification:', notificationError)
        // Continue even if notification creation fails
      }

      return NextResponse.json({
        success: true,
        message: 'Article reported successfully',
        reportId: reportResult.id,
      })
    } catch (reportError) {
      console.error('Error creating report:', reportError)
      return NextResponse.json(
        { error: 'Failed to create report', success: false },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error reporting article:', error)
    return NextResponse.json(
      { error: 'Failed to report article', success: false },
      { status: 500 }
    )
  }
}
