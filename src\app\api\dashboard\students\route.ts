import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    console.log('Students API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    console.log('Token found in cookies:', !!token)

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })
      console.log('Payload instance obtained')

      // Get the teacher's school
      const teacher = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 0, // Don't populate relationships to avoid ObjectId errors
      })
      console.log('Teacher found in database:', !!teacher)

      if (!teacher || !teacher.school) {
        return NextResponse.json({ error: 'Teacher not associated with a school' }, { status: 400 })
      }

      const schoolId = typeof teacher.school === 'object' ? teacher.school.id : teacher.school
      console.log('Teacher school ID:', schoolId)

      // Verify the school exists
      try {
        const school = await payload.findByID({
          collection: 'schools',
          id: schoolId,
        })

        if (!school) {
          console.error(`School with ID ${schoolId} not found`)
          return NextResponse.json({ error: 'Associated school not found' }, { status: 404 })
        }

        console.log(`Teacher from school: ${school.name}`)
      } catch (schoolError) {
        console.error('Error finding school:', schoolError)
        return NextResponse.json({ error: 'Error finding associated school' }, { status: 500 })
      }

      // First, get the student role ID
      const studentRole = await payload.find({
        collection: 'roles',
        where: {
          slug: {
            equals: 'student',
          },
        },
      })

      if (!studentRole || studentRole.docs.length === 0) {
        console.error('Student role not found')
        return NextResponse.json({ error: 'Student role not found' }, { status: 404 })
      }

      const studentRoleId = studentRole.docs[0].id
      console.log('Student role ID:', studentRoleId)

      // Get students from the teacher's school using MongoDB directly to handle mixed formats
      const { db } = await connectToDatabase()
      const { ObjectId } = await import('mongodb')

      // Create queries that handle both ObjectId and string formats
      const roleQuery = {
        $or: [{ role: new ObjectId(studentRoleId) }, { role: studentRoleId }],
      }

      const schoolQuery = {
        $or: [{ school: new ObjectId(schoolId) }, { school: schoolId }],
      }

      // Find students with mixed format support
      const students = await db
        .collection('users')
        .find({
          $and: [roleQuery, schoolQuery],
        })
        .toArray()

      console.log(`Found ${students.length} students`)

      // Convert to Payload format and populate relationships
      const studentsWithPopulation = []
      for (const student of students) {
        try {
          // Get populated student data from Payload
          const populatedStudent = await payload.findByID({
            collection: 'users',
            id: student._id.toString(),
            depth: 1,
          })
          studentsWithPopulation.push(populatedStudent)
        } catch (error) {
          console.warn(`Could not populate student ${student._id}:`, error.message)
          // Fallback: use the raw student data
          studentsWithPopulation.push({
            ...student,
            id: student._id.toString(),
          })
        }
      }

      return NextResponse.json({
        students: studentsWithPopulation,
        totalStudents: studentsWithPopulation.length,
      })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching students:', error)
    return NextResponse.json({ error: 'Failed to fetch students' }, { status: 500 })
  }
}
