import type { CollectionConfig } from 'payload'

const Reports: CollectionConfig = {
  slug: 'reports',
  admin: {
    useAsTitle: 'articleTitle',
    group: 'Content',
    defaultColumns: ['articleTitle', 'status', 'createdAt'],
    listSearchableFields: ['articleTitle', 'reason'],
  },
  access: {
    read: ({ req }) => {
      const { user } = req
      
      if (!user) return false
      
      // Super admin and school admin can access reports
      if (user.role === 'super-admin') return true
      
      if (user.role === 'school-admin' && user.school) {
        // School admins can only see reports for their school's students
        return {
          'author.school': {
            equals: user.school,
          },
        }
      }
      
      return false
    },
    create: ({ req }) => {
      // Anyone can report an article
      return Boolean(req.user)
    },
    update: ({ req }) => {
      // Only admins can update reports
      if (!req.user) return false
      const role = typeof req.user.role === 'object' ? req.user.role.slug : req.user.role
      return ['super-admin', 'school-admin'].includes(role)
    },
    delete: ({ req }) => {
      // Only super admins can delete reports
      if (!req.user) return false
      const role = typeof req.user.role === 'object' ? req.user.role.slug : req.user.role
      return role === 'super-admin'
    },
  },
  fields: [
    {
      name: 'articleTitle',
      type: 'text',
      required: true,
      label: 'Article Title',
    },
    {
      name: 'article',
      type: 'relationship',
      relationTo: 'articles',
      required: true,
      label: 'Related Article',
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      label: 'Article Author',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'authorSchool',
      type: 'relationship',
      relationTo: 'schools',
      label: 'Author School',
      admin: {
        description: 'School of the article author (for filtering)',
        readOnly: true,
      },
    },
    {
      name: 'reportedBy',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      label: 'Reported By',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'reason',
      type: 'textarea',
      required: true,
      label: 'Report Reason',
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'pending',
      options: [
        {
          label: 'Pending',
          value: 'pending',
        },
        {
          label: 'Reviewed',
          value: 'reviewed',
        },
        {
          label: 'Ignored',
          value: 'ignored',
        },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'resolved',
      type: 'checkbox',
      defaultValue: false,
      label: 'Resolved',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'resolvedBy',
      type: 'relationship',
      relationTo: 'users',
      label: 'Resolved By',
      admin: {
        position: 'sidebar',
        condition: (data) => Boolean(data.resolved),
      },
    },
    {
      name: 'resolvedAt',
      type: 'date',
      label: 'Resolved At',
      admin: {
        position: 'sidebar',
        condition: (data) => Boolean(data.resolved),
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'actions',
      type: 'array',
      label: 'Actions Taken',
      admin: {
        condition: (data) => Boolean(data.resolved),
      },
      fields: [
        {
          name: 'action',
          type: 'select',
          required: true,
          options: [
            {
              label: 'Article Removed',
              value: 'article-removed',
            },
            {
              label: 'Content Edited',
              value: 'content-edited',
            },
            {
              label: 'Warning Issued',
              value: 'warning-issued',
            },
            {
              label: 'No Action Required',
              value: 'no-action',
            },
          ],
        },
        {
          name: 'actionBy',
          type: 'relationship',
          relationTo: 'users',
          required: true,
          label: 'Action By',
        },
        {
          name: 'notes',
          type: 'textarea',
          label: 'Notes',
        },
        {
          name: 'actionDate',
          type: 'date',
          required: true,
          defaultValue: () => new Date(),
          admin: {
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
      ],
    },
  ],
  timestamps: true,
}

export default Reports 