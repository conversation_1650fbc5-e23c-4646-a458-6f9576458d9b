import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { incrementViewCount } from '@/utils/viewUtils'
import config from '@/payload.config'

export async function GET(req: NextRequest, { params }: { params: { slug: string } }) {
  try {
    // Get the slug from the URL and decode it
    let { slug } = params
    // Decode the slug to handle special characters
    slug = decodeURIComponent(slug)
    console.log(`News Slug (decoded): ${slug}`)

    // Get the payload instance
    const payload = await getPayload({ config })

    // Find the news item by slug
    const news = await payload.find({
      collection: 'news',
      where: {
        slug: {
          equals: slug,
        },
        status: {
          equals: 'published',
        },
      },
      depth: 2, // Populate relationships
    })

    console.log(`News Items: ${JSON.stringify(news, null, 2)}`)

    // Check if news item exists
    if (!news.docs || news.docs.length === 0) {
      console.log('News Item not found')
      return NextResponse.json({ error: 'News not found' }, { status: 404 })
    }

    // Get the first matching news item
    const newsItem = news.docs[0]

    // Increment view count asynchronously without waiting for the result
    // This won't block the API response, and we don't need to return the updated view count
    incrementViewCount('news', newsItem.id).catch(error => {
      console.error('Error incrementing view count:', error)
    })

    return NextResponse.json(newsItem)
  } catch (error) {
    console.error('Error fetching news by slug:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
