import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'

/**
 * API endpoint to calculate and update school points
 * Schools get points based on the sum of all their users' points
 */
export async function GET(req: NextRequest) {
  try {
    console.log('Update school points API called')
    
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the token and get the user ID
    const { userId } = await verifyJWT(token)

    if (!userId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get the payload instance
    const payload = await getPayload({ config })
    
    // Get the current user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })

    // Verify user is a super admin or school admin
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'super-admin' && role !== 'school-admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }
    
    // Get all schools
    const schools = await payload.find({
      collection: 'schools',
    })
    
    // Process each school to update its points
    const updatedSchools = []
    
    for (const school of schools.docs) {
      const schoolId = school.id
      
      // Get all users from this school
      const users = await payload.find({
        collection: 'users',
        where: {
          school: { equals: schoolId },
        },
      })
      
      // Calculate total points for this school
      let totalPoints = 0
      users.docs.forEach(user => {
        totalPoints += user.points || 0
      })
      
      // Create or update school statistics
      const existingStats = await payload.find({
        collection: 'statistics',
        where: {
          type: { equals: 'schoolPoints' },
          school: { equals: schoolId },
        },
      })
      
      if (existingStats.docs.length > 0) {
        // Update existing statistics
        await payload.update({
          collection: 'statistics',
          id: existingStats.docs[0].id,
          data: {
            name: `${school.name} Points`,
            data: { points: totalPoints },
          },
        })
      } else {
        // Create new statistics
        await payload.create({
          collection: 'statistics',
          data: {
            name: `${school.name} Points`,
            type: 'schoolPoints',
            school: schoolId,
            data: { points: totalPoints },
          },
        })
      }
      
      updatedSchools.push({
        id: schoolId,
        name: school.name,
        points: totalPoints,
      })
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'School points updated successfully',
      schools: updatedSchools,
    })
  } catch (error) {
    console.error('Error updating school points:', error)
    return NextResponse.json(
      { 
        error: 'Internal Server Error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}