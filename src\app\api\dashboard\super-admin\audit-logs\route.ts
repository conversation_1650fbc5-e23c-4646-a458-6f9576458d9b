import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    if (!token) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
    const userId = typeof decoded === 'object' ? decoded.id : null
    if (!userId) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    const payload = await getPayload({ config })
    const user = await payload.findByID({ collection: 'users', id: userId, depth: 1 })
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'super-admin') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    try {
      const { db } = await connectToDatabase()
      const logs = await db.collection('auditLogs').find({}).sort({ timestamp: -1 }).limit(100).toArray()
      if (logs.length > 0) {
        return NextResponse.json({ logs: logs.map((log: any) => ({
          id: log._id?.toString() || log.id,
          action: log.action,
          collection: log.collection,
          documentId: log.documentId,
          userId: log.userId,
          userName: log.userName,
          timestamp: log.timestamp,
          details: log.details,
          schoolId: log.schoolId,
          schoolName: log.schoolName,
        })) })
      }
    } catch (mongoError) {
      console.warn('Error fetching audit logs from MongoDB, falling back to Payload:', mongoError)
    }
    // Fallback to Payload CMS (only if 'auditLogs' is a valid collection slug)
    // Skipping Payload CMS block if not a valid collection
    // Fallback mock data
    return NextResponse.json({ logs: [
      {
        id: '1',
        action: 'create',
        collection: 'articles',
        documentId: 'art123',
        userId: 'user1',
        userName: 'John Doe',
        timestamp: new Date().toISOString(),
        schoolId: 'school1',
        schoolName: 'Westview High',
      },
      {
        id: '2',
        action: 'update',
        collection: 'users',
        documentId: 'user456',
        userId: 'user2',
        userName: 'Admin User',
        timestamp: new Date().toISOString(),
        details: 'Updated role from student to teacher',
        schoolId: 'school2',
        schoolName: 'Eastlake Academy',
      },
    ] })
  } catch (error) {
    console.error('Error fetching audit logs:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 