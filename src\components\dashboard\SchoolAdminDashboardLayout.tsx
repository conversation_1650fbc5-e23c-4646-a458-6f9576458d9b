'use client'

import { useState, ReactNode, useEffect } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { NotificationProvider } from '@/contexts/NotificationContext'
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown'
import {
  LayoutDashboard,
  Users,
  FileText,
  UserCircle,
  Menu,
  X,
  Moon,
  Sun,
  Bell,
  Search,
  LogOut,
  Settings,
  School,
  BarChart,
  MessageSquare,
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface SchoolAdminDashboardLayoutProps {
  children: ReactNode
}

export default function SchoolAdminDashboardLayout({ children }: SchoolAdminDashboardLayoutProps) {
  const router = useRouter()
  const [darkMode, setDarkMode] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const pathname = usePathname()

  const navItems = [
    {
      icon: <LayoutDashboard className="w-5 h-5" />,
      text: 'لوحة التحكم',
      href: '/school-admin-dashboard',
    },
    {
      icon: <Users className="w-5 h-5" />,
      text: 'المعلمون',
      href: '/school-admin-dashboard/teachers',
    },
    {
      icon: <Users className="w-5 h-5" />,
      text: 'الموجهون',
      href: '/school-admin-dashboard/mentors',
    },
    {
      icon: <Users className="w-5 h-5" />,
      text: 'الطلاب',
      href: '/school-admin-dashboard/students',
    },
    {
      icon: <FileText className="w-5 h-5" />,
      text: 'المقالات',
      href: '/school-admin-dashboard/articles',
    },
    {
      icon: <MessageSquare className="w-5 h-5" />,
      text: 'الأنشطة',
      href: '/school-admin-dashboard/activities',
    },
    {
      icon: <BarChart className="w-5 h-5" />,
      text: 'التقارير',
      href: '/school-admin-dashboard/reports',
    },
    {
      icon: <School className="w-5 h-5" />,
      text: 'ملف المدرسة',
      href: '/school-admin-dashboard/school-profile',
    },
    {
      icon: <UserCircle className="w-5 h-5" />,
      text: 'ملفي الشخصي',
      href: '/school-admin-dashboard/my-profile',
    },
  ]

  const toggleDarkMode = () => {
    setDarkMode(!darkMode)
    document.documentElement.classList.toggle('dark')
  }

  const handleLogout = async () => {
    try {
      // Call the logout API
      const response = await fetch('/api/logout')

      if (response.ok) {
        // Clear any local storage or state
        localStorage.removeItem('notifications')

        // Redirect to login page
        router.push('/login')
      } else {
        console.error('Logout failed')
      }
    } catch (error) {
      console.error('Error during logout:', error)
    }
  }

  const [userId, setUserId] = useState('')
  const [schoolId, setSchoolId] = useState('')

  useEffect(() => {
    // In a real app, this would be fetched from an API or auth context
    // For now, we'll use a mock user ID
    setUserId('school-admin1')
    setSchoolId('school1')
  }, [])

  return (
    <NotificationProvider userRole="school-admin" userId={userId} schoolId={schoolId}>
      <div className={`min-h-screen ${darkMode ? 'dark' : ''}`} dir="rtl">
        <div className="flex h-screen overflow-hidden">
          {/* Sidebar */}
          <aside
            className={`${sidebarOpen ? 'w-64' : 'w-20'} bg-white dark:bg-gray-800 h-screen transition-all duration-300 ease-in-out border-l border-border`}
          >
            <div className="flex flex-col h-full">
              <div className="flex items-center gap-2 p-4 h-16 border-b border-border">
                <School className="w-8 h-8 text-primary" />
                {sidebarOpen && <h1 className="text-xl font-bold dark:text-white">مدير المدرسة</h1>}
              </div>

              <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
                {navItems.map((item, index) => {
                  const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`)
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                        isActive
                          ? 'bg-primary/10 text-primary'
                          : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                    >
                      {item.icon}
                      {sidebarOpen && <span>{item.text}</span>}
                    </Link>
                  )
                })}
              </nav>

              <div className="p-4 border-t border-border">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleDarkMode}
                  className="w-full flex items-center justify-center p-3 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                >
                  {darkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
                  {sidebarOpen && (
                    <span className="mr-3">{darkMode ? 'الوضع الفاتح' : 'الوضع الداكن'}</span>
                  )}
                </Button>
              </div>
            </div>
          </aside>

          {/* Main Content */}
          <div className="flex-1 flex flex-col overflow-hidden bg-gray-50 dark:bg-gray-900">
            {/* Navbar */}
            <header className="bg-white dark:bg-gray-800 border-b border-border h-16">
              <div className="flex items-center justify-between px-4 h-full">
                <div className="flex items-center gap-4">
                  <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(!sidebarOpen)}>
                    {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
                  </Button>
                  <div className="hidden md:flex relative w-64">
                    <Search className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <input
                      type="search"
                      placeholder="بحث..."
                      className="w-full pr-8 h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    />
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <NotificationDropdown />

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="relative">
                        <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center">
                          <span className="text-sm font-medium">م م</span>
                        </div>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => router.push('/school-admin-dashboard/my-profile')}
                      >
                        <UserCircle className="ml-2 h-4 w-4" />
                        <span>ملفي الشخصي</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => router.push('/school-admin-dashboard/school-profile')}
                      >
                        <School className="ml-2 h-4 w-4" />
                        <span>ملف المدرسة</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Settings className="ml-2 h-4 w-4" />
                        <span>الإعدادات</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={handleLogout}>
                        <LogOut className="ml-2 h-4 w-4" />
                        <span>تسجيل الخروج</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </header>

            {/* Main Content */}
            <main className="flex-1 overflow-y-auto p-6">{children}</main>
          </div>
        </div>
      </div>
    </NotificationProvider>
  )
}
