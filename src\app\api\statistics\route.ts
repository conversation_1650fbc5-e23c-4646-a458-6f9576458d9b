import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { getTopUsers, updatePointsAndRankings } from '@/utils/points'

export async function GET(_req: NextRequest) {
  try {
    console.info('Statistics API called');
    
    // Get the payload instance
    const payload = await getPayload({ config })

    // First check if we need to update rankings
    const lastUpdateDoc = await payload.find({
      collection: 'statistics',
      where: {
        type: {
          equals: 'studentRanking',
        },
      },
      limit: 1,
    });
    
    const shouldUpdate = 
      !lastUpdateDoc.docs.length || 
      !lastUpdateDoc.docs[0].lastUpdated || 
      (new Date().getTime() - new Date(lastUpdateDoc.docs[0].lastUpdated).getTime() > 3600000); // Update if older than 1 hour
    
    if (shouldUpdate) {
      console.info('Updating rankings before serving statistics...');
      await updatePointsAndRankings({ payload });
    }

    // Get all statistics
    const statistics = await payload.find({
      collection: 'statistics',
      depth: 2,
    })
    
    console.info(`Retrieved ${statistics.docs.length} statistics documents`);

    // Get counts for other collections
    const [users, articles, news, schools, activitiesRes] = await Promise.all([
      payload.find({ collection: 'users' }),
      payload.find({ collection: 'articles' }),
      payload.find({ collection: 'news' }),
      payload.find({ collection: 'schools' }),
      payload.find({ collection: 'activities' }),
    ])

    console.info(`Retrieved collection counts:
      - Users: ${users.totalDocs}
      - Articles: ${articles.totalDocs}
      - News: ${news.totalDocs}
      - Schools: ${schools.totalDocs}
      - Activities: ${activitiesRes.docs.length}
    `);

    const activities = activitiesRes.docs

    // Get the leaderboards from the statistics collection
    const studentRankingsDoc = statistics.docs.find(doc => doc.type === 'studentRanking');
    const teacherRankingsDoc = statistics.docs.find(doc => doc.type === 'teacherRanking');
    const mentorRankingsDoc = statistics.docs.find(doc => doc.type === 'mentorRanking');
    
    let topStudents = [];
    let topTeachers = [];
    let topMentors = [];
    
    // Check if we have statistics documents with data
    if (studentRankingsDoc && Array.isArray(studentRankingsDoc.data) && studentRankingsDoc.data.length > 0) {
      topStudents = studentRankingsDoc.data;
      console.info(`Found ${topStudents.length} students in rankings`);
    } else {
      // Fallback to calculating from users if statistics not available
      console.warn('No student rankings found in statistics, calculating from users...');
      const students = users.docs.filter(user => 
        typeof user.role === 'object' ? user.role?.slug === 'student' : user.role === 'student'
      ).sort((a, b) => (b.points || 0) - (a.points || 0)).slice(0, 10);
      
      topStudents = await getTopUsers({ 
        payload, 
        role: 'student', 
        limit: 10,
        users: students
      });
    }
    
    if (teacherRankingsDoc && Array.isArray(teacherRankingsDoc.data) && teacherRankingsDoc.data.length > 0) {
      topTeachers = teacherRankingsDoc.data;
      console.info(`Found ${topTeachers.length} teachers in rankings`);
    } else {
      console.warn('No teacher rankings found in statistics, calculating from users...');
      const teachers = users.docs.filter(user => 
        typeof user.role === 'object' ? user.role?.slug === 'teacher' : user.role === 'teacher'
      ).sort((a, b) => (b.points || 0) - (a.points || 0)).slice(0, 10);
      
      topTeachers = await getTopUsers({ 
        payload, 
        role: 'teacher', 
        limit: 10,
        users: teachers
      });
    }
    
    if (mentorRankingsDoc && Array.isArray(mentorRankingsDoc.data) && mentorRankingsDoc.data.length > 0) {
      topMentors = mentorRankingsDoc.data;
      console.info(`Found ${topMentors.length} mentors in rankings`);
    } else {
      console.warn('No mentor rankings found in statistics, calculating from users...');
      const mentors = users.docs.filter(user => 
        typeof user.role === 'object' ? user.role?.slug === 'mentor' : user.role === 'mentor'
      ).sort((a, b) => (b.points || 0) - (a.points || 0)).slice(0, 10);
      
      topMentors = await getTopUsers({ 
        payload, 
        role: 'mentor', 
        limit: 10,
        users: mentors
      });
    }

    // Verify the leaderboard data
    if (topStudents.length === 0) {
      console.warn('No student data found in leaderboards');
    }
    
    if (topTeachers.length === 0) {
      console.warn('No teacher data found in leaderboards');
    }
    
    if (topMentors.length === 0) {
      console.warn('No mentor data found in leaderboards');
    }

    // Log sample user data to debug image URLs
    if (topStudents.length > 0) {
      // Type assertion to avoid unknown type errors
      const sampleStudent = topStudents[0] as any;
      console.info('Sample student data:', {
        id: sampleStudent.id,
        name: sampleStudent.name,
        points: sampleStudent.points,
        image: sampleStudent.image
      });
    }
    
    if (topTeachers.length > 0) {
      // Type assertion to avoid unknown type errors
      const sampleTeacher = topTeachers[0] as any;
      console.info('Sample teacher data:', {
        id: sampleTeacher.id,
        name: sampleTeacher.name,
        points: sampleTeacher.points,
        image: sampleTeacher.image
      });
    }
    
    if (topMentors.length > 0) {
      // Type assertion to avoid unknown type errors
      const sampleMentor = topMentors[0] as any;
      console.info('Sample mentor data:', {
        id: sampleMentor.id,
        name: sampleMentor.name,
        points: sampleMentor.points,
        image: sampleMentor.image
      });
    }
    
    // Find school data
    const schoolRankingsDoc = statistics.docs.find(doc => doc.type === 'schoolRanking');
    let schoolRankings: Array<{
      id: string;
      name: string;
      count?: number;
      points?: number;
      image?: string;
    }> = [];
    
    if (schoolRankingsDoc && Array.isArray(schoolRankingsDoc.data)) {
      schoolRankings = schoolRankingsDoc.data as any;
      console.info(`Found ${schoolRankings.length} schools in rankings`);
    } else {
      console.warn('No school rankings found in statistics');
    }

    // Compile platform statistics
    const platformStats = {
      totalUsers: users.totalDocs,
      totalArticles: articles.totalDocs,
      totalNews: news.totalDocs,
      totalSchools: schools.totalDocs,
      globalActivityHeatmap: activities.reduce(
        (acc: { [key: string]: number }, activity: any) => {
          const date = activity.createdAt.split('T')[0]
          acc[date] = (acc[date] || 0) + 1
          return acc
        },
        {},
      ),
      crossSchoolStatistics: {
        averageUsersPerSchool:
          schools.docs.length > 0 ? users.docs.length / schools.docs.length : 0,
      },
      systemHealthMonitor: {
        cpuUsage: 75,
        memoryUsage: 60,
        uptime: '2 days',
      },
      rawAuditLogs: {
        creationEvents: 100,
        updateEvents: 50,
        deletionEvents: 25,
      },
      systemConfigurations: {
        featureToggles: {
          darkMode: true,
          notifications: false,
        },
        multiTenancyFlags: {
          enabled: true,
          schoolSpecificSettings: true,
        },
      },
      usersByRole: {
        students: users.docs.filter((user) =>
          typeof user.role === 'object' ? user.role?.slug === 'student' : user.role === 'student',
        ).length,
        teachers: users.docs.filter((user) =>
          typeof user.role === 'object' ? user.role?.slug === 'teacher' : user.role === 'teacher',
        ).length,
        mentors: users.docs.filter((user) =>
          typeof user.role === 'object' ? user.role?.slug === 'mentor' : user.role === 'mentor',
        ).length,
        admins: users.docs.filter((user) =>
          typeof user.role === 'object'
            ? user.role?.slug === 'super-admin' || user.role?.slug === 'school-admin'
            : user.role === 'super-admin' || user.role === 'school-admin',
        ).length,
      },
      articlesByStatus: {
        published: articles.docs.filter((article) => article.status === 'published').length,
        pendingReview: articles.docs.filter((article) => article.status === 'pending-review')
          .length,
        draft: articles.docs.filter((article) => article.status === 'draft').length,
      },
      // Add the leaderboards
      leaderboards: {
        students: topStudents,
        teachers: topTeachers,
        mentors: topMentors,
        schools: schoolRankings,
      },
    }

    console.info('Sending statistics response');
    return NextResponse.json({
      statistics: statistics.docs,
      platformStats,
    })
  } catch (error) {
    console.error('Error fetching statistics:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
