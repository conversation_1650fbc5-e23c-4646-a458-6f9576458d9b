import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import { ObjectId } from 'mongodb'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { isValidObjectId } from '@/utils/mongoUtils'

// DELETE: Delete an article and send notifications
export async function DELETE(req: NextRequest, context: { params: any }) {
  try {
    const params = await context.params
    const { id } = params
    const articleId = String(id)
    
    console.log('Processing delete request for article:', articleId)
    
    // Parse request body for report information
    const body = await req.json()
    const { reportId, reason, notifyUsers = true } = body
    
    if (!reason) {
      return NextResponse.json(
        { success: false, error: 'سبب الحذف مطلوب' },
        { status: 400 }
      )
    }

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به' },
        { status: 401 }
      )
    }

    // Verify token to get user info
    let userId, userRole, user
    try {
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      userId = typeof decoded === 'object' ? decoded.id : null
      
      if (!userId) {
        return NextResponse.json(
          { success: false, error: 'غير مصرح به' },
          { status: 401 }
        )
      }
      
      // Initialize PayloadCMS
      const payload = await getPayload({ config })
      
      // Get current user details
      user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2
      })
      
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'المستخدم غير موجود' },
          { status: 404 }
        )
      }
      
      // Extract role information
      const rawRole = user.role
      
      if (typeof rawRole === 'object' && rawRole !== null && 'slug' in rawRole) {
        userRole = rawRole.slug
      } else if (typeof rawRole === 'string') {
        try {
          const roleDoc = await payload.findByID({ 
            collection: 'roles', 
            id: rawRole 
          })
          
          if (roleDoc && roleDoc.slug) {
            userRole = roleDoc.slug
          } else {
            userRole = rawRole
          }
        } catch {
          userRole = rawRole
        }
      } else {
        userRole = typeof rawRole === 'string' ? rawRole : 'unknown'
      }
    } catch (err) {
      console.error('Auth error:', err)
      return NextResponse.json(
        { success: false, error: 'خطأ في المصادقة' },
        { status: 401 }
      )
    }

    // Only school-admin or super-admin can delete articles
    const allowedRoles = ['school-admin', 'super-admin', 'admin', 'schoolAdmin', 'superAdmin']
    const isAdmin = allowedRoles.some(role => 
      userRole === role || 
      (typeof userRole === 'string' && userRole.includes(role))
    )
    
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'صلاحيات غير كافية لحذف المقالات' },
        { status: 403 }
      )
    }

    // Connect to database
    const payload = await getPayload({ config })
    const { db } = await connectToDatabase()
    
    console.log('Connected to database, fetching article')
    
    // Find the article in the database
    let article
    try {
      // Try finding by ID
      if (isValidObjectId(articleId)) {
        article = await db.collection('articles').findOne({
          _id: new ObjectId(articleId)
        })
      }
      
      // If not found, try finding by direct ID or slug
      if (!article) {
        article = await db.collection('articles').findOne({
          $or: [{ id: articleId }, { slug: articleId }]
        })
      }
      
      if (!article) {
        return NextResponse.json(
          { success: false, error: 'المقال غير موجود' },
          { status: 404 }
        )
      }
      
      console.log('Found article:', {
        id: article._id || article.id,
        title: article.title,
        authorId: article.author
      })
    } catch (error) {
      console.error('Error finding article:', error)
      return NextResponse.json(
        { success: false, error: 'خطأ في البحث عن المقال' },
        { status: 500 }
      )
    }
    
    // Find report details
    let report
    if (reportId) {
      try {
        if (isValidObjectId(reportId)) {
          report = await db.collection('article_reports').findOne({
            _id: new ObjectId(reportId)
          })
        }
        
        if (!report) {
          report = await db.collection('article_reports').findOne({
            id: reportId
          })
        }
        
        if (report) {
          console.log('Found report:', {
            id: report._id || report.id,
            reportedBy: report.reportedBy,
            reason: report.reason
          })
        }
      } catch (error) {
        console.error('Error finding report:', error)
        // Continue even if report is not found - still delete the article
      }
    }
    
    // Get author information for notification
    const authorId = article.author
    let authorUser
    
    try {
      if (isValidObjectId(authorId)) {
        authorUser = await db.collection('users').findOne({
          $or: [
            { _id: new ObjectId(authorId) },
            { id: authorId }
          ]
        })
        
        if (authorUser) {
          console.log('Found article author:', {
            id: authorUser._id || authorUser.id,
            email: authorUser.email
          })
        }
      }
    } catch (error) {
      console.error('Error finding author:', error)
      // Continue even if author is not found
    }
    
    // Get reporter information if available
    let reporterUser
    if (report && report.reportedBy) {
      try {
        if (isValidObjectId(report.reportedBy)) {
          reporterUser = await db.collection('users').findOne({
            $or: [
              { _id: new ObjectId(report.reportedBy) },
              { id: report.reportedBy }
            ]
          })
          
          if (reporterUser) {
            console.log('Found reporter:', {
              id: reporterUser._id || reporterUser.id,
              email: reporterUser.email
            })
          }
        }
      } catch (error) {
        console.error('Error finding reporter:', error)
        // Continue even if reporter is not found
      }
    }
    
    // Start transaction for deleting article and updating report
    try {
      // 1. Delete the article
      const deleteResult = await db.collection('articles').deleteOne({
        _id: new ObjectId(article._id || article.id)
      })
      
      if (deleteResult.deletedCount === 0) {
        return NextResponse.json(
          { success: false, error: 'فشل في حذف المقال' },
          { status: 500 }
        )
      }
      
      console.log('Article deleted successfully')
      
      // 2. Update the report status if found
      if (report) {
        await db.collection('article_reports').updateOne(
          { _id: new ObjectId(report._id || report.id) },
          { 
            $set: { 
              status: 'resolved',
              resolved: true,
              resolution: {
                resolvedBy: userId,
                resolvedAt: new Date(),
                comments: reason,
                action: 'deleted'
              }
            } 
          }
        )
        console.log('Report updated to resolved status')
      }
      
      // 3. Send notifications if requested
      if (notifyUsers) {
        // Create notification records
        const notifications = []
        
        // Notification for article author
        if (authorUser) {
          notifications.push({
            recipient: authorUser._id || authorUser.id,
            type: 'article-deleted',
            title: 'تم حذف مقالك',
            message: `تم حذف مقالك "${article.title}" بسبب: ${reason}`,
            createdAt: new Date(),
            read: false,
            data: {
              articleId: article._id || article.id,
              articleTitle: article.title,
              reason
            }
          })
        }
        
        // Notification for the reporter
        if (reporterUser) {
          notifications.push({
            recipient: reporterUser._id || reporterUser.id,
            type: 'report-actioned',
            title: 'تم معالجة تقرير المشكلة',
            message: `تم حذف المقال المبلغ عنه "${article.title}"`,
            createdAt: new Date(),
            read: false,
            data: {
              articleId: article._id || article.id,
              articleTitle: article.title,
              reportId: report ? (report._id || report.id) : null
            }
          })
        }
        
        // Insert notifications
        if (notifications.length > 0) {
          await db.collection('notifications').insertMany(notifications)
          console.log(`Created ${notifications.length} notifications`)
        }
      }
      
      return NextResponse.json({
        success: true,
        message: 'تم حذف المقال وإرسال الإشعارات بنجاح'
      })
    } catch (error) {
      console.error('Error in delete transaction:', error)
      return NextResponse.json(
        { success: false, error: 'خطأ في حذف المقال' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
} 