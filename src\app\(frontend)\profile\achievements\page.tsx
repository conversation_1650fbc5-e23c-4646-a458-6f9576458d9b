import { headers as getHeaders } from 'next/headers.js'
import { redirect } from 'next/navigation'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { ResponsiveLayout } from '@/components/ResponsiveLayout'
import { AchievementsDisplay } from './achievements-display'

export default async function AchievementsPage() {
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // Redirect to login if not authenticated
  if (!user) {
    redirect('/login?redirect=/profile/achievements')
  }

  // Fetch user's achievements
  const userAchievements = await payload.find({
    collection: 'user-achievements',
    where: {
      'user.id': {
        equals: user.id,
      },
    },
    depth: 2, // Populate the achievement relationship
  })

  // Fetch all possible achievements
  const allAchievements = await payload.find({
    collection: 'achievements',
    limit: 100,
  })

  return (
    <ResponsiveLayout>
      <div className="bg-blue-700 text-white py-12 px-4">
        <div className="container mx-auto">
          <h1 className="text-4xl font-bold mb-4">Your Achievements</h1>
          <p className="text-xl">Track your progress and earn badges</p>
        </div>
      </div>

      <div className="py-12 px-4">
        <div className="container mx-auto max-w-4xl">
          <AchievementsDisplay 
            userAchievements={userAchievements.docs} 
            allAchievements={allAchievements.docs}
            user={user}
          />
        </div>
      </div>
    </ResponsiveLayout>
  )
}
