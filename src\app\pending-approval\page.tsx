import React from 'react'
import Link from 'next/link'
import { CheckCircle2, Clock, Mail, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

export default function PendingApprovalPage() {
  return (
    <div className="min-h-screen bg-background flex flex-col items-center justify-center p-4">
      <Card className="max-w-3xl w-full p-8 text-center shadow-lg">
        <div className="mb-8">
          <div className="flex justify-center mb-6">
            <div className="relative w-32 h-32 flex items-center justify-center">
              <Clock className="w-24 h-24 text-primary" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-primary mb-2">Registration Successful!</h1>
          <div className="w-16 h-1 bg-primary mx-auto mb-6 rounded-full"></div>
        </div>

        <div>
          <div className="mb-8">
            <div className="bg-primary/10 p-4 rounded-lg inline-flex items-center mb-6">
              <Clock className="text-primary w-6 h-6 mr-2" />
              <span className="font-medium">Your account is pending approval</span>
            </div>
            
            <p className="text-muted-foreground mb-6">
              Thank you for registering with Young Reporter! Your application has been received 
              and is currently under review by our team. Once approved, you'll receive an email 
              notification and can start using your account.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-card border border-border rounded-lg p-4 flex items-start">
                <CheckCircle2 className="text-green-500 w-6 h-6 mr-3 shrink-0 mt-1" />
                <div className="text-left">
                  <h3 className="font-semibold mb-1">What happens now?</h3>
                  <p className="text-sm text-muted-foreground">
                    Our team will review your application to ensure you meet our requirements.
                    This typically takes 24-48 hours.
                  </p>
                </div>
              </div>
              
              <div className="bg-card border border-border rounded-lg p-4 flex items-start">
                <Mail className="text-blue-500 w-6 h-6 mr-3 shrink-0 mt-1" />
                <div className="text-left">
                  <h3 className="font-semibold mb-1">Stay tuned</h3>
                  <p className="text-sm text-muted-foreground">
                    Please check your email regularly. We'll notify you as soon as your 
                    account is approved and ready to use.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Button variant="outline" asChild>
              <Link href="/" className="flex items-center">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Return Home
              </Link>
            </Button>
            
            <Button asChild>
              <Link href="/login">
                Continue to Login
              </Link>
            </Button>
          </div>
        </div>
      </Card>
      
      <p className="text-sm text-muted-foreground mt-8">
        &copy; {new Date().getFullYear()} Young Reporter - All Rights Reserved.
      </p>
    </div>
  )
} 