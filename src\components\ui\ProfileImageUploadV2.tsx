'use client'

import { useState } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import IsolatedFileUpload from './IsolatedFileUpload'
import { toast } from '@/components/ui/use-toast'

interface ProfileImageUploadV2Props {
  currentImageUrl?: string
  pendingImageUrl?: string
  imageStatus?: string
  onImageUpload: (imageId: string) => void
  onUploadStart?: () => void
  onUploadError?: () => void
  firstName?: string
  lastName?: string
}

export default function ProfileImageUploadV2({
  currentImageUrl,
  pendingImageUrl,
  imageStatus,
  onImageUpload,
  onUploadStart,
  onUploadError,
  firstName,
  lastName,
}: ProfileImageUploadV2Props) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)

  const handleUploadStart = () => {
    console.log('Upload starting...')
    // Notify parent component that upload is starting
    if (onUploadStart) {
      onUploadStart()
    }
  }

  const handleFileUploaded = (fileId: string, fileUrl: string) => {
    console.log('File uploaded:', fileId, fileUrl)

    // Set the preview URL
    setPreviewUrl(fileUrl)

    // Get the user role from the parent component
    const userRole = imageStatus === 'approved' ? 'admin' : 'student'

    // Call the parent component's handler with the image ID
    try {
      onImageUpload(fileId)
    } catch (error) {
      console.error('Error in onImageUpload callback:', error)
      // Notify parent component of error
      if (onUploadError) {
        onUploadError()
      }
      toast({
        title: 'Error',
        description: 'Failed to process the image upload in parent component',
        variant: 'destructive',
      })
    }
  }

  const getStatusMessage = () => {
    switch (imageStatus) {
      case 'pending':
        return 'Your profile image is pending approval'
      case 'rejected':
        return 'Your profile image was rejected'
      default:
        return null
    }
  }

  const statusMessage = getStatusMessage()

  return (
    <div className="flex flex-col items-center">
      <Avatar className="h-24 w-24 mb-4">
        {previewUrl ? (
          <AvatarImage src={previewUrl} alt="Preview" />
        ) : pendingImageUrl && imageStatus === 'pending' ? (
          <AvatarImage src={pendingImageUrl} alt="Pending profile image" />
        ) : currentImageUrl ? (
          <AvatarImage src={currentImageUrl} alt="Profile image" />
        ) : (
          <AvatarFallback>
            {firstName?.[0]}
            {lastName?.[0]}
          </AvatarFallback>
        )}
      </Avatar>

      {statusMessage && (
        <div className="text-sm text-amber-600 dark:text-amber-400 mb-2">{statusMessage}</div>
      )}

      <IsolatedFileUpload
        onFileUploaded={handleFileUploaded}
        onUploadStart={handleUploadStart}
        altText={`Profile image for ${firstName} ${lastName}`}
      />

      <p className="text-xs text-gray-500 mt-2">
        Upload a profile picture (max 5MB). Image will be reviewed before appearing on your profile.
      </p>
    </div>
  )
}
