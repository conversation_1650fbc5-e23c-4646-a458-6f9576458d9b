import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import { isMediaPath } from '@/lib/media-utils'
import config from '@/payload.config'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ articleId: string }> }
) {
  try {
    // Get the current user
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized: Please log in' }, { status: 401 })
    }

    const user = await extractUserFromToken(token)

    if (!user || user.role !== 'mentor') {
      return NextResponse.json(
        { error: 'Unauthorized: Only mentors can access article content for review' },
        { status: 403 },
      )
    }

    const resolvedParams = await params
    const { articleId } = resolvedParams

    // Check if the article ID is a media path
    if (isMediaPath(articleId)) {
      return NextResponse.json({ error: 'Invalid article ID' }, { status: 400 })
    }

    const payload = await getPayload({ config })

    // Get the article with full content and relationships
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
      depth: 2, // Populate relationships
    })

    if (!article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 })
    }

    // Get author information
    let authorInfo = 'Unknown Author'
    if (article.author) {
      if (typeof article.author === 'object' && article.author !== null) {
        const author = article.author as any
        authorInfo = `${author.firstName || ''} ${author.lastName || ''}`.trim() || author.email || 'Unknown Author'
      } else {
        // If author is just an ID, fetch the user
        try {
          const authorUser = await payload.findByID({
            collection: 'users',
            id: article.author as string,
          })
          authorInfo = `${authorUser.firstName || ''} ${authorUser.lastName || ''}`.trim() || authorUser.email || 'Unknown Author'
        } catch (error) {
          console.error('Error fetching author:', error)
        }
      }
    }

    // Format teacher reviews with reviewer information
    const formattedTeacherReviews = []
    if (article.teacherReview && Array.isArray(article.teacherReview)) {
      for (let i = 0; i < article.teacherReview.length; i++) {
        const review = article.teacherReview[i]
        let reviewerName = 'Unknown Teacher'
        
        if (review.reviewer) {
          if (typeof review.reviewer === 'object' && review.reviewer !== null) {
            const reviewer = review.reviewer as any
            reviewerName = `${reviewer.firstName || ''} ${reviewer.lastName || ''}`.trim() || reviewer.email || 'Unknown Teacher'
          } else {
            // If reviewer is just an ID, fetch the user
            try {
              const reviewerUser = await payload.findByID({
                collection: 'users',
                id: review.reviewer as string,
              })
              reviewerName = `${reviewerUser.firstName || ''} ${reviewerUser.lastName || ''}`.trim() || reviewerUser.email || 'Unknown Teacher'
            } catch (error) {
              console.error('Error fetching reviewer:', error)
            }
          }
        }

        formattedTeacherReviews.push({
          index: i,
          reviewer: review.reviewer,
          reviewerName,
          rating: review.rating || 0,
          comment: review.comment || '',
          approved: review.approved || false,
          createdAt: (review as any).createdAt || article.createdAt,
          // Check if already reviewed by mentor
          mentorReviewed: !!(
            ('mentorFeedback' in review && review.mentorFeedback && typeof review.mentorFeedback === 'object') ||
            ('mentorReview' in review && review.mentorReview === true) ||
            ('mentorRating' in review && (review as any).mentorRating) ||
            ('mentorComment' in review && (review as any).mentorComment)
          ),
        })
      }
    }

    // Return the article content with formatted data
    return NextResponse.json(
      {
        success: true,
        article: {
          id: article.id,
          title: article.title || 'Untitled Article',
          content: article.content || {},
          summary: article.summary || '',
          author: authorInfo,
          status: article.status,
          createdAt: article.createdAt,
          updatedAt: article.updatedAt,
          featuredImage: article.featuredImage,
          category: article.category,
          tags: article.tags || [],
          teacherReviews: formattedTeacherReviews,
          views: article.views || 0,
        },
      },
      { status: 200 },
    )
  } catch (error) {
    console.error('Error fetching article content for mentor review:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch article content',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}
