import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function POST(req: NextRequest) {
  try {
    console.log('Profile image approval API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    console.log('Token found:', token ? 'Yes' : 'No')

    if (!token) {
      console.log('No token found, returning 401')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      console.log('Request body:', body)

      const { targetUserId, action } = body

      if (!targetUserId || !action) {
        console.log('Missing required fields')
        return NextResponse.json(
          { error: 'Target user ID and action are required' },
          { status: 400 },
        )
      }

      if (action !== 'approve' && action !== 'reject') {
        console.log('Invalid action')
        return NextResponse.json(
          { error: 'Action must be either "approve" or "reject"' },
          { status: 400 },
        )
      }

      // Initialize Payload
      const payload = await getPayload({ config })

      // Check if the current user is authorized to approve/reject images
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      const userRole = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role

      // Only teachers, mentors, school admins, and super admins can approve/reject images
      if (!['teacher', 'mentor', 'school-admin', 'super-admin'].includes(userRole)) {
        console.log('User not authorized to approve/reject images')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
      }

      // Get the target user
      const targetUser = await payload.findByID({
        collection: 'users',
        id: targetUserId,
      })

      if (!targetUser) {
        console.log('Target user not found')
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      // Check if the target user has a pending profile image
      if (!targetUser.pendingProfileImage) {
        console.log('Target user has no pending profile image')
        return NextResponse.json(
          { error: 'User has no pending profile image' },
          { status: 400 },
        )
      }

      // Update the user based on the action
      const updateData: Record<string, any> = {
        profileImageStatus: action === 'approve' ? 'approved' : 'rejected',
      }

      if (action === 'approve') {
        // Move the pending image to the profile image
        updateData.profileImage = targetUser.pendingProfileImage
        updateData.pendingProfileImage = null
      }

      // Update the user
      const updatedUser = await payload.update({
        collection: 'users',
        id: targetUserId,
        data: updateData,
      })

      console.log('User updated successfully')
      return NextResponse.json({
        success: true,
        message: `Profile image ${action === 'approve' ? 'approved' : 'rejected'} successfully`,
      })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error approving/rejecting profile image:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
