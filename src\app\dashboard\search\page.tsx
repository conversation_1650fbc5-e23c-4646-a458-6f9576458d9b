'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { extractPlainText } from '@/utils/richTextUtils'
import Link from 'next/link'
import { ArrowLeft, FileText, Newspaper, User } from 'lucide-react'

interface SearchResult {
  id: string
  title: string
  content: any
  type: 'article' | 'news' | 'user'
  createdAt: string
  author?: {
    firstName: string
    lastName: string
  }
  status?: string
}

export default function SearchPage() {
  const searchParams = useSearchParams()
  const query = searchParams.get('q') || ''
  
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('all')
  
  useEffect(() => {
    if (!query) return
    
    async function performSearch() {
      setIsLoading(true)
      setError('')
      
      try {
        // For now, we'll just search in Payload CMS
        // In a real implementation, you would call a search API
        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`, {
          credentials: 'include',
        })
        
        if (!response.ok) {
          throw new Error('Search failed')
        }
        
        const data = await response.json()
        setResults(data.results || [])
      } catch (err) {
        console.error('Search error:', err)
        setError('Failed to perform search. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }
    
    // For now, we'll use mock data
    const mockResults: SearchResult[] = [
      {
        id: '1',
        title: 'Climate Change Impact',
        content: 'This article discusses the impact of climate change on our environment.',
        type: 'article',
        createdAt: new Date().toISOString(),
        author: {
          firstName: 'John',
          lastName: 'Doe'
        },
        status: 'published'
      },
      {
        id: '2',
        title: 'School News Update',
        content: 'Latest news about our school events and achievements.',
        type: 'news',
        createdAt: new Date(Date.now() - 86400000).toISOString(),
        author: {
          firstName: 'Jane',
          lastName: 'Smith'
        },
        status: 'published'
      },
      {
        id: '3',
        title: 'Student Profile',
        content: 'Profile information about a student.',
        type: 'user',
        createdAt: new Date(Date.now() - 172800000).toISOString(),
      }
    ]
    
    // Filter mock results based on query
    const filteredResults = mockResults.filter(result => 
      result.title.toLowerCase().includes(query.toLowerCase()) || 
      (typeof result.content === 'string' && result.content.toLowerCase().includes(query.toLowerCase()))
    )
    
    setResults(filteredResults)
    setIsLoading(false)
    
    // In a real implementation, you would call performSearch() instead
    // performSearch()
  }, [query])
  
  const filteredResults = activeTab === 'all' 
    ? results 
    : results.filter(result => result.type === activeTab)
  
  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Button variant="outline" size="sm" asChild className="mr-4">
            <Link href="/dashboard">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Search Results</h1>
        </div>
        
        <Card className="mb-6">
          <CardContent className="pt-6">
            <form action="/dashboard/search" method="GET" className="flex gap-4">
              <div className="flex-1">
                <Label htmlFor="search" className="sr-only">Search</Label>
                <Input 
                  id="search" 
                  name="q" 
                  placeholder="Search..." 
                  defaultValue={query}
                  className="w-full"
                />
              </div>
              <Button type="submit">Search</Button>
            </form>
          </CardContent>
        </Card>
        
        {query && (
          <div className="mb-4">
            <p className="text-sm text-gray-500">
              Showing results for: <span className="font-medium">{query}</span>
            </p>
          </div>
        )}
        
        {isLoading ? (
          <div className="flex items-center justify-center h-40">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        ) : (
          <>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-6">
              <TabsList>
                <TabsTrigger value="all">All ({results.length})</TabsTrigger>
                <TabsTrigger value="article">
                  Articles ({results.filter(r => r.type === 'article').length})
                </TabsTrigger>
                <TabsTrigger value="news">
                  News ({results.filter(r => r.type === 'news').length})
                </TabsTrigger>
                <TabsTrigger value="user">
                  Users ({results.filter(r => r.type === 'user').length})
                </TabsTrigger>
              </TabsList>
            </Tabs>
            
            {filteredResults.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center py-8">
                    <p className="text-gray-500">No results found for "{query}"</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredResults.map((result) => (
                  <Card key={result.id}>
                    <CardContent className="pt-6">
                      <div className="flex items-start gap-4">
                        <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                          {result.type === 'article' && <FileText className="h-5 w-5 text-blue-500" />}
                          {result.type === 'news' && <Newspaper className="h-5 w-5 text-green-500" />}
                          {result.type === 'user' && <User className="h-5 w-5 text-purple-500" />}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium">
                              <Link 
                                href={
                                  result.type === 'article' 
                                    ? `/dashboard/my-articles/edit/${result.id}` 
                                    : result.type === 'news'
                                    ? `/dashboard/news/${result.id}`
                                    : `/dashboard/users/${result.id}`
                                }
                                className="hover:underline"
                              >
                                {result.title}
                              </Link>
                            </h3>
                            <div className="flex items-center gap-2">
                              {result.status && (
                                <span 
                                  className={`px-2 py-1 rounded-full text-xs ${
                                    result.status === 'published' 
                                      ? 'bg-green-100 text-green-800' 
                                      : result.status === 'pending-review'
                                      ? 'bg-yellow-100 text-yellow-800'
                                      : 'bg-blue-100 text-blue-800'
                                  }`}
                                >
                                  {result.status === 'published' 
                                    ? 'Published' 
                                    : result.status === 'pending-review'
                                    ? 'Pending Review'
                                    : 'Draft'}
                                </span>
                              )}
                              <span className="text-xs text-gray-500">
                                {new Date(result.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {typeof result.content === 'string' 
                              ? result.content.substring(0, 200) + (result.content.length > 200 ? '...' : '')
                              : extractPlainText(result.content).substring(0, 200) + '...'}
                          </p>
                          {result.author && (
                            <p className="text-xs text-gray-500 mt-2">
                              By {result.author.firstName} {result.author.lastName}
                            </p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  )
}
