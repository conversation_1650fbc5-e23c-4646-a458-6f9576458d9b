import { ObjectId } from 'mongodb'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { isMediaPath, isMediaPathError, logMediaPathError, safeMediaQuery, safePayloadResponse, filterMediaPathArticles } from '@/lib/media-utils'

// GET all articles (admin only)
export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check user role
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'
      const isMentor = role === 'mentor'
      const isTeacher = role === 'teacher'
      const isStudent = role === 'student'

      // Get URL parameters
      const url = new URL(req.url)
      const reviewerId = url.searchParams.get('reviewerId')
      const authorId = url.searchParams.get('authorId')
      const status = url.searchParams.get('status')
      const limit = parseInt(url.searchParams.get('limit') || '10')
      const page = parseInt(url.searchParams.get('page') || '1')

      // Build the query
      const query: any = {}

      if (reviewerId) {
        // For reviewerId, we need to query the array of teacher reviews
        query['teacherReview.reviewer'] = { equals: reviewerId }
      }

      if (authorId) {
        query['author.id'] = { equals: authorId }
      }

      if (status) {
        query.status = { equals: status }
      }

      // Different roles have different access
      if (isStudent) {
        // Students can only see their own articles or published articles
        query.or = [
          { 'author.id': { equals: userId } },
          { status: { equals: 'published' } },
        ]
      } else if (isTeacher) {
        // Teachers can see articles from their school
        const schoolId = typeof currentUser.school === 'object' ? currentUser.school?.id : currentUser.school

        if (schoolId) {
          query['author.school'] = { equals: schoolId }
        }
      } else if (isMentor) {
        // Mentors can see articles from their school
        const schoolId = typeof currentUser.school === 'object' ? currentUser.school?.id : currentUser.school

        if (schoolId) {
          query['author.school'] = { equals: schoolId }
        }
      }
      // Super admins can see all articles

      // Apply safe media query to prevent ObjectId casting errors
      const safeQuery = safeMediaQuery(query)
      console.log('Safe query for articles:', JSON.stringify(safeQuery))

      try {
        // Get articles
        const articlesResponse = await payload.find({
          collection: 'articles',
          where: safeQuery,
          sort: '-createdAt',
          limit,
          page,
          depth: 2,
        })

        // Process the response to filter out any media paths
        const safeArticlesResponse = safePayloadResponse(articlesResponse)

        return NextResponse.json(safeArticlesResponse)
      } catch (findError) {
        // Check if this is a media path error
        if (isMediaPathError(findError)) {
          logMediaPathError(findError, 'articles GET API')

          // Try a more direct approach with MongoDB
          try {
            console.log('Falling back to direct MongoDB query')
            const { db } = await connectToDatabase()
            const mongoArticles = await db.collection('articles').find().limit(limit).skip((page - 1) * limit).toArray()

            // Filter out any articles with media path IDs
            const safeArticles = filterMediaPathArticles(mongoArticles)

            // Format the response to match Payload's format
            const response = {
              docs: safeArticles,
              totalDocs: safeArticles.length,
              limit,
              totalPages: Math.ceil(safeArticles.length / limit),
              page,
              pagingCounter: (page - 1) * limit + 1,
              hasPrevPage: page > 1,
              hasNextPage: page * limit < safeArticles.length,
              prevPage: page > 1 ? page - 1 : null,
              nextPage: page * limit < safeArticles.length ? page + 1 : null,
            }

            return NextResponse.json(response)
          } catch (mongoError) {
            console.error('Error with MongoDB fallback:', mongoError)
            // Return empty results instead of an error
            return NextResponse.json({
              docs: [],
              totalDocs: 0,
              limit,
              totalPages: 0,
              page,
              pagingCounter: 1,
              hasPrevPage: false,
              hasNextPage: false,
              prevPage: null,
              nextPage: null,
            })
          }
        }

        // If it's not a media path error, rethrow it
        throw findError
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    // Check if this is a media path error
    if (isMediaPathError(error)) {
      logMediaPathError(error, 'articles GET API (main handler)')

      // Return empty results instead of an error
      return NextResponse.json({
        docs: [],
        totalDocs: 0,
        limit: 10,
        totalPages: 0,
        page: 1,
        pagingCounter: 1,
        hasPrevPage: false,
        hasNextPage: false,
        prevPage: null,
        nextPage: null,
      })
    }

    console.error('Error fetching articles:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// CREATE a new article
export async function POST(req: NextRequest) {
  try {
    console.log('Article POST API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    console.log('Token found:', token ? 'Yes' : 'No')

    if (!token) {
      console.log('No token found, returning 401')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      console.log('Verifying token...')
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      console.log('Request body received:', JSON.stringify(body).substring(0, 200) + '...')

      const { title, content, summary, status, category, tags, featuredImage } = body

      // Validate required fields
      if (!title || !content) {
        console.log('Missing required fields')
        return NextResponse.json({ error: 'Title and content are required' }, { status: 400 })
      }

      // Handle featuredImage - ensure it's a proper path
      let processedFeaturedImage = null;
      if (featuredImage) {
        // If it's a string but not already a valid media path, convert it
        if (typeof featuredImage === 'string') {
          if (!featuredImage.includes('/api/media/file/') && !featuredImage.startsWith('/media/')) {
            // This is likely just an ID or filename, convert it to a path
            processedFeaturedImage = `/api/media/file/${featuredImage}`;
            console.log('Converted ID to path for featuredImage:', processedFeaturedImage);
          } else {
            // Already a path, use it directly
            processedFeaturedImage = featuredImage;
            console.log('Using existing path for featuredImage:', processedFeaturedImage);
          }
        } else if (typeof featuredImage === 'object' && featuredImage !== null) {
          // Handle object format if present
          if (featuredImage.id) {
            processedFeaturedImage = `/api/media/file/${featuredImage.id}`;
          } else if (featuredImage.filename) {
            processedFeaturedImage = `/api/media/file/${featuredImage.filename}`;
          }
          console.log('Converted object to path for featuredImage:', processedFeaturedImage);
        }
      }

      // Generate summary if not provided
      const articleSummary =
        summary || content.substring(0, 200) + (content.length > 200 ? '...' : '')

      // Try to create in MongoDB first
      try {
        console.log('Connecting to MongoDB...')
        const { db } = await connectToDatabase()
        console.log('MongoDB connection successful')

        // Get the user from MongoDB
        console.log('Looking up user in MongoDB with ID:', userId)
        let user = await db.collection('users').findOne({ id: userId })

        // If not found, try with string ID
        if (!user) {
          console.log('User not found with ID, trying string ID')
          user = await db.collection('users').findOne({ id: userId.toString() })
        }

        // If still not found, try with _id
        if (!user) {
          console.log('User not found with string ID, trying _id')
          try {
            if (ObjectId.isValid(userId)) {
              user = await db.collection('users').findOne({ _id: new ObjectId(userId) })
            }
          } catch (err) {
            console.log('Error with ObjectId:', err)
          }
        }

        // If still not found, try a more flexible query
        if (!user) {
          console.log('User not found with _id, trying flexible query')
          const users = await db
            .collection('users')
            .find({
              $or: [
                { id: userId },
                { id: userId.toString() },
                { email: { $regex: new RegExp(userId, 'i') } },
              ],
            })
            .toArray()

          if (users.length > 0) {
            user = users[0]
            console.log('Found user with flexible query')
          }
        }

        // If still not found, try to get the last used user ID
        if (!user) {
          console.log('User not found with flexible query, trying last user ID')
          const lastUserSetting = await db
            .collection('app_settings')
            .findOne({ key: 'last_user_id' })

          if (lastUserSetting && lastUserSetting.value) {
            console.log('Found last user ID:', lastUserSetting.value)
            user = await db.collection('users').findOne({ id: lastUserSetting.value })
          }
        }

        if (!user) {
          console.log('No user found in MongoDB, falling back to Payload')
        } else {
          console.log('User found in MongoDB:', user.firstName, user.lastName)

          // Create article in MongoDB
          const articleId = new Date().getTime().toString()
          const newArticle = {
            id: articleId,
            title,
            content,
            summary: articleSummary, // Add summary field here too
            status,
            category,
            tags,
            featuredImage: processedFeaturedImage, // Use our processed image path
            author: {
              id: user.id || user._id.toString(),
              firstName: user.firstName || 'Unknown',
              lastName: user.lastName || 'User',
              email: user.email || '<EMAIL>',
            },
            views: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }

          console.log('Creating article in MongoDB...')
          await db.collection('articles').insertOne(newArticle)
          console.log('Article created in MongoDB with ID:', articleId)

          // Create notification
          const notificationId = new Date().getTime().toString() + '1'
          const notification = {
            id: notificationId,
            message:
              status === 'pending-review'
                ? `تم تقديم مقالك للمراجعة.`
                : `تم حفظ مقالك كمسودة.`,
            type: 'info',
            userId: user.id || user._id.toString(),
            userRole: user.role?.slug || 'student',
            read: false,
            date: new Date().toISOString(),
            createdAt: new Date().toISOString(),
          }

          console.log('Creating notification in MongoDB...')
          await db.collection('notifications').insertOne(notification)
          console.log('Notification created in MongoDB with ID:', notificationId)

          return NextResponse.json({
            success: true,
            message: 'Article created successfully',
            article: newArticle,
          })
        }
      } catch (mongoError) {
        console.warn('Error creating article in MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS
      console.log('Falling back to Payload CMS')
      const payload = await getPayload({ config })

      // Get the user
      console.log('Looking up user in Payload with ID:', userId)
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      if (!user) {
        console.log('User not found in Payload')
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      console.log('User found in Payload:', user.firstName, user.lastName)

      // Create article in Payload
      console.log('Creating article in Payload...')

      // Check if user.id is a media path
      if (isMediaPath(user.id)) {
        console.error('User ID is a media path, cannot create article:', user.id)
        return NextResponse.json({
          success: false,
          error: 'Invalid user ID (media path detected)',
          details: 'The system detected a media file path that cannot be used as a user ID',
        }, { status: 400 })
      }

      let article;
      try {
        article = await payload.create({
          collection: 'articles',
          data: {
            title,
            content,
            status,
            category,
            tags,
            featuredImage: processedFeaturedImage, // Use processed featuredImage
            author: user.id,
            // Use type assertion to add the summary property
            summary: articleSummary,
          } as any, // Type assertion to bypass TypeScript check
        })

        console.log('Article created in Payload with ID:', article.id)
      } catch (createError) {
        // Check if this is a media path error
        if (isMediaPathError(createError)) {
          logMediaPathError(createError, 'articles POST API')
          return NextResponse.json({
            success: false,
            error: 'Cannot create article due to media path issue',
            details: 'The system detected a media file path that cannot be processed',
          }, { status: 400 })
        }

        // If it's not a media path error, rethrow it
        throw createError
      }

      // Create notification
      try {
        console.log('Creating notification in Payload...')
        await payload.create({
          collection: 'notifications',
          data: {
            message:
              status === 'pending-review'
                ? `تم تقديم مقالك للمراجعة.`
                : `تم حفظ مقالك كمسودة.`,
            type: 'info',
            user: userId,
            read: false,
          },
        })
        console.log('Notification created in Payload')
      } catch (notificationError) {
        console.warn('Error creating notification in Payload:', notificationError)
      }

      return NextResponse.json({
        success: true,
        message: 'Article created successfully',
        article,
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    // Check if this is a media path error
    if (isMediaPathError(error)) {
      logMediaPathError(error, 'articles POST API (main handler)')
      return NextResponse.json({
        success: false,
        error: 'Cannot create article due to media path issue',
        details: 'The system detected a media file path that cannot be processed',
      }, { status: 400 })
    }

    console.error('Error creating article:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
