'use client'

import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import 'react-quill/dist/quill.snow.css'

// Import ReactQuill dynamically to avoid SSR issues
// Use a more specific import to avoid findDOMNode issues
const ReactQuill = dynamic(
  async () => {
    const { default: RQ } = await import('react-quill')
    // Prevent SSR issues by returning a component that doesn't use findDOMNode
    return function CustomReactQuill({ forwardedRef, ...props }: any) {
      return <RQ ref={forwardedRef} {...props} />
    }
  },
  {
    ssr: false,
    loading: () => <div className="h-64 w-full bg-gray-100 animate-pulse rounded-md"></div>,
  },
)

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  readOnly?: boolean
  className?: string
}

const modules = {
  toolbar: [
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ list: 'ordered' }, { list: 'bullet' }],
    [{ indent: '-1' }, { indent: '+1' }],
    ['link', 'image'],
    ['clean'],
  ],
}

const formats = [
  'header',
  'bold',
  'italic',
  'underline',
  'strike',
  'list',
  'bullet',
  'indent',
  'link',
  'image',
]

export function RichTextEditor({
  value,
  onChange,
  placeholder = 'Write something...',
  readOnly = false,
  className = '',
}: RichTextEditorProps) {
  const [mounted, setMounted] = useState(false)
  const [editorValue, setEditorValue] = useState('')

  // Set initial value once component is mounted
  useEffect(() => {
    setMounted(true)
    setEditorValue(value)
  }, [value])

  // Handle editor change
  const handleChange = (content: string) => {
    setEditorValue(content)
    onChange(content)
  }

  if (!mounted) {
    return <div className="h-64 w-full bg-gray-100 animate-pulse rounded-md"></div>
  }

  // Use a simpler approach for now - if ReactQuill is causing issues
  // we'll fall back to a basic textarea
  return (
    <div className={`rich-text-editor ${className}`}>
      {typeof window !== 'undefined' ? (
        <ReactQuill
          forwardedRef={null}
          theme="snow"
          value={editorValue}
          onChange={handleChange}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          readOnly={readOnly}
          className="min-h-[200px]"
        />
      ) : (
        <textarea
          value={editorValue}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={placeholder}
          readOnly={readOnly}
          className="w-full min-h-[200px] p-4 border rounded-md"
        />
      )}
    </div>
  )
}

// Helper function to convert HTML to Lexical format
export function htmlToLexical(html: string) {
  // This is a simplified conversion
  // In a real implementation, you would need a more robust parser
  return {
    root: {
      type: 'root',
      children: [
        {
          type: 'paragraph',
          children: [
            {
              type: 'text',
              text: html.replace(/<[^>]*>/g, ''), // Strip HTML tags for simple text
              version: 1,
            },
          ],
          direction: null,
          format: '',
          indent: 0,
          version: 1,
        },
      ],
      direction: null,
      format: '',
      indent: 0,
      version: 1,
    },
  }
}

// Helper function to convert Lexical format to HTML
export function lexicalToHtml(lexical: any) {
  try {
    if (!lexical || !lexical.root || !lexical.root.children) {
      return ''
    }

    // Extract text from children
    let html = ''
    lexical.root.children.forEach((child: any) => {
      if (child.type === 'paragraph') {
        const paragraphText = child.children.map((textNode: any) => textNode.text || '').join('')
        html += `<p>${paragraphText}</p>`
      }
    })

    return html
  } catch (error) {
    console.error('Error converting Lexical to HTML:', error)
    return ''
  }
}
