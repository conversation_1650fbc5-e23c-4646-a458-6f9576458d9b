'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { UserPlus, AlertCircle, CheckCircle } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

interface CreateAccountFormProps {
  schoolId: string
}

export default function CreateAccountForm({ schoolId }: CreateAccountFormProps) {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    role: 'teacher',
    password: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleRoleChange = (value: string) => {
    setFormData(prev => ({ ...prev, role: value }))
  }

  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Reset messages
    setError('')
    setSuccess('')
    
    // Validate form
    if (!formData.firstName.trim()) {
      setError('First name is required')
      return
    }
    
    if (!formData.lastName.trim()) {
      setError('Last name is required')
      return
    }
    
    if (!formData.email.trim() || !validateEmail(formData.email)) {
      setError('Valid email is required')
      return
    }
    
    if (!formData.password.trim() || formData.password.length < 8) {
      setError('Password is required and must be at least 8 characters')
      return
    }
    
    try {
      setIsSubmitting(true)
      
      const response = await fetch('/api/dashboard/school-admin/create-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          schoolId,
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create account')
      }
      
      const data = await response.json()
      
      setSuccess(`Successfully created account for ${formData.firstName} ${formData.lastName}`)
      toast({
        title: 'Account created',
        description: `Account for ${formData.firstName} ${formData.lastName} has been created. A welcome email has been sent with login instructions.`,
      })
      
      // Reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        role: 'teacher',
        password: '',
      })
      
    } catch (err: any) {
      console.error('Error creating account:', err)
      setError(err.message || 'Failed to create account. Please try again.')
      toast({
        title: 'Error',
        description: err.message || 'Failed to create account. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <UserPlus className="mr-2 h-5 w-5" />
          إنشاء حساب موظف
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="bg-red-50 text-red-500 p-4 rounded-md mb-4 flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </div>
        )}
        
        {success && (
          <div className="bg-green-50 text-green-500 p-4 rounded-md mb-4 flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            {success}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">الاسم الأول</Label>
              <Input
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                placeholder="أدخل الاسم الأول"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="lastName">اسم العائلة</Label>
              <Input
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                placeholder="أدخل اسم العائلة"
                required
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">البريد الإلكتروني</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="أدخل عنوان البريد الإلكتروني"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="password">كلمة المرور</Label>
            <div className="flex items-center gap-2">
              <Input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleChange}
                placeholder="أدخل كلمة المرور"
                required
                minLength={8}
              />
              <Button type="button" variant="ghost" onClick={() => setShowPassword(v => !v)}>
                {showPassword ? 'إخفاء' : 'إظهار'}
              </Button>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="role">الدور</Label>
            <Select name="role" value={formData.role} onValueChange={handleRoleChange}>
              <SelectTrigger>
                <SelectValue placeholder="اختر الدور" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="teacher">معلم</SelectItem>
                <SelectItem value="mentor">موجه</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Button type="submit" className="w-full" disabled={isSubmitting}>
            {isSubmitting ? 'جاري الإنشاء...' : 'إنشاء الحساب'}
          </Button>
        </form>
      </CardContent>
      <CardFooter className="justify-center text-sm text-muted-foreground">
        سيتم إرسال بريد إلكتروني للموظف الجديد مع تعليمات تسجيل الدخول.
      </CardFooter>
    </Card>
  )
}
