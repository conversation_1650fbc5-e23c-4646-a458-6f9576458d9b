'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { Star, MessageSquare, ThumbsUp, ThumbsDown } from 'lucide-react'

interface Review {
  id: string
  reviewer: string
  reviewerName: string
  type: 'article' | 'news'
  title: string
  comment: string
  rating: number
  approved?: boolean | null
  date: string
}

interface TeacherReviewsOverviewProps {
  schoolId?: string
}

export default function TeacherReviewsOverview({ schoolId }: TeacherReviewsOverviewProps) {
  const [loading, setLoading] = useState(true)
  const [reviews, setReviews] = useState<Review[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchReviews() {
      setLoading(true)
      try {
        // Fetch articles and news for the school (or all schools if super admin)
        const apiEndpoint = schoolId
          ? `/api/dashboard/school-admin/content`
          : `/api/dashboard/super-admin/content`

        const [articlesRes, newsRes] = await Promise.all([
          fetch(`${apiEndpoint}?type=article${schoolId ? `&schoolId=${schoolId}` : ''}`),
          fetch(`${apiEndpoint}?type=news${schoolId ? `&schoolId=${schoolId}` : ''}`),
        ])

        const articlesData = await articlesRes.json()
        const newsData = await newsRes.json()

        console.log('Articles data:', articlesData)
        console.log('News data:', newsData)

        const articleReviews: Review[] = (articlesData.content || []).flatMap((item: any) =>
          (item.teacherReview || []).map((review: any, index: number) => {
            // Handle reviewer data - it could be an object or string
            let reviewerName = 'مراجع غير معروف'
            let reviewerId = ''

            if (typeof review.reviewer === 'object' && review.reviewer) {
              reviewerName =
                review.reviewer.firstName && review.reviewer.lastName
                  ? `${review.reviewer.firstName} ${review.reviewer.lastName}`
                  : review.reviewer.name || review.reviewer.email || 'مراجع غير معروف'
              reviewerId = review.reviewer.id || review.reviewer._id || ''
            } else if (typeof review.reviewer === 'string') {
              reviewerId = review.reviewer
              reviewerName = 'مراجع غير معروف'
            }

            return {
              id: review.id || `${item.id}-review-${index}`,
              reviewer: reviewerId,
              reviewerName,
              type: 'article' as const,
              title: item.title || 'عنوان غير محدد',
              comment: review.comment || '',
              rating: review.rating || 0,
              approved: review.approved,
              date:
                review.createdAt || review.reviewDate || item.updatedAt || new Date().toISOString(),
            }
          }),
        )

        const newsReviews: Review[] = (newsData.content || []).flatMap((item: any) =>
          (item.teacherReview || []).map((review: any, index: number) => {
            // Handle reviewer data - it could be an object or string
            let reviewerName = 'مراجع غير معروف'
            let reviewerId = ''

            if (typeof review.reviewer === 'object' && review.reviewer) {
              reviewerName =
                review.reviewer.firstName && review.reviewer.lastName
                  ? `${review.reviewer.firstName} ${review.reviewer.lastName}`
                  : review.reviewer.name || review.reviewer.email || 'مراجع غير معروف'
              reviewerId = review.reviewer.id || review.reviewer._id || ''
            } else if (typeof review.reviewer === 'string') {
              reviewerId = review.reviewer
              reviewerName = 'مراجع غير معروف'
            }

            return {
              id: review.id || `${item.id}-review-${index}`,
              reviewer: reviewerId,
              reviewerName,
              type: 'news' as const,
              title: item.title || 'عنوان غير محدد',
              comment: review.comment || '',
              rating: review.rating || 0,
              approved: review.approved,
              date:
                review.createdAt || review.reviewDate || item.updatedAt || new Date().toISOString(),
            }
          }),
        )

        setReviews([...articleReviews, ...newsReviews])
        setLoading(false)
      } catch (err) {
        console.error('Error fetching reviews:', err)
        setError('فشل في تحميل المراجعات')
        setLoading(false)
      }
    }
    fetchReviews()
  }, [schoolId])

  if (loading) return <Skeleton className="h-[400px] w-full" />
  if (error) return <div className="text-red-500 text-center p-4">{error}</div>

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="ml-2 h-5 w-5" />
          مراجعات المعلمين والموجهين
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto" dir="rtl">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>النوع</TableHead>
                <TableHead>العنوان</TableHead>
                <TableHead>المراجع</TableHead>
                <TableHead>التعليق</TableHead>
                <TableHead>التقييم</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>التاريخ</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reviews.length > 0 ? (
                reviews.map((review) => (
                  <TableRow key={review.id}>
                    <TableCell>
                      <Badge variant={review.type === 'article' ? 'default' : 'secondary'}>
                        {review.type === 'article' ? 'مقال' : 'خبر'}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium max-w-xs truncate" title={review.title}>
                      {review.title}
                    </TableCell>
                    <TableCell>{review.reviewerName}</TableCell>
                    <TableCell className="max-w-xs truncate" title={review.comment}>
                      {review.comment || 'لا يوجد تعليق'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-500 ml-1" />
                        <span>{review.rating}/10</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {review.approved === true ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          <ThumbsUp className="h-3 w-3 ml-1" />
                          موافق عليه
                        </Badge>
                      ) : review.approved === false ? (
                        <Badge variant="destructive" className="bg-red-100 text-red-800">
                          <ThumbsDown className="h-3 w-3 ml-1" />
                          مرفوض
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-gray-100 text-gray-800">
                          قيد المراجعة
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(review.date).toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                      })}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    لا توجد مراجعات
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
