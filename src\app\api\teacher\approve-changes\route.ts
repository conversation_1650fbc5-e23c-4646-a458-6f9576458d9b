import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    console.log('Teacher approve changes API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    console.log('Token found:', token ? 'Yes' : 'No')

    if (!token) {
      console.log('No token found, returning 401')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      console.log('Request body:', body)

      const { studentId, changeType, action } = body

      if (!studentId || !changeType || !action) {
        console.log('Missing required fields')
        return NextResponse.json(
          { error: 'Student ID, change type, and action are required' },
          { status: 400 },
        )
      }

      if (changeType !== 'profile-image' && changeType !== 'name') {
        console.log('Invalid change type')
        return NextResponse.json(
          { error: 'Change type must be either "profile-image" or "name"' },
          { status: 400 },
        )
      }

      if (action !== 'approve' && action !== 'reject') {
        console.log('Invalid action')
        return NextResponse.json(
          { error: 'Action must be either "approve" or "reject"' },
          { status: 400 },
        )
      }

      // Initialize Payload
      const payload = await getPayload({ config })

      // Check if the current user is authorized to approve changes
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      const userRole = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role

      // Only teachers, mentors, school admins, and super admins can approve changes
      if (!['teacher', 'mentor', 'school-admin', 'super-admin'].includes(userRole)) {
        console.log('User not authorized to approve changes')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
      }

      // Get the student
      const student = await payload.findByID({
        collection: 'users',
        id: studentId,
      })

      if (!student) {
        console.log('Student not found')
        return NextResponse.json({ error: 'Student not found' }, { status: 404 })
      }

      // Check if the student is from the same school as the teacher
      const studentSchool = typeof student.school === 'object' ? student.school?.id : student.school
      const teacherSchool = typeof currentUser.school === 'object' ? currentUser.school?.id : currentUser.school

      if (userRole !== 'super-admin' && studentSchool !== teacherSchool) {
        console.log('Teacher can only approve students from their own school')
        return NextResponse.json(
          { error: 'You can only approve students from your own school' },
          { status: 403 },
        )
      }

      // Update the student based on the change type and action
      let updateData = {}
      let activityType = ''
      let notificationMessage = ''

      if (changeType === 'profile-image') {
        activityType = 'profile-image-approval'
        
        if (action === 'approve') {
          updateData = {
            profileImage: student.pendingProfileImage,
            pendingProfileImage: null,
            profileImageStatus: 'approved',
          }
          notificationMessage = 'Your profile image has been approved'
        } else {
          updateData = {
            pendingProfileImage: null,
            profileImageStatus: 'rejected',
          }
          notificationMessage = 'Your profile image has been rejected'
        }
      } else if (changeType === 'name') {
        activityType = 'name-change-approval'
        
        if (action === 'approve') {
          updateData = {
            firstName: student.pendingFirstName,
            lastName: student.pendingLastName,
            pendingFirstName: null,
            pendingLastName: null,
            nameChangeStatus: 'approved',
          }
          notificationMessage = 'Your name change has been approved'
        } else {
          updateData = {
            pendingFirstName: null,
            pendingLastName: null,
            nameChangeStatus: 'rejected',
          }
          notificationMessage = 'Your name change has been rejected'
        }
      }

      // Update the student
      const updatedStudent = await payload.update({
        collection: 'users',
        id: studentId,
        data: updateData,
      })

      // Create an activity record
      await payload.create({
        collection: 'activities',
        data: {
          userId: userId,
          targetUserId: studentId,
          activityType: activityType,
          details: {
            action: action,
            studentName: `${student.firstName} ${student.lastName}`,
            studentEmail: student.email,
            changeType: changeType,
          },
          school: teacherSchool,
          points: 5, // 5 points for approving a change
        },
      })

      // Award points to the teacher for approving a change
      if (action === 'approve') {
        const currentPoints = currentUser.points || 0
        await payload.update({
          collection: 'users',
          id: userId,
          data: {
            points: currentPoints + 5, // 5 points for approving a change
          },
        })
      }

      // Notify the student about the approval/rejection
      await payload.create({
        collection: 'notifications',
        data: {
          user: studentId,
          message: notificationMessage,
          type: changeType + '-' + action,
          read: false,
        },
      })

      console.log('Student change updated successfully')
      return NextResponse.json({
        success: true,
        message: `Student ${changeType} ${action === 'approve' ? 'approved' : 'rejected'} successfully`,
        points: action === 'approve' ? 5 : 0,
      })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error approving/rejecting student change:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
