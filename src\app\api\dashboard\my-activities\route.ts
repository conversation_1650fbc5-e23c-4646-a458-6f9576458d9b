import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the payload instance
    const payload = await getPayload({ config })

    // Get the current user
    const { user } = await payload.verifyToken(token)

    if (!user || !user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Try to get activities from MongoDB first
    try {
      const { db } = await connectToDatabase()
      const activities = await db
        .collection('activities')
        .find({ 'user.id': user.id })
        .sort({ date: -1 })
        .toArray()

      if (activities && activities.length > 0) {
        console.log('Fetched my activities from MongoDB:', activities.length)
        return NextResponse.json({ activities })
      }
    } catch (mongoError) {
      console.warn(
        'Error fetching my activities from MongoDB, falling back to Payload:',
        mongoError,
      )
    }

    // Fallback to Payload CMS if MongoDB fails
    // Get activities for the current user
    const activitiesResponse = await payload.find({
      collection: 'activities',
      where: {
        user: {
          equals: user.id,
        },
      },
      depth: 1,
    })

    return NextResponse.json({ activities: activitiesResponse.docs })
  } catch (error) {
    console.error('Error fetching my activities:', error)
    return NextResponse.json({ error: 'Failed to fetch activities' }, { status: 500 })
  }
}
