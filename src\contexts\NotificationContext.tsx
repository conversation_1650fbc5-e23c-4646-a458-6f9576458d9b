'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export interface Notification {
  id: string
  _id?: string // MongoDB ObjectId
  title?: string
  message: string
  type?: 'info' | 'success' | 'warning' | 'error' | 'points' | 'article-deleted' | 'report-actioned'
  read: boolean
  date?: string
  createdAt?: string // MongoDB timestamp
  updatedAt?: string
  link?: string
  pointsChange?: number
  user?: string | { $oid: string }
  userId?: string
  recipient?: string
  schoolId?: string
  userRole?: string
  data?: {
    articleId?: string
    articleTitle?: string
    reason?: string
    reportId?: string
    [key: string]: any // Allow any additional custom data
  }
}

interface NotificationContextType {
  notifications: Notification[]
  unreadCount: number
  addNotification: (notification: Omit<Notification, 'id' | 'date' | 'read'>) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  clearNotifications: () => void
  translateMessage: (message: string) => string
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export function useNotifications() {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

interface NotificationProviderProps {
  children: ReactNode
  userRole?: string
  userId?: string
  schoolId?: string
}

// Utility function for translating notification messages
const translateMessage = (message: string): string => {
  // Common English notification patterns and their Arabic translations
  const translations: Record<string, string> = {
    'Your article': 'مقالك',
    'has been approved': 'تمت الموافقة عليه',
    'has been rejected': 'تم رفضه',
    'has been deleted': 'تم حذفه',
    'Registration Approved': 'تمت الموافقة على التسجيل',
    'Article Feedback': 'ملاحظات على المقال',
    'Points Earned': 'نقاط مكتسبة',
    'New Rank Achieved': 'تم تحقيق رتبة جديدة',
    'Your registration has been approved': 'تمت الموافقة على تسجيلك',
    'has received feedback': 'تلقى ملاحظات',
    'received': 'حصل على',
    'stars': 'نجوم',
    'You earned': 'لقد ربحت',
    'points': 'نقطة',
    'Congratulations': 'تهانينا',
    'You\'ve reached the': 'لقد وصلت إلى',
    'rank with your points': 'رتبة بنقاطك',
    'Reporter': 'المراسل',
    'Pending Approvals': 'موافقات معلقة',
    'Articles Pending Review': 'مقالات بانتظار المراجعة',
    'Daily Tasks Reminder': 'تذكير بالمهام اليومية',
    'Mentor Feedback': 'ملاحظات الموجه',
    'You have': 'لديك',
    'student registrations pending approval': 'تسجيلات طلاب بانتظار الموافقة',
    'There are': 'هناك',
    'student articles waiting for your review': 'مقالات طلاب بانتظار مراجعتك',
    'You need to approve': 'عليك الموافقة على',
    'more posts and': 'مشاركات إضافية و',
    'more user registration today': 'تسجيل مستخدم إضافي اليوم',
    'for approving a student article': 'لموافقتك على مقالة طالب',
    'You received positive feedback from your mentor': 'تلقيت ملاحظات إيجابية من موجهك',
    'Teacher Activities': 'أنشطة المعلم',
    'News Update Required': 'تحديث الأخبار مطلوب',
    'Teacher Review Needed': 'مراجعة المعلم مطلوبة',
    'new teacher activities in your school': 'أنشطة معلم جديدة في مدرستك',
    'The school news page hasn\'t been updated in': 'لم يتم تحديث صفحة أخبار المدرسة منذ',
    'weeks': 'أسابيع',
    'for creating a new post': 'لإنشاء منشور جديد',
    'for reviewing a teacher': 'لمراجعة معلم',
    'has completed': 'أكمل',
    'approvals and needs your review': 'موافقات ويحتاج إلى مراجعتك',
    'Monthly Report Available': 'التقرير الشهري متاح',
    'Teacher Reports': 'تقارير المعلم',
    'Global Report Generated': 'تم إنشاء التقرير العالمي',
    'New School Registration': 'تسجيل مدرسة جديدة',
    'The monthly activity report for your school': 'تقرير النشاط الشهري لمدرستك',
    'is now available': 'متاح الآن',
    'student reports from teachers that need your attention': 'تقارير طلاب من المعلمين تحتاج إلى اهتمامك',
    'The monthly global activity report has been generated': 'تم إنشاء تقرير النشاط العالمي الشهري',
    'A new school has registered and is pending approval': 'تم تسجيل مدرسة جديدة وهي بانتظار الموافقة',
    'Climate Change Effects': 'آثار تغير المناخ',
    'Teacher Sarah Williams': 'المعلمة سارة ويليامز',
    'Teacher Robert Johnson': 'المعلم روبرت جونسون',
    'Your article has been submitted for review.': 'تم تقديم مقالك للمراجعة.',
    'Your article has been saved as a draft.': 'تم حفظ مقالك كمسودة.',
  };
  
  // Check for direct matches first
  if (translations[message]) {
    return translations[message];
  }
  
  // Check for partial matches and replace them
  let translatedMessage = message;
  for (const [english, arabic] of Object.entries(translations)) {
    // Only replace if it's a standalone phrase or part of a phrase with quotes
    const regex = new RegExp(`(^|\\s|")(${english})($|\\s|")`, 'gi');
    translatedMessage = translatedMessage.replace(regex, `$1${arabic}$3`);
  }
  
  // Handle specific patterns
  translatedMessage = translatedMessage.replace(
    /Your article "([^"]+)" has been (approved|rejected|deleted)/gi,
    (match, title, action) => {
      const arabicAction = translations[`has been ${action}`] || action;
      return `${translations['Your article'] || 'مقالك'} "${title}" ${arabicAction}`;
    }
  );
  
  return translatedMessage;
};

export function NotificationProvider({
  children,
  userRole = '',
  userId = '',
  schoolId = '',
}: NotificationProviderProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  // Load notifications from localStorage on mount
  useEffect(() => {
    const storedNotifications = localStorage.getItem('notifications')
    if (storedNotifications) {
      try {
        const parsedNotifications = JSON.parse(storedNotifications)
        setNotifications(parsedNotifications)
        setUnreadCount(parsedNotifications.filter((n: Notification) => !n.read).length)
      } catch (error) {
        console.error('Error parsing stored notifications:', error)
      }
    }
  }, [])

  // Save notifications to localStorage whenever they change
  useEffect(() => {
    if (notifications.length > 0) {
      localStorage.setItem('notifications', JSON.stringify(notifications))
    }
  }, [notifications])

  // Update unread count whenever notifications change
  useEffect(() => {
    setUnreadCount(notifications.filter((n) => !n.read).length)
  }, [notifications])

  // Fetch user-specific notifications from the API
  useEffect(() => {
    if (!userRole || !userId) return

    const fetchNotifications = async () => {
      try {
        // Build query parameters
        const params = new URLSearchParams()
        params.append('userId', userId)
        params.append('userRole', userRole)
        if (schoolId) {
          params.append('schoolId', schoolId)
        }

        // Fetch notifications from API
        const response = await fetch(`/api/notifications?${params.toString()}`)

        if (!response.ok) {
          throw new Error('Failed to fetch notifications')
        }

        const data = await response.json()

        if (data.success && data.notifications && data.notifications.length > 0) {
          // Format the notifications to match our expected structure
          const formattedNotifications = data.notifications.map((notification: any) => ({
            id: notification.id || notification._id,
            title: notification.title || '',
            message: notification.message,
            type: notification.type || 'info',
            read: notification.read || false,
            date: notification.date || notification.createdAt,
            link: notification.link,
            pointsChange: notification.pointsChange || 0,
            data: notification.data || {},
            recipient: notification.recipient || notification.userId || notification.user,
            userId: notification.userId || notification.user,
          }));

          // Add the notifications to the state
          setNotifications((prev) => {
            // Filter out duplicates by id
            const existingIds = new Set(prev.map((n) => n.id))
            const newNotifications = formattedNotifications.filter(
              (n: Notification) => !existingIds.has(n.id)
            )
            return [...prev, ...newNotifications]
          })
          return
        }

        // If no notifications exist yet, seed with role-specific mock data
        const mockNotifications: Notification[] = []

        if (userRole === 'student') {
          // Student notifications
          mockNotifications.push({
            id: '1',
            title: 'تمت الموافقة على التسجيل',
            message: 'تمت الموافقة على تسجيلك من قبل المعلمة سارة ويليامز.',
            type: 'success',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/my-activities',
          })

          mockNotifications.push({
            id: '2',
            title: 'ملاحظات على المقال',
            message: 'تلقى مقالك "آثار تغير المناخ" ملاحظات من معلمك.',
            type: 'info',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/my-articles',
          })

          mockNotifications.push({
            id: '3',
            title: 'نقاط مكتسبة',
            message: 'حصل مقالك "آثار تغير المناخ" على 4 نجوم! لقد ربحت 30 نقطة.',
            type: 'points',
            read: false,
            date: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            link: '/dashboard/my-activities',
            pointsChange: 30,
          })

          mockNotifications.push({
            id: '4',
            title: 'تم تحقيق رتبة جديدة',
            message: 'تهانينا! لقد وصلت إلى رتبة "المراسل" بنقاطك.',
            type: 'points',
            read: false,
            date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
            link: '/dashboard/my-activities',
            pointsChange: 0,
          })
        } else if (userRole === 'teacher') {
          // Teacher notifications
          mockNotifications.push({
            id: '1',
            title: 'موافقات معلقة',
            message: 'لديك 12 تسجيل طالب بانتظار الموافقة.',
            type: 'warning',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/students',
          })

          mockNotifications.push({
            id: '2',
            title: 'مقالات بانتظار المراجعة',
            message: 'هناك 15 مقال طالب بانتظار مراجعتك.',
            type: 'info',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/articles',
          })

          mockNotifications.push({
            id: '3',
            title: 'تذكير بالمهام اليومية',
            message: 'عليك الموافقة على 3 مشاركات إضافية و 1 تسجيل مستخدم إضافي اليوم.',
            type: 'warning',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/activities',
          })

          mockNotifications.push({
            id: '4',
            title: 'نقاط مكتسبة',
            message: 'لقد ربحت 5 نقاط لموافقتك على مقالة طالب.',
            type: 'points',
            read: false,
            date: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
            link: '/dashboard/activities',
            pointsChange: 5,
          })

          mockNotifications.push({
            id: '5',
            title: 'ملاحظات الموجه',
            message: 'تلقيت ملاحظات إيجابية من موجهك. +8 نقاط!',
            type: 'points',
            read: false,
            date: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            link: '/dashboard/activities',
            pointsChange: 8,
          })
        } else if (userRole === 'mentor') {
          // Mentor notifications
          mockNotifications.push({
            id: '1',
            title: 'أنشطة المعلم',
            message: 'هناك 14 نشاط معلم جديد في مدرستك.',
            type: 'info',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/teachers',
          })

          mockNotifications.push({
            id: '2',
            title: 'تحديث الأخبار مطلوب',
            message: 'لم يتم تحديث صفحة أخبار المدرسة منذ أسبوعين.',
            type: 'warning',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/news',
          })

          mockNotifications.push({
            id: '3',
            title: 'نقاط مكتسبة',
            message: 'لقد ربحت 20 نقطة لإنشاء منشور جديد.',
            type: 'points',
            read: false,
            date: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
            link: '/dashboard/news',
            pointsChange: 20,
          })

          mockNotifications.push({
            id: '4',
            title: 'نقاط مكتسبة',
            message: 'لقد ربحت 15 نقطة لمراجعة معلم.',
            type: 'points',
            read: false,
            date: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            link: '/dashboard/teachers',
            pointsChange: 15,
          })

          mockNotifications.push({
            id: '5',
            title: 'مراجعة المعلم مطلوبة',
            message: 'المعلم روبرت جونسون أكمل 10 موافقات ويحتاج إلى مراجعتك.',
            type: 'warning',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/teachers',
          })
        } else if (userRole === 'school-admin') {
          // School admin notifications
          mockNotifications.push({
            id: '1',
            title: 'التقرير الشهري متاح',
            message: 'تقرير النشاط الشهري لمدرستك متاح الآن.',
            type: 'info',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/activities',
          })

          mockNotifications.push({
            id: '2',
            title: 'تقارير المعلم',
            message: 'هناك 3 تقارير طلاب من المعلمين تحتاج إلى اهتمامك.',
            type: 'warning',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/activities',
          })
        } else if (userRole === 'super-admin') {
          // Super admin notifications
          mockNotifications.push({
            id: '1',
            title: 'تم إنشاء التقرير العالمي',
            message: 'تم إنشاء تقرير النشاط العالمي الشهري.',
            type: 'success',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/activities',
          })

          mockNotifications.push({
            id: '2',
            title: 'تسجيل مدرسة جديدة',
            message: 'تم تسجيل مدرسة جديدة وهي بانتظار الموافقة.',
            type: 'info',
            read: false,
            date: new Date().toISOString(),
            link: '/dashboard/schools',
          })
        }

        // Add the mock notifications to the state
        if (mockNotifications.length > 0) {
          setNotifications((prev) => {
            // Filter out duplicates by id
            const existingIds = new Set(prev.map((n) => n.id))
            const newNotifications = mockNotifications.filter((n) => !existingIds.has(n.id))
            return [...prev, ...newNotifications]
          })
        }
      } catch (error) {
        console.error('Error fetching notifications:', error)
      }
    }

    fetchNotifications()
  }, [userRole, userId, schoolId])

  const addNotification = async (notification: Omit<Notification, 'id' | 'date' | 'read'>) => {
    try {
      // Create notification object
      const newNotification: Notification = {
        ...notification,
        id: Date.now().toString(),
        date: new Date().toISOString(),
        read: false,
      }

      // Update state first for immediate UI feedback
      setNotifications((prev) => [newNotification, ...prev])

      // Then save to database
      await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newNotification,
          userId: userId || '',
          userRole: userRole || '',
          schoolId: schoolId || undefined,
        }),
      })
    } catch (error) {
      console.error('Error adding notification:', error)
    }
  }

  const markAsRead = async (id: string) => {
    try {
      // Update in the state first for immediate UI feedback
      setNotifications((prev) =>
        prev.map((notification) =>
          notification.id === id ? { ...notification, read: true } : notification,
        ),
      )

      // Then update in the database
      await fetch('/api/notifications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, read: true }),
      })
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAllAsRead = async () => {
    try {
      // Update in the state first for immediate UI feedback
      setNotifications((prev) => prev.map((notification) => ({ ...notification, read: true })))

      // Then update each notification in the database
      const updatePromises = notifications
        .filter((notification) => !notification.read)
        .map((notification) =>
          fetch('/api/notifications', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: notification.id, read: true }),
          }),
        )

      await Promise.all(updatePromises)
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
    }
  }

  const clearNotifications = () => {
    setNotifications([])
    localStorage.removeItem('notifications')
  }

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        clearNotifications,
        translateMessage,
      }}
    >
      {children}
    </NotificationContext.Provider>
  )
}
