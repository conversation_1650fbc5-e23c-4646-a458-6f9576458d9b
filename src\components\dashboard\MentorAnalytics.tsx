'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>Chart,
  PieChart,
  Activity,
  TrendingUp,
  Users,
  School,
  AlertTriangle
} from 'lucide-react'

interface TeacherReviewData {
  teacherId: string;
  teacherName: string;
  totalReviews: number;
  approvalRate: number;
  averageRating: number;
  reviewsThisMonth: number;
}

interface ArticleQualityData {
  month: string;
  averageRating: number;
  approvalRate: number;
  totalArticles: number;
}

interface SchoolEngagementData {
  schoolId: string;
  schoolName: string;
  activeTeachers: number;
  activeStudents: number;
  articlesSubmitted: number;
  articlesPublished: number;
  loginFrequency: number;
}

interface MentorAnalyticsProps {
  schoolId: string;
}

export default function MentorAnalytics({ schoolId }: MentorAnalyticsProps) {
  const [activeTab, setActiveTab] = useState('teacher-reviews')
  const [teacherReviews, setTeacherReviews] = useState<TeacherReviewData[]>([])
  const [articleQuality, setArticleQuality] = useState<ArticleQualityData[]>([])
  const [schoolEngagement, setSchoolEngagement] = useState<SchoolEngagementData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [chartLoaded, setChartLoaded] = useState(false)
  const [Chart, setChart] = useState<any>(null)

  // Load Chart.js dynamically on the client side
  useEffect(() => {
    const loadChart = async () => {
      try {
        // Dynamic import of Chart.js
        const chartModule = await import('chart.js/auto')
        const reactChartModule = await import('react-chartjs-2')

        // Register required components
        chartModule.Chart.register(
          chartModule.CategoryScale,
          chartModule.LinearScale,
          chartModule.BarElement,
          chartModule.Title,
          chartModule.Tooltip,
          chartModule.Legend
        )

        setChart({
          Bar: reactChartModule.Bar,
          Line: reactChartModule.Line,
          Pie: reactChartModule.Pie,
        })
        setChartLoaded(true)
      } catch (error) {
        console.error('Failed to load Chart.js:', error)
      }
    }

    loadChart()
  }, [])

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true)
        setError('')

        // Fetch teacher review analytics
        try {
          const teacherReviewsResponse = await fetch(`/api/dashboard/mentor/teacher-reviews?schoolId=${schoolId}`, {
            credentials: 'include',
          })

          if (!teacherReviewsResponse.ok) {
            console.warn('Failed to fetch teacher review analytics:', await teacherReviewsResponse.text())
            // Continue with empty data
          } else {
            const teacherReviewsData = await teacherReviewsResponse.json()
            setTeacherReviews(teacherReviewsData.teachers || [])
          }
        } catch (err) {
          console.error('Error fetching teacher review analytics:', err)
          // Continue with other requests
        }

        // Fetch article quality trends
        try {
          const articleQualityResponse = await fetch(`/api/dashboard/mentor/article-quality?schoolId=${schoolId}`, {
            credentials: 'include',
          })

          if (!articleQualityResponse.ok) {
            console.warn('Failed to fetch article quality trends:', await articleQualityResponse.text())
            // Continue with empty data
          } else {
            const articleQualityData = await articleQualityResponse.json()
            setArticleQuality(articleQualityData.trends || [])
          }
        } catch (err) {
          console.error('Error fetching article quality trends:', err)
          // Continue with other requests
        }

        // Fetch school engagement metrics
        try {
          const schoolEngagementResponse = await fetch(`/api/dashboard/mentor/school-engagement?schoolId=${schoolId}`, {
            credentials: 'include',
          })

          if (!schoolEngagementResponse.ok) {
            console.warn('Failed to fetch school engagement metrics:', await schoolEngagementResponse.text())
            // Continue with empty data
          } else {
            const schoolEngagementData = await schoolEngagementResponse.json()
            setSchoolEngagement(schoolEngagementData.schools || [])
          }
        } catch (err) {
          console.error('Error fetching school engagement metrics:', err)
          // Continue with other requests
        }

        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching mentor analytics data:', err)
        setError('فشل تحميل بيانات التحليلات. يرجى المحاولة مرة أخرى.')
        setIsLoading(false)
      }
    }

    if (schoolId) {
      fetchData()
    } else {
      setIsLoading(false)
      setError('لم يتم توفير معرّف المدرسة. يرجى التأكد من ارتباطك بمدرسة.')
    }
  }, [schoolId])

  // Prepare data for teacher review chart
  const teacherReviewChartData = {
    labels: teacherReviews.length > 0
      ? teacherReviews.map(teacher => teacher.teacherName)
      : ['لا توجد بيانات'],
    datasets: [
      {
        label: 'معدل الموافقة (%)',
        data: teacherReviews.length > 0
          ? teacherReviews.map(teacher => teacher.approvalRate * 100)
          : [0],
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
      {
        label: 'التقييم المتوسط',
        data: teacherReviews.length > 0
          ? teacherReviews.map(teacher => teacher.averageRating * 10) // Scale to 0-100 for visibility
          : [0],
        backgroundColor: 'rgba(16, 185, 129, 0.6)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 1,
      },
    ],
  }

  // Prepare data for article quality trends chart
  const articleQualityChartData = {
    labels: articleQuality.length > 0
      ? articleQuality.map(item => item.month)
      : ['لا توجد بيانات'],
    datasets: [
      {
        label: 'التقييم المتوسط',
        data: articleQuality.length > 0
          ? articleQuality.map(item => item.averageRating)
          : [0],
        borderColor: 'rgba(59, 130, 246, 1)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'معدل الموافقة (%)',
        data: articleQuality.length > 0
          ? articleQuality.map(item => item.approvalRate * 100)
          : [0],
        borderColor: 'rgba(16, 185, 129, 1)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4,
        fill: true,
      },
    ],
  }

  // Prepare data for school engagement chart
  const schoolEngagementChartData = {
    labels: schoolEngagement.length > 0
      ? schoolEngagement.map(school => school.schoolName)
      : ['لا توجد بيانات'],
    datasets: [
      {
        label: 'المعلمون النشطون',
        data: schoolEngagement.length > 0
          ? schoolEngagement.map(school => school.activeTeachers)
          : [0],
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
      {
        label: 'المقالات المنشورة',
        data: schoolEngagement.length > 0
          ? schoolEngagement.map(school => school.articlesPublished)
          : [0],
        backgroundColor: 'rgba(16, 185, 129, 0.6)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 1,
      },
      {
        label: 'معدل تسجيل الدخول',
        data: schoolEngagement.length > 0
          ? schoolEngagement.map(school => school.loginFrequency)
          : [0],
        backgroundColor: 'rgba(245, 158, 11, 0.6)',
        borderColor: 'rgba(245, 158, 11, 1)',
        borderWidth: 1,
      },
    ],
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" dir="rtl">
        {error}
      </div>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-3 w-full">
          <TabsTrigger value="teacher-reviews" className="flex items-center">
            <BarChart className="h-4 w-4 ml-2" />
            مراجعات المعلمين
          </TabsTrigger>
          <TabsTrigger value="article-quality" className="flex items-center">
            <TrendingUp className="h-4 w-4 ml-2" />
            جودة المقالات
          </TabsTrigger>
          <TabsTrigger value="school-engagement" className="flex items-center">
            <School className="h-4 w-4 ml-2" />
            مشاركة المدرسة
          </TabsTrigger>
        </TabsList>

        {/* Teacher Review Analytics */}
        <TabsContent value="teacher-reviews" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">إجمالي المراجعات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {teacherReviews.reduce((sum, teacher) => sum + teacher.totalReviews, 0)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">متوسط معدل الموافقة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round(
                    teacherReviews.reduce((sum, teacher) => sum + teacher.approvalRate, 0) /
                    (teacherReviews.length || 1) * 100
                  )}%
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">متوسط التقييم</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(
                    teacherReviews.reduce((sum, teacher) => sum + teacher.averageRating, 0) /
                    (teacherReviews.length || 1)
                  ).toFixed(1)}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>مقارنة أداء المعلمين</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                {chartLoaded && Chart ? (
                  <Chart.Bar
                    data={teacherReviewChartData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true,
                          max: 100,
                        },
                      },
                      plugins: {
                        legend: {
                          position: 'top',
                        },
                        tooltip: {
                          callbacks: {
                            label: function(context: { dataset: { label: string }; raw: any }) {
                              const label = context.dataset.label || '';
                              const value = context.raw;
                              return label === 'التقييم المتوسط'
                                ? `${label}: ${(value / 10).toFixed(1)}/10`
                                : `${label}: ${value.toFixed(1)}%`;
                            }
                          }
                        }
                      },
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-gray-500">جاري تحميل الرسم البياني...</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Article Quality Trends */}
        <TabsContent value="article-quality" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>جودة المقالات بمرور الوقت</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                {chartLoaded && Chart ? (
                  <Chart.Line
                    data={articleQualityChartData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true,
                          max: 100,
                        },
                      },
                      plugins: {
                        legend: {
                          position: 'top',
                        },
                        tooltip: {
                          callbacks: {
                            label: function(context: { dataset: { label: string }; raw: any }) {
                              const label = context.dataset.label || '';
                              const value = context.raw;
                              return label === 'التقييم المتوسط'
                                ? `${label}: ${value.toFixed(1)}/10`
                                : `${label}: ${value.toFixed(1)}%`;
                            }
                          }
                        }
                      },
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-gray-500">جاري تحميل الرسم البياني...</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* School Engagement Metrics */}
        <TabsContent value="school-engagement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>مقاييس مشاركة المدرسة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                {chartLoaded && Chart ? (
                  <Chart.Bar
                    data={schoolEngagementChartData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true,
                        },
                      },
                      plugins: {
                        legend: {
                          position: 'top',
                        },
                      },
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-gray-500">جاري تحميل الرسم البياني...</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
