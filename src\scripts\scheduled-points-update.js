/**
 * Scheduled Points Update Script
 * 
 * This script can be run periodically (daily or hourly) using a cron job
 * to update the points and rankings for all users.
 * 
 * To schedule with cron:
 * 1. Make this file executable: chmod +x src/scripts/scheduled-points-update.js
 * 2. Add to crontab: 0 0 * * * /path/to/node /path/to/src/scripts/scheduled-points-update.js
 *    (This runs daily at midnight)
 */

const { spawn } = require('child_process')
const path = require('path')

// Path to the TypeScript node runner
const tsNodePath = path.resolve(process.cwd(), 'node_modules/.bin/ts-node')

// Path to the update script
const updateScriptPath = path.resolve(process.cwd(), 'src/scripts/update-points-rankings.ts')

console.log(`Starting scheduled points update at ${new Date().toISOString()}`)

// Spawn the process to run the TS file
const child = spawn(tsNodePath, [updateScriptPath], {
  stdio: 'inherit',
  shell: true,
})

child.on('close', (code) => {
  if (code === 0) {
    console.log(`Points update completed successfully at ${new Date().toISOString()}`)
  } else {
    console.error(`Points update failed with code ${code} at ${new Date().toISOString()}`)
  }
})

child.on('error', (err) => {
  console.error(`Failed to start points update process: ${err.message}`)
}) 