'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { CheckCircle, AlertCircle, Star, UserCheck, Loader2 } from 'lucide-react'

interface Goal {
  type: string
  required: number
  completed: number
  points: number
}

interface WeeklyGoal {
  type: string
  required: number
  completed: number
  days?: string[]
}

interface TasksData {
  dailyGoals: Goal[]
  weeklyGoals: WeeklyGoal[]
  dailyProgress: number
  weeklyProgress: number
}

export default function TeacherTaskBar() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [tasksData, setTasksData] = useState<TasksData>({
    dailyGoals: [],
    weeklyGoals: [],
    dailyProgress: 0,
    weeklyProgress: 0,
  })

  useEffect(() => {
    const fetchTasks = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/dashboard/tasks')

        if (!response.ok) {
          throw new Error('Failed to fetch tasks data')
        }

        const data = await response.json()
        setTasksData(data)
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching tasks:', err)
        setError('Failed to load tasks data')
        setIsLoading(false)
      }
    }

    fetchTasks()
  }, [])

  if (isLoading) {
    return (
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Daily Tasks</CardTitle>
          <CardDescription>
            Complete these tasks to earn points and maintain your rank
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center h-40">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Daily Tasks</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4 rounded-md text-red-800 dark:text-red-300">
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="mb-6">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Daily Tasks</CardTitle>
        <CardDescription>
          Complete these tasks to earn points and maintain your rank
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-1">
              <div className="text-sm font-medium">Daily Progress</div>
              <div className="text-sm text-gray-500">{tasksData.dailyProgress}% Complete</div>
            </div>
            <Progress value={tasksData.dailyProgress} className="h-2" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {tasksData.dailyGoals.map((goal, index) => {
              const isCompleted = goal.completed >= goal.required
              
              return (
                <div
                  key={index}
                  className={`flex items-center p-3 rounded-lg border ${
                    isCompleted ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' : 'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700'
                  }`}
                >
                  <div className="mr-3">
                    {goal.type === 'article_review' ? (
                      <Star className={`h-8 w-8 ${isCompleted ? 'text-green-500' : 'text-gray-400'}`} />
                    ) : (
                      <UserCheck className={`h-8 w-8 ${isCompleted ? 'text-green-500' : 'text-gray-400'}`} />
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">
                      {goal.type === 'article_review' ? 'Review Articles' : 'Approve Students'}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {goal.completed}/{goal.required} completed ({goal.points} points each)
                    </p>
                  </div>
                  <div>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-amber-500" />
                    )}
                  </div>
                </div>
              )
            })}
          </div>

          <div>
            <div className="flex justify-between items-center mb-1">
              <div className="text-sm font-medium">Weekly Progress</div>
              <div className="text-sm text-gray-500">{tasksData.weeklyProgress}% Complete</div>
            </div>
            <Progress value={tasksData.weeklyProgress} className="h-2" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
