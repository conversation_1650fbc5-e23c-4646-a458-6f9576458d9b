/* Statistics Carousel Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family:<PERSON><PERSON><PERSON> !important;
}

.rankings-section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  padding: 80px 0;
}

.rankings-title {
  font-size: 7.5rem;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: -0.02em;
  position: absolute;
  top: 45px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
  white-space: nowrap;
  font-family: "Arial Black", "Arial Bold", Arial, sans-serif;
  background: linear-gradient(
    to bottom,
    rgb(var(--primary) / 35%) 30%,
    rgb(255 255 255 / 0%) 76%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.carousel-container {
  width: 100%;
  max-width: 1200px;
  height: 450px;
  position: relative;
  perspective: 1000px;
  margin-top: 80px;
}

.carousel-track {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  transform-style: preserve-3d;
  transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: transform;
}

.card {
  position: absolute;
  width: 260px;
  height: 360px;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-origin: center center;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: transform;
  background-position: center;
  background-size: cover;
}

.card-rank {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: hsl(var(--primary));
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 10;
  border: 2px solid white;
  transition: all 0.3s ease;
}

.card-points {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 12px;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

/* Gold medal - 1st place */
.card[data-rank="1"] {
  box-shadow: 0 0 25px 5px rgba(255, 215, 0, 0.6);
  animation: goldGlow 3s infinite alternate;
}

.card[data-rank="1"] .card-rank {
  background: linear-gradient(135deg, #f2c94c, #f2994a);
  color: #fff;
  border: 2px solid #fff;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
  font-weight: 900;
}

/* Silver medal - 2nd place */
.card[data-rank="2"] {
  box-shadow: 0 0 25px 5px rgba(192, 192, 192, 0.6);
  animation: silverGlow 3s infinite alternate;
}

.card[data-rank="2"] .card-rank {
  background: linear-gradient(135deg, #e0e0e0, #b0b0b0);
  color: #fff;
  border: 2px solid #fff;
  box-shadow: 0 0 15px rgba(192, 192, 192, 0.8);
  font-weight: 900;
}

/* Bronze medal - 3rd place */
.card[data-rank="3"] {
  box-shadow: 0 0 25px 5px rgba(205, 127, 50, 0.6);
  animation: bronzeGlow 3s infinite alternate;
}

.card[data-rank="3"] .card-rank {
  background: linear-gradient(135deg, #cd7f32, #a05a2c);
  color: #fff;
  border: 2px solid #fff;
  box-shadow: 0 0 15px rgba(205, 127, 50, 0.8);
  font-weight: 900;
}

@keyframes goldGlow {
  0% {
    box-shadow: 0 0 15px 2px rgba(255, 215, 0, 0.5);
  }
  100% {
    box-shadow: 0 0 30px 8px rgba(255, 215, 0, 0.8);
  }
}

@keyframes silverGlow {
  0% {
    box-shadow: 0 0 15px 2px rgba(192, 192, 192, 0.5);
  }
  100% {
    box-shadow: 0 0 30px 8px rgba(192, 192, 192, 0.8);
  }
}

@keyframes bronzeGlow {
  0% {
    box-shadow: 0 0 15px 2px rgba(205, 127, 50, 0.5);
  }
  100% {
    box-shadow: 0 0 30px 8px rgba(205, 127, 50, 0.8);
  }
}

.card.center .card-rank {
  transform: scale(1.2);
}

.card.center[data-rank="1"] {
  animation: goldGlow 2s infinite alternate;
}

.card.center[data-rank="1"] .card-rank {
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.9);
  animation: goldRankPulse 2s infinite alternate;
}

.card.center[data-rank="2"] {
  animation: silverGlow 2s infinite alternate;
}

.card.center[data-rank="2"] .card-rank {
  box-shadow: 0 0 20px rgba(192, 192, 192, 0.9);
  animation: silverRankPulse 2s infinite alternate;
}

.card.center[data-rank="3"] {
  animation: bronzeGlow 2s infinite alternate;
}

.card.center[data-rank="3"] .card-rank {
  box-shadow: 0 0 20px rgba(205, 127, 50, 0.9);
  animation: bronzeRankPulse 2s infinite alternate;
}

@keyframes goldRankPulse {
  0% {
    box-shadow: 0 0 10px 2px rgba(255, 215, 0, 0.7);
  }
  100% {
    box-shadow: 0 0 25px 5px rgba(255, 215, 0, 1);
  }
}

@keyframes silverRankPulse {
  0% {
    box-shadow: 0 0 10px 2px rgba(192, 192, 192, 0.7);
  }
  100% {
    box-shadow: 0 0 25px 5px rgba(192, 192, 192, 1);
  }
}

@keyframes bronzeRankPulse {
  0% {
    box-shadow: 0 0 10px 2px rgba(205, 127, 50, 0.7);
  }
  100% {
    box-shadow: 0 0 25px 5px rgba(205, 127, 50, 1);
  }
}

.medal-icon {
  font-size: 1.4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: medalPulse 2s infinite alternate;
}

@keyframes medalPulse {
  0% {
    transform: scale(1);
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }
  100% {
    transform: scale(1.15);
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
  }
}

/* Card title and subtitle styles moved to member-info section */

.card.center {
  z-index: 10;
  transform: scale(1.15) translateZ(0);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.card.center .card-points {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.8);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.card[data-rank="1"] .card-points {
  background: linear-gradient(135deg, rgba(242, 201, 76, 0.8), rgba(242, 153, 74, 0.8));
}

.card[data-rank="2"] .card-points {
  background: linear-gradient(135deg, rgba(224, 224, 224, 0.8), rgba(176, 176, 176, 0.8));
}

.card[data-rank="3"] .card-points {
  background: linear-gradient(135deg, rgba(205, 127, 50, 0.8), rgba(160, 90, 44, 0.8));
}

.card.left-2 {
  z-index: 1;
  transform: translateX(-60px) scale(0.8) translateZ(-80px);
  opacity: 0.7;
}

.card.left-1 {
  z-index: 5;
  transform: translateX(90px) scale(0.9) translateZ(-30px);
  opacity: 0.9;
}

.card.right-1 {
  z-index: 5;
  transform: translateX(-90px) scale(0.9) translateZ(-30px);
  opacity: 0.9;
}

.card.right-2 {
  z-index: 1;
  transform: translateX(60px) scale(0.8) translateZ(-80px);
  opacity: 0.7;
}

.card.hidden {
  opacity: 0;
  pointer-events: none;
}

.member-info {
  text-align: center;
  margin-top: 40px;
  transition: all 0.5s ease-out;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  border-radius: 15px;
  animation: fadeIn 0.5s ease-out;
  position: relative;
}

/* Gold rank styling */
.member-info.rank-1 {
  background: linear-gradient(to bottom, white, rgba(255, 215, 0, 0.1));
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.member-info.rank-1 .member-name {
  background: linear-gradient(to right, #f2c94c, #f2994a, #f2c94c);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: goldTextShine 3s infinite linear;
  background-size: 200% auto;
}

.member-info.rank-1 .member-name::before,
.member-info.rank-1 .member-name::after {
  background: linear-gradient(to right, #f2c94c, #f2994a);
}

/* Silver rank styling */
.member-info.rank-2 {
  background: linear-gradient(to bottom, white, rgba(192, 192, 192, 0.1));
  box-shadow: 0 10px 30px rgba(192, 192, 192, 0.2);
  border: 1px solid rgba(192, 192, 192, 0.3);
}

.member-info.rank-2 .member-name {
  background: linear-gradient(to right, #e0e0e0, #b0b0b0, #e0e0e0);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: silverTextShine 3s infinite linear;
  background-size: 200% auto;
}

.member-info.rank-2 .member-name::before,
.member-info.rank-2 .member-name::after {
  background: linear-gradient(to right, #e0e0e0, #b0b0b0);
}

/* Bronze rank styling */
.member-info.rank-3 {
  background: linear-gradient(to bottom, white, rgba(205, 127, 50, 0.1));
  box-shadow: 0 10px 30px rgba(205, 127, 50, 0.2);
  border: 1px solid rgba(205, 127, 50, 0.3);
}

.member-info.rank-3 .member-name {
  background: linear-gradient(to right, #cd7f32, #a05a2c, #cd7f32);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: bronzeTextShine 3s infinite linear;
  background-size: 200% auto;
}

.member-info.rank-3 .member-name::before,
.member-info.rank-3 .member-name::after {
  background: linear-gradient(to right, #cd7f32, #a05a2c);
}

/* Text shine animations */
@keyframes goldTextShine {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes silverTextShine {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes bronzeTextShine {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

.rank-badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  padding: 5px 15px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  color: white;
}

.rank-badge.gold {
  background: linear-gradient(135deg, #f2c94c, #f2994a);
  animation: goldBadgePulse 2s infinite alternate;
}

.rank-badge.silver {
  background: linear-gradient(135deg, #e0e0e0, #b0b0b0);
  animation: silverBadgePulse 2s infinite alternate;
}

.rank-badge.bronze {
  background: linear-gradient(135deg, #cd7f32, #a05a2c);
  animation: bronzeBadgePulse 2s infinite alternate;
}

@keyframes goldBadgePulse {
  0% {
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.4);
  }
  100% {
    box-shadow: 0 8px 16px rgba(255, 215, 0, 0.7);
  }
}

@keyframes silverBadgePulse {
  0% {
    box-shadow: 0 4px 8px rgba(192, 192, 192, 0.4);
  }
  100% {
    box-shadow: 0 8px 16px rgba(192, 192, 192, 0.7);
  }
}

@keyframes bronzeBadgePulse {
  0% {
    box-shadow: 0 4px 8px rgba(205, 127, 50, 0.4);
  }
  100% {
    box-shadow: 0 8px 16px rgba(205, 127, 50, 0.7);
  }
}

.member-name {
  color: hsl(var(--primary));
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  position: relative;
  display: inline-block;
}

.member-name::before,
.member-name::after {
  content: "";
  position: absolute;
  top: 100%;
  width: 60px;
  height: 2px;
  background: hsl(var(--primary));
}

.member-name::before {
  left: -80px;
}

.member-name::after {
  right: -80px;
}

.member-role {
  color: #555;
  font-size: 1.5rem;
  font-weight: 500;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 10px 0;
  position: relative;
}

/* Special styling for top 3 member roles */
.member-info.rank-1 .member-role {
  color: #b17e0d;
  font-weight: 600;
}

.member-info.rank-2 .member-role {
  color: #707070;
  font-weight: 600;
}

.member-info.rank-3 .member-role {
  color: #8b4513;
  font-weight: 600;
}

.dots {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 60px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: hsl(var(--primary) / 20%);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: hsl(var(--primary));
  transform: scale(1.2);
}

.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: hsl(var(--primary) / 60%);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  transition: all 0.3s ease;
  font-size: 1.5rem;
  border: none;
  outline: none;
  padding-bottom: 4px;
}

.nav-arrow:hover {
  background: hsl(var(--primary));
  transform: translateY(-50%) scale(1.1);
}

.nav-arrow.left {
  left: 20px;
  padding-right: 3px;
}

.nav-arrow.right {
  right: 20px;
  padding-left: 3px;
}

.tabs {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
}

.tab-button {
  padding: 10px 20px;
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.tab-button.active {
  background-color: hsl(var(--primary));
  color: white;
}

.placeholder-notice {
  position: relative;
  z-index: 10;
  margin-bottom: 20px;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 768px) {
  .rankings-title {
    font-size: 4.5rem;
  }

  .card {
    width: 180px;
    height: 260px;
  }

  .card.left-2 {
    transform: translateX(-80px) scale(0.8) translateZ(-60px);
  }

  .card.left-1 {
    transform: translateX(-40px) scale(0.9) translateZ(-20px);
  }

  .card.right-1 {
    transform: translateX(40px) scale(0.9) translateZ(-20px);
  }

  .card.right-2 {
    transform: translateX(80px) scale(0.8) translateZ(-60px);
  }

  .member-name {
    font-size: 2rem;
  }

  .member-role {
    font-size: 1.2rem;
  }

  .member-name::before,
  .member-name::after {
    width: 50px;
  }

  .member-name::before {
    left: -70px;
  }

  .member-name::after {
    right: -70px;
  }
}
