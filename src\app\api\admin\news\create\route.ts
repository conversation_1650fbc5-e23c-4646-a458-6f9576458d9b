import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    // Get the payload instance
    const payload = await getPayload({
      config,
    })

    // Get the current user from cookies
    const cookieStore = req.cookies
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication token not found' },
        { status: 401 }
      )
    }

    // Get the current user
    const { user } = await payload.auth({ token })

    if (!user) {
      return NextResponse.json(
        { error: 'You must be logged in to create a news post' },
        { status: 401 }
      )
    }

    // Check if user is a mentor or admin
    const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
    const isMentor = userRole === 'mentor'
    const isAdmin = userRole === 'super-admin' || userRole === 'school-admin'

    if (!isMentor && !isAdmin) {
      return NextResponse.json(
        { error: 'Only mentors and admins can create news posts' },
        { status: 403 }
      )
    }

    // Check if the request is JSON or form data
    let title, slug, content, status;

    if (req.headers.get('content-type')?.includes('application/json')) {
      // Handle JSON request
      const body = await req.json();
      title = body.title;
      slug = body.slug;
      content = body.content;
      status = body.status;
    } else {
      // Handle form data request
      const formData = await req.formData();
      title = formData.get('title') as string;
      slug = formData.get('slug') as string;
      content = formData.get('content') as string;
      status = formData.get('status') as string;
    }

    if (!title || !slug || !content || !status) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      )
    }

    // Validate status
    if (status !== 'draft' && status !== 'published') {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      )
    }

    // Create the news post
    const newsData = {
      title,
      slug,
      content,
      author: user.id,
      status,
    }

    // If publishing now, add publishedAt date
    if (status === 'published') {
      newsData.publishedAt = new Date().toISOString()
    }

    const news = await payload.create({
      collection: 'news',
      data: newsData,
    })

    // Redirect to the news page
    return NextResponse.redirect(new URL('/dashboard/news', req.url))
  } catch (error) {
    console.error('Error creating news post:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred. The slug may already be in use.' },
      { status: 500 }
    )
  }
}
