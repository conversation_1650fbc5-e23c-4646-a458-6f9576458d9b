'use client'

import { useState, useEffect } from 'react'

interface SimpleRichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  readOnly?: boolean
  className?: string
}

export function SimpleRichTextEditor({
  value,
  onChange,
  placeholder = 'Write something...',
  readOnly = false,
  className = '',
}: SimpleRichTextEditorProps) {
  const [editorValue, setEditorValue] = useState('')

  // Set initial value
  useEffect(() => {
    setEditorValue(value)
  }, [value])

  // Handle editor change
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const content = e.target.value
    setEditorValue(content)
    onChange(content)
  }

  return (
    <div className={`simple-rich-text-editor ${className}`}>
      <textarea
        value={editorValue}
        onChange={handleChange}
        placeholder={placeholder}
        readOnly={readOnly}
        className="w-full min-h-[300px] p-4 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        style={{ resize: 'vertical' }}
      />
      <div className="mt-2 text-sm text-gray-500">
        <p>Basic formatting is supported:</p>
        <ul className="list-disc pl-5">
          <li>Use **bold** for <strong>bold text</strong></li>
          <li>Use *italic* for <em>italic text</em></li>
          <li>Use # Heading for headings</li>
          <li>Use - item for bullet lists</li>
        </ul>
      </div>
    </div>
  )
}

// Helper function to convert plain text to Lexical format
export function textToLexical(text: string) {
  return {
    root: {
      type: 'root',
      children: [
        {
          type: 'paragraph',
          children: [
            {
              type: 'text',
              text: text || '',
              version: 1,
            },
          ],
          direction: null,
          format: '',
          indent: 0,
          version: 1,
        },
      ],
      direction: null,
      format: '',
      indent: 0,
      version: 1,
    },
  }
}

// Helper function to convert Lexical format to plain text
export function lexicalToText(lexical: any) {
  try {
    if (!lexical || !lexical.root || !lexical.root.children) {
      return ''
    }
    
    // Extract text from children
    let text = ''
    lexical.root.children.forEach((child: any) => {
      if (child.type === 'paragraph') {
        const paragraphText = child.children
          .map((textNode: any) => textNode.text || '')
          .join('')
        text += paragraphText + '\n\n'
      }
    })
    
    return text.trim()
  } catch (error) {
    console.error('Error converting Lexical to text:', error)
    return ''
  }
}
