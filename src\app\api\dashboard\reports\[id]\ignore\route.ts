import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    // Also check for token in Authorization header
    const authHeader = req.headers.get('Authorization')
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null

    // Use token from cookie or header
    const finalToken = token || headerToken

    if (!finalToken) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized: No token provided' },
        { status: 401 }
      )
    }

    // Verify token to get user info
    let userId: string | null = null;
    let userRole: string | null = null;
    
    try {
      const JWT_SECRET = process.env.PAYLOAD_SECRET || 'default-secret'
      const decoded = jwt.verify(finalToken, JWT_SECRET) as any
      
      // Handle both string roles and object roles
      userId = decoded.id || null
      
      if (typeof decoded.role === 'object' && decoded.role !== null) {
        userRole = decoded.role.slug || decoded.role.value || null
      } else {
        userRole = decoded.role || null
      }
    } catch (err) {
      console.error('Error verifying token:', err)
      return NextResponse.json(
        { success: false, error: 'Unauthorized: Invalid token' },
        { status: 401 }
      )
    }

    // Only allow school admin and super admin
    if (!userRole || (userRole !== 'school-admin' && userRole !== 'super-admin') || !userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized: Insufficient permissions' },
        { status: 403 }
      )
    }

    const reportId = params.id

    // Initialize PayloadCMS
    const payload = await getPayload({ config })

    try {
      // Update the report status
      await payload.update({
        collection: 'reports' as any,
        id: reportId,
        data: {
          status: 'ignored',
          resolved: true,
          resolvedBy: userId,
          resolvedAt: new Date().toISOString(),
          actions: [
            {
              action: 'no-action',
              actionBy: userId,
              notes: 'Report reviewed and dismissed as not requiring action',
              actionDate: new Date().toISOString(),
            },
          ],
        },
      })

      return NextResponse.json({
        success: true,
        message: 'Report marked as ignored',
      })
    } catch (error) {
      console.error('Error updating report:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to update report' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error processing report action:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
} 