import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { NotificationProvider } from '@/contexts/NotificationContext'
import { Toaster } from '@/components/ui/toaster'
import { ThemeScript } from '@/components/ThemeScript'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Young Reporter',
  description: 'A platform for student journalists',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        />
        <link 
          href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" 
          rel="stylesheet"
        />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                // Apply dark mode immediately
                try {
                  const isDarkMode = localStorage.getItem('darkMode') === 'true';
                  if (isDarkMode) {
                    document.documentElement.classList.add('dark');
                  }
                  
                  // Apply theme color
                  const themeColor = localStorage.getItem('themeColor');
                  if (themeColor) {
                    document.documentElement.setAttribute('data-theme', themeColor);
                  }
                } catch (e) {}
              })();
            `,
          }}
        />
        <style>
          {`
            @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
            
            body, html {
              font-family: 'Tajawal', 'Cairo', 'Inter', sans-serif;
            }
          `}
        </style>
      </head>
      <body className={inter.className}>
        <ThemeScript />
        <NotificationProvider>{children}</NotificationProvider>
        <Toaster />
      </body>
    </html>
  )
}
