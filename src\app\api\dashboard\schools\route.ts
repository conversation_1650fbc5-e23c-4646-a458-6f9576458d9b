import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Try to get schools from MongoDB first
    try {
      const { db } = await connectToDatabase()
      const schools = await db.collection('schools').find().toArray()

      if (schools && schools.length > 0) {
        console.log('Fetched schools from MongoDB:', schools.length)
        return NextResponse.json({ schools })
      }
    } catch (mongoError) {
      console.warn('Error fetching schools from MongoDB, falling back to Payload:', mongoError)
    }

    // Fallback to Payload CMS if MongoDB fails
    const payload = await getPayload({ config })

    // Get all schools
    const schoolsResponse = await payload.find({
      collection: 'schools',
      depth: 1,
    })

    return NextResponse.json({ schools: schoolsResponse.docs })
  } catch (error) {
    console.error('Error fetching schools:', error)
    return NextResponse.json({ error: 'Failed to fetch schools' }, { status: 500 })
  }
}
