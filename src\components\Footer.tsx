'use client'

import Link from 'next/link'
import React from 'react'
import { InteractiveDots } from './InteractiveDots'

export const Footer: React.FC = () => {
  return (
    <footer
      className="relative text-white py-8 px-4 overflow-hidden"
      style={{
        backgroundColor: 'hsl(var(--primary))',
        minHeight: '300px', // Ensure enough height for dots
        position: 'relative',
      }}
    >
      {/* Interactive Dots Background */}
      <div
        className="absolute inset-0 w-full h-full z-0"
        style={{
          minHeight: '300px',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
      >
        <InteractiveDots color="white" />
      </div>

      {/* Footer Content */}
      <div className="container mx-auto relative z-10 p-8">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-6 md:mb-0">
            <h3 className="text-xl font-bold">المراسل الشاب</h3>
            <p className="mt-2">تمكين الصحفيين الطلاب</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
            <div>
              <h4 className="font-semibold mb-3">التنقل</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/" className="hover:text-cyan-300">
                    الصفحة الرئيسية
                  </Link>
                </li>
                <li>
                  <Link href="/articles" className="hover:text-cyan-300">
                    المقالات
                  </Link>
                </li>
                <li>
                  <Link href="/news" className="hover:text-cyan-300">
                    الأخبار
                  </Link>
                </li>
                <li>
                  <Link href="/statistics" className="hover:text-cyan-300">
                    الإحصائيات
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">حول</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/about" className="hover:text-cyan-300">
                    مهمتنا
                  </Link>
                </li>
                <li>
                  <Link href="/about#team" className="hover:text-cyan-300">
                    فريقنا
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="hover:text-cyan-300">
                    تواصل معنا
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">الموارد</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/resources" className="hover:text-cyan-300">
                    للطلاب
                  </Link>
                </li>
                <li>
                  <Link href="/resources/teachers" className="hover:text-cyan-300">
                    للمعلمين
                  </Link>
                </li>
                <li>
                  <Link href="/faq" className="hover:text-cyan-300">
                    FAQ
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">قانوني</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/privacy" className="hover:text-cyan-300">
                    سياسة الخصوصية
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="hover:text-cyan-300">
                    شروط الاستخدام
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className="mt-8 pt-8 border-t border-cyan-800 text-center text-cyan-200">
          <p>© {new Date().getFullYear()} المراسل الشاب. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    </footer>
  )
}
