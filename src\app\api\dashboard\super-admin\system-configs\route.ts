import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

interface SystemConfig {
  id: string
  key: string
  value: string | boolean | number
  description: string
  category: string
}

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can access this endpoint' },
          { status: 403 },
        )
      }

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Check if system_configs collection exists
        const collections = await db.listCollections({ name: 'system_configs' }).toArray()
        
        if (collections.length === 0) {
          // Collection doesn't exist, create default configs
          await createDefaultConfigs(db)
        }

        // Get configs
        const configs = await db.collection('system_configs').find({}).toArray()
        
        // Map MongoDB _id to id for consistency
        const formattedConfigs = configs.map(config => ({
          id: config._id.toString(),
          key: config.key,
          value: config.value,
          description: config.description,
          category: config.category,
        }))

        return NextResponse.json({ configs: formattedConfigs })
      } catch (mongoError) {
        console.error('Error fetching system configs from MongoDB:', mongoError)
        
        // Return mock data as fallback
        return NextResponse.json({ configs: getMockConfigs() })
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching system configs:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

export async function PUT(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can access this endpoint' },
          { status: 403 },
        )
      }

      // Get request body
      const body = await req.json()
      const { configs } = body

      if (!configs || !Array.isArray(configs)) {
        return NextResponse.json(
          { error: 'Bad Request', message: 'Invalid configs format' },
          { status: 400 },
        )
      }

      // Try to update data in MongoDB
      try {
        const { db } = await connectToDatabase()

        // Update each config
        for (const config of configs) {
          await db.collection('system_configs').updateOne(
            { _id: new ObjectId(config.id) },
            { $set: { value: config.value } }
          )
        }

        return NextResponse.json({ success: true, message: 'Configurations updated successfully' })
      } catch (mongoError) {
        console.error('Error updating system configs in MongoDB:', mongoError)
        return NextResponse.json(
          { error: 'Database Error', message: 'Failed to update configurations' },
          { status: 500 },
        )
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating system configs:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// Helper function to create default configs
async function createDefaultConfigs(db: any) {
  const defaultConfigs = getMockConfigs()
  
  // Convert id to _id with ObjectId
  const configs = defaultConfigs.map(config => ({
    _id: new ObjectId(config.id),
    key: config.key,
    value: config.value,
    description: config.description,
    category: config.category,
  }))
  
  await db.collection('system_configs').insertMany(configs)
}

// Helper function to get mock configs
function getMockConfigs(): SystemConfig[] {
  return [
    {
      id: '1',
      key: 'enable_registration',
      value: true,
      description: 'Allow new users to register',
      category: 'access',
    },
    {
      id: '2',
      key: 'article_auto_publish',
      value: false,
      description: 'Auto-publish articles without review',
      category: 'content',
    },
    {
      id: '3',
      key: 'maintenance_mode',
      value: false,
      description: 'Put system in maintenance mode',
      category: 'system',
    },
    {
      id: '4',
      key: 'max_file_size_mb',
      value: 10,
      description: 'Maximum file upload size in MB',
      category: 'content',
    },
    {
      id: '5',
      key: 'analytics_enabled',
      value: true,
      description: 'Enable analytics collection',
      category: 'system',
    },
    {
      id: '6',
      key: 'require_email_verification',
      value: true,
      description: 'Require email verification for new accounts',
      category: 'access',
    },
    {
      id: '7',
      key: 'site_name',
      value: 'Young Reporter',
      description: 'Site name displayed in emails and UI',
      category: 'system',
    },
    {
      id: '8',
      key: 'max_articles_per_user',
      value: 50,
      description: 'Maximum articles per user',
      category: 'content',
    },
  ]
} 