import React, { useEffect, useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Eye, ArrowUp, ArrowDown, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ReactNode } from 'react'

interface ViewStatsItem {
  id: string
  title: string
  views: number
  slug: string
}

interface ViewStatsCardProps {
  type: 'news' | 'articles'
  title?: string
  limit?: number
}

export function ViewStatsCard({ type, title, limit = 5 }: ViewStatsCardProps) {
  const [items, setItems] = useState<ViewStatsItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchStats() {
      try {
        setIsLoading(true)
        setError(null)

        // Log the API request for debugging
        console.log(`Fetching view stats for ${type} with limit ${limit}`)
        
        const response = await fetch(`/api/analytics/views?type=${type}&limit=${limit}`, {
          credentials: 'include',
        })

        if (!response.ok) {
          throw new Error(`Failed to fetch view statistics: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()
        
        // Log the API response for debugging
        console.log(`Received view stats for ${type}:`, data)
        
        if (!data.items || !Array.isArray(data.items)) {
          console.error('Invalid data format received:', data)
          setItems([])
        } else {
          // Sort by views in descending order to ensure highest views are first
          const sortedItems = [...data.items].sort((a, b) => (b.views || 0) - (a.views || 0))
          setItems(sortedItems)
        }
      } catch (err) {
        console.error('Error fetching view statistics:', err)
        setError('Failed to load view statistics')
      } finally {
        setIsLoading(false)
      }
    }

    fetchStats()
  }, [type, limit])

  // Format the title based on the type
  const cardTitle = title || (type === 'news' ? 'أهم الأخبار' : 'أهم المقالات')

  // Determine the link path based on the type
  const getLinkPath = (item: ViewStatsItem) => {
    if (type === 'news') {
      return `/news/${item.slug || item.id}`
    } else {
      return `/articles/${item.slug || item.id}`
    }
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-md font-medium flex items-center">
          <Eye className="h-4 w-4 mr-2" />
          {cardTitle} حسب المشاهدات
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-6">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="text-sm text-red-500 py-2">{error}</div>
        ) : items.length === 0 ? (
          <div className="text-sm text-gray-500 py-2">لا توجد بيانات متاحة</div>
        ) : (
          <div className="space-y-4">
            {items.map((item, index) => (
              <div key={item.id} className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="mr-2 h-6 w-6 flex-shrink-0 rounded-full bg-primary/10 text-primary flex items-center justify-center text-xs font-bold">
                    {index + 1}
                  </div>
                  <Link
                    href={getLinkPath(item)}
                    className="text-sm font-medium hover:underline line-clamp-1"
                  >
                    {item.title || 'بدون عنوان'}
                  </Link>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">{item.views || 0}</span>
                  <Eye className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface StatCardProps {
  title: string
  value: number | string
  icon: ReactNode
  color: string
  link: string
  className?: string
}

export function StatCard({ title, value, icon, color, link, className = '' }: StatCardProps) {
  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-3xl font-bold">{value.toLocaleString()}</div>
      </CardContent>
      <CardFooter className="pt-0">
        <Button asChild variant="link" className="p-0 h-auto" dir="rtl">
          <a href={link}>عرض التفاصيل</a>
        </Button>
      </CardFooter>
      <div className={`absolute top-0 right-0 mt-4 mr-4 ${color} p-2 rounded-full`}>
        {icon}
      </div>
    </Card>
  )
}