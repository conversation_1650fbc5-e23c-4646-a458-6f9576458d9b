// Import dependencies
import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
// Temporarily disabled S3 storage due to compatibility issues
// import { s3Storage } from '@payloadcms/storage-s3'
import path from 'path'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'
import sharp from 'sharp'
import { Users } from './collections/Users'
import { Media } from './collections/Media'
import { Roles } from './collections/Roles'
import { Schools } from './collections/Schools'
import { Articles } from './collections/Articles'
import { News } from './collections/News'
import { Statistics } from './collections/Statistics'
import { Notifications } from './collections/Notifications'
import { Achievements } from './collections/Achievements'
import { UserAchievements } from './collections/UserAchievements'
import { Activities } from './collections/Activities'
import SystemicIssues from './collections/SystemicIssues'
import Reports from './collections/Reports'
import { MentorReviews } from './collections/MentorReviews'

// Import types as needed

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

// Payload CMS configuration

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  collections: [
    Users,
    Media,
    Roles,
    Schools,
    Articles,
    News,
    Statistics,
    Notifications,
    Achievements,
    UserAchievements,
    Activities,
    SystemicIssues,
    Reports,
    MentorReviews,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: mongooseAdapter({
    url:
      process.env.DATABASE_URI ||
      'mongodb+srv://Codic:<EMAIL>/young-reporter',
    // Always use the same database name to prevent synchronization issues
  }),
  sharp,
  plugins: [
    payloadCloudPlugin(),
    // Temporarily disabled S3 storage due to compatibility issues
    // s3Storage({
    //   collections: {
    //     media: true,
    //   },
    //   bucket: process.env.BACKBLAZE_BUCKET_NAME || 'YoungReporter',
    //   config: {
    //     endpoint: 's3.us-east-005.backblazeb2.com',
    //     credentials: {
    //       accessKeyId: process.env.BACKBLAZE_KEY_ID || '',
    //       secretAccessKey: process.env.BACKBLAZE_API || '',
    //     },
    //     region: 'us-east-005',
    //     forcePathStyle: true,
    //   }),
  ],
})

// Note: If you need to extend Payload CMS functionality,
// use plugins or hooks instead of modifying prototypes
