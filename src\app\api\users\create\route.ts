import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      
      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      
      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
      
      // Get the payload instance
      const payload = await getPayload({ config })
      
      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })
      
      // Check if user is an admin
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'
      
      if (!isAdmin) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }
      
      // Get the request body
      const body = await req.json()
      
      // Validate required fields
      if (!body.email || !body.password || !body.role) {
        return NextResponse.json(
          { error: 'Email, password, and role are required' },
          { status: 400 }
        )
      }
      
      // Check if school is required based on role
      const selectedRole = await payload.findByID({
        collection: 'roles',
        id: body.role,
      })
      
      const roleSlug = selectedRole.slug
      
      // School is required for all roles except super-admin
      if (roleSlug !== 'super-admin' && !body.school) {
        return NextResponse.json(
          { error: 'School is required for this role' },
          { status: 400 }
        )
      }
      
      // Create the user
      const newUser = await payload.create({
        collection: 'users',
        data: {
          email: body.email,
          password: body.password,
          firstName: body.firstName || '',
          lastName: body.lastName || '',
          role: body.role,
          school: body.school || undefined,
        },
      })
      
      return NextResponse.json(newUser)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
