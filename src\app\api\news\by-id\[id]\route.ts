import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

// Helper function for standardized error responses
function errorResponse(error: string, status: number) {
  return NextResponse.json(
    {
      success: false,
      error,
    },
    { status },
  )
}

// GET a single news item
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  // Await params before accessing its properties
  const resolvedParams = await params
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
        },
        { status: 401 },
      )
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return errorResponse('Unauthorized', 401)
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is an admin
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'

      if (!isAdmin) {
        return errorResponse('Forbidden', 403)
      }

      // Get the requested news item
      const news = await payload.findByID({
        collection: 'news',
        id: resolvedParams.id,
        depth: 2,
      })

      return NextResponse.json({
        success: true,
        data: news,
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return errorResponse('Unauthorized', 401)
    }
  } catch (error) {
    console.error('Error fetching news:', error)
    return errorResponse('Internal Server Error', 500)
  }
}

// UPDATE a news item
export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  // Await params before accessing its properties
  const resolvedParams = await params
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return errorResponse('Unauthorized', 401)
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return errorResponse('Unauthorized', 401)
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is an admin
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'

      if (!isAdmin) {
        return errorResponse('Forbidden', 403)
      }

      // Get the request body
      const body = await req.json()

      // Update the news item
      const updatedNews = await payload.update({
        collection: 'news',
        id: resolvedParams.id,
        data: body,
      })

      return NextResponse.json({
        success: true,
        data: updatedNews,
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return errorResponse('Unauthorized', 401)
    }
  } catch (error) {
    console.error('Error updating news:', error)
    return errorResponse('Internal Server Error', 500)
  }
}

// DELETE a news item
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Await params before accessing its properties
    const resolvedParams = await params

    // Get the token from cookies (cookies() is synchronous)
    const cookieStore = cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return errorResponse('Unauthorized', 401)
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return errorResponse('Unauthorized', 401)
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is an admin
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'

      if (!isAdmin) {
        return errorResponse('Forbidden', 403)
      }

      // Delete the news item
      const deletedNews = await payload.delete({
        collection: 'news',
        id: resolvedParams.id,
      })

      return NextResponse.json({ success: true, message: 'News item deleted successfully' })
    } catch (error) {
      console.error('Token verification error:', error)
      return errorResponse('Unauthorized', 401)
    }
  } catch (error) {
    console.error('Error deleting news:', error)
    return errorResponse('Internal Server Error', 500)
  }
}
