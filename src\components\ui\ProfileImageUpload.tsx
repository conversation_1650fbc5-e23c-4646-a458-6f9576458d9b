'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Loader2, Upload, X } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'

interface ProfileImageUploadProps {
  currentImageUrl?: string
  pendingImageUrl?: string
  imageStatus?: string
  onImageUpload: (imageId: string) => void
  firstName?: string
  lastName?: string
}

export default function ProfileImageUpload({
  currentImageUrl,
  pendingImageUrl,
  imageStatus,
  onImageUpload,
  firstName,
  lastName,
}: ProfileImageUploadProps) {
  // Prevent any form submission when this component is used
  const preventFormSubmission = (e: React.MouseEvent | React.FormEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }
  const [isUploading, setIsUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    // Prevent default behavior to avoid page reload
    preventFormSubmission(e)

    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Error',
        description: 'Please upload an image file',
        variant: 'destructive',
      })
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: 'Error',
        description: 'Image size should be less than 5MB',
        variant: 'destructive',
      })
      return
    }

    // Create preview
    const reader = new FileReader()
    reader.onload = () => {
      setPreviewUrl(reader.result as string)
    }
    reader.readAsDataURL(file)

    // Upload file
    setIsUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('alt', `Profile image for ${firstName} ${lastName}`)

      console.log('Uploading image file:', file.name, file.type, file.size)

      const response = await fetch('/api/media', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      })

      console.log('Image upload response status:', response.status)

      if (!response.ok) {
        throw new Error(`Failed to upload image: ${response.status}`)
      }

      const data = await response.json()
      console.log('Image upload response data:', data)

      if (data.id) {
        // Call the parent component's handler with the image ID
        onImageUpload(data.id)
        toast({
          title: 'Success',
          description: 'Image uploaded successfully and pending approval',
        })
      } else {
        throw new Error('No image ID returned')
      }
    } catch (error) {
      console.error('Error uploading image:', error)
      toast({
        title: 'Error',
        description: 'Failed to upload image. Please try again.',
        variant: 'destructive',
      })
      setPreviewUrl(null)
    } finally {
      setIsUploading(false)
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleCancelPreview = () => {
    setPreviewUrl(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const getStatusMessage = () => {
    switch (imageStatus) {
      case 'pending':
        return 'Your profile image is pending approval'
      case 'rejected':
        return 'Your profile image was rejected'
      default:
        return null
    }
  }

  const statusMessage = getStatusMessage()

  return (
    <div
      className="flex flex-col items-center"
      onSubmit={preventFormSubmission}
      onClick={preventFormSubmission}
    >
      <Avatar className="h-24 w-24 mb-4">
        {previewUrl ? (
          <AvatarImage src={previewUrl} alt="Preview" />
        ) : pendingImageUrl && imageStatus === 'pending' ? (
          <AvatarImage src={pendingImageUrl} alt="Pending profile image" />
        ) : currentImageUrl ? (
          <AvatarImage src={currentImageUrl} alt="Profile image" />
        ) : (
          <AvatarFallback>
            {firstName?.[0]}
            {lastName?.[0]}
          </AvatarFallback>
        )}
      </Avatar>

      {statusMessage && (
        <div className="text-sm text-amber-600 dark:text-amber-400 mb-2">{statusMessage}</div>
      )}

      <div className="flex gap-2 mt-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={(e) => {
            preventFormSubmission(e)
            fileInputRef.current?.click()
          }}
          disabled={isUploading}
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload Image
            </>
          )}
        </Button>

        {previewUrl && (
          <Button
            type="button"
            variant="destructive"
            size="sm"
            onClick={(e) => {
              preventFormSubmission(e)
              handleCancelPreview()
            }}
          >
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
        )}
      </div>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
        onClick={(e) => e.stopPropagation()}
      />

      <p className="text-xs text-gray-500 mt-2">
        Upload a profile picture (max 5MB). Image will be reviewed before appearing on your profile.
      </p>
    </div>
  )
}
