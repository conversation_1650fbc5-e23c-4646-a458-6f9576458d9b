'use client'

import React from 'react'
import { useState } from 'react'

interface ClientImageProps {
  src: string
  alt: string
  className?: string
  fallbackSrc?: string
}

export function ClientImage({
  src,
  alt,
  className = '',
  fallbackSrc = 'https://images.unsplash.com/photo-1745500415839-503883982264?q=80&w=3987&auto=format&fit=crop&ixlib=rb-4.0.3',
}: ClientImageProps) {
  const [imgSrc, setImgSrc] = useState(src)
  const [hasError, setHasError] = useState(false)

  const handleError = () => {
    if (!hasError) {
      console.log('Image failed to load:', src)
      setImgSrc(fallbackSrc)
      setHasError(true)
    }
  }

  // Log the image source for debugging
  React.useEffect(() => {
    console.log('ClientImage rendering with src:', src)
  }, [src])

  return <img src={imgSrc} alt={alt} className={className} onError={handleError} />
}
