@tailwind base;
@tailwind components;
@tailwind utilities;

@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;700;900&family=Space+Mono:wght@400;700&display=swap");

:root {
  --bg-color: #f1f1f1;
  --card-width: 300px;
  --card-height: 400px;
  --black: #080808;
  --white: #fafafa;
  --accent: #0ea5e9;
  --grid-color: rgba(0, 0, 0, 0.08);
  --frame-color: rgba(8, 8, 8, 0.8);
  --transition-slow: 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  --transition-fast: 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.card-system {
  display: flex;
  gap: 40px;
}

.deconstructed-card {
  position: relative;
  width: var(--card-width);
  height: var(--card-height);
  cursor: pointer;
  transition: transform 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  transform-style: preserve-3d;
  flex-shrink: 0;
}

.card-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform var(--transition-slow), opacity var(--transition-slow);
}

.card-image {
  overflow: hidden;
  z-index: 1;
}

.image-background {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  background-blend-mode: multiply;
}

.image-background-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.color-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  mix-blend-mode: multiply;
}

.wave-svg {
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  transition: transform 1.2s cubic-bezier(0.16, 1, 0.3, 1);
}

.card-frame {
  z-index: 3;
  pointer-events: none;
}

.frame-path {
  fill: none;
  stroke: var(--frame-color);
  stroke-width: 1;
  stroke-dasharray: 1520;
  stroke-dashoffset: 1520;
  transition: stroke-dashoffset 1.5s cubic-bezier(0.16, 1, 0.3, 1);
}

.card-background {
  z-index: 0;
  background-color: var(--white);
}

.bg-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.grid-line {
  position: absolute;
  background-color: var(--grid-color);
  transition: transform var(--transition-slow), opacity var(--transition-fast);
}

.grid-line.horizontal {
  width: 100%;
  height: 1px;
  transform: scaleX(0.3);
  transform-origin: left;
}

.grid-line.vertical {
  height: 100%;
  width: 1px;
  transform: scaleY(0.3);
  transform-origin: top;
}

.bg-objects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.bg-object {
  position: absolute;
  opacity: 0.3;
  transition: transform var(--transition-slow), opacity var(--transition-slow);
}

.bg-object.circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  bottom: 40px;
  left: -30px;
  transform: translateY(20px);
}

.bg-object.square {
  width: 60px;
  height: 60px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  top: 40px;
  right: 30px;
  transform: rotate(45deg) translateY(-20px);
}

.bg-object.triangle {
  width: 0;
  height: 0;
  border-left: 40px solid transparent;
  border-right: 40px solid transparent;
  border-bottom: 70px solid rgba(0, 0, 0, 0.05);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.5);
}

.card-content {
  z-index: 2;
  padding: 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.content-fragment {
  position: relative;
}

.fragment-heading {
  margin-top: auto;
  margin-bottom: 1.5rem;
}

.content-text {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  color: var(--white);
  transition: transform var(--transition-fast), opacity var(--transition-fast);
  transform: translateY(10px);
  opacity: 0.9;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.content-title {
  position: absolute;
  bottom: 60px;
  left: 30px;
  right: 30px;
  font-size: 1.25rem;
}

.fragment-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  position: absolute;
  top: 30px;
  left: 30px;
}

.meta-line {
  width: 40px;
  height: 1px;
  background-color: var(--white);
  transform: scaleX(0.5);
  transform-origin: left;
  transition: transform var(--transition-fast);
}

.meta-text {
  font-family: "Space Mono", monospace;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  opacity: 0.8;
  color: var(--white);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  transform: translateX(-5px);
  transition: transform var(--transition-fast), opacity var(--transition-fast);
}

.fragment-body {
  max-width: 85%;
  margin: 1.5rem 0;
}

.fragment-body .content-text {
  font-size: 0.9rem;
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: normal;
  opacity: 0.6;
}

.fragment-cta {
  position: absolute;
  bottom: 30px;
  left: 30px;
  overflow: visible;
}

.cta-link {
  text-decoration: none;
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 8px 0;
}

.cta-box {
  position: absolute;
  top: 0;
  left: -10px;
  width: calc(100% + 20px);
  height: 100%;
  background-color: var(--white);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-fast);
}

.cta-text {
  position: relative;
  font-family: "Space Mono", monospace;
  font-size: 0.75rem;
  font-weight: 700;
  letter-spacing: 0.1em;
  color: var(--white);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  transition: color var(--transition-fast);
  transform: translateX(-5px);
  opacity: 0.9;
  transition: transform var(--transition-fast), opacity var(--transition-fast),
    color var(--transition-fast);
}

/* Hover effects */
.deconstructed-card:hover .frame-path {
  stroke-dashoffset: 0;
}

.deconstructed-card:hover .content-text {
  transform: translateY(0);
  opacity: 1;
  transition-delay: 0.05s;
}

.deconstructed-card:hover .meta-text {
  transform: translateX(0);
  opacity: 1;
  transition-delay: 0.15s;
}

.deconstructed-card:hover .meta-line {
  transform: scaleX(1);
  transition-delay: 0.05s;
}

.deconstructed-card:hover .wave-svg {
  transform: translateY(-5px);
}

.deconstructed-card:hover .cta-text {
  transform: translateX(0);
  opacity: 1;
  transition-delay: 0.2s;
}

.deconstructed-card:hover .cta-link:hover .cta-box {
  transform: scaleX(1);
}

.deconstructed-card:hover .cta-link:hover .cta-text {
  color: var(--black);
  text-shadow: none;
}

.deconstructed-card:hover .grid-line.horizontal {
  transform: scaleX(1);
}

.deconstructed-card:hover .grid-line.vertical {
  transform: scaleY(1);
}

.deconstructed-card:hover .bg-object {
  opacity: 1;
  transform: translate(0, 0) rotate(0);
}

.deconstructed-card:hover .bg-object.square {
  transform: rotate(45deg) translate(0, 0);
}

.deconstructed-card:hover .bg-object.triangle {
  transform: translate(-50%, -50%) scale(1);
}

/* Gradient backgrounds for different card types */
.article-content .card-image svg {
  background: linear-gradient(135deg, #0ea5e9, #38bdf8);
}

.news-content .card-image svg {
  background: linear-gradient(135deg, #4f46e5, #818cf8);
}

/* Light text on dark backgrounds */
.article-content .content-text,
.article-content .meta-text,
.article-content .cta-text,
.news-content .content-text,
.news-content .meta-text,
.news-content .cta-text {
  color: var(--white);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.article-content .meta-line,
.news-content .meta-line {
  background-color: var(--white);
}

.article-content .cta-box,
.news-content .cta-box {
  background-color: var(--white);
}

.article-content .cta-link:hover .cta-text,
.news-content .cta-link:hover .cta-text {
  color: var(--black) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :root {
    --card-width: 280px;
    --card-height: 380px;
  }

  .card-content {
    padding: 20px;
  }

  .content-text {
    font-size: 1.8rem;
  }
}
