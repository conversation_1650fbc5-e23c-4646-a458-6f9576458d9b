import { connectToDatabase } from './mongodb'
import { ObjectId, Document, WithId } from 'mongodb'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { Article as PayloadArticle } from '@/payload-types'
import {
  isMediaPathError,
  logMediaPathError,
  safeMediaQuery,
  filterMediaPathArticles,
} from './media-utils'
import { isUserAuthor } from './id-utils'

// Define a type that can handle both MongoDB and Payload article formats
type Article = (WithId<Document> | PayloadArticle) & {
  _id?: ObjectId | string
  id?: string
  slug?: string
}

/**
 * Find an article by ID or slug using MongoDB and Payload CMS
 */
export async function findArticleByIdOrSlug(idOrSlug: string): Promise<Article | null> {
  console.log('Finding article by ID or slug:', idOrSlug)

  // First check if the ID is a media path - if so, return null immediately
  if (!idOrSlug || typeof idOrSlug !== 'string') {
    console.log('Invalid ID or slug (null or not a string)')
    return null
  }

  if (
    idOrSlug.includes('/api/media') ||
    idOrSlug.includes('/media') ||
    idOrSlug.includes('/file/') ||
    /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(idOrSlug)
  ) {
    console.log('Skipping lookup for media path:', idOrSlug)
    return null
  }

  try {
    // First try MongoDB
    const { db } = await connectToDatabase()

    // Try to find by numeric ID or slug
    let article = await db.collection('articles').findOne({
      $or: [{ id: idOrSlug }, { slug: idOrSlug }],
    })

    if (article) {
      console.log('Article found by ID or slug in MongoDB')
      return article
    }

    // If not found, try to parse as ObjectId
    if (idOrSlug.match(/^[0-9a-fA-F]{24}$/)) {
      try {
        // Double-check it's not a media path before trying ObjectId
        if (!idOrSlug.includes('/api/media') && !idOrSlug.includes('/media')) {
          article = await db.collection('articles').findOne({
            _id: new ObjectId(idOrSlug),
          })

          if (article) {
            console.log('Article found by ObjectId in MongoDB')
            return article
          }
        } else {
          console.log('Skipping ObjectId lookup for media path:', idOrSlug)
        }
      } catch (error) {
        // Check if this is a media path error
        if (isMediaPathError(error)) {
          logMediaPathError(error, 'findArticleByIdOrSlug')
        } else {
          console.log('Error finding article by ObjectId:', error)
        }
      }
    }

    // Try to find by numeric value
    if (!isNaN(Number(idOrSlug))) {
      const numericId = Number(idOrSlug)
      article = await db.collection('articles').findOne({
        $or: [{ id: numericId }, { id: idOrSlug }],
      })

      if (article) {
        console.log('Article found by numeric ID in MongoDB')
        return article
      }
    }

    console.log('Article not found in MongoDB, trying Payload CMS')

    // If not found in MongoDB, try Payload CMS
    try {
      const payload = await getPayload({ config })

      // First try to find by ID - but only if it's not a media path
      try {
        // Double-check it's not a media path and is a valid ObjectId format
        if (
          idOrSlug.match(/^[0-9a-fA-F]{24}$/) &&
          !idOrSlug.includes('/api/media') &&
          !idOrSlug.includes('/media') &&
          !idOrSlug.includes('/file/') &&
          !/\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(idOrSlug)
        ) {
          const payloadArticle = await payload.findByID({
            collection: 'articles',
            id: idOrSlug,
            depth: 2,
          })

          if (payloadArticle) {
            console.log('Article found by ID in Payload CMS')
            // Convert Payload article to our Article type
            return {
              ...payloadArticle,
              id: payloadArticle.id,
              slug: payloadArticle.slug || undefined,
            }
          }
        } else if (
          idOrSlug.includes('/api/media') ||
          idOrSlug.includes('/media') ||
          idOrSlug.includes('/file/') ||
          /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(idOrSlug)
        ) {
          console.log('Skipping Payload lookup for media path:', idOrSlug)
        }
      } catch (error) {
        // Check if this is a media path error
        if (isMediaPathError(error)) {
          logMediaPathError(error, 'findArticleByIdOrSlug Payload')
        } else {
          console.log('Error finding article by ID in Payload CMS:', error)
        }
      }

      // Then try to find by slug
      try {
        const payloadArticles = await payload.find({
          collection: 'articles',
          where: {
            slug: {
              equals: idOrSlug,
            },
          },
          depth: 2,
          limit: 1,
        })

        if (payloadArticles.docs && payloadArticles.docs.length > 0) {
          console.log('Article found by slug in Payload CMS')
          // Convert Payload article to our Article type
          const payloadArticle = payloadArticles.docs[0]
          return {
            ...payloadArticle,
            id: payloadArticle.id,
            slug: payloadArticle.slug || undefined,
          }
        }
      } catch (error) {
        console.log('Error finding article by slug in Payload CMS:', error)
      }

      console.log('Article not found in Payload CMS either')
    } catch (error) {
      console.error('Error accessing Payload CMS:', error)
    }

    return null
  } catch (error) {
    console.error('Error finding article:', error)
    return null
  }
}

/**
 * Delete an article by ID or slug using MongoDB directly
 */
export async function deleteArticleByIdOrSlug(idOrSlug: string) {
  console.log('Deleting article by ID or slug:', idOrSlug)

  try {
    const { db } = await connectToDatabase()

    // First find the article to get its ID
    const article = await findArticleByIdOrSlug(idOrSlug)

    if (!article) {
      console.log('Article not found for deletion')
      return { success: false, message: 'Article not found' }
    }

    // Use the _id for deletion if it exists
    let result
    if (article._id) {
      // Convert to ObjectId if it's a string
      const objectId = typeof article._id === 'string' ? new ObjectId(article._id) : article._id

      result = await db.collection('articles').deleteOne({
        _id: objectId,
      })
    } else {
      // Otherwise use the id or slug
      result = await db.collection('articles').deleteOne({
        $or: [{ id: article.id }, { slug: article.slug }],
      })
    }

    console.log('MongoDB deletion result:', result)

    if (result.deletedCount === 0) {
      return { success: false, message: 'Failed to delete article' }
    }

    return { success: true, message: 'Article deleted successfully' }
  } catch (error) {
    console.error('Error deleting article:', error)
    return { success: false, message: 'Error deleting article' }
  }
}

/**
 * Get articles for a specific user
 */
export async function getUserArticles(userId: string) {
  console.log('Getting articles for user:', userId)

  try {
    const { db } = await connectToDatabase()

    // Create safe queries that handle media paths
    const queries = [
      { 'author.id': userId },
      { author: userId },
      // Add more query variations to increase chances of finding articles
      { 'author.id': userId.toString() },
      { author: userId.toString() },
      // Add queries for MongoDB $oid format
      { 'author.$oid': userId },
      { 'author.$oid': userId.toString() }
    ]

    // Apply safe media handling to queries
    const safeQueries = queries.map((query) => safeMediaQuery(query))

    try {
      // Find articles where the user is the author
      const articles = await db
        .collection('articles')
        .find({
          $or: safeQueries,
        })
        .toArray()

      console.log(`Found ${articles.length} articles for user ${userId}`)

      if (articles.length > 0) {
        return articles
      }
    } catch (error) {
      // Check if this is a media path error
      if (isMediaPathError(error)) {
        logMediaPathError(error, 'getUserArticles')
      } else {
        console.error('Error getting user articles:', error)
      }
    }

    // If we get here, either no articles were found or there was an error
    // Try a more generic approach
    console.log('Trying generic query for user articles')

    try {
      // Get all articles
      const allArticles = await db.collection('articles').find({}).toArray()
      console.log(`Found ${allArticles.length} total articles in the database`)

      // Log sample of articles for debugging
      if (allArticles.length > 0) {
        console.log('Sample of first 2 articles:')
        allArticles.slice(0, 2).forEach((article, i) => {
          console.log(`Article ${i+1}:`, {
            id: article.id,
            _id: article._id,
            title: article.title,
            author: typeof article.author === 'object' 
              ? JSON.stringify(article.author).substring(0, 100) 
              : article.author
          })
        })
      }

      // Use the imported utilities for filtering
      const userArticles = allArticles.filter((article) => {
        // Skip null or undefined articles
        if (!article) return false

        // Skip articles with media path IDs
        if (
          article.id &&
          typeof article.id === 'string' &&
          (article.id.includes('/api/media') || article.id.includes('/media'))
        ) {
          return false
        }

        // Check if the article has the user's ID in any author format
        if (article.author) {
          // Direct string comparison
          if (typeof article.author === 'string') {
            if (article.author === userId || article.author === userId.toString()) {
              return true;
            }
          }
          // Object with id
          else if (typeof article.author === 'object' && article.author.id) {
            if (article.author.id === userId || article.author.id === userId.toString()) {
              return true;
            }
          }
          // MongoDB $oid format
          else if (typeof article.author === 'object' && article.author.$oid) {
            if (article.author.$oid === userId || article.author.$oid === userId.toString()) {
              return true;
            }
          }
          // ObjectId direct
          else if (typeof article.author === 'object' && article.author._id) {
            const authorId = typeof article.author._id === 'string' 
              ? article.author._id 
              : article.author._id.toString();
            if (authorId === userId || authorId === userId.toString()) {
              return true;
            }
          }
        }

        // Use our utility function as a fallback
        return isUserAuthor(userId, article)
      })

      // Apply one final media path filter
      const safeArticles = filterMediaPathArticles(userArticles)

      console.log(`Found ${safeArticles.length} articles for user ${userId} with generic query`)
      return safeArticles
    } catch (genericError) {
      // Check if this is a media path error
      if (isMediaPathError(genericError)) {
        logMediaPathError(genericError, 'getUserArticles generic query')
        return [] // Return empty array for media path errors
      } else {
        console.error('Error with generic article query:', genericError)
        return []
      }
    }
  } catch (error) {
    console.error('Error getting user articles:', error)
    return []
  }
}

/**
 * Update an article by ID or slug using MongoDB directly
 */
export async function updateArticleByIdOrSlug(idOrSlug: string, data: Record<string, any>) {
  console.log('Updating article by ID or slug:', idOrSlug)
  console.log('Update data:', JSON.stringify(data).substring(0, 200) + '...')

  try {
    const { db } = await connectToDatabase()
    console.log('Connected to MongoDB database')

    // First find the article to get its ID
    console.log('Finding article before update...')
    const article = await findArticleByIdOrSlug(idOrSlug)

    if (!article) {
      console.log('Article not found for update')
      return { success: false, message: 'Article not found' }
    }

    console.log('Found article for update:', {
      id: article.id,
      _id: article._id,
      slug: article.slug,
      title: article.title,
    })

    // Use the _id for update if it exists
    let result
    if (article._id) {
      // Convert string _id to ObjectId if needed
      const objectId = typeof article._id === 'string' ? new ObjectId(article._id) : article._id
      console.log('Updating article by _id:', objectId)

      result = await db
        .collection('articles')
        .updateOne({ _id: objectId }, { $set: { ...data, updatedAt: new Date() } })
    } else {
      // Otherwise use the id or slug
      console.log('Updating article by id or slug:', { id: article.id, slug: article.slug })

      result = await db
        .collection('articles')
        .updateOne(
          { $or: [{ id: article.id }, { slug: article.slug }] },
          { $set: { ...data, updatedAt: new Date() } },
        )
    }

    console.log('MongoDB update result:', {
      matchedCount: result.matchedCount,
      modifiedCount: result.modifiedCount,
      upsertedCount: result.upsertedCount,
      acknowledged: result.acknowledged,
    })

    if (result.matchedCount === 0) {
      console.log('No documents matched the update criteria')
      return { success: false, message: 'Failed to update article' }
    }

    // Get the updated article
    console.log('Fetching updated article...')
    const updatedArticle = await findArticleByIdOrSlug(idOrSlug)
    console.log('Updated article retrieved successfully')

    return {
      success: true,
      message: 'Article updated successfully',
      article: updatedArticle,
    }
  } catch (error) {
    console.error('Error updating article:', error)
    return { success: false, message: 'Error updating article' }
  }
}

/**
 * Get article statistics for a user
 */
export async function getUserArticleStats(userId: string) {
  console.log('Getting article statistics for user:', userId)

  try {
    // Get all articles for the user
    const articles = await getUserArticles(userId)

    // Count articles by status
    const totalArticles = articles.length
    const publishedArticles = articles.filter((article) => article.status === 'published').length
    const pendingArticles = articles.filter(
      (article) => article.status === 'pending-review' || article.status === 'ready-for-review',
    ).length
    const draftArticles = articles.filter((article) => article.status === 'draft').length

    // Get recent teacher reviews
    const articlesWithReviews = articles.filter(
      (article) => article.teacherReview && article.teacherReview.length > 0,
    )

    const recentReviews = articlesWithReviews
      .flatMap((article) =>
        (article.teacherReview || []).map((review: any) => ({
          articleId: article.id,
          articleTitle: article.title,
          reviewerId: typeof review.reviewer === 'object' ? review.reviewer.id : review.reviewer,
          reviewerName: typeof review.reviewer === 'object' ? review.reviewer.name : 'Unknown',
          comment: review.comment,
          rating: review.rating,
          approved: review.approved,
          date: review.date || article.updatedAt,
        })),
      )
      .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 5)

    // Get recent articles
    const recentArticles = [...articles]
      .sort(
        (a, b) =>
          new Date(b.updatedAt || b.createdAt).getTime() -
          new Date(a.updatedAt || a.createdAt).getTime(),
      )
      .slice(0, 5)

    return {
      totalArticles,
      publishedArticles,
      pendingArticles,
      draftArticles,
      recentReviews,
      recentArticles,
    }
  } catch (error) {
    console.error('Error getting user article statistics:', error)
    return {
      totalArticles: 0,
      publishedArticles: 0,
      pendingArticles: 0,
      draftArticles: 0,
      recentReviews: [],
      recentArticles: [],
    }
  }
}
