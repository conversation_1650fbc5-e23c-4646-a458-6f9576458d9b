
/* Custom styles for Payload CMS admin panel */

/* Fix for relationship fields */
.relationship-field {
  width: 100%;
}

/* Fix for select fields */
.select-field {
  width: 100%;
}

/* Fix for form fields */
.field-type {
  width: 100%;
}

/* Fix for buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Fix for create-first-user page */
.create-first-user {
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem;
}

/* Fix for form layout */
.form-layout {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Fix for relationship select */
.relationship-select {
  width: 100%;
}

/* Fix for role selection */
.role-select {
  width: 100%;
}

/* Fix for school selection */
.school-select {
  width: 100%;
}