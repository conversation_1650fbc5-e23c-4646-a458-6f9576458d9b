'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Award, AlertCircle } from 'lucide-react'
import {
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ResponsiveContainer,
  Tooltip,
} from 'recharts'

interface MentorScore {
  mentorId: string
  mentorName: string
  scores: {
    accuracy: number
    completeness: number
    helpfulness: number
    timeliness: number
    clarity: number
  }
}

interface MentorReviewQualityScoresProps {
  schoolId: string
}

export default function MentorReviewQualityScores({
  schoolId,
}: MentorReviewQualityScoresProps) {
  const [mentorScores, setMentorScores] = useState<MentorScore[]>([])
  const [selectedMentorIndex, setSelectedMentorIndex] = useState<number>(0)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function fetchMentorScores() {
      try {
        setIsLoading(true)
        const response = await fetch(
          `/api/dashboard/school-admin/mentor-reviews?schoolId=${schoolId}`
        )

        if (!response.ok) {
          throw new Error('فشل في جلب بيانات تقييم المشرفين')
        }

        const data = await response.json()
        setMentorScores(data.mentorScores || [])
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching mentor scores:', err)
        setError('فشل في تحميل بيانات تقييم المشرفين')
        setIsLoading(false)
      }
    }

    fetchMentorScores()
  }, [schoolId])

  const transformScoreData = (scores: MentorScore['scores']) => {
    return [
      {
        subject: 'الدقة',
        A: scores.accuracy * 100,
        fullMark: 100,
      },
      {
        subject: 'الشمولية',
        A: scores.completeness * 100,
        fullMark: 100,
      },
      {
        subject: 'المساعدة',
        A: scores.helpfulness * 100,
        fullMark: 100,
      },
      {
        subject: 'التوقيت المناسب',
        A: scores.timeliness * 100,
        fullMark: 100,
      },
      {
        subject: 'الوضوح',
        A: scores.clarity * 100,
        fullMark: 100,
      },
    ]
  }

  const calculateAverageScore = (scores: MentorScore['scores']) => {
    const values = Object.values(scores)
    return values.reduce((a, b) => a + b, 0) / values.length
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center" dir="rtl">
            <Award className="ml-2 h-5 w-5" />
            تقييمات جودة مراجعات المشرفين
          </CardTitle>
        </CardHeader>
        <CardContent dir="rtl">
          <div className="flex flex-col items-center justify-center p-4 bg-red-50 text-red-500 rounded-md">
            <AlertCircle className="h-6 w-6 mb-2" />
            <p>{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center" dir="rtl">
          <Award className="ml-2 h-5 w-5" />
          تقييمات جودة مراجعات المشرفين
        </CardTitle>
      </CardHeader>
      <CardContent dir="rtl">
        {isLoading ? (
          <div className="flex flex-col items-center space-y-6">
            <Skeleton className="h-64 w-full" />
            <div className="grid grid-cols-3 gap-4 w-full">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          </div>
        ) : mentorScores.length > 0 ? (
          <>
            <div className="h-64 w-full mb-6">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart
                  cx="50%"
                  cy="50%"
                  outerRadius="80%"
                  data={transformScoreData(mentorScores[selectedMentorIndex].scores)}
                >
                  <PolarGrid />
                  <PolarAngleAxis dataKey="subject" />
                  <PolarRadiusAxis angle={30} domain={[0, 100]} />
                  <Radar
                    name="النتيجة"
                    dataKey="A"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.6}
                  />
                  <Tooltip formatter={(value) => [`${value}%`, 'النتيجة']} />
                </RadarChart>
              </ResponsiveContainer>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {mentorScores.map((mentor, index) => {
                const avgScore = calculateAverageScore(mentor.scores)
                return (
                  <button
                    key={mentor.mentorId}
                    onClick={() => setSelectedMentorIndex(index)}
                    className={`p-3 text-right border rounded-lg transition-all ${
                      selectedMentorIndex === index
                        ? 'border-blue-500 bg-blue-50'
                        : 'hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium truncate">{mentor.mentorName}</div>
                    <div className="text-sm text-gray-500">
                      متوسط النتيجة: {(avgScore * 100).toFixed(0)}%
                    </div>
                  </button>
                )
              })}
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-10 text-center">
            <AlertCircle className="h-12 w-12 text-gray-300 mb-4" />
            <p className="text-gray-500 mb-2">لا تتوفر بيانات تقييم للمشرفين</p>
            <p className="text-sm text-gray-400">
              يتم تحديث تقييمات المشرفين بناءً على استطلاعات الرأي من المعلمين والطلاب
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
