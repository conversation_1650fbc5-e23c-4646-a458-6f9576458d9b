'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Trash2 } from 'lucide-react'
import { formatDistanceToNow } from '@/lib/date-utils'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useNotifications, Notification } from '@/contexts/NotificationContext'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'

export function NotificationDropdown() {
  const router = useRouter()
  const { notifications, unreadCount, markAsRead, markAllAsRead, clearNotifications, translateMessage } =
    useNotifications()
  const [isOpen, setIsOpen] = useState(false)

  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id)
    
    // If the notification has a link, navigate to it
    if (notification.link) {
      router.push(notification.link)
    } else if (notification.data?.articleId) {
      // Check if there's an article ID in the data field
      router.push(`/articles/${notification.data.articleId}`)
    }
    
    setIsOpen(false)
  }

  const getNotificationIcon = (type?: string, pointsChange?: number) => {
    switch (type) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800 border-green-300">نجاح</Badge>
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">تنبيه</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800 border-red-300">خطأ</Badge>
      case 'points':
        if (pointsChange === undefined) {
          return <Badge className="bg-purple-100 text-purple-800 border-purple-300">نقاط</Badge>
        }
        return pointsChange > 0 ? (
          <Badge className="bg-green-100 text-green-800 border-green-300">
            +{pointsChange} نقطة
          </Badge>
        ) : (
          <Badge className="bg-red-100 text-red-800 border-red-300">{pointsChange} نقطة</Badge>
        )
      case 'article-deleted':
        return <Badge className="bg-red-100 text-red-800 border-red-300">تم الحذف</Badge>
      case 'report-actioned':
        return <Badge className="bg-green-100 text-green-800 border-green-300">تم الإجراء</Badge>
      case 'info':
      default:
        return <Badge className="bg-blue-100 text-blue-800 border-blue-300">معلومات</Badge>
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'مؤخرًا';
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch (error) {
      return 'مؤخرًا'
    }
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <div className="relative inline-block">
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center">
                {unreadCount > 9 ? '9+' : unreadCount}
              </span>
            )}
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <div dir="rtl" className="w-full">
          <div className="flex items-center justify-between p-4">
            <h3 className="font-medium">الإشعارات</h3>
            <div className="flex gap-2">
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  className="h-8 px-2 text-xs flex flex-row-reverse"
                >
                  <CheckCheck className="h-3.5 w-3.5 mr-1" />
                  تحديد الكل كمقروء
                </Button>
              )}
              {notifications.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearNotifications}
                  className="h-8 px-2 text-xs flex flex-row-reverse"
                >
                  <Trash2 className="h-3.5 w-3.5 mr-1" />
                  مسح الكل
                </Button>
              )}
            </div>
          </div>
          <DropdownMenuSeparator />

          {notifications.length > 0 ? (
            <ScrollArea className="h-[300px]">
              {notifications.map((notification) => (
                <div key={notification.id}>
                  <DropdownMenuItem
                    className={`p-4 cursor-pointer ${!notification.read ? 'bg-muted/50' : ''}`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex flex-col gap-1 w-full text-right">
                      <div className="flex justify-between items-start">
                        <span className="font-medium">
                          {translateMessage(notification.title || notification.message)}
                        </span>
                        {getNotificationIcon(notification.type, notification.pointsChange)}
                      </div>
                      {notification.title && (
                        <p className="text-sm text-muted-foreground">
                          {translateMessage(notification.message)}
                        </p>
                      )}
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-xs text-muted-foreground">
                          {formatDate(notification.date || notification.createdAt)}
                        </span>
                        {!notification.read && (
                          <Badge variant="outline" className="text-xs h-5 px-1">
                            جديد
                          </Badge>
                        )}
                      </div>
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                </div>
              ))}
            </ScrollArea>
          ) : (
            <div className="p-4 text-center text-muted-foreground">
              <p>لا توجد إشعارات</p>
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
