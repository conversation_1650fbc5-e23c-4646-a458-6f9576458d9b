import type { CollectionConfig } from 'payload'
import { hasRole } from '../access'

export const Schools: CollectionConfig = {
  slug: 'schools',
  admin: {
    useAsTitle: 'name',
  },
  access: {
    read: () => true,
    create: ({ req }) => hasRole(req, 'super-admin'),
    update: ({ req }) => hasRole(req, 'super-admin') || hasRole(req, 'school-admin'),
    delete: ({ req }) => hasRole(req, 'super-admin'),
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'address',
      type: 'textarea',
    },
    {
      name: 'city',
      type: 'text',
      label: 'City',
      admin: {
        description: 'City where the school is located',
      },
    },
    {
      name: 'state',
      type: 'text',
      label: 'State/Province',
      admin: {
        description: 'State/province where the school is located',
      },
    },
    {
      name: 'zipCode',
      type: 'text',
      label: 'Postal/ZIP Code',
      admin: {
        description: 'Postal/ZIP code',
      },
    },
    {
      name: 'country',
      type: 'text',
      label: 'Country',
      admin: {
        description: 'Country where the school is located',
      },
    },
    {
      name: 'phone',
      type: 'text',
      label: 'Phone',
      admin: {
        description: 'Contact phone number',
      },
    },
    {
      name: 'email',
      type: 'email',
      label: 'Email',
      admin: {
        description: 'Contact email address',
      },
    },
    {
      name: 'website',
      type: 'text',
      label: 'Website',
      admin: {
        description: 'School website URL',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
      admin: {
        description: 'Description or mission statement of the school',
      },
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
      label: 'School Logo/Image',
    },
    {
      name: 'imageUrl',
      type: 'text',
      label: 'School Image URL',
      admin: {
        description: 'Direct URL to the school image (alternative to the media relationship)',
      },
    },
    {
      name: 'admin',
      type: 'relationship',
      relationTo: 'users',
      // In Payload CMS v3, we'll handle this in the UI
      // by filtering the options manually
    },
  ],
}
