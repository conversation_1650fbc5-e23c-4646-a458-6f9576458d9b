import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import config from '@/payload.config'

export async function GET(req: NextRequest) {
  try {
    // Get the payload instance
    const payload = await getPayload({ config })

    // Get the URL parameters
    const url = new URL(req.url)
    const page = parseInt(url.searchParams.get('page') || '1', 10)
    const limit = parseInt(url.searchParams.get('limit') || '10', 10)
    const search = url.searchParams.get('search') || ''
    const sort = url.searchParams.get('sort') || '-createdAt'

    // Build the query
    const query: any = {
      status: {
        equals: 'published',
      },
    }

    // Add search term if provided
    if (search) {
      query.or = [
        {
          title: {
            like: search,
          },
        },
        {
          content: {
            like: search,
          },
        },
      ]
    }

    // Get all news
    const news = await payload.find({
      collection: 'news',
      where: query,
      sort,
      page,
      limit,
      depth: 2, // Populate relationships
    })

    console.log(`Fetched news from MongoDB: ${news.docs.length}`)

    return NextResponse.json(news)
  } catch (error) {
    console.error('Error fetching news:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
