import { Payload } from 'payload'

import { sendEmail } from './email'

type NotificationType = 'info' | 'success' | 'warning' | 'error'

interface CreateNotificationArgs {
  payload: Payload
  user: string
  message: string
  type?: NotificationType
  link?: string
}

/**
 * Create a notification for a user
 */
export const createNotification = async ({
  payload,
  user,
  message,
  type = 'info',
  link,
}: CreateNotificationArgs): Promise<void> => {
  try {
    await payload.create({
      collection: 'notifications',
      data: {
        user,
        message,
        type,
        link,
        read: false,
        createdAt: new Date().toISOString(),
      },
    })
  } catch (error) {
    console.error('Error creating notification:', error)
  }
}

/**
 * Send notifications to users when an article is submitted for review
 */
export const notifyArticleSubmitted = async ({
  payload,
  articleId,
  articleTitle,
  authorId,
}: {
  payload: Payload
  articleId: string
  articleTitle: string
  authorId: string
}): Promise<void> => {
  try {
    // Find teachers to notify
    const teachers = await payload.find({
      collection: 'users',
      where: {
        'role.slug': {
          equals: 'teacher',
        },
      },
    })

    // Create notifications for each teacher
    for (const teacher of teachers.docs) {
      // Create in-app notification
      await createNotification({
        payload,
        user: teacher.id,
        message: `New article "${articleTitle}" submitted for review`,
        type: 'info',
        link: `/dashboard/teacher/review/${articleId}`,
      })

      // Send email notification if teacher has an email and has enabled email notifications
      if (
        teacher.email &&
        teacher.notificationPreferences?.emailNotifications &&
        (!teacher.notificationPreferences.emailTypes ||
          teacher.notificationPreferences.emailTypes.includes('articleSubmissions'))
      ) {
        const articleUrl = `${process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'}/dashboard/teacher/review/${articleId}`

        await sendEmail({
          to: teacher.email,
          template: 'articleSubmitted',
          data: {
            articleTitle,
            articleUrl,
          },
        })
      }
    }
  } catch (error) {
    console.error('Error notifying about article submission:', error)
  }
}

/**
 * Send notification to student when their article is reviewed
 */
export const notifyArticleReviewed = async ({
  payload,
  articleId,
  articleTitle,
  studentId,
  approved,
}: {
  payload: Payload
  articleId: string
  articleTitle: string
  studentId: string
  approved: boolean
}): Promise<void> => {
  try {
    // Create in-app notification
    await createNotification({
      payload,
      user: studentId,
      message: `Your article "${articleTitle}" has been ${
        approved ? 'approved and published' : 'reviewed but not approved'
      }`,
      type: approved ? 'success' : 'info',
      link: `/articles/${articleId}`,
    })

    // Find student to get email
    const student = await payload.findByID({
      collection: 'users',
      id: studentId,
    })

    // Send email notification if student has an email and has enabled email notifications
    if (
      student &&
      student.email &&
      student.notificationPreferences?.emailNotifications &&
      (!student.notificationPreferences.emailTypes ||
        student.notificationPreferences.emailTypes.includes('articleReviews'))
    ) {
      const articleUrl = `${process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'}/articles/${articleId}`

      await sendEmail({
        to: student.email,
        template: 'articleReviewed',
        data: {
          articleTitle,
          articleUrl,
          approved,
        },
      })
    }
  } catch (error) {
    console.error('Error notifying about article review:', error)
  }
}

/**
 * Send notification to teacher when their review is evaluated by a mentor
 */
export const notifyReviewEvaluated = async ({
  payload,
  articleId,
  articleTitle,
  teacherId,
  rating,
}: {
  payload: Payload
  articleId: string
  articleTitle: string
  teacherId: string
  rating: number
}): Promise<void> => {
  try {
    // Determine notification type based on rating
    let type: NotificationType = 'info'
    if (rating >= 8) {
      type = 'success'
    } else if (rating <= 3) {
      type = 'warning'
    }

    // Create in-app notification
    await createNotification({
      payload,
      user: teacherId,
      message: `Your review of "${articleTitle}" has been evaluated by a mentor (${rating}/10)`,
      type,
      link: `/articles/${articleId}`,
    })

    // Find teacher to get email
    const teacher = await payload.findByID({
      collection: 'users',
      id: teacherId,
    })

    // Send email notification if teacher has an email and has enabled email notifications
    if (
      teacher &&
      teacher.email &&
      teacher.notificationPreferences?.emailNotifications &&
      (!teacher.notificationPreferences.emailTypes ||
        teacher.notificationPreferences.emailTypes.includes('reviewEvaluations'))
    ) {
      const articleUrl = `${process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'}/articles/${articleId}`

      await sendEmail({
        to: teacher.email,
        template: 'reviewEvaluated',
        data: {
          articleTitle,
          articleUrl,
          rating,
        },
      })
    }
  } catch (error) {
    console.error('Error notifying about review evaluation:', error)
  }
}
