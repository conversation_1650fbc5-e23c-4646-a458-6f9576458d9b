import Link from 'next/link'
import React, { useState } from 'react'

interface Notification {
  id: string
  message: string
  title?: string
  type: 'info' | 'success' | 'warning' | 'error' | 'points' | 'article-deleted' | 'report-actioned'
  link?: string
  read: boolean
  createdAt: string
  data?: {
    articleId?: string
    articleTitle?: string
    reason?: string
    reportId?: string
    [key: string]: any
  }
}

interface NotificationBellProps {
  notifications: Notification[]
  onMarkAsRead: (id: string) => void
  translateMessage?: (message: string) => string
}

export const NotificationBell: React.FC<NotificationBellProps> = ({
  notifications,
  onMarkAsRead,
  translateMessage
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const unreadCount = notifications.filter((n) => !n.read).length

  const toggleDropdown = () => {
    setIsOpen(!isOpen)
  }

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      onMarkAsRead(notification.id)
    }
    
    // Close dropdown first for better UX
    setIsOpen(false)
    
    // If the notification has a direct link, navigate to it
    if (notification.link) {
      // Let Link component handle this
      return
    }
    
    // If the notification has an articleId in data field, create a link to it
    if (notification.data?.articleId) {
      window.location.href = `/articles/${notification.data.articleId}`
    }
  }

  // Fallback translation function if none is provided
  const translateNotificationMessage = (message: string) => {
    if (translateMessage) {
      return translateMessage(message);
    }
    
    // Common English notification patterns and their Arabic translations
    const translations: Record<string, string> = {
      'Your article': 'تمت الموافقة على مقالك',
      'has been approved': 'تمت الموافقة',
      'has been rejected': 'تم رفض',
      'has been deleted': 'تم حذف',
      'New article': 'مقال جديد',
      'New comment': 'تعليق جديد',
      'Points awarded': 'تم منح نقاط',
      'Your profile': 'تم تحديث ملفك الشخصي',
      'has been updated': 'تم التحديث',
      'test article': 'مقال اختباري',
      'Test Article': 'مقال اختباري',
      'Success': 'نجاح',
      'Warning': 'تنبيه',
      'Error': 'خطأ',
      'Info': 'معلومات',
      'Points': 'نقاط',
      'New': 'جديد',
      'Notifications': 'الإشعارات',
      'No notifications': 'لا توجد إشعارات',
      'View all notifications': 'عرض جميع الإشعارات'
    }
    
    // Check for direct matches first
    if (translations[message]) {
      return translations[message]
    }
    
    // Check for partial matches and replace them
    let translatedMessage = message
    for (const [english, arabic] of Object.entries(translations)) {
      // Only replace if it's a standalone phrase or part of a phrase with quotes
      const regex = new RegExp(`(^|\\s|")(${english})($|\\s|")`, 'gi')
      translatedMessage = translatedMessage.replace(regex, `$1${arabic}$3`)
    }
    
    // If we detect an article title in quotes, keep the quotes but don't translate the title
    translatedMessage = translatedMessage.replace(
      /Your article "([^"]+)" has been (approved|rejected|deleted)/gi,
      (match, title, action) => {
        const arabicAction = translations[`has been ${action}`] || action
        return `${translations['Your article'] || 'مقالك'} "${title}" ${arabicAction}`
      }
    )
    
    return translatedMessage
  }

  // Get background color based on notification type
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-100 border-green-500'
      case 'warning':
        return 'bg-yellow-100 border-yellow-500'
      case 'error':
        return 'bg-red-100 border-red-500'
      case 'points':
        return 'bg-purple-100 border-purple-500'
      case 'article-deleted':
        return 'bg-red-100 border-red-500'
      case 'report-actioned':
        return 'bg-green-100 border-green-500'
      default:
        return 'bg-blue-100 border-blue-500'
    }
  }

  return (
    <div className="relative">
      <button
        onClick={toggleDropdown}
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none"
        aria-label="الإشعارات"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
          />
        </svg>
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
            {unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-50">
          <div className="py-2 px-3 bg-gray-100 border-b border-gray-200" dir="rtl">
            <h3 className="text-sm font-semibold text-gray-800">الإشعارات</h3>
          </div>
          <div className="max-h-96 overflow-y-auto" dir="rtl">
            {notifications.length === 0 ? (
              <div className="py-4 px-3 text-sm text-gray-500 text-center">
                لا توجد إشعارات
              </div>
            ) : (
              <div>
                {notifications.map((notification) => {
                  // Determine the appropriate href
                  const href = notification.link || 
                    (notification.data?.articleId ? `/articles/${notification.data.articleId}` : '#');
                  
                  return (
                    <div
                      key={notification.id}
                      className={`border-r-4 ${getTypeColor(
                        notification.type
                      )} ${!notification.read ? 'bg-gray-50' : ''}`}
                    >
                      {href !== '#' ? (
                        <Link
                          href={href}
                          className="block py-3 px-4 hover:bg-gray-100 text-right"
                          onClick={() => handleNotificationClick(notification)}
                        >
                          <p className="text-sm text-gray-800">
                            {translateNotificationMessage(notification.title || notification.message)}
                          </p>
                          {notification.title && (
                            <p className="text-sm text-gray-600">
                              {translateNotificationMessage(notification.message)}
                            </p>
                          )}
                          <p className="text-xs text-gray-500 mt-1">
                            {new Date(notification.createdAt).toLocaleString('ar', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit',
                              calendar: 'gregory'
                            })}
                          </p>
                        </Link>
                      ) : (
                        <div
                          className="block py-3 px-4 hover:bg-gray-100 cursor-pointer text-right"
                          onClick={() => handleNotificationClick(notification)}
                        >
                          <p className="text-sm text-gray-800">
                            {translateNotificationMessage(notification.title || notification.message)}
                          </p>
                          {notification.title && (
                            <p className="text-sm text-gray-600">
                              {translateNotificationMessage(notification.message)}
                            </p>
                          )}
                          <p className="text-xs text-gray-500 mt-1">
                            {new Date(notification.createdAt).toLocaleString('ar', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit',
                              calendar: 'gregory'
                            })}
                          </p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
          <div className="py-2 px-3 bg-gray-100 border-t border-gray-200 text-xs text-center" dir="rtl">
            <Link href="/notifications" className="text-blue-600 hover:underline">
              عرض جميع الإشعارات
            </Link>
          </div>
        </div>
      )}
    </div>
  )
}
