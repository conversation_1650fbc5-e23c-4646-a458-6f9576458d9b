import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      const { contentId, contentType, reason, details } = body

      if (!contentId) {
        return NextResponse.json({ error: 'Content ID is required' }, { status: 400 })
      }

      if (!contentType || !['article', 'news', 'comment', 'user'].includes(contentType)) {
        return NextResponse.json({ error: 'Valid content type is required' }, { status: 400 })
      }

      if (!reason) {
        return NextResponse.json({ error: 'Reason for escalation is required' }, { status: 400 })
      }

      // Try to use MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the user from MongoDB
        const mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })

        if (mongoUser) {
          // Check if user is a school admin
          const userRole = typeof mongoUser.role === 'object' ? mongoUser.role.slug : mongoUser.role
          if (userRole !== 'school-admin') {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get school ID
          const schoolId =
            typeof mongoUser.school === 'object' ? mongoUser.school.id : mongoUser.school

          if (!schoolId) {
            return NextResponse.json(
              { error: 'School admin must be associated with a school' },
              { status: 400 },
            )
          }

          // Verify the content exists and belongs to the school
          let content
          let contentCollection = contentType === 'user' ? 'users' : contentType + 's'

          if (contentType === 'article' || contentType === 'news') {
            content = await db.collection(contentCollection).findOne({
              _id: new ObjectId(contentId),
              'author.school.id': schoolId,
            })
          } else if (contentType === 'user') {
            content = await db.collection(contentCollection).findOne({
              _id: new ObjectId(contentId),
              'school.id': schoolId,
            })
          } else {
            content = await db.collection(contentCollection).findOne({
              _id: new ObjectId(contentId),
              schoolId: schoolId,
            })
          }

          if (!content) {
            return NextResponse.json(
              {
                error: `${contentType} not found or does not belong to your school`,
              },
              { status: 404 },
            )
          }

          // Create the escalation
          const escalation = {
            contentId,
            contentType,
            reason,
            details: details || '',
            reporterId: userId,
            reporterName: `${mongoUser.firstName} ${mongoUser.lastName}`,
            reporterRole: userRole,
            schoolId,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }

          const result = await db.collection('escalations').insertOne(escalation)

          // Flag the content as escalated
          await db
            .collection(contentCollection)
            .updateOne(
              { _id: new ObjectId(contentId) },
              { $set: { isEscalated: true, escalationId: result.insertedId.toString() } },
            )

          // Create a notification for super admins
          const superAdmins = await db
            .collection('users')
            .find({
              'role.slug': 'super-admin',
            })
            .toArray()

          for (const admin of superAdmins) {
            await db.collection('notifications').insertOne({
              userId: admin._id.toString(),
              type: 'escalation',
              title: `New ${contentType} escalation`,
              message: `A ${contentType} has been escalated by ${mongoUser.firstName} ${mongoUser.lastName} from ${content.school?.name || 'Unknown School'}`,
              isRead: false,
              createdAt: new Date().toISOString(),
              data: {
                escalationId: result.insertedId.toString(),
                contentId,
                contentType,
              },
            })
          }

          // Log the escalation activity
          await db.collection('activities').insertOne({
            userId: userId,
            userName: `${mongoUser.firstName} ${mongoUser.lastName}`,
            userRole: userRole,
            schoolId: schoolId,
            activityType: 'content-escalation',
            description: `Escalated ${contentType} for super-admin review`,
            date: new Date().toISOString(),
            details: {
              contentId,
              contentType,
              reason,
            },
          })

          return NextResponse.json({
            success: true,
            message: `Successfully escalated ${contentType} for super-admin review`,
            escalationId: result.insertedId.toString(),
          })
        }
      } catch (mongoError) {
        console.warn('Error escalating content with MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user is a school admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'school-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get school ID
      const schoolId = typeof user.school === 'object' ? user.school?.id : user.school

      if (!schoolId) {
        return NextResponse.json(
          { error: 'School admin must be associated with a school' },
          { status: 400 },
        )
      }

      // Verify the content exists and belongs to the school
      let content
      let contentCollection = contentType === 'user' ? 'users' : contentType + 's'

      try {
        content = await payload.findByID({
          collection: contentCollection,
          id: contentId,
          depth: 1,
        })

        // Check if content belongs to the school
        let contentSchoolId

        if (contentType === 'article' || contentType === 'news') {
          contentSchoolId =
            typeof content.author?.school === 'object'
              ? content.author?.school?.id
              : content.author?.school
        } else if (contentType === 'user') {
          contentSchoolId = typeof content.school === 'object' ? content.school?.id : content.school
        } else {
          contentSchoolId = content.schoolId
        }

        if (contentSchoolId !== schoolId) {
          return NextResponse.json(
            {
              error: `${contentType} does not belong to your school`,
            },
            { status: 403 },
          )
        }
      } catch (error) {
        return NextResponse.json({ error: `${contentType} not found` }, { status: 404 })
      }

      // Create the escalation
      const escalation = await payload.create({
        collection: 'escalations',
        data: {
          contentId,
          contentType,
          reason,
          details: details || '',
          reporterId: userId,
          reporterName: `${user.firstName} ${user.lastName}`,
          reporterRole: role,
          schoolId,
          status: 'pending',
        },
      })

      // Flag the content as escalated
      await payload.update({
        collection: contentCollection,
        id: contentId,
        data: {
          isEscalated: true,
          escalationId: escalation.id,
        },
      })

      // Create a notification for super admins
      const superAdmins = await payload.find({
        collection: 'users',
        where: {
          role: { equals: 'super-admin' },
        },
      })

      for (const admin of superAdmins.docs) {
        await payload.create({
          collection: 'notifications',
          data: {
            userId: admin.id,
            type: 'escalation',
            title: `New ${contentType} escalation`,
            message: `A ${contentType} has been escalated by ${user.firstName} ${user.lastName} from ${content.school?.name || 'Unknown School'}`,
            isRead: false,
            data: {
              escalationId: escalation.id,
              contentId,
              contentType,
            },
          },
        })
      }

      // Log the escalation activity
      await payload.create({
        collection: 'activities',
        data: {
          userId: userId,
          userName: `${user.firstName} ${user.lastName}`,
          userRole: role,
          schoolId: schoolId,
          activityType: 'content-escalation',
          description: `Escalated ${contentType} for super-admin review`,
          date: new Date().toISOString(),
          details: {
            contentId,
            contentType,
            reason,
          },
        },
      })

      return NextResponse.json({
        success: true,
        message: `Successfully escalated ${contentType} for super-admin review`,
        escalationId: escalation.id,
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error escalating content:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
