import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

// GET dashboard data based on user role
export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      
      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      
      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
      
      // Get the payload instance
      const payload = await getPayload({ config })
      
      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })
      
      // Determine user role
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      
      // Get school ID if applicable
      const schoolId = typeof user.school === 'object' ? user.school?.id : user.school
      
      // Prepare response based on user role
      let dashboardData = {}
      
      // Admin dashboard data
      if (role === 'super-admin' || role === 'school-admin') {
        // Get counts
        const usersCount = await payload.find({
          collection: 'users',
          limit: 0,
        })
        
        const articlesCount = await payload.find({
          collection: 'articles',
          limit: 0,
        })
        
        const newsCount = await payload.find({
          collection: 'news',
          limit: 0,
        })
        
        const schoolsCount = await payload.find({
          collection: 'schools',
          limit: 0,
        })
        
        const activitiesCount = await payload.find({
          collection: 'activities',
          limit: 0,
        })
        
        // Get recent activities
        const recentActivities = await payload.find({
          collection: 'activities',
          limit: 5,
          sort: '-createdAt',
          ...(role === 'school-admin' && schoolId ? { where: { schoolId: { equals: schoolId } } } : {}),
        })
        
        // Get recent schools
        const recentSchools = await payload.find({
          collection: 'schools',
          limit: 5,
          sort: '-createdAt',
        })
        
        dashboardData = {
          stats: {
            users: usersCount.totalDocs,
            articles: articlesCount.totalDocs,
            news: newsCount.totalDocs,
            schools: schoolsCount.totalDocs,
            activities: activitiesCount.totalDocs,
          },
          recentActivities: recentActivities.docs,
          recentSchools: recentSchools.docs,
        }
      }
      
      // Teacher dashboard data
      else if (role === 'teacher') {
        // Get pending student approvals
        const pendingStudents = await payload.find({
          collection: 'users',
          where: {
            role: { equals: 'student' },
            school: { equals: schoolId },
            status: { equals: 'pending' },
          },
          limit: 5,
        })
        
        // Get pending article reviews
        const pendingArticles = await payload.find({
          collection: 'articles',
          where: {
            status: { equals: 'pending-review' },
            'author.school': { equals: schoolId },
          },
          limit: 5,
        })
        
        // Get teacher stats
        const completedReviews = await payload.find({
          collection: 'activities',
          where: {
            userId: { equals: userId },
            activityType: { equals: 'article-review' },
          },
          limit: 0,
        })
        
        const totalStudents = await payload.find({
          collection: 'users',
          where: {
            role: { equals: 'student' },
            school: { equals: schoolId },
            status: { equals: 'active' },
          },
          limit: 0,
        })
        
        dashboardData = {
          stats: {
            pendingApprovals: pendingStudents.totalDocs,
            pendingReviews: pendingArticles.totalDocs,
            completedReviews: completedReviews.totalDocs,
            totalStudents: totalStudents.totalDocs,
            points: user.points || 0,
          },
          pendingStudents: pendingStudents.docs,
          pendingArticles: pendingArticles.docs,
        }
      }
      
      // Mentor dashboard data
      else if (role === 'mentor') {
        // Get teachers in the school
        const teachers = await payload.find({
          collection: 'users',
          where: {
            role: { equals: 'teacher' },
            school: { equals: schoolId },
          },
          limit: 5,
        })
        
        // Get teacher activities
        const teacherActivities = await payload.find({
          collection: 'activities',
          where: {
            schoolId: { equals: schoolId },
            userRole: { equals: 'teacher' },
          },
          limit: 5,
          sort: '-createdAt',
        })
        
        // Get mentor stats
        const newsCreated = await payload.find({
          collection: 'news',
          where: {
            'author.id': { equals: userId },
          },
          limit: 0,
        })
        
        const totalTeacherActivities = await payload.find({
          collection: 'activities',
          where: {
            schoolId: { equals: schoolId },
            userRole: { equals: 'teacher' },
          },
          limit: 0,
        })
        
        dashboardData = {
          stats: {
            totalTeachers: teachers.totalDocs,
            teacherActivities: totalTeacherActivities.totalDocs,
            newsCreated: newsCreated.totalDocs,
            points: user.points || 0,
          },
          teachers: teachers.docs,
          recentActivities: teacherActivities.docs,
        }
      }
      
      // Student dashboard data
      else if (role === 'student') {
        // Get student articles
        const articles = await payload.find({
          collection: 'articles',
          where: {
            'author.id': { equals: userId },
          },
          limit: 5,
          sort: '-createdAt',
        })
        
        // Get article stats
        const publishedArticles = await payload.find({
          collection: 'articles',
          where: {
            'author.id': { equals: userId },
            status: { equals: 'published' },
          },
          limit: 0,
        })
        
        const draftArticles = await payload.find({
          collection: 'articles',
          where: {
            'author.id': { equals: userId },
            status: { equals: 'draft' },
          },
          limit: 0,
        })
        
        const pendingArticles = await payload.find({
          collection: 'articles',
          where: {
            'author.id': { equals: userId },
            status: { equals: 'pending-review' },
          },
          limit: 0,
        })
        
        // Get recent reviews
        const articlesWithReviews = await payload.find({
          collection: 'articles',
          where: {
            'author.id': { equals: userId },
            'teacherReview.0': { exists: true },
          },
          limit: 5,
          depth: 2,
        })
        
        // Format reviews
        const recentReviews = []
        for (const article of articlesWithReviews.docs) {
          if (article.teacherReview && article.teacherReview.length > 0) {
            for (const review of article.teacherReview) {
              recentReviews.push({
                articleId: article.id,
                articleTitle: article.title,
                review,
              })
            }
          }
        }
        
        // Sort reviews by date
        recentReviews.sort((a, b) => {
          return new Date(b.review.createdAt).getTime() - new Date(a.review.createdAt).getTime()
        })
        
        dashboardData = {
          stats: {
            totalArticles: articles.totalDocs,
            publishedArticles: publishedArticles.totalDocs,
            draftArticles: draftArticles.totalDocs,
            pendingArticles: pendingArticles.totalDocs,
            points: user.points || 0,
          },
          recentArticles: articles.docs,
          recentReviews: recentReviews.slice(0, 5),
        }
      }
      
      return NextResponse.json(dashboardData)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching dashboard data:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
