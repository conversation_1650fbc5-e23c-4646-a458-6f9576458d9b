'use client'

import { usePathname } from 'next/navigation'
import React from 'react'

import { ResponsiveNavigation } from './ResponsiveNavigation'

type NewsPageNavigationProps = {
  user: any
  adminRoute: string
}

export const NewsPageNavigation: React.FC<NewsPageNavigationProps> = ({ user, adminRoute }) => {
  const pathname = usePathname()
  
  // Check if we're on the news page
  const isNewsPage = pathname === '/news' || pathname.startsWith('/news/')
  
  return (
    <ResponsiveNavigation 
      user={user} 
      adminRoute={adminRoute} 
      forceBlackText={isNewsPage}
    />
  )
} 