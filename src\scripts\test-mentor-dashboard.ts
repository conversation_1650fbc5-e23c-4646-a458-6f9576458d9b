/**
 * Test Mentor Dashboard Script
 *
 * This script tests the mentor dashboard functionality by:
 * 1. Creating a test mentor user
 * 2. Creating test data (teachers, students, articles, reviews)
 * 3. Testing the mentor dashboard API endpoints
 */

// Using CommonJS require instead of ES modules
const { getPayload } = require('payload')
const path = require('path')
// Resolve the config path relative to the current directory
const config = require(path.resolve('./src/payload.config'))

async function createTestMentor() {
  console.log('Creating test mentor...')

  const payload = await getPayload({ config })

  // Check if test mentor already exists
  const existingMentors = await payload.find({
    collection: 'users',
    where: {
      email: { equals: '<EMAIL>' },
    },
  })

  if (existingMentors.docs.length > 0) {
    console.log('Test mentor already exists')
    return existingMentors.docs[0]
  }

  // Get mentor role
  const mentorRole = await payload.find({
    collection: 'roles',
    where: {
      slug: { equals: 'mentor' },
    },
  })

  if (mentorRole.docs.length === 0) {
    throw new Error('Mentor role not found')
  }

  // Create a test school
  const school = await payload.create({
    collection: 'schools',
    data: {
      name: 'Test School',
      address: '123 Test St',
      city: 'Test City',
      state: 'Test State',
      zip: '12345',
      country: 'Test Country',
    },
  })

  // Create a test mentor
  const mentor = await payload.create({
    collection: 'users',
    data: {
      email: '<EMAIL>',
      password: 'Password123!',
      firstName: 'Test',
      lastName: 'Mentor',
      role: mentorRole.docs[0].id,
      school: school.id,
    },
  })

  console.log('Test mentor created')
  return mentor
}

async function createTestTeachers(schoolId: string, count: number = 3) {
  console.log(`Creating ${count} test teachers...`)

  const payload = await getPayload({ config })

  // Get teacher role
  const teacherRole = await payload.find({
    collection: 'roles',
    where: {
      slug: { equals: 'teacher' },
    },
  })

  if (teacherRole.docs.length === 0) {
    throw new Error('Teacher role not found')
  }

  const teachers = []

  for (let i = 0; i < count; i++) {
    // Check if test teacher already exists
    const existingTeachers = await payload.find({
      collection: 'users',
      where: {
        email: { equals: `test-teacher-${i}@example.com` },
      },
    })

    if (existingTeachers.docs.length > 0) {
      console.log(`Test teacher ${i} already exists`)
      teachers.push(existingTeachers.docs[0])
      continue
    }

    // Create a test teacher
    const teacher = await payload.create({
      collection: 'users',
      data: {
        email: `test-teacher-${i}@example.com`,
        password: 'Password123!',
        firstName: `Test`,
        lastName: `Teacher ${i}`,
        role: teacherRole.docs[0].id,
        school: schoolId,
      },
    })

    teachers.push(teacher)
  }

  console.log(`${teachers.length} test teachers created or found`)
  return teachers
}

async function createTestStudents(schoolId: string, count: number = 5) {
  console.log(`Creating ${count} test students...`)

  const payload = await getPayload({ config })

  // Get student role
  const studentRole = await payload.find({
    collection: 'roles',
    where: {
      slug: { equals: 'student' },
    },
  })

  if (studentRole.docs.length === 0) {
    throw new Error('Student role not found')
  }

  const students = []

  for (let i = 0; i < count; i++) {
    // Check if test student already exists
    const existingStudents = await payload.find({
      collection: 'users',
      where: {
        email: { equals: `test-student-${i}@example.com` },
      },
    })

    if (existingStudents.docs.length > 0) {
      console.log(`Test student ${i} already exists`)
      students.push(existingStudents.docs[0])
      continue
    }

    // Create a test student
    const student = await payload.create({
      collection: 'users',
      data: {
        email: `test-student-${i}@example.com`,
        password: 'Password123!',
        firstName: `Test`,
        lastName: `Student ${i}`,
        role: studentRole.docs[0].id,
        school: schoolId,
      },
    })

    students.push(student)
  }

  console.log(`${students.length} test students created or found`)
  return students
}

async function createTestArticles(students: any[], teachers: any[], count: number = 10) {
  console.log(`Creating ${count} test articles...`)

  const payload = await getPayload({ config })

  const articles = []

  for (let i = 0; i < count; i++) {
    // Select a random student as the author
    const student = students[Math.floor(Math.random() * students.length)]

    // Select a random teacher as the reviewer
    const teacher = teachers[Math.floor(Math.random() * teachers.length)]

    // Generate a random date in the last 6 months
    const date = new Date()
    date.setMonth(date.getMonth() - Math.floor(Math.random() * 6))

    // Check if test article already exists
    const existingArticles = await payload.find({
      collection: 'articles',
      where: {
        title: { equals: `Test Article ${i}` },
      },
    })

    if (existingArticles.docs.length > 0) {
      console.log(`Test article ${i} already exists`)
      articles.push(existingArticles.docs[0])
      continue
    }

    // Create a test article
    const article = await payload.create({
      collection: 'articles',
      data: {
        title: `Test Article ${i}`,
        content: `This is test article ${i}. It contains some content for testing purposes.`,
        author: student.id,
        status: Math.random() > 0.3 ? 'published' : 'draft',
        teacherReview: [
          {
            reviewer: teacher.id,
            comment: `This is a test review for article ${i}.`,
            approved: Math.random() > 0.3,
            rating: Math.floor(Math.random() * 10) + 1,
          },
        ],
        createdAt: date.toISOString(),
      },
    })

    articles.push(article)
  }

  console.log(`${articles.length} test articles created or found`)
  return articles
}

async function createTestActivities(users: any[], schoolId: string, count: number = 20) {
  console.log(`Creating ${count} test activities...`)

  const payload = await getPayload({ config })

  const activities = []

  const activityTypes = ['login', 'article-create', 'article-edit', 'article-review', 'article-publish']

  for (let i = 0; i < count; i++) {
    // Select a random user
    const user = users[Math.floor(Math.random() * users.length)]

    // Select a random activity type
    const activityType = activityTypes[Math.floor(Math.random() * activityTypes.length)]

    // Generate a random date in the last 30 days
    const date = new Date()
    date.setDate(date.getDate() - Math.floor(Math.random() * 30))

    // Create a test activity
    const activity = await payload.create({
      collection: 'activities',
      data: {
        userId: user.id,
        activityType,
        details: {
          description: `Test activity ${i}`,
        },
        school: schoolId,
        createdAt: date.toISOString(),
      },
    })

    activities.push(activity)
  }

  console.log(`${activities.length} test activities created`)
  return activities
}

async function testMentorDashboard() {
  try {
    console.log('Testing mentor dashboard...')

    // Create test data
    const mentor = await createTestMentor()
    const teachers = await createTestTeachers(mentor.school, 3)
    const students = await createTestStudents(mentor.school, 5)
    const articles = await createTestArticles(students, teachers, 10)
    const allUsers = [...teachers, ...students, mentor]
    const activities = await createTestActivities(allUsers, mentor.school, 20)

    console.log('Test data created successfully')
    console.log('Mentor dashboard should now have data to display')
    console.log('Login with the following credentials:')
    console.log('Email: <EMAIL>')
    console.log('Password: Password123!')

  } catch (error) {
    console.error('Error testing mentor dashboard:', error)
  }
}

// Run the test
testMentorDashboard()
