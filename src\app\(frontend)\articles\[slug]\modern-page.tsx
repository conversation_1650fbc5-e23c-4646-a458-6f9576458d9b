import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { RichText } from '@/components/RichText'
import { ClientShareButtons } from './ClientShareButtons'
import { ViewCounter } from './ViewCounter'

// Import fonts
import { Lora, Old_Standard_TT } from 'next/font/google'

// Import styles
import './article-styles.css'

const lora = Lora({ subsets: ['latin'], variable: '--font-lora' })
const oldStandard = Old_Standard_TT({
  weight: ['400'],
  subsets: ['latin'],
  variable: '--font-old-standard',
})

export default async function ModernArticleDetailPage({ params }: { params: { slug: string } }) {
  // Get the slug from params - await it first
  const resolvedParams = await params
  const { slug } = resolvedParams
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // Try multiple approaches to find the article
  let article

  // First try MongoDB directly
  try {
    console.log('Trying to find article in MongoDB directly...')
    const { db } = await import('@/lib/mongodb').then((mod) => mod.connectToDatabase())

    // Try to find by slug or ID
    const mongoArticle = await db.collection('articles').findOne({
      $or: [{ slug: slug }, { id: slug }],
    })

    if (mongoArticle) {
      console.log('Article found in MongoDB directly')
      article = mongoArticle
    }
  } catch (mongoError) {
    console.error('Error finding article in MongoDB:', mongoError)
  }

  // If not found in MongoDB, try Payload
  if (!article) {
    console.log('Article not found in MongoDB, trying Payload...')

    // Try to find by slug
    const articles = await payload.find({
      collection: 'articles',
      where: {
        or: [{ slug: { equals: slug } }, { id: { equals: slug } }],
      },
      depth: 2, // Populate relationships
    })

    article = articles.docs[0]

    // If still not found, try by ID as a last resort
    if (!article) {
      try {
        article = await payload.findByID({
          collection: 'articles',
          id: slug,
          depth: 2,
        })
      } catch (error) {
        console.error('Error finding article by ID:', error)
      }
    }
  }

  // If article is still not found after all attempts, return a simple message
  if (!article) {
    console.log('Article not found after all attempts')
    return (
      <div className="py-12 px-4 text-center">
        <div className="container mx-auto">
          <h1 className="text-3xl font-bold mb-4">Article Not Found</h1>
          <p className="mb-8">
            The article you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
          <Link href="/articles" className="text-blue-600 font-semibold hover:text-blue-800">
            ← Back to All Articles
          </Link>
        </div>
      </div>
    )
  }

  // Get article ID for view tracking
  const articleId = article.id || (article._id ? article._id.toString() : null)

  // Get author name
  const authorName = typeof article.author === 'object' ? article.author.email : 'Unknown Author'

  // Get school name
  const schoolName =
    typeof article.author === 'object' &&
    article.author.school &&
    typeof article.author.school === 'object'
      ? article.author.school.name
      : 'Unknown School'

  // Get teacher reviews
  const teacherReviews = article.teacherReview || []

  // Calculate average rating if there are reviews
  const averageRating =
    teacherReviews.length > 0
      ? teacherReviews.reduce((sum: any, review: { rating: any }) => sum + review.rating, 0) /
        teacherReviews.length
      : undefined

  // Check if user is logged in and their role
  const userRole = user?.role ? (typeof user.role === 'object' ? user.role.slug : user.role) : null

  // Determine if the user is the author of the article
  const isAuthor = user && typeof article.author === 'object' && user.id === article.author.id

  // Determine if the user should see teacher names
  // Only teachers, mentors, and admins should see teacher names, or the author of the article
  const canSeeTeacherNames =
    userRole === 'teacher' ||
    userRole === 'mentor' ||
    userRole === 'super-admin' ||
    userRole === 'school-admin' ||
    isAuthor

  // Get the article status for the floating indicator
  const articleStatus = article.status || 'draft'

  // Get the featured image URL or use a default
  const featuredImageUrl = article.featuredImage
    ? typeof article.featuredImage === 'object'
      ? article.featuredImage.url
      : article.featuredImage
    : 'https://images.unsplash.com/photo-1745500415839-503883982264?q=80&w=3987&auto=format&fit=crop&ixlib=rb-4.0.3'

  return (
    <div
      className={`${lora.variable} ${oldStandard.variable} bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 min-h-screen`}
    >
      {/* Hidden view counter component to track the view */}
      {articleId && <ViewCounter id={articleId} type="articles" />}
      
      {/* Profile Section (Featured Image) */}
      <div
        className="profile h-[50vh] bg-center bg-cover md:fixed md:inset-y-0 md:left-0 md:w-[40%] md:h-auto xl:w-[50%]"
        style={{ backgroundImage: `url('${featuredImageUrl}')` }}
      ></div>

      {/* Article Section */}
      <article className="md:ml-[40%] xl:ml-[50%] max-w-[66em] px-4">
        <header className="text-center mt-8">
          <h1 className="font-old-standard text-cyan-600 dark:text-cyan-400 uppercase">
            <div className="text-[19vw] leading-none md:text-[10.5vw]">
              {article.title
                .split(' ')
                .slice(0, Math.ceil(article.title.split(' ').length / 2))
                .join(' ')}
            </div>
            <div className="text-[14.5vw] leading-none md:text-[8vw]">
              {article.title
                .split(' ')
                .slice(Math.ceil(article.title.split(' ').length / 2))
                .join(' ')}
            </div>
          </h1>
          <p className="text-xl max-w-[20em] mx-auto mt-4">
            {article.summary || 'Article by ${authorName} from ${schoolName}'.replace("'", "'")}
          </p>
        </header>

        <div className="custom-hr my-20"></div>

        <section className="columns-1 gap-5 md:columns-2 md:px-12 font-lora">
          <div className="first-letter-style prose dark:prose-invert max-w-none">
            <RichText content={article.content} className="text-gray-800 dark:text-gray-200" />
          </div>

          <p className="mt-8">— {authorName}</p>

          {/* Share Buttons */}
          <ClientShareButtons
            title={article.title}
            url={`/articles/${article.slug}`}
            description={
              typeof article.summary === 'string'
                ? article.summary
                : `Article by ${authorName} from ${schoolName}`
            }
          />
        </section>

        {/* Back to Articles */}
        <div className="mt-8 pb-8 text-center">
          <Link
            href="/articles"
            className="text-cyan-600 dark:text-cyan-400 font-semibold hover:text-cyan-800 dark:hover:text-cyan-300"
          >
            ← Back to All Articles
          </Link>
        </div>
      </article>

      {/* Status Indicator for preview mode - moved to bottom */}
      {(articleStatus === 'draft' || articleStatus === 'pending-review') && (
        <div className="w-full bg-yellow-500 text-black py-4 px-4 text-center font-bold">
          This article is in {articleStatus === 'draft' ? 'DRAFT' : 'PENDING REVIEW'} mode and is
          not publicly visible
        </div>
      )}
    </div>
  )
}
