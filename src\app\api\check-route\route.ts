import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function GET(request: NextRequest) {
  const url = new URL(request.url)
  const routePath = url.searchParams.get('path')

  if (!routePath) {
    return NextResponse.json({ exists: false, error: 'No path provided' }, { status: 400 })
  }

  try {
    // Clean the path - remove leading slash and query parameters
    const cleanPath = routePath.startsWith('/') ? routePath.substring(1) : routePath
    const parts = cleanPath.split('?')[0].split('/')

    // Check for the app directory structure
    // For example, /pending-approval would be in src/app/pending-approval/page.tsx
    const basePath = path.join(process.cwd(), 'src', 'app')
    
    // Check different possible page patterns
    const possiblePaths = [
      path.join(basePath, ...parts, 'page.tsx'),
      path.join(basePath, ...parts, 'page.jsx'),
      path.join(basePath, ...parts, 'page.js'),
      path.join(basePath, ...parts, 'page.ts'),
      path.join(basePath, '(frontend)', ...parts, 'page.tsx'),
      path.join(basePath, '(frontend)', ...parts, 'page.jsx'),
      path.join(basePath, '(frontend)', ...parts, 'page.js'),
      path.join(basePath, '(frontend)', ...parts, 'page.ts'),
      path.join(basePath, '(auth)', ...parts, 'page.tsx'),
      path.join(basePath, '(auth)', ...parts, 'page.jsx'),
      path.join(basePath, '(auth)', ...parts, 'page.js'),
      path.join(basePath, '(auth)', ...parts, 'page.ts'),
    ]

    let exists = false
    for (const p of possiblePaths) {
      if (fs.existsSync(p)) {
        exists = true
        break
      }
    }

    return NextResponse.json({ exists, path: routePath })
  } catch (error) {
    console.error('Error checking route:', error)
    return NextResponse.json({ exists: false, error: 'Error checking route' }, { status: 500 })
  }
} 