import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'
import { addUserPoints, updateSchoolPoints } from '@/utils/points'

/**
 * API endpoint to add points to a user
 */
export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the token and get the user ID
    const { userId } = await verifyJWT(token)

    if (!userId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Parse request body
    const body = await req.json()
    const { targetUserId, type, points, description, reference } = body

    if (!targetUserId || !type || !points || !description) {
      return NextResponse.json(
        { error: 'Missing required fields: targetUserId, type, points, description' },
        { status: 400 }
      )
    }

    // Get the payload instance
    const payload = await getPayload({ config })
    
    // Get the current user to check permissions
    const currentUser = await payload.findByID({
      collection: 'users',
      id: userId,
    })

    // Verify user has permissions to add points
    const userRole = typeof currentUser.role === 'object' 
      ? currentUser.role?.slug 
      : currentUser.role
    
    // Only admins, mentors, or teachers can add points to users
    if (!['super-admin', 'school-admin', 'mentor', 'teacher'].includes(userRole)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get the target user
    const targetUser = await payload.findByID({
      collection: 'users',
      id: targetUserId,
    })

    if (!targetUser) {
      return NextResponse.json({ error: 'Target user not found' }, { status: 404 })
    }

    // Get user's school
    const schoolId = typeof targetUser.school === 'object' 
      ? targetUser.school?.id 
      : targetUser.school

    // Add points to the user
    const newTotalPoints = await addUserPoints({
      payload,
      userId: targetUserId,
      type,
      points,
      description,
      reference,
      createActivity: true,
      createNotification: true,
      schoolId,
    })

    // Update school points
    if (schoolId) {
      await updateSchoolPoints({ payload, schoolId })
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: `Added ${points} points to user`,
      userId: targetUserId,
      totalPoints: newTotalPoints,
    })
  } catch (error) {
    console.error('Error adding points to user:', error)
    return NextResponse.json(
      { 
        error: 'Internal Server Error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}