import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import config from '@/payload.config'

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params
    const payload = await getPayload({
      config,
    })

    // Get the current user
    const { user } = await payload.auth({ req })

    if (!user) {
      return NextResponse.json(
        { error: 'You must be logged in to update an article' },
        { status: 401 },
      )
    }

    // Check if user is a student
    const isStudent =
      typeof user.role === 'object' ? user.role?.slug === 'student' : user.role === 'student'

    if (!isStudent) {
      return NextResponse.json({ error: 'Only students can update articles' }, { status: 403 })
    }

    // Get the article
    const article = await payload.findByID({
      collection: 'articles',
      id,
    })

    // Check if the user is the author
    const isAuthor =
      article.author === user.id ||
      (typeof article.author === 'object' && article.author?.id === user.id)

    if (!isAuthor) {
      return NextResponse.json({ error: 'You can only update your own articles' }, { status: 403 })
    }

    // Check if article is editable (not published)
    if (article.status === 'published') {
      return NextResponse.json({ error: 'Published articles cannot be edited' }, { status: 400 })
    }

    const formData = await req.formData()
    const title = formData.get('title') as string
    const content = formData.get('content') as string
    const action = formData.get('action') as string

    // Validate inputs
    if (!title || !content) {
      return NextResponse.json({ error: 'Title and content are required' }, { status: 400 })
    }

    // Update the article
    const updateData = {
      title,
      content,
    }

    // If action is submit, update status to pending-review
    if (action === 'submit' && article.status === 'draft') {
      updateData.status = 'pending-review'
    }

    await payload.update({
      collection: 'articles',
      id,
      data: updateData,
    })

    // Redirect to the student dashboard
    return NextResponse.redirect(new URL('/dashboard/student', req.url))
  } catch (error) {
    console.error('Error updating article:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
