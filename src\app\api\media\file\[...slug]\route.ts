import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import path from 'path'
import fs from 'fs/promises'

/**
 * API endpoint to serve media files by filename or path
 * This endpoint serves files from the media directory or public/uploads
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  try {
    const { slug } = params
    
    if (!slug || !Array.isArray(slug) || slug.length === 0) {
      return NextResponse.json({ error: 'File path is required' }, { status: 400 })
    }
    
    // Reconstruct the file path from the slug array
    const filePath = slug.join('/')
    
    // Try to serve from media directory (Payload CMS default location)
    try {
      const mediaPath = path.join(process.cwd(), 'media', filePath)
      await fs.access(mediaPath)
      
      // Read the file
      const fileBuffer = await fs.readFile(mediaPath)
      
      // Determine MIME type based on file extension
      const ext = path.extname(filePath).toLowerCase()
      let contentType = 'application/octet-stream'
      
      if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg'
      else if (ext === '.png') contentType = 'image/png'
      else if (ext === '.gif') contentType = 'image/gif'
      else if (ext === '.webp') contentType = 'image/webp'
      else if (ext === '.svg') contentType = 'image/svg+xml'
      else if (ext === '.pdf') contentType = 'application/pdf'
      
      // Return the file with appropriate headers
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=31536000',
        },
      })
    } catch (mediaError) {
      console.log('File not found in media directory, trying uploads directory')
    }
    
    // Try to serve from public/uploads directory
    try {
      const uploadsPath = path.join(process.cwd(), 'public', 'uploads', filePath)
      await fs.access(uploadsPath)
      
      // Read the file
      const fileBuffer = await fs.readFile(uploadsPath)
      
      // Determine MIME type based on file extension
      const ext = path.extname(filePath).toLowerCase()
      let contentType = 'application/octet-stream'
      
      if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg'
      else if (ext === '.png') contentType = 'image/png'
      else if (ext === '.gif') contentType = 'image/gif'
      else if (ext === '.webp') contentType = 'image/webp'
      else if (ext === '.svg') contentType = 'image/svg+xml'
      else if (ext === '.pdf') contentType = 'application/pdf'
      
      // Return the file with appropriate headers
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=31536000',
        },
      })
    } catch (uploadsError) {
      console.error('File not found in uploads directory')
    }
    
    // If the file isn't an ID or path, it might be a filename in the media collection
    // Try to look it up in the database
    try {
      const payload = await getPayload({ config })
      
      const mediaDoc = await payload.find({
        collection: 'media',
        where: {
          filename: {
            equals: filePath,
          },
        },
        limit: 1,
      })
      
      if (mediaDoc.docs.length > 0 && mediaDoc.docs[0].url) {
        // If we found the media and it has a URL, redirect to it
        return NextResponse.redirect(new URL(mediaDoc.docs[0].url, request.url))
      }
    } catch (dbError) {
      console.error('Error looking up file in database:', dbError)
    }
    
    // If we've reached this point, we couldn't find the file
    return NextResponse.json(
      { error: 'File not found' }, 
      { status: 404 }
    )
  } catch (error) {
    console.error('Error serving media file:', error)
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    )
  }
} 