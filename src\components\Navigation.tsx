import Link from 'next/link'
import React from 'react'

import { ClientNotificationBell } from './ClientNotificationBell'
import { NotificationProvider } from './NotificationProvider'
import { UserMenu } from './UserMenu'

type NavigationProps = {
  user: any
  adminRoute: string
}

export const Navigation: React.FC<NavigationProps> = ({ user, adminRoute }) => {
  return (
    <nav className="bg-blue-600 text-black p-4 shadow-md">
      <div className="container mx-auto flex justify-between items-center">
        <div className="text-xl font-bold">Young Reporter</div>
        <div className="flex space-x-6">
          <Link href="/">Home</Link>
          <Link href="/articles">Articles</Link>
          <Link href="/news">News</Link>
          <Link href="/statistics">Statistics</Link>
          <Link href="/about">About</Link>
          <Link href="/contact">Contact</Link>
          <UserMenu />
        </div>
      </div>
    </nav>
  )
}
