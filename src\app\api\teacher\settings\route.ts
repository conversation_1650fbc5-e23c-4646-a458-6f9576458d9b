import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

// GET teacher settings
export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user is a teacher
      const role = typeof user.role === 'object' ? user.role?.slug : user.role

      if (role !== 'teacher') {
        return NextResponse.json(
          {
            error: 'Forbidden',
            message: 'Only teachers can access this endpoint',
            userRole: role,
          },
          { status: 403 },
        )
      }

      // Extract settings from user
      const settings = {
        profile: {
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email || '',
          bio: user.bio || '',
          profileImage: user.profileImage || null,
          pendingProfileImage: user.pendingProfileImage || null,
          profileImageStatus: user.profileImageStatus || 'none',
          pendingFirstName: user.pendingFirstName || '',
          pendingLastName: user.pendingLastName || '',
          nameChangeStatus: user.nameChangeStatus || 'none',
        },
        notifications: {
          emailNotifications: user.notificationPreferences?.emailNotifications || false,
          articleSubmissions: user.notificationPreferences?.emailTypes?.includes('articleSubmissions') || false,
          articleReviews: user.notificationPreferences?.emailTypes?.includes('articleReviews') || false,
          reviewEvaluations: user.notificationPreferences?.emailTypes?.includes('reviewEvaluations') || false,
          systemAnnouncements: user.notificationPreferences?.emailTypes?.includes('systemAnnouncements') || false,
        },
        preferences: {
          showCompletedTasks: user.preferences?.showCompletedTasks !== false, // Default to true
          autoApproveStudents: user.preferences?.autoApproveStudents || false,
          reviewNotificationThreshold: user.preferences?.reviewNotificationThreshold || 5,
        },
      }

      return NextResponse.json({
        success: true,
        settings,
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching teacher settings:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// UPDATE teacher settings
export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      console.log('Request body:', body)

      const { profile, notifications, preferences } = body

      // Initialize Payload
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      // Verify user is a teacher
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role

      if (userRole !== 'teacher') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Build update data
      const updateData: Record<string, any> = {}

      // Handle profile updates
      if (profile) {
        // For teachers, name changes require approval from school admin
        if (profile.firstName && profile.firstName !== user.firstName) {
          updateData.pendingFirstName = profile.firstName
          updateData.nameChangeStatus = 'pending'
        }

        if (profile.lastName && profile.lastName !== user.lastName) {
          updateData.pendingLastName = profile.lastName
          updateData.nameChangeStatus = 'pending'
        }

        // Bio can be updated directly
        if (profile.bio !== undefined) {
          updateData.bio = profile.bio
        }

        // Profile image is handled separately through the ProfileImageUpload component
      }

      // Handle notification preferences
      if (notifications) {
        const emailTypes = []
        if (notifications.articleSubmissions) emailTypes.push('articleSubmissions')
        if (notifications.articleReviews) emailTypes.push('articleReviews')
        if (notifications.reviewEvaluations) emailTypes.push('reviewEvaluations')
        if (notifications.systemAnnouncements) emailTypes.push('systemAnnouncements')

        updateData.notificationPreferences = {
          emailNotifications: notifications.emailNotifications,
          emailTypes,
        }
      }

      // Handle teacher preferences
      if (preferences) {
        updateData.preferences = {
          ...(user.preferences || {}),
          ...preferences,
        }
      }

      // Include role to avoid validation errors
      if (user.role) {
        if (typeof user.role === 'object' && user.role.id) {
          updateData.role = user.role.id
        } else if (typeof user.role === 'string') {
          updateData.role = user.role
        }
      }

      // Include school to avoid validation errors
      if (user.school) {
        if (typeof user.school === 'object' && user.school.id) {
          updateData.school = user.school.id
        } else if (typeof user.school === 'string') {
          updateData.school = user.school
        }
      }

      console.log('Updating teacher with data:', updateData)

      // Update the user
      try {
        const updatedUser = await payload.update({
          collection: 'users',
          id: userId,
          data: updateData,
        })

        console.log('Teacher updated successfully')

        // If name change is pending, notify school admins
        if (updateData.nameChangeStatus === 'pending') {
          const schoolId = typeof user.school === 'object' ? user.school.id : user.school
          
          if (schoolId) {
            // Find school admins
            const schoolAdmins = await payload.find({
              collection: 'users',
              where: {
                'role.slug': { equals: 'school-admin' },
                school: { equals: schoolId },
              },
            })

            // Create notifications for each school admin
            for (const admin of schoolAdmins.docs) {
              await payload.create({
                collection: 'notifications',
                data: {
                  user: admin.id,
                  message: `Teacher ${user.firstName} ${user.lastName} has requested a name change`,
                  type: 'name-change-request',
                  read: false,
                  details: {
                    userId: userId,
                    currentName: `${user.firstName} ${user.lastName}`,
                    requestedName: `${updateData.pendingFirstName || user.firstName} ${updateData.pendingLastName || user.lastName}`,
                  },
                },
              })
            }
          }
        }

        return NextResponse.json({
          success: true,
          message: 'Settings updated successfully',
        })
      } catch (updateError) {
        console.error('Error updating teacher:', updateError)
        return NextResponse.json({ error: 'Failed to update settings' }, { status: 500 })
      }
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating teacher settings:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
