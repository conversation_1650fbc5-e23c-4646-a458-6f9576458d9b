import { notFound } from 'next/navigation'
import { cookies } from 'next/headers';

async function getSchool(id: string) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  const cookieHeader = cookies().toString();
  const res = await fetch(`${baseUrl}/api/dashboard/super-admin/schools/${id}`, {
    cache: 'no-store',
    headers: {
      cookie: cookieHeader,
    },
  });
  if (!res.ok) return null;
  return res.json();
}

export default async function SchoolDetailPage({ params }: { params: { id: string } }) {
  const school = await getSchool(params.id)
  if (!school) return notFound()

  return (
    <div className="max-w-2xl mx-auto py-8">
      <h1 className="text-2xl font-bold mb-4">{school.name}</h1>
      <div className="space-y-2">
        <div><strong>Address:</strong> {school.address}</div>
        <div><strong>Created At:</strong> {school.createdAt ? new Date(school.createdAt).toLocaleString() : '-'}</div>
        <div><strong>Updated At:</strong> {school.updatedAt ? new Date(school.updatedAt).toLocaleString() : '-'}</div>
        {/* Add more school details as needed */}
      </div>
    </div>
  )
} 