import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { RichText } from '@/components/RichText'
import { ClientShareButtons } from './ClientShareButtons'
import CurvedHeaderClient from './CurvedHeaderClient'
import { getArticleId } from '@/lib/id-utils'
import {
  isMediaPath,
  isMediaPathError,
  logMediaPathError,
  safeMediaQuery,
  safePayloadResponse,
} from '@/lib/media-utils'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'
import { ClientImage } from '@/components/ClientImage'
import { getImageUrl, getImageAlt } from '@/utils/imageUtils'
import { ViewCounter } from './ViewCounter'

// Import styles
import './curved-header.css'

interface TeacherReview {
  reviewer:
    | string
    | {
        email: any
        id: string
        firstName: string
        lastName: string
      }
  reviewerName?: string
  comment: string
  rating: number
  approved: boolean
  reviewDate?: string
}

interface Article {
  id: string
  title: string
  content: Record<string, unknown> // Rich text content from Payload CMS
  author:
    | {
        id: string
        firstName: string
        lastName: string
        profileImage?:
          | {
              url: string
            }
          | string
      }
    | string
  category?: string
  createdAt: string
  status: string
  featuredImage?:
    | {
        url: string
        width?: number
        height?: number
        alt?: string
      }
    | string
  teacherReview?: TeacherReview[] // Use the new interface
}

export default async function CurvedArticleDetailPage({ params }: { params: { slug: string } }) {
  // Get the slug from params - await it first
  const resolvedParams = await params
  const { slug } = resolvedParams
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // Try multiple approaches to find the article
  let article

  // First check if the slug is a media path - if so, skip all lookups
  if (
    slug.includes('/api/media') ||
    slug.includes('/media') ||
    slug.includes('/file/') ||
    /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(slug)
  ) {
    console.log('Slug is a media path, skipping article lookup:', slug)
    // article will remain null
  } else {
    // First try using our utility function which has better error handling
    try {
      console.log('Trying to find article using article-utils...')
      const { findArticleByIdOrSlug } = await import('@/lib/article-utils')
      const foundArticle = await findArticleByIdOrSlug(slug)

      if (foundArticle) {
        console.log('Article found using article-utils')
        article = foundArticle
      }
    } catch (utilsError) {
      console.error('Error finding article using article-utils:', utilsError)
    }

    // If not found with article-utils, try MongoDB directly
    if (!article) {
      try {
        console.log('Trying to find article in MongoDB directly...')
        const { db } = await import('@/lib/mongodb').then((mod) => mod.connectToDatabase())

        // Try to find by slug or ID
        const mongoArticle = await db.collection('articles').findOne({
          $or: [{ slug: slug }, { id: slug }],
        })

        if (mongoArticle) {
          console.log('Article found in MongoDB directly')
          article = mongoArticle
        }
      } catch (mongoError) {
        console.error('Error finding article in MongoDB:', mongoError)
      }
    }

    // If not found in MongoDB, try Payload
    if (!article) {
      console.log('Article not found in MongoDB, trying Payload...')

      // Apply safe media query to prevent ObjectId casting errors
      const safeQuery = safeMediaQuery({
        or: [{ slug: { equals: slug } }, { id: { equals: slug } }],
      })

      try {
        // Try to find by slug with safe query
        const articlesResponse = await payload.find({
          collection: 'articles',
          where: safeQuery,
          depth: 2, // Populate relationships
        })

        // Process the response to filter out any media paths
        const safeArticlesResponse = safePayloadResponse(articlesResponse)
        article = safeArticlesResponse.docs[0]
      } catch (findError) {
        // Check if this is a media path error
        if (isMediaPathError(findError)) {
          logMediaPathError(findError, 'article detail page')
          console.log('Media path error in article detail page, trying MongoDB directly')

          // Try MongoDB directly as a fallback
          try {
            const { db } = await connectToDatabase()
            const mongoArticle = await db.collection('articles').findOne({
              $or: [
                { slug: slug },
                { id: slug },
                // Only try ObjectId if it's a valid format and not a media path
                ...(slug.match(/^[0-9a-fA-F]{24}$/) && !isMediaPath(slug)
                  ? [{ _id: new ObjectId(slug) }]
                  : []),
              ],
            })

            if (mongoArticle) {
              console.log('Article found in MongoDB after media path error')
              article = mongoArticle
            }
          } catch (mongoError) {
            console.error('Error with MongoDB fallback:', mongoError)
          }
        } else {
          console.error('Error finding article:', findError)
        }
      }

      // If still not found, try by ID as a last resort
      if (!article) {
        try {
          // Check if the slug looks like a valid ObjectId before trying findByID
          if (slug && /^[0-9a-fA-F]{24}$/.test(slug) && !isMediaPath(slug)) {
            try {
              article = await payload.findByID({
                collection: 'articles',
                id: slug,
                depth: 2,
              })
            } catch (idError) {
              // Check if this is a media path error
              if (isMediaPathError(idError)) {
                logMediaPathError(idError, 'article detail page findByID')
              } else {
                console.error('Error finding article by ID:', idError)
              }
            }
          } else {
            console.log('Slug is not a valid ObjectId or is a media path:', slug)
          }
        } catch (error) {
          console.error('Error in findByID block:', error)
        }
      }
    }
  }

  // Special case for generated IDs from title slugs
  if (!article && slug.includes('-')) {
    console.log('Trying to find article by title slug:', slug)

    try {
      // Try to find by title that might match the slug
      const { db } = await connectToDatabase()

      // First try an exact match on the slug field
      const articleBySlug = await db.collection('articles').findOne({
        slug: slug,
      })

      if (articleBySlug) {
        console.log('Found article by exact slug match')
        article = articleBySlug
      } else {
        // Create a regex pattern that would match titles that could generate this slug
        // Convert hyphens back to possible spaces or special characters
        const titlePattern = slug.replace(/-/g, '[\\s-]')

        const articleByTitle = await db.collection('articles').findOne({
          title: { $regex: titlePattern, $options: 'i' },
        })

        if (articleByTitle) {
          console.log('Found article by matching title pattern')
          article = articleByTitle
        } else {
          // Try a more flexible approach - look for articles with similar words in the title
          const words = slug.split('-').filter((word) => word.length > 3)

          if (words.length > 0) {
            console.log('Trying to find article by matching words in title:', words)

            // Create a query that matches any of the significant words
            const wordQueries = words.map((word) => ({
              title: { $regex: word, $options: 'i' },
            }))

            const articlesByWords = await db
              .collection('articles')
              .find({ $or: wordQueries })
              .limit(1)
              .toArray()

            if (articlesByWords.length > 0) {
              console.log('Found article by matching words in title')
              article = articlesByWords[0]
            }
          }
        }
      }
    } catch (titleError) {
      console.error('Error finding article by title pattern:', titleError)
    }
  }

  // If article is still not found after all attempts, return a simple message
  if (!article) {
    console.log('Article not found after all attempts for slug:', slug)
    return (
      <div className="py-12 px-4 text-center">
        <div className="container mx-auto">
          <h1 className="text-3xl font-bold mb-4">Article Not Found</h1>
          <p className="mb-8">
            The article you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
          <p className="mb-4 text-gray-600">Requested article ID or slug: {slug}</p>
          <Link href="/articles" className="text-blue-600 font-semibold hover:text-blue-800">
            ← Back to All Articles
          </Link>
        </div>
      </div>
    )
  }

  // Get article ID for view tracking
  const articleId = article.id || (article._id ? article._id.toString() : null)

  // Get author name
  const authorName = typeof article.author === 'object' ? article.author.email : 'Unknown Author'

  // Get school name
  const schoolName =
    typeof article.author === 'object' &&
    article.author.school &&
    typeof article.author.school === 'object'
      ? article.author.school.name
      : 'Unknown School'

  // Get teacher reviews
  const teacherReviews = article.teacherReview || []

  // Calculate average rating if there are reviews
  const averageRating =
    teacherReviews.length > 0
      ? teacherReviews.reduce((sum: number, review: TeacherReview) => sum + review.rating, 0) /
        teacherReviews.length
      : undefined

  // Check if user is logged in and their role
  const userRole = user?.role ? (typeof user.role === 'object' ? user.role.slug : user.role) : null

  // Determine if the user is the author of the article
  const isAuthor = user && typeof article.author === 'object' && user.id === article.author.id

  // Determine if the user should see teacher names
  // Only teachers, mentors, and admins should see teacher names, or the author of the article
  const canSeeTeacherNames =
    userRole === 'teacher' ||
    userRole === 'mentor' ||
    userRole === 'super-admin' ||
    userRole === 'school-admin' ||
    isAuthor

  // Get the article status for the floating indicator
  const articleStatus = article.status || 'draft'

  // Log the raw featuredImage for debugging
  console.log('Raw featuredImage:', article.featuredImage)

  // Handle different types of featuredImage
  let imageToUse = article.featuredImage

  // If it's a media path string, use it directly
  if (article.featuredImage && typeof article.featuredImage === 'string' && isMediaPath(article.featuredImage)) {
    console.log('Article has media path as featuredImage, using it directly:', article.featuredImage)
    imageToUse = article.featuredImage
  }

  // If it's a MongoDB ID string, try to construct a media URL
  if (article.featuredImage && typeof article.featuredImage === 'string' &&
      /^[0-9a-fA-F]{24}$/.test(article.featuredImage) && !isMediaPath(article.featuredImage)) {
    console.log('Article has MongoDB ID as featuredImage, constructing URL')
    imageToUse = { url: `/api/media/${article.featuredImage}` }
  }

  // Get the featured image URL using the utility function
  const featuredImageUrl = getImageUrl(
    imageToUse, // Pass the processed featuredImage - getImageUrl will handle all cases
    'https://images.unsplash.com/photo-1745500415839-503883982264?q=80&w=3987&auto=format&fit=crop&ixlib=rb-4.0.3',
  )

  // Get the alt text using the utility function
  const imageAlt = getImageAlt(
    article.featuredImage, // Pass the entire featuredImage - getImageAlt will handle all cases
    article.title || 'Article featured image',
  )

  // Log the image URL for debugging
  console.log('Featured image URL:', featuredImageUrl)

  // Format the date using native JavaScript (avoid date-fns)
  const formattedDate = article.createdAt
    ? new Date(article.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
    : 'Unknown date'

  return (
    <div className="bg-background text-foreground min-h-screen">
      {/* Hidden view counter component to track the view */}
      {articleId && <ViewCounter id={articleId} type="articles" />}
      
      {/* Curved Header */}
      <figure className="intro">
        {/* Use client component for image with error handling */}
        <div className="relative w-full h-full">
          <ClientImage
            src={featuredImageUrl}
            alt={imageAlt}
            className="w-full h-full object-cover"
            fallbackSrc="https://images.unsplash.com/photo-1745500415839-503883982264?q=80&w=3987&auto=format&fit=crop&ixlib=rb-4.0.3"
          />
        </div>
        <figcaption className="caption">
          <h1>{article.title || 'Untitled Article'}</h1>
        </figcaption>
        <span className="overlay">
          <svg
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            viewBox="0 0 500 250"
            enableBackground="new 0 0 500 250"
            xmlSpace="preserve"
            preserveAspectRatio="none"
          >
            <path
              fill="#FFFFFF"
              d="M250,246.5c-97.85,0-186.344-40.044-250-104.633V250h500V141.867C436.344,206.456,347.85,246.5,250,246.5z"
            />
          </svg>
        </span>
      </figure>

      {/* Article Content */}
      <article className="article-content">
        <p className="teaser">
          {article.summary || `An article by ${authorName} from ${schoolName}`}
        </p>

        <div className="article-meta">
          <div className="author">
            By {authorName} • {schoolName}
          </div>
          <div className="date">{formattedDate}</div>
        </div>

        <div className="rich-text-content">
          <RichText content={article.content} />
        </div>

        {/* Share Buttons */}
        <div className="mt-8">
          <ClientShareButtons
            title={article.title}
            url={`/articles/${getArticleId(article)}`}
            description={
              typeof article.summary === 'string'
                ? article.summary
                : `Article by ${authorName} from ${schoolName}`
            }
          />
        </div>

        {/* Back to Articles */}
        <Link href="/articles" className="back-link">
          ← Back to All Articles
        </Link>
      </article>

      {/* Status Indicator for preview mode - moved to bottom */}
      {(articleStatus === 'draft' || articleStatus === 'pending-review') && (
        <div className="w-full bg-black text-white py-4 px-4 text-center font-bold mt-8">
          This article is in {articleStatus === 'draft' ? 'DRAFT' : 'PENDING REVIEW'} mode and is
          not pblacky visible
        </div>
      )}

      {/* Client component for scroll effects */}
      <CurvedHeaderClient />
    </div>
  )
}
