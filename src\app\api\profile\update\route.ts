import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      let body
      try {
        body = await req.json()
      } catch (error) {
        console.log('Error parsing JSON, trying formData')
        // Fallback to formData if JSON parsing fails
        const formData = await req.formData()
        body = {
          firstName: formData.get('firstName'),
          lastName: formData.get('lastName'),
          bio: formData.get('bio'),
          grade: formData.get('grade'),
        }
      }

      const { firstName, lastName, bio, grade } = body

      // Validate required fields
      if (!firstName || !lastName) {
        return NextResponse.json(
          { error: 'First name and last name are required' },
          { status: 400 },
        )
      }

      // Try to update in MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get user to determine role
        const user = await db.collection('users').findOne({ id: userId })
        if (!user) {
          return NextResponse.json({ error: 'User not found' }, { status: 404 })
        }

        const userRole = typeof user.role === 'object' ? user.role?.slug : user.role

        // Build update data
        const updateData: any = {
          firstName,
          lastName,
          bio,
          updatedAt: new Date().toISOString(),
        }

        // Only include grade if provided and user is a student
        if (grade && userRole === 'student') {
          updateData.grade = grade
        }

        // Update user in MongoDB
        const result = await db.collection('users').updateOne({ id: userId }, { $set: updateData })

        if (result.matchedCount > 0) {
          console.log('User updated in MongoDB')
          return NextResponse.json({ success: true, message: 'Profile updated successfully' })
        }
      } catch (mongoError) {
        console.warn('Error updating user in MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS
      const payload = await getPayload({ config })

      // Get user to determine role
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role

      // Build update data
      const updateData: any = {
        firstName,
        lastName,
        bio,
      }

      // Only include grade if provided and user is a student
      if (grade && userRole === 'student') {
        updateData.grade = grade
      }

      // Update user in Payload
      await payload.update({
        collection: 'users',
        id: userId,
        data: updateData,
      })

      return NextResponse.json({ success: true, message: 'Profile updated successfully' })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating profile:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
