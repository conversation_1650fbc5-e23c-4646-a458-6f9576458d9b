'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  AlertTriangle,
  Eye,
  Flag,
  MessageSquare,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Search,
  AlertCircle,
  Check,
} from 'lucide-react'
import { formatDistanceToNow } from '@/lib/date-utils'
import { useToast } from '@/components/ui/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import EscalateIssueForm from './EscalateIssueForm'

interface Issue {
  id: string
  articleId?: string
  articleTitle?: string
  authorId?: string
  reportedBy?: string | {
    id: string
    name: string
    role: string
  }
  reason?: string
  status: 'pending' | 'resolved' | 'dismissed' | 'escalated'
  resolved?: boolean
  createdAt: string
  reportedAt?: string
  severity?: 'low' | 'medium' | 'high'
  targetType?: 'user' | 'article' | 'content' | 'other'
  targetId?: string
  type?: string
  title?: string
  description?: string
  resolution?: string | {
    resolvedBy: {
      id: string
      name: string
      role: string
    }
    comments: string
    resolvedAt: string
  }
  resolvedBy?: string
  resolvedAt?: string
  targetName?: string
}

interface ReportedIssuesTableProps {
  schoolId: string
  userRole: string
}

export default function ReportedIssuesTable({
  schoolId,
  userRole,
}: ReportedIssuesTableProps) {
  const { toast } = useToast()
  const [issues, setIssues] = useState<Issue[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [selectedIssue, setSelectedIssue] = useState<Issue | null>(null)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isEscalateDialogOpen, setIsEscalateDialogOpen] = useState(false)
  const [resolutionComment, setResolutionComment] = useState('')
  const [resolveLoading, setResolveLoading] = useState(false)
  const [dismissLoading, setDismissLoading] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  useEffect(() => {
    async function fetchIssues() {
      try {
        setIsLoading(true)
        
        // Only filter by schoolId for school admins (not super admins)
        const isSchoolAdmin = userRole && (userRole.includes('school') || userRole === 'school-admin');
        
        // Create URL with schoolId parameter for filtering at the API level
        const url = isSchoolAdmin && schoolId ? 
          `/api/dashboard/reports?schoolId=${schoolId}` : 
          '/api/dashboard/reports';
        
        console.log(`Fetching reports with URL: ${url}, userRole: ${userRole}`);
        
        const response = await fetch(url)
        
        if (!response.ok) {
          throw new Error('فشل في جلب المشكلات المبلغ عنها')
        }
        
        const data = await response.json()
        
        console.log('Raw API response:', data);
        
        if (!data.success) {
          throw new Error(data.error || 'فشل في تحميل المشكلات المبلغ عنها')
        }
        
        // Log full structure to debug
        console.log('API response structure:', {
          hasIssues: Array.isArray(data.issues),
          hasReports: Array.isArray(data.reports),
          topLevelKeys: Object.keys(data)
        });
        
        // Get the reports array from the API response
        const reportsArray = data.reports || [];
        
        console.log(`Found ${reportsArray.length} reports in API response`);
        
        if (reportsArray.length === 0) {
          console.warn('No reports found in the API response');
        }
        
        const formattedIssues = reportsArray.map((report: any) => {
          console.log('Processing report:', report);
          return {
            id: report._id || report.id,
            type: 'article_report',
            title: report.articleTitle || 'مقالة مبلغ عنها',
            description: report.reason || 'لم يتم تقديم سبب',
            reportedBy: {
              id: report.reportedBy?.$oid || report.reportedBy || 'unknown',
              name: 'مستخدم',
              role: 'user'
            },
            reportedAt: report.createdAt?.$date || report.createdAt,
            createdAt: report.createdAt?.$date || report.createdAt,
            status: report.resolved ? 'resolved' : (report.status === 'dismissed' ? 'dismissed' : 'pending'),
            severity: report.severity || 'medium',
            targetType: 'article',
            targetId: report.articleId || report.article?.$oid || report.article,
            targetName: report.articleTitle,
            resolution: report.resolution || '',
          };
        });
        
        console.log('Formatted issues:', formattedIssues);
        
        setIssues(formattedIssues);
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching reported issues:', err)
        setError('فشل في تحميل المشكلات المبلغ عنها')
        setIsLoading(false)
      }
    }

    fetchIssues()
  }, [schoolId, userRole])

  const handleViewIssue = (issue: Issue) => {
    setSelectedIssue(issue)
    setIsViewDialogOpen(true)
  }

  const handleEscalateIssue = (issue: Issue) => {
    setSelectedIssue(issue)
    setIsEscalateDialogOpen(true)
  }

  const handleResolveIssue = async () => {
    if (!selectedIssue) return
    
    try {
      setResolveLoading(true)
      
      const response = await fetch(`/api/dashboard/reports/${selectedIssue.id}/resolve`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          comments: resolutionComment,
        }),
      })
      
      if (!response.ok) {
        throw new Error('فشل في حل المشكلة')
      }
      
      // Update the local state
      setIssues((prevIssues) =>
        prevIssues.map((issue) =>
          issue.id === selectedIssue.id
            ? {
                ...issue,
                status: 'resolved',
                resolution: {
                  resolvedBy: {
                    id: 'current-user', 
                    name: 'المستخدم الحالي',
                    role: userRole,
                  },
                  comments: resolutionComment,
                  resolvedAt: new Date().toISOString(),
                },
              }
            : issue
        )
      )
      
      toast({
        title: "تم حل المشكلة",
        description: "تم حل المشكلة بنجاح",
        duration: 3000,
      })
      
      setIsViewDialogOpen(false)
      setResolutionComment('')
      setSelectedIssue(null)
    } catch (error) {
      console.error('Error resolving issue:', error)
      toast({
        title: "خطأ",
        description: "فشل في حل المشكلة، الرجاء المحاولة مرة أخرى",
        variant: "destructive",
        duration: 3000,
      })
    } finally {
      setResolveLoading(false)
    }
  }

  const handleDismissIssue = async () => {
    if (!selectedIssue) return
    
    try {
      setDismissLoading(true)
      
      const response = await fetch(`/api/dashboard/reports/${selectedIssue.id}/dismiss`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          comments: resolutionComment || 'تم رفض البلاغ من قبل مدير المدرسة',
        }),
      })
      
      if (!response.ok) {
        throw new Error('فشل في رفض المشكلة')
      }
      
      // Update the local state
      setIssues((prevIssues) =>
        prevIssues.map((issue) =>
          issue.id === selectedIssue.id
            ? {
                ...issue,
                status: 'dismissed',
              }
            : issue
        )
      )
      
      toast({
        title: "تم رفض المشكلة",
        description: "تم رفض المشكلة بنجاح",
        duration: 3000,
      })
      
      setIsViewDialogOpen(false)
      setResolutionComment('')
      setSelectedIssue(null)
    } catch (error) {
      console.error('Error dismissing issue:', error)
      toast({
        title: "خطأ",
        description: "فشل في رفض المشكلة، الرجاء المحاولة مرة أخرى",
        variant: "destructive",
        duration: 3000,
      })
    } finally {
      setDismissLoading(false)
    }
  }

  const handleDeleteArticle = async () => {
    if (!selectedIssue || !selectedIssue.targetId) return
    
    try {
      setDeleteLoading(true)
      
      const response = await fetch(`/api/dashboard/articles/${selectedIssue.targetId}/delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportId: selectedIssue.id,
          reason: resolutionComment || 'المقال يخالف سياسة النشر',
          notifyUsers: true,
        }),
      })
      
      if (!response.ok) {
        throw new Error('فشل في حذف المقال')
      }
      
      // Update the local state
      setIssues((prevIssues) =>
        prevIssues.map((issue) =>
          issue.id === selectedIssue.id
            ? {
                ...issue,
                status: 'resolved',
                resolution: {
                  resolvedBy: {
                    id: 'current-user', 
                    name: 'المستخدم الحالي',
                    role: userRole,
                  },
                  comments: `تم حذف المقال: ${resolutionComment || 'المقال يخالف سياسة النشر'}`,
                  resolvedAt: new Date().toISOString(),
                },
              }
            : issue
        )
      )
      
      toast({
        title: "تم حذف المقال",
        description: "تم حذف المقال وإرسال إشعار للمستخدمين المعنيين",
        duration: 3000,
      })
      
      setIsViewDialogOpen(false)
      setShowDeleteConfirm(false)
      setResolutionComment('')
      setSelectedIssue(null)
    } catch (error) {
      console.error('Error deleting article:', error)
      toast({
        title: "خطأ",
        description: "فشل في حذف المقال، الرجاء المحاولة مرة أخرى",
        variant: "destructive",
        duration: 3000,
      })
    } finally {
      setDeleteLoading(false)
    }
  }

  const viewReportedArticle = (articleId: string) => {
    if (!articleId) return;
    // Open article in new tab
    window.open(`/articles/${articleId}`, '_blank');
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'high':
        return <Badge variant="destructive">عالية</Badge>
      case 'medium':
        return <Badge variant="default">متوسطة</Badge>
      case 'low':
        return <Badge variant="outline">منخفضة</Badge>
      default:
        return <Badge variant="outline">{severity}</Badge>
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'resolved':
        return <Badge className="bg-green-500 text-white">تم الحل</Badge>
      case 'dismissed':
        return <Badge variant="secondary">تم الإهمال</Badge>
      case 'pending':
        return <Badge variant="outline">معلقة</Badge>
      case 'escalated':
        return <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200">تم التصعيد</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTargetIcon = (targetType: string) => {
    switch (targetType) {
      case 'user':
        return <User className="h-4 w-4" />
      case 'article':
        return <FileText className="h-4 w-4" />
      case 'content':
        return <MessageSquare className="h-4 w-4" />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getTargetTypeText = (targetType: string) => {
    switch (targetType) {
      case 'user':
        return 'المستخدم';
      case 'article':
        return 'المقال';
      case 'content':
        return 'المحتوى';
      default:
        return 'أخرى';
    }
  }

  const filteredIssues = issues.filter((issue) => {
    const titleMatch = issue.title?.toLowerCase().includes(searchQuery.toLowerCase()) || false;
    const descriptionMatch = issue.description?.toLowerCase().includes(searchQuery.toLowerCase()) || false;
    
    let reportedByMatch = false;
    if (typeof issue.reportedBy === 'object' && issue.reportedBy?.name) {
      reportedByMatch = issue.reportedBy.name.toLowerCase().includes(searchQuery.toLowerCase());
    }
    
    const matchesSearch = titleMatch || descriptionMatch || reportedByMatch;
    const matchesStatus = statusFilter === 'all' || issue.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center" dir="rtl">
            <Flag className="ml-2 h-5 w-5" />
            المشكلات المبلغ عنها
          </CardTitle>
        </CardHeader>
        <CardContent dir="rtl">
          <div className="bg-red-50 text-red-500 p-4 rounded-md flex items-center">
            <AlertCircle className="ml-2 h-5 w-5" />
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center" dir="rtl">
            <Flag className="ml-2 h-5 w-5" />
            المشكلات المبلغ عنها
          </CardTitle>
        </CardHeader>
        <CardContent dir="rtl">
          <div className="mb-4 flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="البحث عن المشكلات..."
                value={searchQuery}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                className="pl-3 pr-10"
                dir="rtl"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="pending">معلقة</SelectItem>
                <SelectItem value="resolved">تم الحل</SelectItem>
                <SelectItem value="dismissed">تم الإهمال</SelectItem>
                <SelectItem value="escalated">تم التصعيد</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex justify-between items-center p-4 border rounded-lg">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-64" />
                    <Skeleton className="h-4 w-40" />
                  </div>
                  <Skeleton className="h-8 w-16" />
                </div>
              ))}
            </div>
          ) : filteredIssues.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">العنوان</TableHead>
                    <TableHead className="text-right">تم الإبلاغ بواسطة</TableHead>
                    <TableHead className="text-right">الحالة</TableHead>
                    <TableHead className="text-right">الأولوية</TableHead>
                    <TableHead className="text-right">الفئة</TableHead>
                    <TableHead className="text-right">التاريخ</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredIssues.map((issue) => (
                    <TableRow key={issue.id}>
                      <TableCell className="font-medium truncate max-w-[200px]">
                        {issue.title || 'مقالة مبلغ عنها'}
                      </TableCell>
                      <TableCell>
                        {typeof issue.reportedBy === 'object' ? issue.reportedBy.name : 'مستخدم غير معروف'}
                      </TableCell>
                      <TableCell>{getStatusBadge(issue.status)}</TableCell>
                      <TableCell>{getSeverityBadge(issue.severity || 'medium')}</TableCell>
                      <TableCell>{getTargetTypeText(issue.targetType || 'other')}</TableCell>
                      <TableCell>
                        {new Date(issue.createdAt).toLocaleDateString('ar', { 
                          year: 'numeric', 
                          month: '2-digit', 
                          day: '2-digit',
                          calendar: 'gregory'
                        })}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewIssue(issue)}
                          >
                            عرض
                          </Button>
                          {issue.status !== 'resolved' && issue.status !== 'escalated' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEscalateIssue(issue)}
                            >
                              تصعيد
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-8 border rounded-lg border-dashed">
              <Flag className="h-12 w-12 text-gray-300 mb-4" />
              <h3 className="text-lg font-medium mb-1">لا توجد مشكلات</h3>
              <p className="text-gray-500 text-center">
                لم يتم العثور على مشكلات مبلغ عنها تطابق معايير البحث.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Issue Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl" dir="rtl">
          {selectedIssue && (
            <>
              <DialogHeader>
                <DialogTitle>عرض المشكلة: {selectedIssue.title || 'مقالة مبلغ عنها'}</DialogTitle>
                <DialogDescription>
                  حالة المشكلة: {getStatusBadge(selectedIssue.status)} | تم الإبلاغ في{' '}
                  {new Date(selectedIssue.createdAt).toLocaleDateString('ar', { 
                    year: 'numeric', 
                    month: '2-digit', 
                    day: '2-digit',
                    calendar: 'gregory'
                  })}
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-6">
                <div>
                  <h3 className="font-medium mb-2">الوصف</h3>
                  <div className="bg-gray-50 rounded-md p-3">
                    {selectedIssue.description || 'لم يتم تقديم سبب'}
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h3 className="font-medium mb-2">الأولوية</h3>
                    <div>{getSeverityBadge(selectedIssue.severity || 'medium')}</div>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">الفئة</h3>
                    <div className="flex items-center gap-1">
                      {getTargetIcon(selectedIssue.targetType || 'other')}
                      <span>{getTargetTypeText(selectedIssue.targetType || 'other')}</span>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">تم الإبلاغ بواسطة</h3>
                    <div>
                      {typeof selectedIssue.reportedBy === 'object' 
                        ? `${selectedIssue.reportedBy.name} (${selectedIssue.reportedBy.role})` 
                        : 'مستخدم غير معروف (user)'}
                    </div>
                  </div>
                </div>
                
                {selectedIssue.status === 'resolved' && selectedIssue.resolution && typeof selectedIssue.resolution === 'object' && (
                  <div>
                    <h3 className="font-medium mb-2">تفاصيل الحل</h3>
                    <div className="bg-green-50 rounded-md p-3 border border-green-200">
                      <div className="mb-2">
                        <span className="font-medium">تم الحل بواسطة: </span>
                        {selectedIssue.resolution.resolvedBy.name} (
                        {selectedIssue.resolution.resolvedBy.role})
                      </div>
                      <div className="mb-2">
                        <span className="font-medium">تعليقات: </span>
                        {selectedIssue.resolution.comments}
                      </div>
                      <div className="text-sm text-gray-500">
                        تم الحل في{' '}
                        {new Date(selectedIssue.resolution.resolvedAt).toLocaleDateString('ar', { 
                          year: 'numeric', 
                          month: '2-digit', 
                          day: '2-digit',
                          calendar: 'gregory'
                        })}
                      </div>
                    </div>
                  </div>
                )}
                
                {selectedIssue.status !== 'resolved' && selectedIssue.status !== 'escalated' && (
                  <div>
                    <h3 className="font-medium mb-2">حل المشكلة</h3>
                    <Textarea
                      placeholder="أضف تعليقات حول كيفية حلك للمشكلة..."
                      value={resolutionComment}
                      onChange={(e) => setResolutionComment(e.target.value)}
                      rows={4}
                      dir="rtl"
                    />
                  </div>
                )}
              </div>
              
              <DialogFooter className="flex-row-reverse sm:flex-row sm:justify-between justify-end mt-4">
                <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                  إغلاق
                </Button>
                
                {selectedIssue.status !== 'resolved' && selectedIssue.status !== 'escalated' && (
                  <div className="flex gap-2">
                    {selectedIssue.targetId && !showDeleteConfirm && (
                      <>
                        <Button
                          variant="outline"
                          onClick={() => viewReportedArticle(selectedIssue.targetId || '')}
                          className="gap-2"
                        >
                          <Eye className="h-4 w-4" />
                          عرض المقال
                        </Button>
                        
                        <Button
                          variant="destructive"
                          onClick={() => setShowDeleteConfirm(true)}
                          className="gap-2"
                        >
                          حذف المقال
                        </Button>
                      </>
                    )}
                    
                    {showDeleteConfirm ? (
                      <>
                        <Button
                          variant="outline"
                          onClick={() => setShowDeleteConfirm(false)}
                        >
                          إلغاء
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleDeleteArticle}
                          disabled={deleteLoading || !resolutionComment}
                        >
                          {deleteLoading ? 'جاري الحذف...' : 'تأكيد حذف المقال وإرسال الإشعارات'}
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="secondary"
                          onClick={handleDismissIssue}
                          disabled={dismissLoading}
                          className="gap-2 bg-gray-200 hover:bg-gray-300"
                        >
                          <XCircle className="h-4 w-4" />
                          {dismissLoading ? 'جاري الرفض...' : 'رفض البلاغ'}
                        </Button>
                        
                        <Button
                          onClick={handleResolveIssue}
                          disabled={!resolutionComment || resolveLoading}
                          className="gap-2"
                        >
                          <Check className="h-4 w-4" />
                          {resolveLoading ? 'جاري الحل...' : 'تحديد كمحلول'}
                        </Button>
                      </>
                    )}
                  </div>
                )}
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Escalate Issue Dialog */}
      <Dialog open={isEscalateDialogOpen} onOpenChange={setIsEscalateDialogOpen}>
        <DialogContent className="max-w-2xl" dir="rtl">
          {selectedIssue && (
            <EscalateIssueForm
              issue={selectedIssue}
              onCancel={() => setIsEscalateDialogOpen(false)}
              onEscalateSuccess={() => {
                setIsEscalateDialogOpen(false)
                // Update the local state
                setIssues((prevIssues) =>
                  prevIssues.map((issue) =>
                    issue.id === selectedIssue.id
                      ? {
                          ...issue,
                          status: 'escalated',
                        }
                      : issue
                  )
                )
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
