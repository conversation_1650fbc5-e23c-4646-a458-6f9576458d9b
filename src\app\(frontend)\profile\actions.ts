'use server'

import { headers as getHeaders } from 'next/headers.js'
import { getPayload } from 'payload'
import { revalidatePath } from 'next/cache'

import config from '@/payload.config'

interface UpdateProfileData {
  name: string
  email: string
  notificationPreferences: {
    emailNotifications: boolean
    emailTypes: string[]
  }
}

export async function updateProfile(data: UpdateProfileData) {
  try {
    const headers = await getHeaders()
    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })
    const { user } = await payload.auth({ headers })

    if (!user) {
      return { success: false, error: 'Not authenticated' }
    }

    // Update the user
    await payload.update({
      collection: 'users',
      id: user.id,
      data: {
        name: data.name,
        email: data.email,
        notificationPreferences: {
          emailNotifications: data.notificationPreferences.emailNotifications,
          emailTypes: data.notificationPreferences.emailTypes,
        },
      },
    })

    // Revalidate the profile page
    revalidatePath('/profile')

    return { success: true }
  } catch (error) {
    console.error('Error updating profile:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'An error occurred while updating your profile' 
    }
  }
}
