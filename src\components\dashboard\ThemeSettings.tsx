'use client'

import { useState } from 'react'
import { Check, Circle } from 'lucide-react'

import { useTheme, ThemeColor } from '@/contexts/ThemeContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Button } from '@/components/ui/button'

type ColorOption = {
  value: ThemeColor
  label: string
  color: string
}

export function ThemeSettings() {
  const { darkMode, themeColor, setDarkMode, setThemeColor } = useTheme()
  const [selectedTheme, setSelectedTheme] = useState<ThemeColor>(themeColor)
  const [selectedMode, setSelectedMode] = useState(darkMode)
  
  // Define the available color themes
  const colorOptions: ColorOption[] = [
    { value: 'cyan', label: 'Cyan', color: 'rgb(14, 165, 175)' },
    { value: 'blue', label: 'Blue', color: 'rgb(59, 130, 246)' },
    { value: 'purple', label: 'Purple', color: 'rgb(139, 92, 246)' },
    { value: 'green', label: 'Green', color: 'rgb(34, 197, 94)' },
    { value: 'amber', label: 'Amber', color: 'rgb(245, 158, 11)' },
    { value: 'pink', label: 'Pink', color: 'rgb(236, 72, 153)' },
  ]
  
  const handleSave = () => {
    setDarkMode(selectedMode)
    setThemeColor(selectedTheme)
    document.documentElement.setAttribute('data-theme', selectedTheme)
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Theme Settings</CardTitle>
        <CardDescription>Customize the appearance of your dashboard</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="dark-mode">Dark Mode</Label>
            <Switch 
              id="dark-mode" 
              checked={selectedMode}
              onCheckedChange={setSelectedMode}
            />
          </div>
          <p className="text-sm text-muted-foreground">
            Enable dark mode for a darker color scheme
          </p>
        </div>
        
        <div className="space-y-3">
          <Label>Theme Color</Label>
          <div className="grid grid-cols-3 gap-2">
            {colorOptions.map((color) => (
              <button
                key={color.value}
                className={`p-3 rounded-md flex items-center justify-between ${
                  selectedTheme === color.value 
                    ? 'ring-2 ring-primary ring-offset-2' 
                    : 'hover:bg-accent'
                }`}
                onClick={() => setSelectedTheme(color.value)}
              >
                <div className="flex items-center">
                  <div 
                    className="w-5 h-5 rounded-full mr-2"
                    style={{ backgroundColor: color.color }}
                  ></div>
                  <span>{color.label}</span>
                </div>
                {selectedTheme === color.value && (
                  <Check className="h-4 w-4" />
                )}
              </button>
            ))}
          </div>
          <p className="text-sm text-muted-foreground">
            Select your preferred accent color for buttons and highlights
          </p>
        </div>
        
        <div className="pt-4">
          <h3 className="text-lg font-medium mb-2">Preview</h3>
          <div 
            className={`p-4 rounded-md border ${
              selectedMode ? 'bg-gray-800 text-white' : 'bg-white'
            }`}
          >
            <div className="flex items-center gap-2 mb-3">
              <div 
                className="w-8 h-8 rounded-md"
                style={{ 
                  backgroundColor: colorOptions.find(c => c.value === selectedTheme)?.color 
                }}
              ></div>
              <span className="font-medium">Primary Color</span>
            </div>
            <div className="space-y-2">
              <Button
                style={{ 
                  backgroundColor: colorOptions.find(c => c.value === selectedTheme)?.color 
                }}
              >
                Button Example
              </Button>
              <div 
                className="h-2 rounded-full" 
                style={{ 
                  backgroundColor: colorOptions.find(c => c.value === selectedTheme)?.color,
                  opacity: 0.7
                }}
              ></div>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end">
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </div>
      </CardContent>
    </Card>
  )
} 