/** @jsxImportSource react */
import React, { JSX } from 'react'

/**
 * Extracts plain text from rich text content
 * @param content The rich text content from Payload CMS
 * @returns Plain text string without formatting
 */
export function extractPlainText(content: any): string {
  try {
    if (!content) {
      return ''
    }

    // Handle string content
    if (typeof content === 'string') {
      return content
    }

    // Handle array content (old format)
    if (Array.isArray(content)) {
      return content
        .map((node) => {
          if (typeof node === 'string') {
            return node
          }

          if (node.children) {
            return extractPlainText(node.children)
          }

          return ''
        })
        .join(' ')
    }

    // Handle Payload CMS v3 rich text format
    if (content.root && content.root.children) {
      return content.root.children
        .map((node: any) => {
          if (node.type === 'paragraph' || node.type === 'heading') {
            return node.children
              .map((child: any) => {
                if (child.text) {
                  return child.text
                }

                if (child.type === 'link' && child.children) {
                  return child.children.map((linkChild: any) => linkChild.text).join('')
                }

                return ''
              })
              .join('')
          }

          if (node.type === 'ul' || node.type === 'ol') {
            return node.children
              .map((listItem: any) => {
                return listItem.children
                  .map((child: any) => {
                    if (child.type === 'paragraph') {
                      return child.children
                        .map((paragraphChild: any) => paragraphChild.text)
                        .join('')
                    }
                    return ''
                  })
                  .join('')
              })
              .join(' ')
          }

          return ''
        })
        .join(' ')
    }

    // Fallback for unknown formats
    if (typeof content === 'object') {
      try {
        return JSON.stringify(content)
      } catch (e) {
        return ''
      }
    }

    return ''
  } catch (error) {
    console.error('Error extracting plain text:', error)
    return ''
  }
}

export const isRichTextObject = (obj: any): boolean => {
  return obj && typeof obj === 'object' && obj.root && typeof obj.root === 'object'
}

/**
 * Renders rich text content from Payload CMS
 * @param content The rich text content from Payload CMS
 * @returns JSX elements representing the rich text content
 */
export function renderRichText(content: any): React.ReactNode {
  try {
    if (!content) {
      return null
    }

    // Handle string content
    if (typeof content === 'string') {
      return <p>{content}</p>
    }

    // Handle array content (old format)
    if (Array.isArray(content)) {
      return content.map((node, i) => {
        if (typeof node === 'string') {
          return <p key={i}>{node}</p>
        }

        if (node.children) {
          return <p key={i}>{renderRichText(node.children)}</p>
        }

        return null
      })
    }

    // Handle Payload CMS v3 rich text format
    if (content.root && content.root.children) {
      return content.root.children.map((node: any, i: number) => {
        if (node.type === 'paragraph') {
          return (
            <p key={i} className="mb-4">
              {node.children.map((child: any, j: number) => {
                // Handle text with formatting
                if (child.text) {
                  let element = child.text

                  if (child.bold) {
                    element = <strong key={j}>{element}</strong>
                  }

                  if (child.italic) {
                    element = <em key={j}>{element}</em>
                  }

                  if (child.underline) {
                    element = <u key={j}>{element}</u>
                  }

                  return element
                }

                // Handle links
                if (child.type === 'link') {
                  return (
                    <a
                      key={j}
                      href={child.url}
                      target={child.newTab ? '_blank' : undefined}
                      rel={child.newTab ? 'noopener noreferrer' : undefined}
                      className="text-blue-600 hover:underline"
                    >
                      {child.children.map((linkChild: any, k: number) => linkChild.text)}
                    </a>
                  )
                }

                return null
              })}
            </p>
          )
        }

        if (node.type === 'heading') {
          const HeadingTag = `h${node.level}` as keyof JSX.IntrinsicElements

          return (
            <HeadingTag key={i} className="mt-6 mb-4 font-bold">
              {node.children.map((child: any, j: number) => child.text)}
            </HeadingTag>
          )
        }

        if (node.type === 'ul' || node.type === 'ol') {
          const ListTag = node.type as keyof JSX.IntrinsicElements

          return (
            <ListTag key={i} className="mb-4 pl-6">
              {node.children.map((listItem: any, j: number) => (
                <li key={j}>
                  {listItem.children.map((child: any, k: number) => {
                    if (child.type === 'paragraph') {
                      return child.children.map(
                        (paragraphChild: any, l: number) => paragraphChild.text,
                      )
                    }
                    return null
                  })}
                </li>
              ))}
            </ListTag>
          )
        }

        if (node.type === 'upload') {
          return (
            <div key={i} className="my-4">
              <img
                src={node.value?.url}
                alt={node.value?.alt || 'Uploaded image'}
                className="max-w-full h-auto rounded"
              />
            </div>
          )
        }

        return null
      })
    }

    // Fallback for unknown formats
    return <p>{JSON.stringify(content)}</p>
  } catch (error) {
    console.error('Error rendering rich text:', error)
    return <p className="text-red-500">Error rendering content</p>
  }
}
