import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

/**
 * Super-Admin Roles API
 * Endpoints for managing user roles in the system
 */

// GET all roles with statistics
export async function GET(req: NextRequest) {
  try {
    // Get the token from the cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can access this endpoint' },
          { status: 403 },
        )
      }

      // Parse URL parameters
      const url = new URL(req.url)
      const includeStats = url.searchParams.get('includeStats') === 'true'

      // Connect to MongoDB
      const { db } = await connectToDatabase()

      try {
        // Get all roles
        const roles = await payload.find({
          collection: 'roles',
          limit: 100,
        })

        // If stats are requested, fetch them
        if (includeStats) {
          // Process roles to add statistics
          const rolesWithStats = await Promise.all(
            roles.docs.map(async (role) => {
              const roleId = role.id
              const roleSlug = role.slug

              // Get user count for this role
              const userCount = await db.collection('users').countDocuments({
                $or: [
                  { role: roleId },
                  { 'role.slug': roleSlug },
                ],
              })

              // Get active users with this role
              const activeUserCount = await db.collection('users').countDocuments({
                $or: [
                  { role: roleId },
                  { 'role.slug': roleSlug },
                ],
                status: 'active',
              })

              // Get schools with admin of this role
              let schoolsWithRoleCount = 0
              if (roleSlug === 'school-admin') {
                schoolsWithRoleCount = await db.collection('schools').countDocuments({
                  $or: [
                    { admin: roleId },
                    { 'admin.role': roleId },
                    { 'admin.role.slug': roleSlug },
                  ],
                })
              }

              return {
                ...role,
                stats: {
                  userCount,
                  activeUserCount,
                  schoolsWithRoleCount,
                },
              }
            })
          )

          return NextResponse.json({
            roles: rolesWithStats,
            totalDocs: roles.totalDocs,
            totalPages: roles.totalPages,
            page: roles.page,
            limit: roles.limit,
          })
        }

        // Return roles without stats
        return NextResponse.json(roles)
      } catch (dbError) {
        console.error('Database error:', dbError)
        return NextResponse.json({ error: 'Database error' }, { status: 500 })
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching roles:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// POST a new role
export async function POST(req: NextRequest) {
  try {
    // Get the token from the cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can create roles' },
          { status: 403 },
        )
      }

      // Get request body
      const body = await req.json()

      // Validate required fields
      const requiredFields = ['name', 'slug', 'permissions']
      for (const field of requiredFields) {
        if (!body[field]) {
          return NextResponse.json(
            { error: 'Bad Request', message: `Missing required field: ${field}` },
            { status: 400 },
          )
        }
      }

      // Check if slug is valid
      const slugRegex = /^[a-z0-9-]+$/
      if (!slugRegex.test(body.slug)) {
        return NextResponse.json(
          { 
            error: 'Bad Request', 
            message: 'Slug must contain only lowercase letters, numbers, and hyphens' 
          },
          { status: 400 },
        )
      }

      // Check if slug is unique
      const existingRole = await payload.find({
        collection: 'roles',
        where: {
          slug: { equals: body.slug },
        },
      })

      if (existingRole.docs.length > 0) {
        return NextResponse.json(
          { error: 'Bad Request', message: 'A role with this slug already exists' },
          { status: 400 },
        )
      }

      // Create the role using Payload CMS
      const newRole = await payload.create({
        collection: 'roles',
        data: {
          name: body.name,
          slug: body.slug,
          permissions: body.permissions,
          description: body.description,
        },
      })

      return NextResponse.json(newRole, { status: 201 })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error creating role:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// PUT to update a role
export async function PUT(req: NextRequest) {
  try {
    // Get the token from the cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can update roles' },
          { status: 403 },
        )
      }

      // Get request body
      const body = await req.json()

      // Check if role ID is provided
      if (!body.id) {
        return NextResponse.json(
          { error: 'Bad Request', message: 'Role ID is required' },
          { status: 400 },
        )
      }

      // Prevent updating protected roles
      const protectedRoles = ['super-admin', 'school-admin', 'teacher', 'mentor', 'student']
      const existingRole = await payload.findByID({
        collection: 'roles',
        id: body.id,
      })

      if (protectedRoles.includes(existingRole.slug)) {
        // Allow updating description but not name, slug, or permissions
        const updatedRole = await payload.update({
          collection: 'roles',
          id: body.id,
          data: {
            description: body.description,
          },
        })
        
        return NextResponse.json({
          ...updatedRole,
          message: 'Note: Only description was updated as this is a protected role',
        })
      }

      // Update the role using Payload CMS
      const updatedRole = await payload.update({
        collection: 'roles',
        id: body.id,
        data: {
          name: body.name,
          permissions: body.permissions,
          description: body.description,
        },
      })

      return NextResponse.json(updatedRole)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating role:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// DELETE a role
export async function DELETE(req: NextRequest) {
  try {
    // Get the token from the cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can delete roles' },
          { status: 403 },
        )
      }

      // Parse URL parameters
      const url = new URL(req.url)
      const roleId = url.searchParams.get('id')

      if (!roleId) {
        return NextResponse.json(
          { error: 'Bad Request', message: 'Role ID is required' },
          { status: 400 },
        )
      }

      // Check if this is a protected role
      const protectedRoles = ['super-admin', 'school-admin', 'teacher', 'mentor', 'student']
      const roleToDelete = await payload.findByID({
        collection: 'roles',
        id: roleId,
      })

      if (protectedRoles.includes(roleToDelete.slug)) {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Cannot delete protected system roles' },
          { status: 403 },
        )
      }

      // Check if there are users with this role
      const { db } = await connectToDatabase()
      const usersWithRole = await db.collection('users').countDocuments({
        $or: [
          { role: roleId },
          { 'role.slug': roleToDelete.slug },
        ],
      })

      if (usersWithRole > 0) {
        return NextResponse.json(
          { 
            error: 'Conflict', 
            message: `Cannot delete role: ${usersWithRole} users are currently assigned this role` 
          },
          { status: 409 },
        )
      }

      // Delete the role
      await payload.delete({
        collection: 'roles',
        id: roleId,
      })

      return NextResponse.json({ message: 'Role deleted successfully' })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error deleting role:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 