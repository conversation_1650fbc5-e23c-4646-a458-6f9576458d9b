import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { updatePointsAndRankings } from '@/utils/points'

/**
 * API endpoint to manually update user rankings
 * This is useful for refreshing the statistics when needed
 */
export async function GET(_req: NextRequest) {
  try {
    console.log('Update rankings API called')
    
    // Get the payload instance
    const payload = await getPayload({ config })
    
    // Update rankings
    await updatePointsAndRankings({ payload })
    
    // Confirm the update has been done
    const statistics = await payload.find({
      collection: 'statistics',
      depth: 1,
    })
    
    console.log(`Updated ${statistics.docs.length} statistics documents`)
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Rankings updated successfully',
    })
  } catch (error) {
    console.error('Error updating rankings:', error)
    return NextResponse.json(
      { 
        error: 'Internal Server Error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
} 