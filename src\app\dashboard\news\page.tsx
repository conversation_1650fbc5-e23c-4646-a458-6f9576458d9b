'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { extractPlainText } from '@/utils/richTextUtils'
import { getImageUrl, getImageAlt } from '@/utils/imageUtils'
import { News, ApiResponse } from '@/types'

// Extended API response type to handle both formats
interface NewsApiResponse extends ApiResponse<{ news: News[] }> {
  news?: News[]
}
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { PlusCircle, Eye, Pencil, Trash2 } from 'lucide-react'

import NewsErrorBoundary from '@/components/NewsErrorBoundary'

export default function NewsPage() {
  const router = useRouter()
  const [newsItems, setNewsItems] = useState<News[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [userRole, setUserRole] = useState('')

  const fetchNewsData = async () => {
    // First try the dashboard API
    try {
      const newsResponse = await fetch('/api/dashboard/news', {
        credentials: 'include',
      })

      if (!newsResponse.ok) {
        throw new Error('Failed to fetch news from dashboard API')
      }

      const newsData = (await newsResponse.json()) as NewsApiResponse
      return newsData
    } catch (error) {
      console.error('Error fetching from dashboard API, trying public API:', error)

      // Fallback to public API
      const publicResponse = await fetch('/api/news', {
        cache: 'no-store',
      })

      if (!publicResponse.ok) {
        throw new Error('Failed to fetch news from both APIs')
      }

      const publicData = await publicResponse.json()

      // Return in the expected format
      if (publicData.docs && Array.isArray(publicData.docs)) {
        return publicData // Return the Payload format directly
      } else {
        return {
          success: true,
          data: {
            news: publicData.docs || [],
          },
        }
      }
    }
  }

  useEffect(() => {
    async function fetchData() {
      let attempts = 0
      const maxAttempts = 3

      while (attempts < maxAttempts) {
        try {
          setIsLoading(true)

          // Fetch user info to determine role
          const userResponse = await fetch('/api/auth/me', {
            credentials: 'include',
          })

          if (userResponse.ok) {
            const userData = await userResponse.json()
            if (userData.user) {
              const role =
                typeof userData.user.role === 'object'
                  ? userData.user.role?.slug
                  : userData.user.role
              setUserRole(role)
            }
          }

          // Fetch news
          const newsData = await fetchNewsData()

          if (newsData.success && newsData.data && newsData.data.news) {
            setNewsItems(newsData.data.news || [])
          } else if ('docs' in newsData && Array.isArray(newsData.docs)) {
            // Handle Payload CMS direct response format
            setNewsItems(newsData.docs as News[])
          } else {
            console.error('News data format issue:', newsData)
            setNewsItems([])
            setError('No news available at this time')
          }
          setIsLoading(false)
          return // Success, exit the loop
        } catch (err) {
          console.error(`Error fetching data (attempt ${attempts + 1}):`, err)
          attempts++
          // Wait before retrying (e.g., 1 second)
          await new Promise((resolve) => setTimeout(resolve, 1000))
        }
      }

      // If all attempts failed
      setError('Failed to load news after multiple retries. Please try again later.')
      setIsLoading(false)
    }

    fetchData()
  }, [])

  // Check if user can edit news (admin, mentor)
  const canEditNews = ['super-admin', 'school-admin', 'mentor'].includes(userRole)

  // Add delete handler
  const handleDeleteNews = async (newsId: string) => {
    try {
      setIsLoading(true)
      const res = await fetch(`/api/news/by-id/${newsId}`, {
        method: 'DELETE',
        credentials: 'include',
      })
      if (!res.ok) {
        const data = await res.json().catch(() => ({}))
        throw new Error(data.error || 'Failed to delete news')
      }
      // Refresh news list
      const newsData = await fetchNewsData()
      if (newsData.success && newsData.data && newsData.data.news) {
        setNewsItems(newsData.data.news || [])
      } else if ('docs' in newsData && Array.isArray(newsData.docs)) {
        setNewsItems(newsData.docs as News[])
      }
      setError('')
    } catch (err: any) {
      setError(err.message || 'Failed to delete news')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <DashboardLayout>
      <NewsErrorBoundary>
        <div className="p-6" dir="rtl">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">{canEditNews ? 'إدارة الأخبار' : 'الأخبار'}</h1>
            {canEditNews && (
              <Button onClick={() => router.push('/dashboard/news/create')}>
                <PlusCircle className="ml-2 h-4 w-4" />
                إضافة خبر
              </Button>
            )}
          </div>

          {isLoading && (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              <span className="mr-3">جاري التحميل...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}

          {/* Show table view for admins and mentors */}
          {!isLoading && !error && canEditNews ? (
            <Card>
              <CardHeader>
                <CardTitle>كل الأخبار</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الصورة</TableHead>
                      <TableHead>العنوان</TableHead>
                      <TableHead>الكاتب</TableHead>
                      <TableHead>التاريخ</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {newsItems.length > 0 ? (
                      newsItems.map((news: News, index: number) => (
                        <TableRow key={news.id || `news-table-${index}`}>
                          <TableCell>
                            {(news.featuredImage || news.image) && (
                              <div className="h-16 w-24 relative overflow-hidden rounded">
                                <Image
                                  src={getImageUrl(news.featuredImage || news.image, '/placeholder-news.jpg')}
                                  alt={getImageAlt(news.featuredImage || news.image, news.title)}
                                  fill
                                  className="object-cover"
                                  sizes="96px"
                                />
                              </div>
                            )}
                          </TableCell>
                          <TableCell>{news.title}</TableCell>
                          <TableCell>
                            {typeof news.author === 'object'
                              ? `${news.author.firstName} ${news.author.lastName}`
                              : 'غير معروف'}
                          </TableCell>
                          <TableCell>{new Date(news.createdAt).toLocaleDateString('ar-EG')}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => router.push(`/news/${news.slug || news.id}`)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              {/* Only allow editing if user is admin or the author */}
                              {(userRole === 'super-admin' || userRole === 'school-admin' || news.isOwner) && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => router.push(`/dashboard/news/edit/${news.id}`)}
                                >
                                  <Pencil className="h-4 w-4" />
                                </Button>
                              )}
                              {/* Only allow deletion if user is admin or the author */}
                              {(userRole === 'super-admin' || userRole === 'school-admin' || news.isOwner) && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="text-red-500"
                                  onClick={() => {
                                    if (confirm('هل أنت متأكد من أنك تريد حذف هذا الخبر؟')) {
                                      handleDeleteNews(news.id)
                                    }
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4">
                          لم يتم العثور على أخبار
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            /* Show card view for students and teachers */
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {newsItems.length > 0 ? (
                newsItems.map((news: News, index: number) => (
                  <Card key={news.id || `news-${index}`} className="overflow-hidden">
                    {(news.featuredImage || news.image) && (
                      <div className="h-48 overflow-hidden relative">
                        <Image
                          src={getImageUrl(news.featuredImage || news.image, '/placeholder-news.jpg')}
                          alt={getImageAlt(news.featuredImage || news.image, news.title)}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          onError={() => {
                            // We can't directly set src with Image component
                            // Error handling is built-in with automatic retry
                            console.error('Image failed to load for news:', news.title)
                          }}
                        />
                      </div>
                    )}
                    <CardHeader>
                      <CardTitle>{news.title}</CardTitle>
                      <div className="text-sm text-gray-500">
                        {typeof news.author === 'object'
                          ? `بواسطة ${news.author.firstName} ${news.author.lastName}`
                          : 'كاتب غير معروف'}{' '}
                        | {new Date(news.createdAt).toLocaleDateString('ar-EG')}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="line-clamp-3">
                        {news.content ? extractPlainText(news.content) : 'لا يوجد محتوى متاح'}
                      </div>
                      <Button
                        variant="link"
                        className="p-0 mt-2"
                        onClick={() => router.push(`/news/${news.slug || news.id}`)}
                      >
                        قراءة المزيد
                      </Button>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="col-span-full text-center py-8">
                  لا توجد أخبار متاحة حاليًا.
                </div>
              )}
            </div>
          )}
        </div>
      </NewsErrorBoundary>
    </DashboardLayout>
  )
}
