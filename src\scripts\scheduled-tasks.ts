import { getPayload } from 'payload'
import { updatePointsAndRankings, calculateUserPoints } from '../utils/points'
import config from '../payload.config'

/**
 * Scheduled task to update all points and rankings
 * This should be run periodically (e.g., daily) to ensure consistency
 * 
 * Run with: npm run ts-node src/scripts/scheduled-tasks.ts
 * 
 * You can also set up a cron job to run this script automatically:
 * 0 0 * * * cd /path/to/your/app && npm run ts-node src/scripts/scheduled-tasks.ts > /path/to/logs/update-points.log 2>&1
 */
const runScheduledTasks = async () => {
  try {
    console.log('Starting scheduled tasks at', new Date().toISOString())
    
    // Initialize Payload
    const payload = await getPayload({ config })
    
    // 1. Update user rankings and statistics
    console.log('Updating user rankings and statistics...')
    await updatePointsAndRankings({ payload })
    
    // 2. Verify and fix any user points that may be incorrect
    console.log('Verifying user points...')
    await verifyUserPoints({ payload })
    
    console.log('Scheduled tasks completed successfully at', new Date().toISOString())
    process.exit(0)
  } catch (error) {
    console.error('Error running scheduled tasks:', error)
    process.exit(1)
  }
}

/**
 * Verify and fix user points by recalculating for users with activity in the last 30 days
 */
const verifyUserPoints = async ({ payload }: { payload: any }) => {
  try {
    // Get date 30 days ago
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    // Find users with recent activity
    const recentActivities = await payload.find({
      collection: 'activities',
      where: {
        createdAt: {
          greater_than_equal: thirtyDaysAgo.toISOString(),
        },
      },
      limit: 1000,
    })
    
    // Get unique user IDs from activities
    const userIds = new Set<string>()
    recentActivities.docs.forEach((activity: any) => {
      if (activity.userId) {
        userIds.add(typeof activity.userId === 'object' ? activity.userId.id : activity.userId)
      }
    })
    
    console.log(`Found ${userIds.size} users with recent activity`)
    
    // Check and fix each user's points
    let fixedCount = 0
    for (const userId of userIds) {
      try {
        // Get current user data
        const user = await payload.findByID({
          collection: 'users',
          id: userId,
        })
        
        // Recalculate points
        const calculatedPoints = await calculateUserPoints({ payload, userId })
        
        // If there's a significant discrepancy (more than 5 points difference), update the user
        if (Math.abs((user.points || 0) - calculatedPoints) > 5) {
          console.log(`Fixing points for user ${userId}: ${user.points || 0} -> ${calculatedPoints}`)
          
          await payload.update({
            collection: 'users',
            id: userId,
            data: {
              points: calculatedPoints,
            },
          })
          
          fixedCount++
        }
      } catch (userError) {
        console.error(`Error processing user ${userId}:`, userError)
        // Continue with next user
      }
    }
    
    console.log(`Fixed points for ${fixedCount} users`)
  } catch (error) {
    console.error('Error verifying user points:', error)
  }
}

// Run the script if executed directly
if (require.main === module) {
  runScheduledTasks()
}

export default runScheduledTasks 