'use client'

import { useEffect, useState } from 'react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Eye,
  FileText,
  MessageSquare,
  CheckCircle,
  ImageIcon,
  Edit,
  Newspaper,
  Award,
  LogIn,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ActivityDetailsDialog } from '@/components/activities/ActivityDetailsDialog'
import { Activity as PayloadActivity } from '@/payload-types'
import { User } from '@/payload-types'

// Extended Activity type to handle populated relationships
interface Activity extends Omit<PayloadActivity, 'userId' | 'targetUserId'> {
  userId: string | User
  targetUserId?: string | User | null
}

export default function ActivitiesPage() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  useEffect(() => {
    async function fetchActivities() {
      try {
        const response = await fetch('/api/dashboard/activities', {
          credentials: 'include',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch activities')
        }

        const data = await response.json()
        setActivities(data.activities || [])
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching activities:', err)
        setError('Failed to load activities. Please try again.')
        setIsLoading(false)
      }
    }

    fetchActivities()
  }, [])

  // Function to open the details dialog
  const handleViewDetails = (activity: Activity) => {
    setSelectedActivity(activity)
    setIsDialogOpen(true)
  }

  // Function to get activity icon based on type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'article-review':
        return <FileText className="h-4 w-4 text-blue-500" />
      case 'article-comment':
        return <MessageSquare className="h-4 w-4 text-green-500" />
      case 'student-approval':
        return <CheckCircle className="h-4 w-4 text-purple-500" />
      case 'profile-image-approval':
        return <ImageIcon className="h-4 w-4 text-amber-500" />
      case 'name-change-approval':
        return <Edit className="h-4 w-4 text-indigo-500" />
      case 'news-post':
        return <Newspaper className="h-4 w-4 text-red-500" />
      case 'achievement-earned':
        return <Award className="h-4 w-4 text-yellow-500" />
      case 'login':
        return <LogIn className="h-4 w-4 text-gray-500" />
      default:
        return null
    }
  }

  // Format activity type for display
  const formatActivityType = (type: string) => {
    return type
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">الأنشطة</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>الأنشطة الأخيرة</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المستخدم</TableHead>
                  <TableHead>نوع النشاط</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>التفاصيل</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {activities.length > 0 ? (
                  activities.map((activity) => {
                    return (
                      <TableRow key={activity.id}>
                        <TableCell>
                          <div className="flex flex-col">
                            {/* Display email if userId is populated, otherwise 'Unknown' */}
                            <span className="font-medium">
                              {typeof activity.userId === 'object' && activity.userId?.email
                                ? activity.userId.email
                                : 'غير معروف'}
                            </span>
                            {typeof activity.userId === 'object' &&
                              activity.userId?.firstName &&
                              activity.userId?.lastName && (
                                <span className="text-xs text-muted-foreground">
                                  {activity.userId.firstName} {activity.userId.lastName}
                                </span>
                              )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getActivityIcon(activity.activityType)}
                            <Badge variant="outline" className="capitalize">
                              {formatActivityType(activity.activityType)}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          {/* Ensure createdAt exists and is valid before formatting */}
                          {activity.createdAt ? (
                            <div className="flex flex-col">
                              <span>{new Date(activity.createdAt).toLocaleDateString()}</span>
                              <span className="text-xs text-muted-foreground">
                                {new Date(activity.createdAt).toLocaleTimeString()}
                              </span>
                            </div>
                          ) : (
                            'تاريخ غير صالح'
                          )}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewDetails(activity)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            عرض
                          </Button>
                        </TableCell>
                      </TableRow>
                    )
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-4">
                      لم يتم العثور على أنشطة
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Activity Details Dialog */}
      <ActivityDetailsDialog
        activity={selectedActivity}
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
      />
    </DashboardLayout>
  )
}
