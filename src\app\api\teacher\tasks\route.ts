import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

// GET teacher tasks
export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user is a teacher
      const role = typeof user.role === 'object' ? user.role?.slug : user.role

      if (role !== 'teacher') {
        return NextResponse.json(
          {
            error: 'Forbidden',
            message: 'Only teachers can access this endpoint',
            userRole: role,
          },
          { status: 403 },
        )
      }

      // Get school ID
      const schoolId = typeof user.school === 'object' ? user.school?.id : user.school

      if (!schoolId) {
        return NextResponse.json(
          { error: 'Teacher must be associated with a school' },
          { status: 400 },
        )
      }

      // Get today's date at midnight
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      // Get activities for today
      const activities = await payload.find({
        collection: 'activities',
        where: {
          userId: { equals: userId },
          createdAt: { greater_than: today.toISOString() },
        },
      })

      // Count activities by type
      const activityCounts = {
        'article-review': 0,
        'student-approval': 0,
        'profile-image-approval': 0,
        'name-change-approval': 0,
      }

      activities.docs.forEach((activity) => {
        if (activityCounts[activity.activityType] !== undefined) {
          activityCounts[activity.activityType]++
        }
      })

      // Define daily tasks
      const dailyTasks = [
        {
          id: 'review-articles',
          title: 'Review Student Articles',
          description: 'Review at least 3 student articles',
          target: 3,
          current: activityCounts['article-review'],
          completed: activityCounts['article-review'] >= 3,
          points: 30, // 10 points per review
          type: 'article-review',
        },
        {
          id: 'approve-students',
          title: 'Approve New Students',
          description: 'Approve at least 2 new student registrations',
          target: 2,
          current: activityCounts['student-approval'],
          completed: activityCounts['student-approval'] >= 2,
          points: 10, // 5 points per approval
          type: 'student-approval',
        },
        {
          id: 'approve-profile-changes',
          title: 'Review Profile Changes',
          description: 'Review at least 2 profile image or name change requests',
          target: 2,
          current: activityCounts['profile-image-approval'] + activityCounts['name-change-approval'],
          completed: (activityCounts['profile-image-approval'] + activityCounts['name-change-approval']) >= 2,
          points: 10, // 5 points per approval
          type: 'profile-changes',
        },
      ]

      // Calculate completion percentage
      const completedTasks = dailyTasks.filter((task) => task.completed).length
      const completionPercentage = Math.round((completedTasks / dailyTasks.length) * 100)

      // Calculate total points earned today
      const pointsEarnedToday = activities.docs.reduce((total, activity) => {
        return total + (activity.points || 0)
      }, 0)

      return NextResponse.json({
        tasks: dailyTasks,
        completionPercentage,
        pointsEarnedToday,
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching teacher tasks:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
