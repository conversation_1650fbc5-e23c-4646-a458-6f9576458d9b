import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

/**
 * Super-Admin Schools API
 * Endpoints for managing all schools in the system
 */

// GET all schools with statistics
export async function GET(req: NextRequest) {
  try {
    // Get the token from the cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can access this endpoint' },
          { status: 403 },
        )
      }

      // Parse URL parameters
      const url = new URL(req.url)
      const includeStats = url.searchParams.get('includeStats') === 'true'
      const limit = parseInt(url.searchParams.get('limit') || '50')
      const page = parseInt(url.searchParams.get('page') || '1')
      const sortField = url.searchParams.get('sortField') || 'createdAt'
      const sortOrder = url.searchParams.get('sortOrder') || 'desc'
      const searchTerm = url.searchParams.get('searchTerm') || ''

      // Connect to MongoDB
      const { db } = await connectToDatabase()

      try {
        // Build query
        const query: any = {}
        if (searchTerm) {
          query.$or = [
            { name: { $regex: searchTerm, $options: 'i' } },
            { city: { $regex: searchTerm, $options: 'i' } },
            { state: { $regex: searchTerm, $options: 'i' } },
            { country: { $regex: searchTerm, $options: 'i' } },
          ]
        }

        // Get total count
        const totalCount = await db.collection('schools').countDocuments(query)

        // Get schools with pagination and sorting
        const sortOptions: any = {}
        sortOptions[sortField] = sortOrder === 'asc' ? 1 : -1

        const schools = await db
          .collection('schools')
          .find(query)
          .sort(sortOptions)
          .skip((page - 1) * limit)
          .limit(limit)
          .toArray()

        // If stats are requested, fetch them
        if (includeStats) {
          // Process schools to add statistics
          const schoolsWithStats = await Promise.all(
            schools.map(async (school) => {
              const schoolId = school._id.toString()

              // Get user count by role for this school - handle multiple school field formats
              const userStats = await db
                .collection('users')
                .aggregate([
                  {
                    $match: {
                      $or: [
                        { school: schoolId },
                        { school: new ObjectId(schoolId) },
                        { 'school.id': schoolId },
                        { 'school._id': new ObjectId(schoolId) },
                      ],
                    },
                  },
                  {
                    $group: {
                      _id: { $ifNull: ['$role.slug', '$role'] },
                      count: { $sum: 1 },
                    },
                  },
                ])
                .toArray()

              console.log(`School ${school.name} (${schoolId}) user stats:`, userStats)

              // Debug: Check what users are actually in this school
              const schoolUsers = await db
                .collection('users')
                .find({
                  $or: [
                    { school: schoolId },
                    { school: new ObjectId(schoolId) },
                    { 'school.id': schoolId },
                    { 'school._id': new ObjectId(schoolId) },
                  ],
                })
                .limit(3)
                .toArray()
              console.log(
                `Sample users for school ${school.name}:`,
                schoolUsers.map((u) => ({
                  id: u._id,
                  role: u.role,
                  school: u.school,
                  email: u.email,
                })),
              )

              // Get content counts
              const articlesCount = await db.collection('articles').countDocuments({
                school: schoolId,
              })

              const newsCount = await db.collection('news').countDocuments({
                school: schoolId,
              })

              // Get activity count for last 30 days
              const thirtyDaysAgo = new Date()
              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

              const activityCount = await db.collection('activities').countDocuments({
                $or: [{ school: schoolId }, { schoolId: schoolId }],
                createdAt: { $gte: thirtyDaysAgo },
              })

              return {
                ...school,
                stats: {
                  users: userStats,
                  articlesCount,
                  newsCount,
                  activityLast30Days: activityCount,
                },
              }
            }),
          )

          return NextResponse.json({
            schools: schoolsWithStats,
            pagination: {
              total: totalCount,
              page,
              pageSize: limit,
              totalPages: Math.ceil(totalCount / limit),
            },
          })
        }

        // Return schools without stats
        return NextResponse.json({
          schools,
          pagination: {
            total: totalCount,
            page,
            pageSize: limit,
            totalPages: Math.ceil(totalCount / limit),
          },
        })
      } catch (dbError) {
        console.error('Database error:', dbError)

        // Fallback to Payload CMS if MongoDB fails
        const payloadQuery: any = {
          limit,
          page,
          sort: sortField,
          where: {},
        }

        if (sortOrder === 'desc') {
          payloadQuery.sort = `-${payloadQuery.sort}`
        }

        if (searchTerm) {
          payloadQuery.where.or = [
            { name: { contains: searchTerm } },
            { city: { contains: searchTerm } },
            { state: { contains: searchTerm } },
            { country: { contains: searchTerm } },
          ]
        }

        const schoolsResult = await payload.find({
          collection: 'schools',
          ...payloadQuery,
        })

        return NextResponse.json(schoolsResult)
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching schools:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// POST a new school
export async function POST(req: NextRequest) {
  try {
    // Get the token from the cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can create schools' },
          { status: 403 },
        )
      }

      // Get request body
      const body = await req.json()

      // Validate required fields
      const requiredFields = ['name', 'city', 'state', 'country']
      for (const field of requiredFields) {
        if (!body[field]) {
          return NextResponse.json(
            { error: 'Bad Request', message: `Missing required field: ${field}` },
            { status: 400 },
          )
        }
      }

      // Create the school using Payload CMS
      const newSchool = await payload.create({
        collection: 'schools',
        data: {
          name: body.name,
          address: body.address,
          admin: body.admin,
          // Remove fields that don't exist in the School model
        },
      })

      return NextResponse.json(newSchool, { status: 201 })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error creating school:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// PUT to update a school
export async function PUT(req: NextRequest) {
  try {
    // Get the token from the cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can update schools' },
          { status: 403 },
        )
      }

      // Get request body
      const body = await req.json()

      // Check if school ID is provided
      if (!body.id) {
        return NextResponse.json(
          { error: 'Bad Request', message: 'School ID is required' },
          { status: 400 },
        )
      }

      // Update the school using Payload CMS
      const updatedSchool = await payload.update({
        collection: 'schools',
        id: body.id,
        data: {
          name: body.name,
          address: body.address,
          admin: body.admin,
          // Remove fields that don't exist in the School model
        },
      })

      return NextResponse.json(updatedSchool)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating school:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// DELETE a school (soft delete by changing status to 'inactive')
export async function DELETE(req: NextRequest) {
  try {
    // Get the token from the cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can delete schools' },
          { status: 403 },
        )
      }

      // Parse URL parameters
      const url = new URL(req.url)
      const schoolId = url.searchParams.get('id')
      const hardDelete = url.searchParams.get('hardDelete') === 'true'

      if (!schoolId) {
        return NextResponse.json(
          { error: 'Bad Request', message: 'School ID is required' },
          { status: 400 },
        )
      }

      if (hardDelete) {
        // Hard delete - actually remove from database
        // This should be used with extreme caution
        await payload.delete({
          collection: 'schools',
          id: schoolId,
        })

        return NextResponse.json({ message: 'School permanently deleted' })
      } else {
        // Soft delete - update status to 'inactive'
        await payload.update({
          collection: 'schools',
          id: schoolId,
          data: {
            // Since status field doesn't exist, we can't set it to inactive
            // Instead, we can add a note in the address field
            address: 'INACTIVE - Former school address',
          },
        })

        return NextResponse.json({ message: 'School deactivated successfully' })
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error deleting school:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
