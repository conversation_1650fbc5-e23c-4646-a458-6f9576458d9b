import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'

import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    console.log('Change password API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    console.log('Token found:', token ? 'Yes' : 'No')

    if (!token) {
      console.log('No token found, returning 401')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      console.log('Request body received')

      const { currentPassword, newPassword } = body

      if (!currentPassword || !newPassword) {
        console.log('Missing required fields')
        return NextResponse.json(
          { error: 'Current password and new password are required' },
          { status: 400 },
        )
      }

      // Initialize Payload
      const payload = await getPayload({ config })

      // Get the user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        showHiddenFields: true,
      })

      if (!user) {
        console.log('User not found')
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      // Verify current password
      const isPasswordValid = await bcrypt.compare(currentPassword, user.password)

      if (!isPasswordValid) {
        console.log('Current password is incorrect')
        return NextResponse.json({ error: 'Current password is incorrect' }, { status: 400 })
      }

      // Hash the new password
      const salt = await bcrypt.genSalt(10)
      const hashedPassword = await bcrypt.hash(newPassword, salt)

      // Build update data with password and required fields
      const updateData = {
        password: hashedPassword,
      }

      // Handle role relationship properly
      try {
        // Get the role ID directly from the database
        if (user.role) {
          let roleId = null

          // If role is an object with id, use the id
          if (typeof user.role === 'object' && user.role !== null && user.role.id) {
            console.log('Using role ID from object:', user.role.id)
            updateData.role = user.role.id
          }
          // If role is a string, use it directly
          else if (typeof user.role === 'string') {
            console.log('Using role ID from string:', user.role)
            updateData.role = user.role
          }
          // If role is an object with slug, look up the ID
          else if (typeof user.role === 'object' && user.role !== null && user.role.slug) {
            console.log('Looking up role ID by slug:', user.role.slug)

            // Find the role by slug
            const roleDoc = await payload.find({
              collection: 'roles',
              where: {
                slug: {
                  equals: user.role.slug,
                },
              },
            })

            if (roleDoc.docs && roleDoc.docs.length > 0) {
              roleId = roleDoc.docs[0].id
              console.log('Found role ID by slug:', roleId)
              updateData.role = roleId
            } else {
              console.error('Role not found by slug:', user.role.slug)
            }
          }
        }
      } catch (roleError) {
        console.error('Error handling role relationship:', roleError)
      }

      // Preserve school field if it exists
      if (user.school) {
        // If school is an object with id, use the id
        if (typeof user.school === 'object' && user.school !== null && user.school.id) {
          console.log('Using school ID from object:', user.school.id)
          updateData.school = user.school.id
        }
        // If school is a string, use it directly
        else if (typeof user.school === 'string') {
          console.log('Using school ID from string:', user.school)
          updateData.school = user.school
        }
      }

      console.log('Updating user with data:', JSON.stringify(updateData, null, 2))

      // Update the user's password
      try {
        const updatedUser = await payload.update({
          collection: 'users',
          id: userId,
          data: updateData,
        })
        console.log('Password updated successfully')
      } catch (updateError) {
        console.error('Error updating password:', updateError)

        // Try a more minimal update as a fallback
        try {
          console.log('Trying minimal update as fallback')

          // Create a minimal update with only the password
          const minimalUpdate = {
            password: hashedPassword,
          }

          // Always include role ID
          if (user.role) {
            if (typeof user.role === 'object' && user.role !== null && user.role.id) {
              minimalUpdate.role = user.role.id
            } else if (typeof user.role === 'string') {
              minimalUpdate.role = user.role
            } else if (typeof user.role === 'object' && user.role !== null && user.role.slug) {
              // Try to find the role by slug
              const roleDoc = await payload.find({
                collection: 'roles',
                where: {
                  slug: {
                    equals: user.role.slug,
                  },
                },
              })

              if (roleDoc.docs && roleDoc.docs.length > 0) {
                minimalUpdate.role = roleDoc.docs[0].id
              }
            }
          }

          // Include school ID if needed
          if (
            user.school &&
            typeof user.school === 'object' &&
            user.school !== null &&
            user.school.id
          ) {
            minimalUpdate.school = user.school.id
          } else if (user.school && typeof user.school === 'string') {
            minimalUpdate.school = user.school
          }

          console.log('Minimal update data:', JSON.stringify(minimalUpdate, null, 2))

          const minimalUpdatedUser = await payload.update({
            collection: 'users',
            id: userId,
            data: minimalUpdate,
          })

          console.log('Password updated with minimal data')
        } catch (fallbackError) {
          console.error('Fallback update also failed:', fallbackError)
          return NextResponse.json({ error: 'Failed to update password' }, { status: 500 })
        }
      }

      console.log('Password updated successfully')
      return NextResponse.json({
        success: true,
        message: 'Password updated successfully',
      })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error changing password:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
