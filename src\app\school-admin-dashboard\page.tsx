'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function SchoolAdminDashboardRedirect() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the new unified dashboard
    router.replace('/dashboard')
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100" dir="rtl">
      <div className="p-8 bg-white rounded shadow-md">
        <h1 className="text-2xl font-bold mb-4">جاري التحويل...</h1>
        <p>يرجى الانتظار بينما نقوم بتحويلك إلى لوحة التحكم الجديدة.</p>
      </div>
    </div>
  )
}
