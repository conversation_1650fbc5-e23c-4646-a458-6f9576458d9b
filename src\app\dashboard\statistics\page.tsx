'use client'

import { useEffect, useState } from 'react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CrossSchoolStatistics } from '@/app/dashboard/super-admin/components/cross-school-statistics'
import { RoleDistributionChart } from '@/app/dashboard/super-admin/components/role-distribution-chart'
import { GlobalActivityHeatmap } from '@/app/dashboard/super-admin/components/global-activity-heatmap'

export default function StatisticsPage() {
  // Define a proper type for stats
  const [stats, setStats] = useState<{
    totalUsers?: number
    totalArticles?: number
    totalSchools?: number
    articlesByMonth?: Array<{ name: string; count: number }>
    usersByRole?: Array<{ name: string; value: number }>
  } | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function fetchStatistics() {
      try {
        const response = await fetch('/api/dashboard/statistics', {
          credentials: 'include',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch statistics')
        }

        const data = await response.json()
        setStats(data)
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching statistics:', err)
        setError('Failed to load statistics. Please try again.')
        setIsLoading(false)
      }
    }

    fetchStatistics()
  }, [])

  // Mock data for demonstration
  const mockArticlesByMonth = [
    { name: 'Jan', count: 12 },
    { name: 'Feb', count: 19 },
    { name: 'Mar', count: 15 },
    { name: 'Apr', count: 22 },
    { name: 'May', count: 28 },
    { name: 'Jun', count: 24 },
  ]

  const mockUsersByRole = [
    { name: 'Students', value: 120 },
    { name: 'Teachers', value: 45 },
    { name: 'Mentors', value: 15 },
    { name: 'Admins', value: 5 },
  ]

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        <h1 className="text-2xl font-bold mb-6">Statistics</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Cross-School Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <CrossSchoolStatistics />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Role Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <RoleDistributionChart />
            </CardContent>
          </Card>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Global Activity Heatmap</CardTitle>
          </CardHeader>
          <CardContent>
            <GlobalActivityHeatmap />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
