'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  profileImage?: string | { url?: string; id?: string } | null
  role?:
    | {
        id: string
        name: string
        slug: string
      }
    | string
}

export function UserMenu() {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchUser() {
      try {
        // Add credentials to ensure cookies are sent with the request
        const response = await fetch('/api/auth/me', {
          credentials: 'include',
        })
        const data = await response.json()

        console.log('Auth response status:', response.status)
        console.log('Auth data:', data)

        if (response.ok && data.user) {
          setUser(data.user)
          console.log('User authenticated:', data.user.email)
          console.log(
            'User role:',
            data.role ||
              (typeof data.user.role === 'object' ? data.user.role?.slug : data.user.role),
          )
        } else {
          console.log('User not authenticated or error:', data.error)
        }

        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching user:', err)
        setIsLoading(false)
      }
    }

    fetchUser()
  }, [])

  if (isLoading) {
    return null
  }

  if (!user) {
    return (
      <Button className="bg-transparent border-primary text-primary" asChild variant="outline">
        <Link href="/login">تسجيل الدخول</Link>
      </Button>
    )
  }

  const userInitials =
    `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`.toUpperCase() ||
    user.email[0].toUpperCase()

  // User role is no longer needed for navigation as we're using the unified dashboard

  const getDashboardLink = () => {
    // Always use the unified dashboard
    return '/dashboard'
  }

  const getProfileLink = () => {
    // Always use the unified dashboard profile
    return '/dashboard/profile'
  }

  return (
    <DropdownMenu dir="rtl">
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative border border-primary  h-11 w-11 rounded-full">
          <Avatar>
            {user.profileImage ? (
              <AvatarImage
                src={
                  typeof user.profileImage === 'string'
                    ? user.profileImage
                    : typeof user.profileImage === 'object' && user.profileImage?.url
                      ? user.profileImage.url
                      : ''
                }
                alt={user.email}
              />
            ) : (
              <AvatarImage src="" alt={user.email} />
            )}
            <AvatarFallback>{userInitials}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.email}
            </p>
            <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={getDashboardLink()}>لوحة التحكم</Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={getProfileLink()}>البروفايل</Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => {
            window.location.href = '/api/logout'
          }}
          className="text-red-600 cursor-pointer"
        >
          تسجيل خروج
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
