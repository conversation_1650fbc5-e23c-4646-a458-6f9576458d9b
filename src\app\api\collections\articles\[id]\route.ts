import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { isMediaPath, isMediaPathError } from '@/lib/media-utils'
import payloadConfig from '@/payload.config'
import { extractUserFromToken } from '@/lib/security'
import { ObjectId } from 'mongodb'
import { articleAccess, isAuthor } from '@/access' // Import articleAccess and isAuthor
import { connectToDatabase } from '@/lib/mongodb'
import { error } from 'console'
import jwt from 'jsonwebtoken'
import { incrementViewCount } from '@/utils/viewUtils'

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the ID from the URL
    const { id } = await Promise.resolve(params)

    // Check if the ID is a media path
    if (isMediaPath(id)) {
      return NextResponse.json({ error: 'Invalid article ID' }, { status: 400 })
    }

    // Get the payload instance
    const payload = await getPayload({ config: payloadConfig })

    // Get the article item
    const article = await payload.findByID({
      collection: 'articles',
      id,
      depth: 2, // Populate relationships
    })

    // Only increment view count if it's a GET request from a client
    // We check the Accept header to determine if it's a browser request
    const acceptHeader = request.headers.get('Accept') || ''
    if (acceptHeader.includes('text/html') || acceptHeader.includes('application/json')) {
      // Increment view count asynchronously
      incrementViewCount('articles', id).catch(error => {
        console.error('Error incrementing view count:', error)
      })
    }

    return NextResponse.json(article)
  } catch (error) {
    console.error('Error fetching article item:', error)
    return NextResponse.json({ error: 'Failed to fetch article item' }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the ID from the URL
    const { id } = await Promise.resolve(params)

    // Check if the ID is a media path
    if (isMediaPath(id)) {
      return NextResponse.json({ error: 'Invalid article ID' }, { status: 400 })
    }

    // Get the current user from cookies - await cookies() properly
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Authentication token not found' }, { status: 401 })
    }

    // Get the user using extractUserFromToken
    const userInfo = await extractUserFromToken(token)

    if (!userInfo || !userInfo.id) {
      return NextResponse.json(
        { error: 'You must be logged in to update an article' },
        { status: 401 },
      )
    }

    // Get the payload instance with config
    const payload = await getPayload({ config: payloadConfig })

    // Fetch the full user details from the database with increased depth
    const user = await payload.findByID({
      collection: 'users',
      id: userInfo.id,
      depth: 2, // Increase depth to better populate role
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 })
    }
    
    console.log('PATCH: Found user:', {
      id: user.id,
      email: user.email,
      role: user.role
    });

    // Add the collection property to the user object
    const userWithCollection = {
      ...user,
      collection: 'users' as const,
    }

    // Get the article post
    const articlePost = await payload.findByID({
      collection: 'articles',
      id,
      depth: 2, // Add depth to populate relationships
    })

    if (!articlePost) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 })
    }

    // Create a mock PayloadRequest object
    const mockReq = {
      user: userWithCollection,
      payload,
      method: 'PATCH',
      locale: 'en', // Assuming default locale is 'en'
      payloadAPI: 'REST', // Assuming REST API
    } as any

    // Read the request body once
    const body = await request.json()

    // Handle user.role as object or string for admin checks with enhanced role resolution
    let isSuperAdminUser = false
    let isSchoolAdminUser = false
    
    // Extract role information
    const rawRole = user.role;
    console.log('PATCH: Raw role value:', rawRole);
    
    // Check role directly from user object first
    if (typeof rawRole === 'object' && rawRole !== null && 'slug' in rawRole) {
      // If role is already populated as an object with slug
      isSuperAdminUser = rawRole.slug === 'super-admin';
      isSchoolAdminUser = rawRole.slug === 'school-admin';
      console.log('PATCH: Role from object with slug:', rawRole.slug);
    } 
    // Then try resolving role ID if needed
    else if (rawRole && typeof rawRole === 'string' && rawRole.length === 24) {
      try {
        console.log('PATCH: Attempting to fetch role with ID:', rawRole);
        const roleDoc = await payload.findByID({ 
          collection: 'roles', 
          id: rawRole,
          depth: 2
        });
        
        if (roleDoc && roleDoc.slug) {
          isSuperAdminUser = roleDoc.slug === 'super-admin';
          isSchoolAdminUser = roleDoc.slug === 'school-admin';
          console.log('PATCH: Resolved role slug:', roleDoc.slug);
        } else {
          console.log('PATCH: Role document found but no slug:', roleDoc);
          // Fall back to access control functions
          const superAdminResult = await import('@/access').then((m) => m.isSuperAdmin({ req: mockReq }))
          const schoolAdminResult = await import('@/access').then((m) => m.isSchoolAdmin({ req: mockReq }))
          isSuperAdminUser = typeof superAdminResult === 'boolean' ? superAdminResult : false
          isSchoolAdminUser = typeof schoolAdminResult === 'boolean' ? schoolAdminResult : false
        }
      } catch (roleError) {
        console.error('PATCH: Error fetching role:', roleError);
        // Fall back to access control functions
        const superAdminResult = await import('@/access').then((m) => m.isSuperAdmin({ req: mockReq }))
        const schoolAdminResult = await import('@/access').then((m) => m.isSchoolAdmin({ req: mockReq }))
        isSuperAdminUser = typeof superAdminResult === 'boolean' ? superAdminResult : false
        isSchoolAdminUser = typeof schoolAdminResult === 'boolean' ? schoolAdminResult : false
      }
    } else {
      // Fall back to access control functions
      console.log('PATCH: Using access control functions for role check');
      const superAdminResult = await import('@/access').then((m) => m.isSuperAdmin({ req: mockReq }))
      const schoolAdminResult = await import('@/access').then((m) => m.isSchoolAdmin({ req: mockReq }))
      isSuperAdminUser = typeof superAdminResult === 'boolean' ? superAdminResult : false
      isSchoolAdminUser = typeof schoolAdminResult === 'boolean' ? schoolAdminResult : false
    }
    
    // If still not clearly an admin, try direct MongoDB query as absolute last resort
    if (!isSuperAdminUser && !isSchoolAdminUser) {
      try {
        console.log('PATCH: Attempting direct MongoDB query for role...');
        const { db } = await import('@/lib/mongodb').then((mod) => mod.connectToDatabase());
        const { ObjectId } = await import('mongodb');
        
        // First get user with populated role
        const dbUser = await db.collection('users').findOne({ _id: new ObjectId(userInfo.id) });
        console.log('PATCH: User from MongoDB:', { id: dbUser?._id, email: dbUser?.email, role: dbUser?.role });
        
        if (dbUser && dbUser.role) {
          // Try to get role document
          const roleId = typeof dbUser.role === 'object' && dbUser.role !== null ? 
            (dbUser.role.$oid || dbUser.role._id || dbUser.role.id) : 
            dbUser.role;
            
          console.log('PATCH: Role ID from MongoDB user:', roleId);
          
          if (roleId && typeof roleId === 'string') {
            try {
              const roleDoc = await db.collection('roles').findOne({ 
                $or: [
                  { _id: new ObjectId(roleId) },
                  { id: roleId }
                ] 
              });
              
              if (roleDoc) {
                console.log('PATCH: Role from MongoDB:', roleDoc);
                isSuperAdminUser = roleDoc.slug === 'super-admin';
                isSchoolAdminUser = roleDoc.slug === 'school-admin';
                if (isSuperAdminUser || isSchoolAdminUser) {
                  console.log('PATCH: Admin status granted through direct MongoDB query');
                }
              }
            } catch (roleQueryError) {
              console.error('PATCH: Error querying role from MongoDB:', roleQueryError);
            }
          }
        }
      } catch (mongoError) {
        console.error('PATCH: Error with direct MongoDB query:', mongoError);
      }
    }
    
    console.log('PATCH: Final admin check result:', { isSuperAdminUser, isSchoolAdminUser });

    // Helper function to determine if a user is an admin (handles both role formats)
    async function isUserAdmin(user: any, payload: any): Promise<boolean> {
      // Direct check if role is an object with slug
      if (typeof user.role === 'object' && user.role !== null && 'slug' in user.role) {
        return user.role.slug === 'super-admin' || user.role.slug === 'school-admin';
      }
      
      // If role is an ID reference, fetch the role
      if (user.role) {
        try {
          const role = await payload.findByID({
            collection: 'roles',
            id: user.role,
          });
          
          return role?.slug === 'super-admin' || role?.slug === 'school-admin';
        } catch (error) {
          console.error('Error fetching role for admin check:', error);
          return false;
        }
      }
      
      return false;
    }

    // Check access control based on articleAccess logic
    const accessGranted = await articleAccess({
      req: mockReq,
      id,
      data: body, // Pass the already read body
    })

    if (!accessGranted) {
      return NextResponse.json(
        { error: 'You do not have permission to update this article' },
        { status: 403 },
      )
    }

    const { title, content, featuredImage, status, slug: bodySlug, teacherReview } = body // Destructure slug from body as bodySlug

    // Validate inputs
    if (!title || !status) {
      return NextResponse.json({ error: 'Title and status are required' }, { status: 400 })
    }

    // Validate status
    if (
      status !== 'draft' &&
      status !== 'ready-for-review' &&
      status !== 'pending-review' &&
      status !== 'published'
    ) {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 })
    }

    // {{change 1}} Determine the slug for the update based on provided values
    let finalSlug = articlePost.slug // Default to existing slug

    // If a slug is provided in the body and is not an empty string after trimming, prioritize it.
    // Otherwise, if title is provided, generate slug from the title.
    const trimmedBodySlug = typeof bodySlug === 'string' ? bodySlug.trim() : ''
    function slugify(raw = '') {
      return raw
        .toLowerCase()
        .trim()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-+|-+$/g, '')
    }

    // …

    if (trimmedBodySlug) {
      finalSlug = slugify(trimmedBodySlug)
    } else if (title) {
      finalSlug = slugify(title)
    }

    if (!finalSlug && title) {
      finalSlug = slugify(title)
      console.warn('Generated slug was empty, falling back to generating from title.')
    }
    // Update the article post data
    const updateData: any = {
      title,
      slug: finalSlug, // Use the determined slug
      status,
    }

    // Conditionally set the author if a valid author is provided in the request body
    if (body.author && typeof body.author === 'string' && body.author.length === 24) {
      // Check if the author is a student
      const authorUser = await payload.findByID({
        collection: 'users',
        id: body.author,
      })

      if (
        !authorUser ||
        typeof authorUser.role === 'string' ||
        authorUser.role?.name !== 'student'
      ) {
        if (isSuperAdminUser || isSchoolAdminUser) {
          console.warn(
            `Attempted to set author to invalid user ID: ${body.author} by admin. Removing author field.`,
          )
          delete body.author
        } else {
          console.warn(`Attempted to set author to invalid user ID: ${body.author}`)
        }
      }
    } else if (articlePost.author) {
      // If no valid author is provided in the body, ensure author is a string for Payload
      if (typeof articlePost.author === 'object' && articlePost.author !== null) {
        // Type guard for $oid property
        if ('\$oid' in articlePost.author && typeof (articlePost.author as any).$oid === 'string') {
          updateData.author = (articlePost.author as any).$oid
        } else if (
          'id' in articlePost.author &&
          typeof (articlePost.author as any).id === 'string'
        ) {
          updateData.author = (articlePost.author as any).id
        }
      } else if (typeof articlePost.author === 'string') {
        updateData.author = articlePost.author
      }
    }

    // Handle content field - ensure it's in the exact format Payload CMS expects
    if (content) {
      // If content is already a stringified JSON, parse it
      if (typeof content === 'string') {
        try {
          updateData.content = JSON.parse(content)
        } catch (e) {
          // If parsing fails, create a valid minimal structure
          updateData.content = {
            root: {
              type: 'root',
              children: [
                {
                  type: 'paragraph',
                  children: [{ text: content || '' }],
                  direction: null,
                  format: '',
                  indent: 0,
                  version: 1,
                },
              ],
              direction: null,
              format: '',
              indent: 0,
              version: 1,
            },
          }
        }
      } else if (content.root) {
        // Use the object directly if it has the proper structure
        updateData.content = content
      } else {
        // Create a valid structure with the content as text
        updateData.content = {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [{ text: JSON.stringify(content) || '' }],
                direction: null,
                format: '',
                indent: 0,
                version: 1,
              },
            ],
            direction: null,
            format: '',
            indent: 0,
            version: 1,
          },
        }
      }
    } else {
      // Provide a minimal valid content structure
      updateData.content = {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [{ text: '' }],
              direction: null,
              format: '',
              indent: 0,
              version: 1,
            },
          ],
          direction: null,
          format: '',
          indent: 0,
          version: 1,
        },
      }
    }

    // Handle featuredImage - keep the full path format instead of extracting ID
    if (featuredImage !== undefined) {
      if (featuredImage === null) {
        // Allow null to clear the image
        updateData.featuredImage = null;
      } else if (typeof featuredImage === 'string') {
        if (isMediaPath(featuredImage)) {
          // Keep the full path as is if it's already a valid media path
          console.log('Using media path directly:', featuredImage);
          updateData.featuredImage = featuredImage;
        } else if (ObjectId.isValid(featuredImage)) {
          // If it's a plain ObjectId, convert it to a path
          updateData.featuredImage = `/api/media/file/${featuredImage}`;
          console.log('Converted ID to path:', updateData.featuredImage);
        } else {
          // If it's not a valid path or ID, set to null
          console.warn(`Invalid featuredImage format: ${featuredImage}. Setting to null.`);
          updateData.featuredImage = null;
        }
      } else {
        // If it's not a string or null, set to null
        console.warn(`Invalid featuredImage type: ${typeof featuredImage}. Setting to null.`);
        updateData.featuredImage = null;
      }
    }

    // If publishing for the first time, add publishedAt date
    // Only set publishedAt
    if (
      status === 'published' &&
      articlePost.status === 'draft' &&
      'publishedAt' in articlePost &&
      !articlePost.publishedAt
    ) {
      updateData.publishedAt = new Date().toISOString()
    }

    if (teacherReview) {
      updateData.teacherReview = teacherReview
    }

    // Log the update data for debugging
    console.log('Updating article post with data:', JSON.stringify(updateData, null, 2))

    // Check if the user is a super-admin, school-admin, or the author
    const isAdminUser = await isUserAdmin(user, payload);
    
    // Helper function to safely compare IDs that might be in different formats
    const compareIds = (id1: string | null | undefined, id2: string | null | undefined): boolean => {
      if (!id1 || !id2) return false;
      // Normalize IDs by trimming and converting to string
      const normalizedId1 = String(id1).trim();
      const normalizedId2 = String(id2).trim();
      return normalizedId1 === normalizedId2;
    };
    
    // Enhanced author check with detailed debugging
    console.log('PATCH: Author check - DEBUG:', {
      user: {
        id: userWithCollection.id,
        email: userWithCollection.email,
        role: typeof userWithCollection.role === 'object' ? 
          (userWithCollection.role ? userWithCollection.role.slug : 'null-role-obj') : 
          userWithCollection.role
      },
      article: {
        id: articlePost.id,
        author: articlePost.author,
        authorType: typeof articlePost.author,
        authorId: typeof articlePost.author === 'object' ? 
          (articlePost.author ? articlePost.author.id : 'null-author-id') : 
          articlePost.author
      }
    });
    
    // Detailed isAuthor check with both imported function and manual check
    const isAuthorImported = isAuthor(userWithCollection, articlePost);
    
    // Extract author ID from article
    const articleAuthorId = typeof articlePost.author === 'object' ? 
      (articlePost.author ? articlePost.author.id : null) : 
      articlePost.author;
    
    // Manual author check using our robust compareIds function
    const isAuthorManual = compareIds(articleAuthorId, userWithCollection.id);
    console.log(`PATCH: Author check with compareIds: ${articleAuthorId} === ${userWithCollection.id} => ${isAuthorManual}`);
    
    // Combine results for final check
    const isAuthorUser = isAuthorImported || isAuthorManual;
    
    console.log('PATCH: Final author check:', { 
      isAuthorImported, 
      isAuthorManual, 
      isAuthorUser 
    });

    if (!isSuperAdminUser && !isSchoolAdminUser && !isAuthorUser) {
      return NextResponse.json(
        { error: 'You do not have permission to update this article', details: {
          userId: userInfo.id,
          userRole: typeof user.role === 'object' ? user.role.slug : user.role,
          articleAuthor: typeof articlePost.author === 'object' ? articlePost.author.id : articlePost.author,
          isSuperAdminUser,
          isSchoolAdminUser,
          isAuthorUser,
        } },
        { status: 403 },
      )
    }

    try {
      // Create a clean copy of the update data to avoid any reference issues
      const cleanUpdateData = JSON.parse(JSON.stringify(updateData))

      let updatedArticle
      try {
        // Try to update the article as usual
        updatedArticle = await payload.update({
          collection: 'articles',
          id,
          data: cleanUpdateData,
        })
      } catch (updateError) {
        // Log the full error for debugging
        console.error('Error during article update:', updateError);
        
        if (updateError && typeof updateError === 'object') {
          console.error('Error details:', JSON.stringify(updateError, null, 2));
        }
        
        // If the error is a validation error about the author field, and the user is an admin, try a direct MongoDB update
        const isAdmin = isSuperAdminUser || isSchoolAdminUser;
        console.log('PATCH: Checking if direct MongoDB update is needed. Is admin:', isAdmin);
        
        if (isAdmin) {
          console.log('PATCH: Attempting direct MongoDB update as admin user');
          
          // Fallback: Direct MongoDB update
          try {
            const { db } = await connectToDatabase();
            
            // Ensure we're setting the featuredImage field properly in the direct MongoDB update too
            const cleanUpdateDataForMongo = { ...cleanUpdateData };
            console.log('MongoDB direct update data:', cleanUpdateDataForMongo);
            
            // Convert ID string to ObjectId for MongoDB
            const mongoId = typeof id === 'string' ? new ObjectId(id) : id;
            
            // Perform update
            const updateResult = await db
              .collection('articles')
              .updateOne(
                { _id: mongoId },
                { $set: cleanUpdateDataForMongo },
              );
              
            console.log('PATCH: Direct MongoDB update result:', updateResult);
            
            if (updateResult.modifiedCount === 1) {
              // Fetch the updated article, but handle potential media path errors
              try {
                updatedArticle = await payload.findByID({ collection: 'articles', id });
                console.log('PATCH: Successfully updated article through MongoDB');
              } catch (fetchError) {
                if (isMediaPathError(fetchError)) {
                  // If there's a media path error during fetch, create a minimal response
                  console.log('Media path error when fetching updated article, creating minimal response');
                  updatedArticle = {
                    id: id,
                    message: 'Article updated successfully but could not fetch complete details'
                  };
                } else {
                  throw fetchError;
                }
              }
            } else {
              throw new Error('Failed to update article via MongoDB');
            }
          } catch (mongoError) {
            console.error('PATCH: Error during direct MongoDB update:', mongoError);
            throw mongoError;
          }
        } else {
          // Re-throw the original error if not admin
          throw updateError;
        }
      }

      console.log('Article post updated successfully:', updatedArticle.id)

      return NextResponse.json({
        success: true,
        message: 'Article post updated successfully',
        data: updatedArticle,
      })
    } catch (updateError) {
      console.error('Failed to update article post:', updateError)
      // Log more details of the error
      if (updateError && typeof updateError === 'object') {
        console.error('Error details:', JSON.stringify(updateError, null, 2))
        const errorObj = updateError as Record<string, any>
        if ('data' in errorObj) {
          console.error('Error data:', JSON.stringify(errorObj.data, null, 2))
          if (
            errorObj.data &&
            typeof errorObj.data === 'object' &&
            'errors' in (errorObj.data as any)
          ) {
            console.error('Field errors:', JSON.stringify((errorObj.data as any).errors, null, 2))
          }
        }
      }

      // Enhanced error handling for Payload update errors
      if (updateError && typeof updateError === 'object' && 'data' in updateError) {
        const errorData = updateError.data as any
        if (errorData && errorData.errors) {
          return NextResponse.json(
            {
              error: 'Validation Error',
              message:
                updateError instanceof Error ? updateError.message : 'Unknown validation error',
              fields: errorData.errors,
            },
            { status: 400 },
          )
        }
      }

      // Re-throw to be caught by the outer catch block
      throw updateError
    }
  } catch (error) {
    console.error('Error updating article post:', error)

    // Check for validation errors
    if (error && typeof error === 'object' && 'data' in error) {
      // For Payload validation errors
      const errorData = error.data as any

      if (errorData && errorData.errors) {
        return NextResponse.json(
          {
            error: 'Validation Error',
            details: 'Field validation failed',
            fields: errorData.errors,
          },
          { status: 400 },
        )
      }

      return NextResponse.json(
        {
          error: 'Validation Error',
          details: errorData || 'Invalid field values',
          message: error instanceof Error ? error.message : 'Unknown validation error',
        },
        { status: 400 },
      )
    }

    return NextResponse.json(
      {
        error: 'An unexpected error occurred. The slug may already be in use.',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the ID from the URL
    const { id } = await Promise.resolve(params)

    // Check if the ID is a media path
    if (isMediaPath(id)) {
      return NextResponse.json({ error: 'Invalid article ID' }, { status: 400 })
    }

    // Get the current user from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Authentication token not found' }, { status: 401 })
    }

    // Get the user using extractUserFromToken
    const userInfo = await extractUserFromToken(token)

    if (!userInfo || !userInfo.id) {
      return NextResponse.json(
        { error: 'You must be logged in to delete an article' },
        { status: 401 },
      )
    }

    // Get the payload instance with config
    const payload = await getPayload({ config: payloadConfig })

    // Fetch the full user details from the database
    const user = await payload.findByID({
      collection: 'users',
      id: userInfo.id,
      depth: 2,
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 })
    }

    // Helper function to determine if a user is an admin (handles both role formats)
    async function isUserAdmin(user: any, payload: any): Promise<boolean> {
      // Direct check if role is an object with slug
      if (typeof user.role === 'object' && user.role !== null && 'slug' in user.role) {
        return user.role.slug === 'super-admin' || user.role.slug === 'school-admin';
      }
      
      // If role is an ID reference, fetch the role
      if (user.role) {
        try {
          const role = await payload.findByID({
            collection: 'roles',
            id: user.role,
          });
          
          return role?.slug === 'super-admin' || role?.slug === 'school-admin';
        } catch (error) {
          console.error('Error fetching role for admin check:', error);
          return false;
        }
      }
      
      return false;
    }

    // Add the collection property to the user object
    const userWithCollection = {
      ...user,
      collection: 'users' as const,
    }

    // Create a mock PayloadRequest object
    const mockReq = {
      user: userWithCollection,
      payload,
      method: 'DELETE',
      locale: 'en', // Assuming default locale is 'en'
      payloadAPI: 'REST', // Assuming REST API
    } as any

    // Fetch the article document
    const article = await payload.findByID({
      collection: 'articles',
      id,
    })

    if (!article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 })
    }

    // Check if the user is a super-admin, school-admin, or the author
    const isAdminUser = await isUserAdmin(user, payload);
    
    // Enhanced author check with detailed debugging
    console.log('Author check - Enhanced DEBUG:', {
      user: {
        id: userWithCollection.id,
        email: userWithCollection.email,
        role: typeof userWithCollection.role === 'object' ? 
          (userWithCollection.role ? userWithCollection.role.slug : 'null-role-obj') : 
          userWithCollection.role
      },
      article: {
        id: article.id,
        author: article.author,
        authorType: typeof article.author,
        authorId: typeof article.author === 'object' ? 
          (article.author ? article.author.id : 'null-author-id') : 
          article.author
      }
    });
    
    // Detailed isAuthor check with both imported function and manual check
    const isAuthorImported = isAuthor(userWithCollection, article);
    
    // Manual author check for comparison
    let isAuthorManual = false;
    if (typeof article.author === 'object' && article.author !== null && article.author.id) {
      isAuthorManual = article.author.id === userWithCollection.id;
      console.log(`Author check (object): ${article.author.id} === ${userWithCollection.id} => ${isAuthorManual}`);
    } else if (typeof article.author === 'string') {
      isAuthorManual = article.author === userWithCollection.id;
      console.log(`Author check (string): ${article.author} === ${userWithCollection.id} => ${isAuthorManual}`);
    } else {
      console.log('Author check failed: invalid author format', { author: article.author });
    }
    
    // Combine results for final check
    const isAuthorUser = isAuthorImported || isAuthorManual;
    
    console.log('Final author check:', { 
      isAuthorImported, 
      isAuthorManual, 
      isAuthorUser 
    });

    if (!isAdminUser && !isAuthorUser) {
      return NextResponse.json(
        { error: 'You do not have permission to delete this article', details: {
          userId: userInfo.id,
          userEmail: user.email,
          isAdminUser,
          isAuthorUser,
        } },
        { status: 403 },
      )
    }

    // Delete the article
    await payload.delete({
      collection: 'articles',
      id,
    })

    return NextResponse.json({ success: true, message: 'Article deleted successfully' })
  } catch (error) {
    console.error('Error deleting article:', error)
    return NextResponse.json({ error: 'Failed to delete article' }, { status: 500 })
  }
}
