'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { ActivityFeed, Activity } from '@/components/activities/ActivityFeed'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Download, Calendar } from 'lucide-react'

export default function SchoolActivitiesPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const filterParam = searchParams.get('filter')

  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [currentUserId, setCurrentUserId] = useState('') // Renamed for clarity
  const [currentSchoolId, setCurrentSchoolId] = useState('') // Renamed for clarity
  const [currentSchoolName, setCurrentSchoolName] = useState('') // Renamed for clarity
  const [monthlyReports, setMonthlyReports] = useState<{ month: string; url: string }[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalDocs, setTotalDocs] = useState(0)

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true)
      setError('')
      try {
        // Check if user is authenticated and is a school admin
        const authResponse = await fetch('/api/auth/me')
        if (!authResponse.ok) {
          router.push('/login')
          return
        }
        const authData = await authResponse.json()

        const userRole =
          typeof authData.user?.role === 'object' ? authData.user?.role?.slug : authData.user?.role

        if (!authData.user || userRole !== 'school-admin') {
          router.push('/login')
          return
        }

        const fetchedUserId = authData.user.id
        const fetchedSchoolId = authData.user.school?.id || ''
        const fetchedSchoolName = authData.user.school?.name || 'مدرستك'

        setCurrentUserId(fetchedUserId)
        setCurrentSchoolId(fetchedSchoolId)
        setCurrentSchoolName(fetchedSchoolName)

        if (fetchedSchoolId) {
          // Fetch activities from the API
          const activitiesResponse = await fetch(
            `/api/activities?schoolId=${fetchedSchoolId}&page=${currentPage}&limit=10`,
          )
          if (!activitiesResponse.ok) {
            throw new Error('فشل في جلب الأنشطة')
          }
          const activitiesData = await activitiesResponse.json()
          setActivities(activitiesData.docs || [])
          setTotalPages(activitiesData.totalPages || 1)
          setTotalDocs(activitiesData.totalDocs || 0)
        } else {
          // Handle case where school admin might not have a schoolId (though unlikely)
          setActivities([])
          setTotalPages(1)
          setTotalDocs(0)
          console.warn('لا يوجد معرف للمدرسة لمدير المدرسة.')
        }

        // Mock monthly reports (can be replaced with actual API call later)
        const mockReports = [
          { month: 'أكتوبر 2023', url: '#' },
          { month: 'سبتمبر 2023', url: '#' },
          { month: 'أغسطس 2023', url: '#' },
        ]
        setMonthlyReports(mockReports)
      } catch (err) {
        console.error('Error in fetchData:', err)
        setError(err instanceof Error ? err.message : 'فشل في تحميل البيانات. الرجاء المحاولة مرة أخرى.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [router, currentPage]) // Add currentPage to dependency array

  const handleDownloadReport = (month: string) => {
    console.log(`تحميل تقرير ${month}`)
    // Would call API to download report
    alert(`سيتم تحميل التقرير الخاص بشهر ${month} هنا`)
  }

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= totalPages) {
      setCurrentPage(newPage)
    }
  }

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
        <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded mb-6"></div>
        <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4" dir="rtl">
        {error}
      </div>
    )
  }

  return (
    <div dir="rtl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold dark:text-white">أنشطة المدرسة</h1>
        <p className="text-gray-500 dark:text-gray-400">
          عرض جميع الأنشطة لـ {currentSchoolName}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="md:col-span-2">
          <ActivityFeed
            activities={activities}
            userRole="school-admin" // This is fixed for this page
            userId={currentUserId} // Pass the current user's ID
            schoolId={currentSchoolId} // Pass the school ID for context if needed by ActivityFeed
            showFilters={true}
            title="جميع الأنشطة"
            description={`أنشطة المعلمين والموجهين والطلاب في ${currentSchoolName}`}
          />
          {/* Pagination Controls */}
          {totalDocs > 0 && (
            <div className="mt-6 flex justify-center items-center space-x-2">
              <Button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                variant="outline"
                className="ml-2"
              >
                السابق
              </Button>
              <span>
                الصفحة {currentPage} من {totalPages}
              </span>
              <Button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                variant="outline"
                className="mr-2"
              >
                التالي
              </Button>
            </div>
          )}
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>التقارير الشهرية</CardTitle>
              <CardDescription>تحميل تقارير الأنشطة حسب الشهر</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {monthlyReports.map((report, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center p-3 border rounded-md"
                  >
                    <div className="flex items-center">
                      <Calendar className="h-5 w-5 ml-2 text-gray-500" />
                      <span>{report.month}</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownloadReport(report.month)}
                    >
                      <Download className="h-4 w-4 ml-1" />
                      تحميل
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
