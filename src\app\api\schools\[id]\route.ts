import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

// Helper function for standardized error responses
function errorResponse(error: string, status: number) {
  return NextResponse.json(
    {
      success: false,
      error,
    },
    { status },
  )
}

// Helper function for standardized success responses
function successResponse(data: any) {
  return NextResponse.json({
    success: true,
    data,
  })
}

// GET a single school
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the token from cookies (cookies() is synchronous)
    const cookieStore = cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return errorResponse('Unauthorized', 401)
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return errorResponse('Unauthorized', 401)
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is an admin
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'

      if (!isAdmin) {
        return errorResponse('Forbidden', 403)
      }

      // Get the requested school
      const school = await payload.findByID({
        collection: 'schools',
        id: params.id,
        depth: 2,
      })

      return successResponse(school)
    } catch (error) {
      console.error('Token verification error:', error)
      return errorResponse('Unauthorized', 401)
    }
  } catch (error) {
    console.error('Error fetching school:', error)
    return errorResponse('Internal Server Error', 500)
  }
}

// UPDATE a school
export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the token from cookies (cookies() is synchronous)
    const cookieStore = cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return errorResponse('Unauthorized', 401)
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return errorResponse('Unauthorized', 401)
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is an admin
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'

      if (!isAdmin) {
        return errorResponse('Forbidden', 403)
      }

      // Get the request body
      const body = await req.json()

      // Update the school
      const updatedSchool = await payload.update({
        collection: 'schools',
        id: params.id,
        data: body,
      })

      return successResponse(updatedSchool)
    } catch (error) {
      console.error('Token verification error:', error)
      return errorResponse('Unauthorized', 401)
    }
  } catch (error) {
    console.error('Error updating school:', error)
    return errorResponse('Internal Server Error', 500)
  }
}

// DELETE a school
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the token from cookies (cookies() is synchronous)
    const cookieStore = cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return errorResponse('Unauthorized', 401)
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return errorResponse('Unauthorized', 401)
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is an admin
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'

      if (!isAdmin) {
        return errorResponse('Forbidden', 403)
      }

      // Delete the school
      const deletedSchool = await payload.delete({
        collection: 'schools',
        id: params.id,
      })

      return successResponse({ message: 'School deleted successfully' })
    } catch (error) {
      console.error('Token verification error:', error)
      return errorResponse('Unauthorized', 401)
    }
  } catch (error) {
    console.error('Error deleting school:', error)
    return errorResponse('Internal Server Error', 500)
  }
}
