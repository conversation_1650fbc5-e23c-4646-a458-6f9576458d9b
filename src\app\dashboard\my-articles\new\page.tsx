'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Loader2, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { createRichTextFromPlain } from '@/utils/richTextUtils'
import { EnhancedEditor, textToLexical } from '@/components/ui/EnhancedEditor'
import { ImageUpload } from '@/components/ui/ImageUpload'

export default function NewArticlePage() {
  const router = useRouter()

  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    summary: '',
    status: 'draft',
    category: '',
    tags: '',
    featuredImage: '',
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleRichTextChange = (value: string) => {
    setFormData((prev) => ({ ...prev, content: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleImageChange = (url: string) => {
    setFormData((prev) => ({ ...prev, featuredImage: url }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError('')
    setSuccess('')

    try {
      // Prepare tags as array
      const tagsArray = formData.tags
        .split(',')
        .map((tag) => tag.trim())
        .filter((tag) => tag)

      // Convert plain text content to Lexical rich text format
      const richTextContent = textToLexical(formData.content)
      console.log('Created rich text content from plain text')

      const response = await fetch('/api/articles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...formData,
          content: richTextContent, // Use the rich text format
          tags: tagsArray,
          featuredImage: formData.featuredImage || null,
        }),
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to create article: ${response.status} ${errorText}`)
      }

      const data = await response.json()
      setSuccess('تم إنشاء المقال بنجاح')

      // If status is pending-review, show different message
      if (formData.status === 'pending-review') {
        setSuccess('تم تقديم المقال للمراجعة')
      }

      // Redirect to the article list after a short delay
      setTimeout(() => {
        router.push('/dashboard/my-articles')
      }, 1500)
    } catch (err) {
      console.error('Error creating article:', err)
      setError('فشل في إنشاء المقال. يرجى المحاولة مرة أخرى.')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="p-6" dir="rtl">
        <div className="flex items-center mb-6">
          <Link href="/dashboard/my-articles" className="ml-4">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة إلى المقالات
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">مقال جديد</h1>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {success}
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>تفاصيل المقال</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">العنوان</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  dir="rtl"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="summary">الملخص</Label>
                <Textarea
                  id="summary"
                  name="summary"
                  value={formData.summary}
                  onChange={handleInputChange}
                  placeholder="ملخص موجز لمقالك"
                  rows={3}
                  dir="rtl"
                />
              </div>

              <div className="space-y-2">
                <ImageUpload value={formData.featuredImage} onChange={handleImageChange} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">المحتوى</Label>
                <EnhancedEditor
                  value={formData.content}
                  onChange={handleRichTextChange}
                  placeholder="اكتب مقالك هنا"
                  className="mb-4"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">الفئة</Label>
                  <Input
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    placeholder="مثال: أخبار، رياضة، رأي"
                    dir="rtl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tags">الوسوم</Label>
                  <Input
                    id="tags"
                    name="tags"
                    value={formData.tags}
                    onChange={handleInputChange}
                    placeholder="وسوم مفصولة بفواصل"
                    dir="rtl"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">الحالة</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleSelectChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">مسودة</SelectItem>
                    <SelectItem value="pending-review">تقديم للمراجعة</SelectItem>
                  </SelectContent>
                </Select>
                {formData.status === 'pending-review' && (
                  <p className="text-sm text-amber-600">
                    سيتم تقديم مقالك للمراجعة من قبل معلم.
                  </p>
                )}
              </div>

              <div className="flex justify-end space-x-4">
                <Button
                  type="submit"
                  disabled={isSaving}
                  className="mx-2"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري الحفظ...
                    </>
                  ) : (
                    'إنشاء المقال'
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/dashboard/my-articles')}
                >
                  إلغاء
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
