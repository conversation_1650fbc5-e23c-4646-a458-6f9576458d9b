'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Clock, CheckCircle, XCircle, AlertCircle, FileText, User } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { useRouter } from 'next/navigation'
import { formatDistanceToNow } from '@/lib/date-utils'

interface PendingApproval {
  id: string
  type: string
  requesterId: string
  requesterName: string
  requesterRole: string
  status: string
  createdAt: string
  data: any
}

interface PendingApprovalsOverviewProps {
  schoolId: string
}

export default function PendingApprovalsOverview({ schoolId }: PendingApprovalsOverviewProps) {
  const router = useRouter()
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function fetchPendingApprovals() {
      try {
        setIsLoading(true)
        console.log('Fetching pending approvals for school ID:', schoolId)
        const response = await fetch(`/api/dashboard/school-admin/pending-approvals?schoolId=${schoolId}`)

        if (!response.ok) {
          console.error('Failed to fetch pending approvals:', response.status, await response.text())
          throw new Error('فشل في جلب الموافقات المعلقة')
        }

        const data = await response.json()
        console.log('Pending approvals data:', data)
        
        if (Array.isArray(data.pendingApprovals)) {
          setPendingApprovals(data.pendingApprovals)
          console.log(`Found ${data.pendingApprovals.length} pending approvals`)
        } else {
          console.warn('Invalid pending approvals data format:', data)
          setPendingApprovals([])
        }
        
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching pending approvals:', err)
        setError('فشل في تحميل الموافقات المعلقة')
        setIsLoading(false)
      }
    }

    if (schoolId) {
      fetchPendingApprovals()
    } else {
      console.error('No school ID provided to PendingApprovalsOverview component')
      setError('معرّف المدرسة مطلوب')
      setIsLoading(false)
    }
  }, [schoolId])

  const handleApprove = async (id: string) => {
    try {
      const approval = pendingApprovals.find(approval => approval.id === id);
      
      if (!approval) {
        throw new Error('طلب الموافقة غير موجود');
      }
      
      console.log('Approving request:', approval);
      
      // Get the studentId from requesterId or from data.studentId
      const studentId = approval.data?.studentId || approval.requesterId;
      
      // Determine the approval type from the approval object
      const approvalType = approval.type;
      
      const response = await fetch(`/api/dashboard/school-admin/pending-approvals`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          studentId,
          action: 'approve',
          approvalType
        }),
      });

      if (!response.ok) {
        console.error('Failed to approve request:', await response.text());
        throw new Error('فشل في الموافقة على الطلب');
      }

      // Remove the approved request from the list
      setPendingApprovals(pendingApprovals.filter(approval => approval.id !== id));
    } catch (err) {
      console.error('Error approving request:', err);
      setError('فشل في الموافقة على الطلب');
    }
  };

  const handleReject = async (id: string) => {
    try {
      const approval = pendingApprovals.find(approval => approval.id === id);
      
      if (!approval) {
        throw new Error('طلب الموافقة غير موجود');
      }
      
      console.log('Rejecting request:', approval);
      
      // Get the studentId from requesterId or from data.studentId
      const studentId = approval.data?.studentId || approval.requesterId;
      
      // Determine the approval type from the approval object
      const approvalType = approval.type;
      
      const response = await fetch(`/api/dashboard/school-admin/pending-approvals`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          studentId,
          action: 'reject',
          approvalType
        }),
      });

      if (!response.ok) {
        console.error('Failed to reject request:', await response.text());
        throw new Error('فشل في رفض الطلب');
      }

      // Remove the rejected request from the list
      setPendingApprovals(pendingApprovals.filter(approval => approval.id !== id));
    } catch (err) {
      console.error('Error rejecting request:', err);
      setError('فشل في رفض الطلب');
    }
  };

  const getApprovalIcon = (type: string) => {
    switch (type) {
      case 'article':
        return <FileText className="h-5 w-5" />
      case 'user':
        return <User className="h-5 w-5" />
      default:
        return <AlertCircle className="h-5 w-5" />
    }
  }

  const getApprovalTypeLabel = (type: string) => {
    switch (type) {
      case 'article':
        return 'مراجعة مقال'
      case 'user':
        return 'موافقة مستخدم'
      default:
        return type.charAt(0).toUpperCase() + type.slice(1)
    }
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center" dir="rtl">
            <Clock className="ml-2 h-5 w-5" />
            الموافقات المعلقة
          </CardTitle>
        </CardHeader>
        <CardContent dir="rtl">
          <div className="bg-red-50 text-red-500 p-4 rounded-md">
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center" dir="rtl">
          <Clock className="ml-2 h-5 w-5" />
          الموافقات المعلقة
        </CardTitle>
      </CardHeader>
      <CardContent dir="rtl">
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-start space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            ))}
          </div>
        ) : pendingApprovals.length > 0 ? (
          <div className="space-y-4">
            {pendingApprovals.map((approval) => (
              <div key={approval.id} className="border-b pb-4 last:border-0">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="bg-primary/10 p-2 rounded-full">
                      {getApprovalIcon(approval.type)}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">{approval.requesterName}</h4>
                        <Badge variant="outline">{approval.requesterRole}</Badge>
                      </div>
                      <p className="text-sm text-gray-500">
                        {getApprovalTypeLabel(approval.type)} - {formatDistanceToNow(new Date(approval.createdAt), { addSuffix: true })}
                      </p>
                      {approval.data && approval.data.title && (
                        <p className="text-sm mt-1">{approval.data.title}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="flex items-center"
                      onClick={() => handleApprove(approval.id)}
                    >
                      <CheckCircle className="ml-1 h-4 w-4" />
                      موافقة
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="flex items-center text-destructive"
                      onClick={() => handleReject(approval.id)}
                    >
                      <XCircle className="ml-1 h-4 w-4" />
                      رفض
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
            <p className="text-gray-500">لا توجد موافقات معلقة في الوقت الحالي</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
