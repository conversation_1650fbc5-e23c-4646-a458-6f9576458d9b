import type { CollectionConfig } from 'payload'
import { hasRole } from '../access'

export const Roles: CollectionConfig = {
  slug: 'roles',
  admin: {
    useAsTitle: 'name',
  },
  access: {
    read: () => true,
    create: ({ req }) => hasRole(req, 'super-admin'),
    update: ({ req }) => hasRole(req, 'super-admin'),
    delete: ({ req }) => hasRole(req, 'super-admin'),
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'This is used for programmatic access to roles',
      },
    },
    {
      name: 'permissions',
      type: 'select',
      hasMany: true,
      options: [
        { label: 'Full System Access', value: 'full-access' },
        { label: 'Manage Users', value: 'manage-users' },
        { label: 'Manage Content', value: 'manage-content' },
      ],
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
    },
  ],
}
