import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import bcrypt from 'bcryptjs'
import { MongoClient, ObjectId } from 'mongodb'

import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    const payload = await getPayload({
      config,
    })

    // Determine request type and extract data accordingly
    let email: string, password: string, confirmPassword: string, school: string, grade: string | undefined
    let firstName: string, lastName: string

    // Check if request is JSON or form data
    const contentType = req.headers.get('content-type') || ''
    
    if (contentType.includes('application/json')) {
      // Handle JSON request
      const body = await req.json()
      email = body.email
      firstName = body.firstName
      lastName = body.lastName
      password = body.password
      confirmPassword = body.confirmPassword
      school = body.school
      grade = body.grade
    } else {
      // Handle form data request
      try {
        const formData = await req.formData()
        email = formData.get('email') as string
        firstName = formData.get('firstName') as string
        lastName = formData.get('lastName') as string
        password = formData.get('password') as string
        confirmPassword = formData.get('confirmPassword') as string
        school = formData.get('school') as string
        grade = formData.get('grade') as string
      } catch (error) {
        console.error('Error parsing form data:', error)
        return NextResponse.json(
          { error: 'Invalid form data. Please check your submission format.' },
          { status: 400 }
        )
      }
    }

    // Validate inputs
    if (!email || !password || !confirmPassword || !school || !firstName || !lastName) {
      return NextResponse.json({ error: 'All fields are required except grade' }, { status: 400 })
    }

    if (password !== confirmPassword) {
      return NextResponse.json({ error: 'Passwords do not match' }, { status: 400 })
    }

    // Get the student role
    const studentRoles = await payload.find({
      collection: 'roles',
      where: {
        slug: {
          equals: 'student',
        },
      },
    })

    if (!studentRoles.docs.length) {
      return NextResponse.json({ error: 'Student role not found' }, { status: 500 })
    }

    const studentRoleId = studentRoles.docs[0].id

    try {
      console.log('Creating new user with bcrypt password...')
      
      // Hash the password using bcrypt
      const salt = await bcrypt.genSalt(10)
      const hashedPassword = await bcrypt.hash(password, salt)

      // Connect to MongoDB directly to insert user with bcrypt hash
      const dbUri = process.env.DATABASE_URI || ''
      const client = new MongoClient(dbUri)
      await client.connect()
      
      const db = client.db()
      const usersCollection = db.collection('users')
      
      // Create the user document
      const newUser = {
        email,
        hash: hashedPassword,
        salt,
        firstName,
        lastName,
        role: new ObjectId(studentRoleId), // Convert to ObjectId
        school: new ObjectId(school), // Convert to ObjectId
        points: 0,
        status: 'pending',
        nameChangeStatus: 'none',
        profileImageStatus: 'none',
        createdAt: new Date(),
        updatedAt: new Date(),
        loginAttempts: 0,
        ...(grade ? { grade } : {})
      }

      // Insert the user
      const result = await usersCollection.insertOne(newUser)
      console.log('User created with ID:', result.insertedId.toString())
      
      // Close the MongoDB connection
      await client.close()

      // Redirect to pending approval page
      return NextResponse.redirect(new URL('/pending-approval', req.url))
    } catch (error) {
      console.error('Registration error:', error)
      return NextResponse.json(
        { error: 'Failed to register user. Email may already be in use.' },
        { status: 400 },
      )
    }
  } catch (error) {
    console.error('Server error:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
