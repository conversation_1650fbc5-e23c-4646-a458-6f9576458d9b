import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import { isMediaPath } from '@/lib/media-utils'
import config from '@/payload.config'

export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value || ''

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized: Please log in' }, { status: 401 })
    }

    const user = await extractUserFromToken(token)

    if (!user || user.role !== 'mentor') {
      return NextResponse.json(
        { error: 'Unauthorized: Only mentors can rate teacher activities' },
        { status: 403 },
      )
    }

    // Get the request body
    const body = await request.json()
    const { activityId, rating, feedback } = body

    // Validate inputs
    if (!activityId || !rating) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Check if the activity ID is a media path
    if (isMediaPath(activityId)) {
      return NextResponse.json({ error: 'Invalid activity ID' }, { status: 400 })
    }

    // Validate rating
    if (rating !== 'positive' && rating !== 'negative') {
      return NextResponse.json(
        { error: 'Rating must be either "positive" or "negative"' },
        { status: 400 },
      )
    }

    // Get the payload instance
    const payload = await getPayload({ config })

    // Get the activity
    const activity = await payload.findByID({
      collection: 'activities',
      id: activityId,
      depth: 2, // To get user details
    })

    if (!activity) {
      return NextResponse.json({ error: 'Activity not found' }, { status: 404 })
    }

    // Check if the activity has already been rated (check details field)
    const activityDetails =
      typeof activity.details === 'object' && activity.details ? (activity.details as any) : {}
    if (activityDetails.mentorRating) {
      return NextResponse.json({ error: 'Activity has already been rated' }, { status: 400 })
    }

    // Update the activity details with the mentor rating
    const updatedActivity = await payload.update({
      collection: 'activities',
      id: activityId,
      data: {
        details: {
          ...activityDetails,
          mentorRating: rating,
          mentorFeedback: feedback || undefined,
          mentorId: user.id, // Assuming mentorId is stored in details
          ratedAt: new Date().toISOString(), // Assuming ratedAt is stored in details
        },
      },
    })

    // Update teacher stats
    if (activity.userId) {
      // Use activity.userId
      const teacherId = typeof activity.userId === 'object' ? activity.userId.id : activity.userId

      if (teacherId) {
        try {
          const teacher = await payload.findByID({
            collection: 'users',
            id: teacherId,
          })

          if (teacher) {
            // Update teacher stats
            const positiveRatings =
              (teacher.stats?.positiveRatings || 0) + (rating === 'positive' ? 1 : 0)
            const negativeRatings =
              (teacher.stats?.negativeRatings || 0) + (rating === 'negative' ? 1 : 0)
            const totalRatings = positiveRatings + negativeRatings

            await payload.update({
              collection: 'users',
              id: teacherId,
              data: {
                stats: {
                  ...(teacher.stats || {}),
                  positiveRatings,
                  negativeRatings,
                  totalRatings,
                  approvalRate: totalRatings > 0 ? positiveRatings / totalRatings : 0,
                },
              },
            })
          }
        } catch (error) {
          console.error('Error updating teacher stats:', error)
          // Continue even if teacher stats update fails
        }
      }
    }

    // Create a notification for the teacher
    try {
      if (activity.userId) {
        // Use activity.userId
        const teacherId = typeof activity.userId === 'object' ? activity.userId.id : activity.userId

        if (teacherId) {
          await payload.create({
            collection: 'notifications',
            data: {
              user: teacherId,
              type: 'info', // Changed type to a valid NotificationType
              message:
                `Your activity has been ${rating === 'positive' ? 'approved' : 'rejected'}. ` +
                (feedback || `A mentor has provided feedback.`), // Combine title and message
              read: false,
              createdAt: new Date().toISOString(),
            },
          })
        }
      }
    } catch (error) {
      console.error('Error creating notification:', error)
      // Continue even if notification creation fails
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Activity rated successfully',
        activity: updatedActivity,
      },
      { status: 200 },
    )
  } catch (error) {
    console.error('Error rating teacher activity:', error)
    return NextResponse.json({ error: 'Failed to rate teacher activity' }, { status: 500 })
  }
}
