'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useSearchParams } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Filter,
  ThumbsUp,
  ThumbsDown,
  Eye,
  FileText,
  MessageSquare,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  Clock,
  AlertTriangle,
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { getImageUrl } from '@/utils/imageUtils'

interface Activity {
  id: string
  userId: string
  userName?: string
  userEmail?: string
  userProfileImage?: string
  activityType: string
  details: any
  createdAt: string
  school?: string
  points?: number
  isRated?: boolean
  rating?: 'positive' | 'negative'
  mentorFeedback?: string
}

interface Teacher {
  id: string
  firstName?: string
  lastName?: string
  email: string
  profileImage?: string
  stats?: {
    articlesReviewed?: number
    studentsApproved?: number
    averageRating?: number
    positiveRatings?: number
    negativeRatings?: number
    positiveReviewCount?: number
    negativeReviewCount?: number
  }
}

export default function TeacherActivitiesPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [activities, setActivities] = useState<Activity[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [activityTypeFilter, setActivityTypeFilter] = useState('all')
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null)
  const [feedbackText, setFeedbackText] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalActivities, setTotalActivities] = useState(0)

  // Get filter from URL params
  const filterParam = searchParams.get('filter') || 'all'
  const teacherIdParam = searchParams.get('teacherId')

  useEffect(() => {
    fetchActivities()
    fetchTeachers()
  }, [filterParam, teacherIdParam, currentPage])

  async function fetchActivities() {
    try {
      setIsLoading(true)
      setError('')

      // Build query parameters
      const queryParams = new URLSearchParams()
      queryParams.append('page', currentPage.toString())
      queryParams.append('limit', '10')

      if (filterParam !== 'all') {
        queryParams.append('filter', filterParam)
      }

      if (teacherIdParam) {
        queryParams.append('teacherId', teacherIdParam)
      }

      if (searchQuery) {
        queryParams.append('search', searchQuery)
      }

      if (activityTypeFilter !== 'all') {
        queryParams.append('activityType', activityTypeFilter)
      }

      const response = await fetch(`/api/dashboard/teachers/activities?${queryParams.toString()}`, {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to fetch teacher activities')
      }

      const data = await response.json()
      setActivities(data.activities || [])
      setTotalPages(data.totalPages || 1)
      setTotalActivities(data.totalDocs || 0)
      setIsLoading(false)
    } catch (err) {
      console.error('Error fetching teacher activities:', err)
      setError('Failed to load teacher activities. Please try again.')
      setIsLoading(false)
    }
  }

  async function fetchTeachers() {
    try {
      const response = await fetch('/api/dashboard/teachers', {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to fetch teachers')
      }

      const data = await response.json()
      setTeachers(data.teachers || [])
    } catch (err) {
      console.error('Error fetching teachers:', err)
      // Don't set error state here to avoid blocking the main functionality
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1) // Reset to first page on new search
    fetchActivities()
  }

  const handleFilterChange = (value: string) => {
    setActivityTypeFilter(value)
    setCurrentPage(1) // Reset to first page on filter change
    fetchActivities()
  }

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return
    setCurrentPage(page)
  }

  const handleRateActivity = async (activityId: string, isPositive: boolean) => {
    try {
      setIsSubmitting(true)

      const response = await fetch('/api/dashboard/teachers/rate-activity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          activityId,
          rating: isPositive ? 'positive' : 'negative',
          feedback: feedbackText,
        }),
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to rate activity')
      }

      // Update the activity in the list
      setActivities((prevActivities) =>
        prevActivities.map((activity) =>
          activity.id === activityId
            ? {
                ...activity,
                isRated: true,
                rating: isPositive ? 'positive' : 'negative',
                mentorFeedback: feedbackText,
              }
            : activity,
        ),
      )

      setSuccessMessage(`Activity has been ${isPositive ? 'approved' : 'rejected'} successfully.`)
      setFeedbackText('')
      setSelectedActivity(null)

      // Show success message briefly
      setTimeout(() => {
        setSuccessMessage('')
      }, 3000)
    } catch (err) {
      console.error('Error rating activity:', err)
      setError('Failed to rate activity. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'article-review':
        return <FileText className="h-4 w-4 text-blue-500" />
      case 'article-creation':
        return <FileText className="h-4 w-4 text-green-500" />
      case 'login':
        return <User className="h-4 w-4 text-gray-500" />
      case 'profile-update':
        return <User className="h-4 w-4 text-purple-500" />
      case 'student-approval':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'student-rejection':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'comment':
        return <MessageSquare className="h-4 w-4 text-blue-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    }
  }

  const formatActivityType = (type: string) => {
    return type
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Teacher Activities</h1>
            <p className="text-muted-foreground">
              Review and rate teacher activities to provide feedback
            </p>
          </div>

          <div className="mt-4 md:mt-0 flex items-center space-x-2">
            <form onSubmit={handleSearch} className="flex items-center space-x-2">
              <Input
                placeholder="Search activities..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64"
              />
              <Button type="submit" size="sm">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </form>
          </div>
        </div>

        {successMessage && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
            {successMessage}
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="md:col-span-3">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle>Activities</CardTitle>
                  <Select value={activityTypeFilter} onValueChange={handleFilterChange}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Activities</SelectItem>
                      <SelectItem value="article-review">Article Reviews</SelectItem>
                      <SelectItem value="article-creation">Article Creation</SelectItem>
                      <SelectItem value="student-approval">Student Approvals</SelectItem>
                      <SelectItem value="student-rejection">Student Rejections</SelectItem>
                      <SelectItem value="comment">Comments</SelectItem>
                      <SelectItem value="login">Logins</SelectItem>
                      <SelectItem value="profile-update">Profile Updates</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <CardDescription>
                  Showing {activities.length} of {totalActivities} activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                  </div>
                ) : activities.length > 0 ? (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Teacher</TableHead>
                          <TableHead>Activity</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {activities.map((activity) => (
                          <TableRow key={activity.id}>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Avatar className="h-8 w-8">
                                  {activity.userProfileImage ? (
                                    <AvatarImage src={getImageUrl(activity.userProfileImage)} />
                                  ) : (
                                    <AvatarFallback>
                                      {activity.userName?.charAt(0) ||
                                        activity.userEmail?.charAt(0) ||
                                        'T'}
                                    </AvatarFallback>
                                  )}
                                </Avatar>
                                <div>
                                  <div className="font-medium">
                                    {activity.userName || 'Unknown Teacher'}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {activity.userEmail}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                {getActivityIcon(activity.activityType)}
                                <span>{formatActivityType(activity.activityType)}</span>
                              </div>
                              {activity.details?.articleTitle && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  Article: {activity.details.articleTitle}
                                </div>
                              )}
                              {activity.details?.studentName && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  Student: {activity.details.studentName}
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-col">
                                <div className="flex items-center">
                                  <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
                                  <span className="text-xs">{formatDate(activity.createdAt)}</span>
                                </div>
                                <div className="flex items-center mt-1">
                                  <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                                  <span className="text-xs">{formatTime(activity.createdAt)}</span>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              {activity.isRated ? (
                                activity.rating === 'positive' ? (
                                  <Badge
                                    variant="outline"
                                    className="bg-green-50 text-green-700 border-green-200"
                                  >
                                    <ThumbsUp className="h-3 w-3 mr-1" />
                                    Approved
                                  </Badge>
                                ) : (
                                  <Badge
                                    variant="outline"
                                    className="bg-red-50 text-red-700 border-red-200"
                                  >
                                    <ThumbsDown className="h-3 w-3 mr-1" />
                                    Rejected
                                  </Badge>
                                )
                              ) : (
                                <Badge
                                  variant="outline"
                                  className="bg-yellow-50 text-yellow-700 border-yellow-200"
                                >
                                  Pending Review
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setSelectedActivity(activity)}
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  Review
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-12 text-muted-foreground">
                    No activities found matching your criteria.
                  </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center items-center space-x-2 mt-6">
                    <Button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      variant="outline"
                      size="sm"
                    >
                      Previous
                    </Button>
                    <span className="text-sm">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      variant="outline"
                      size="sm"
                    >
                      Next
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Teacher Stats</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {teachers.slice(0, 5).map((teacher) => (
                    <div key={teacher.id} className="border rounded-md p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <Avatar className="h-8 w-8">
                          {teacher.profileImage ? (
                            <AvatarImage src={getImageUrl(teacher.profileImage)} />
                          ) : (
                            <AvatarFallback>
                              {teacher.firstName?.charAt(0) || teacher.email.charAt(0)}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {teacher.firstName && teacher.lastName
                              ? `${teacher.firstName} ${teacher.lastName}`
                              : teacher.email}
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="text-muted-foreground">Articles:</span>{' '}
                          <span className="font-medium">
                            {teacher.stats?.articlesReviewed || 0}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Approvals:</span>{' '}
                          <span className="font-medium">
                            {teacher.stats?.studentsApproved || 0}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Rating:</span>{' '}
                          <span className="font-medium">
                            {teacher.stats?.averageRating
                              ? (teacher.stats.averageRating * 10).toFixed(1) + '/10'
                              : 'N/A'}
                          </span>
                        </div>
                        <div>
                          <Button
                            variant="link"
                            size="sm"
                            className="p-0 h-auto text-xs"
                            onClick={() =>
                              router.push(`/dashboard/teachers/activities?teacherId=${teacher.id}`)
                            }
                          >
                            View Activities
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}

                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => router.push('/dashboard/teachers')}
                  >
                    View All Teachers
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Activity Review Dialog */}
      {selectedActivity && (
        <Dialog
          open={!!selectedActivity}
          onOpenChange={(open) => !open && setSelectedActivity(null)}
        >
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Review Teacher Activity</DialogTitle>
            </DialogHeader>

            <div className="grid gap-6 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">Teacher</Label>
                  <div className="font-medium">
                    {selectedActivity.userName || 'Unknown Teacher'}
                  </div>
                  <div className="text-sm text-muted-foreground">{selectedActivity.userEmail}</div>
                </div>

                <div>
                  <Label className="text-muted-foreground">Activity Type</Label>
                  <div className="font-medium flex items-center">
                    {getActivityIcon(selectedActivity.activityType)}
                    <span className="ml-2">
                      {formatActivityType(selectedActivity.activityType)}
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-muted-foreground">Date & Time</Label>
                <div className="font-medium">
                  {formatDate(selectedActivity.createdAt)} at{' '}
                  {formatTime(selectedActivity.createdAt)}
                </div>
              </div>

              <div>
                <Label className="text-muted-foreground">Details</Label>
                <Card className="mt-2">
                  <CardContent className="p-4">
                    {selectedActivity.activityType === 'article-review' && (
                      <div className="space-y-2">
                        <div>
                          <span className="font-medium">Article:</span>{' '}
                          {selectedActivity.details?.articleTitle || 'Unknown Article'}
                        </div>
                        <div>
                          <span className="font-medium">Rating:</span>{' '}
                          {selectedActivity.details?.rating || 'N/A'}/10
                        </div>
                        <div>
                          <span className="font-medium">Status:</span>{' '}
                          {selectedActivity.details?.approved ? 'Approved' : 'Rejected'}
                        </div>
                        {selectedActivity.details?.comment && (
                          <div>
                            <span className="font-medium">Comment:</span>
                            <div className="mt-1 p-2 bg-gray-50 rounded-md text-sm">
                              {selectedActivity.details.comment}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {selectedActivity.activityType === 'article-creation' && (
                      <div className="space-y-2">
                        <div>
                          <span className="font-medium">Article:</span>{' '}
                          {selectedActivity.details?.articleTitle || 'Unknown Article'}
                        </div>
                        {selectedActivity.details?.summary && (
                          <div>
                            <span className="font-medium">Summary:</span>
                            <div className="mt-1 p-2 bg-gray-50 rounded-md text-sm">
                              {selectedActivity.details.summary}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {selectedActivity.activityType === 'student-approval' && (
                      <div className="space-y-2">
                        <div>
                          <span className="font-medium">Student:</span>{' '}
                          {selectedActivity.details?.studentName || 'Unknown Student'}
                        </div>
                        <div>
                          <span className="font-medium">Email:</span>{' '}
                          {selectedActivity.details?.studentEmail || 'N/A'}
                        </div>
                        {selectedActivity.details?.reason && (
                          <div>
                            <span className="font-medium">Reason:</span>
                            <div className="mt-1 p-2 bg-gray-50 rounded-md text-sm">
                              {selectedActivity.details.reason}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Generic details display for other activity types */}
                    {!['article-review', 'article-creation', 'student-approval'].includes(
                      selectedActivity.activityType,
                    ) && (
                      <div className="space-y-2">
                        {Object.entries(selectedActivity.details || {}).map(([key, value]) => (
                          <div key={key}>
                            <span className="font-medium">
                              {key.charAt(0).toUpperCase() + key.slice(1)}:
                            </span>{' '}
                            {typeof value === 'string' ? value : JSON.stringify(value)}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {!selectedActivity.isRated && (
                <>
                  <div>
                    <Label htmlFor="feedback">Your Feedback</Label>
                    <Textarea
                      id="feedback"
                      placeholder="Provide feedback on this teacher's activity..."
                      value={feedbackText}
                      onChange={(e) => setFeedbackText(e.target.value)}
                      rows={4}
                      className="mt-2"
                    />
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setSelectedActivity(null)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => handleRateActivity(selectedActivity.id, false)}
                      disabled={isSubmitting}
                    >
                      <ThumbsDown className="h-4 w-4 mr-2" />
                      Reject
                    </Button>
                    <Button
                      variant="default"
                      onClick={() => handleRateActivity(selectedActivity.id, true)}
                      disabled={isSubmitting}
                    >
                      <ThumbsUp className="h-4 w-4 mr-2" />
                      Approve
                    </Button>
                  </div>
                </>
              )}

              {selectedActivity.isRated && (
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="flex items-center mb-2">
                    <span className="font-medium mr-2">Your Rating:</span>
                    {selectedActivity.rating === 'positive' ? (
                      <Badge
                        variant="outline"
                        className="bg-green-50 text-green-700 border-green-200"
                      >
                        <ThumbsUp className="h-3 w-3 mr-1" />
                        Approved
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                        <ThumbsDown className="h-3 w-3 mr-1" />
                        Rejected
                      </Badge>
                    )}
                  </div>

                  {selectedActivity.mentorFeedback && (
                    <div>
                      <span className="font-medium">Your Feedback:</span>
                      <div className="mt-1 p-2 bg-white rounded-md text-sm border">
                        {selectedActivity.mentorFeedback}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </DashboardLayout>
  )
}
