'use client'

import { useEffect, useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { Plus, MoreHorizontal, PencilIcon, Trash2Icon, UsersIcon } from 'lucide-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'
import { useRouter } from 'next/navigation'

interface School {
  id: string
  name: string
  location: string
  contactEmail: string
  studentCount: number
  createdAt: string
}

const schoolFormSchema = z.object({
  name: z.string().min(2, { message: "يجب أن يكون اسم المدرسة على الأقل حرفين" }),
  location: z.string().min(2, { message: "يجب أن يكون الموقع على الأقل حرفين" }),
  contactEmail: z.string().email({ message: "يرجى إدخال عنوان بريد إلكتروني صالح" }),
})

const API_URL = '/api/dashboard/super-admin/schools'

export function SchoolsManager() {
  const [loading, setLoading] = useState(true)
  const [schools, setSchools] = useState<School[]>([])
  const [error, setError] = useState<string | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [isEditing, setIsEditing] = useState<string | null>(null)
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState<string | null>(null)
  const router = useRouter()

  const createForm = useForm<z.infer<typeof schoolFormSchema>>({
    resolver: zodResolver(schoolFormSchema),
    defaultValues: {
      name: "",
      location: "",
      contactEmail: "",
    },
  })

  const editForm = useForm<z.infer<typeof schoolFormSchema>>({
    resolver: zodResolver(schoolFormSchema),
    defaultValues: {
      name: "",
      location: "",
      contactEmail: "",
    },
  })

  useEffect(() => {
    const fetchSchools = async () => {
      try {
        setLoading(true)
        const response = await fetch(API_URL)
        if (!response.ok) throw new Error('فشل في جلب المدارس')
        const data = await response.json()
        const schools = (data.schools || data || []).map((school: any) => ({
          ...school,
          id: school.id || school._id || '',
        }))
        setSchools(schools)
        setLoading(false)
      } catch (error) {
        console.error('Error fetching schools:', error)
        setError('فشل في تحميل المدارس')
        setLoading(false)
      }
    }
    fetchSchools()
  }, [])

  const onCreateSubmit = async (values: z.infer<typeof schoolFormSchema>) => {
    try {
      setLoading(true)
      const response = await fetch(API_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      })
      if (!response.ok) throw new Error('فشل في إنشاء المدرسة')
      const newSchool = await response.json()
      setSchools([...schools, newSchool])
      toast({ title: 'تم إنشاء المدرسة', description: `تمت إضافة ${values.name} بنجاح` })
      createForm.reset()
      setIsCreating(false)
      setLoading(false)
    } catch (error) {
      console.error('Error creating school:', error)
      toast({ title: 'خطأ في إنشاء المدرسة', description: 'حدثت مشكلة أثناء إنشاء المدرسة', variant: 'destructive' })
      setLoading(false)
    }
  }

  const onEditSubmit = async (values: z.infer<typeof schoolFormSchema>) => {
    if (!isEditing) return
    try {
      setLoading(true)
      const response = await fetch(API_URL, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: isEditing, ...values }),
      })
      if (!response.ok) throw new Error('فشل في تحديث المدرسة')
      const updatedSchool = await response.json()
      setSchools(schools.map(school => school.id === isEditing ? updatedSchool : school))
      toast({ title: 'تم تحديث المدرسة', description: `تم تحديث ${values.name} بنجاح` })
      editForm.reset()
      setIsEditing(null)
      setLoading(false)
    } catch (error) {
      console.error('Error updating school:', error)
      toast({ title: 'خطأ في تحديث المدرسة', description: 'حدثت مشكلة أثناء تحديث المدرسة', variant: 'destructive' })
      setLoading(false)
    }
  }

  const handleEditClick = (school: School) => {
    editForm.reset({
      name: school.name,
      location: school.location,
      contactEmail: school.contactEmail,
    })
    setIsEditing(school.id)
  }

  const handleDeleteClick = async (id: string) => {
    try {
      setLoading(true)
      const response = await fetch(`${API_URL}?id=${id}`, { method: 'DELETE' })
      if (!response.ok) throw new Error('فشل في حذف المدرسة')
      setSchools(schools.filter(school => school.id !== id))
      toast({ title: 'تم حذف المدرسة', description: 'تم حذف المدرسة بنجاح' })
      setDeleteConfirmOpen(null)
      setLoading(false)
    } catch (error) {
      console.error('Error deleting school:', error)
      toast({ title: 'خطأ في حذف المدرسة', description: 'حدثت مشكلة أثناء حذف المدرسة', variant: 'destructive' })
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-[400px] w-full" />
      </div>
    )
  }

  if (error) {
    return <div className="text-red-500" dir="rtl">خطأ: {error}</div>
  }

  return (
    <div className="space-y-4" dir="rtl">
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          إدارة جميع المدارس في النظام
        </div>
        
        <Dialog open={isCreating} onOpenChange={setIsCreating}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 ml-2" /> إضافة مدرسة
            </Button>
          </DialogTrigger>
          <DialogContent dir="rtl">
            <DialogHeader>
              <DialogTitle>إضافة مدرسة جديدة</DialogTitle>
              <DialogDescription>
                إنشاء مدرسة جديدة في النظام
              </DialogDescription>
            </DialogHeader>
            
            <Form {...createForm}>
              <form onSubmit={createForm.handleSubmit(onCreateSubmit)} className="space-y-4">
                <FormField
                  control={createForm.control}
                  name="name"
                  render={({ field }: { field: any }) => (
                    <FormItem>
                      <FormLabel>اسم المدرسة</FormLabel>
                      <FormControl>
                        <Input placeholder="أدخل اسم المدرسة" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={createForm.control}
                  name="location"
                  render={({ field }: { field: any }) => (
                    <FormItem>
                      <FormLabel>الموقع</FormLabel>
                      <FormControl>
                        <Input placeholder="المدينة، المنطقة" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={createForm.control}
                  name="contactEmail"
                  render={({ field }: { field: any }) => (
                    <FormItem>
                      <FormLabel>البريد الإلكتروني للاتصال</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <DialogClose asChild>
                    <Button variant="outline">إلغاء</Button>
                  </DialogClose>
                  <Button type="submit">إنشاء مدرسة</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>اسم المدرسة</TableHead>
              <TableHead>الموقع</TableHead>
              <TableHead>البريد الإلكتروني</TableHead>
              <TableHead>عدد الطلاب</TableHead>
              <TableHead>تاريخ الإنشاء</TableHead>
              <TableHead className="text-left">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {schools.length > 0 ? (
              schools.map((school) => (
                <TableRow key={school.id}>
                  <TableCell className="font-medium">{school.name}</TableCell>
                  <TableCell>{school.location}</TableCell>
                  <TableCell>{school.contactEmail}</TableCell>
                  <TableCell>{school.studentCount || 0}</TableCell>
                  <TableCell>{new Date(school.createdAt).toLocaleDateString('ar-EG')}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">فتح القائمة</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        
                        <DropdownMenuItem onClick={() => router.push(`/dashboard/super-admin/schools/${school.id}`)}>
                          <UsersIcon className="h-4 w-4 ml-2" />
                          عرض التفاصيل
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem onClick={() => handleEditClick(school)}>
                          <PencilIcon className="h-4 w-4 ml-2" />
                          تعديل
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem 
                          onClick={() => setDeleteConfirmOpen(school.id)}
                          className="text-red-600"
                        >
                          <Trash2Icon className="h-4 w-4 ml-2" />
                          حذف
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                  لم يتم العثور على مدارس
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      <Dialog open={isEditing !== null} onOpenChange={(open) => !open && setIsEditing(null)}>
        <DialogContent dir="rtl">
          <DialogHeader>
            <DialogTitle>تعديل المدرسة</DialogTitle>
            <DialogDescription>
              تحديث معلومات المدرسة
            </DialogDescription>
          </DialogHeader>
          
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }: { field: any }) => (
                  <FormItem>
                    <FormLabel>اسم المدرسة</FormLabel>
                    <FormControl>
                      <Input placeholder="أدخل اسم المدرسة" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={editForm.control}
                name="location"
                render={({ field }: { field: any }) => (
                  <FormItem>
                    <FormLabel>الموقع</FormLabel>
                    <FormControl>
                      <Input placeholder="المدينة، المنطقة" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={editForm.control}
                name="contactEmail"
                render={({ field }: { field: any }) => (
                  <FormItem>
                    <FormLabel>البريد الإلكتروني للاتصال</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">إلغاء</Button>
                </DialogClose>
                <Button type="submit">حفظ التغييرات</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      <Dialog open={deleteConfirmOpen !== null} onOpenChange={(open) => !open && setDeleteConfirmOpen(null)}>
        <DialogContent dir="rtl">
          <DialogHeader>
            <DialogTitle>تأكيد الحذف</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف هذه المدرسة؟ لا يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
            <Button 
              variant="destructive" 
              onClick={() => deleteConfirmOpen && handleDeleteClick(deleteConfirmOpen)}
            >
              نعم، حذف المدرسة
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 