import { NextRequest, NextResponse } from 'next/server'
// Remove MongoDB import as it's not compatible with Edge Runtime

// Define protected routes that require authentication
const protectedRoutes = ['/dashboard']

// Define user roles type
type UserRole = 'super-admin' | 'school-admin' | 'teacher' | 'mentor' | 'student'

// Define role-based access control
const roleBasedRoutes: Record<UserRole, string[]> = {
  'super-admin': [
    '/dashboard/users',
    '/dashboard/articles',
    '/dashboard/news',
    '/dashboard/schools',
    '/dashboard/activities',
    '/dashboard/statistics',
    '/dashboard/achievements',
    '/dashboard/settings',
  ],
  'school-admin': [
    '/dashboard/users',
    '/dashboard/articles',
    '/dashboard/news',
    '/dashboard/activities',
    '/dashboard/statistics',
    '/dashboard/achievements',
    '/dashboard/settings',
  ],
  teacher: [
    '/dashboard/students',
    '/dashboard/articles',
    '/dashboard/activities',
    '/dashboard/settings',
  ],
  mentor: [
    // UI routes
    '/dashboard/teachers',
    '/dashboard/news',
    '/dashboard/activities',
    '/dashboard/settings',
    
    // API routes with wildcard matching
    '/api/dashboard/teachers/:path*',
    '/api/dashboard/teachers/activities'
  ],
  student: [
    '/dashboard',
    '/dashboard/my-articles',
    '/dashboard/news',
    '/dashboard/my-activities',
    '/dashboard/achievements',
    '/dashboard/settings',
    '/dashboard/student/profile',
  ],
}

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname

  // Skip the pending-approval page itself from checks
  if (path === '/pending-approval') {
    return NextResponse.next()
  }
  
  // Check if the path is a protected route
  const isProtectedRoute = protectedRoutes.some(
    (route) => path === route || path.startsWith(`${route}/`),
  )

  if (isProtectedRoute) {
    // Get the token from cookies
    const token = request.cookies.get('payload-token')?.value

    if (!token) {
      // Redirect to login if no token
      console.log('No token found, redirecting to login')
      return NextResponse.redirect(new URL('/login', request.url), { status: 302 })
    }

    try {
      // Parse the token to get basic information (without verification)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let decoded: any = null
      try {
        // JWT format: header.payload.signature
        const parts = token.split('.')
        if (parts.length === 3) {
          const payload = parts[1]
          const decodedPayload = Buffer.from(payload, 'base64').toString()
          decoded = JSON.parse(decodedPayload)
          console.log('Middleware: Extracted token payload (not verified)')
        }
      } catch (parseError) {
        console.error('Middleware: Error parsing token:', parseError)
        // Continue anyway, we'll just have a null decoded value
      }

      // Check for user status directly from the token
      // This is safer than connecting to MongoDB in the middleware
      if (decoded && decoded.userStatus === 'pending') {
        console.log('Middleware: User status is pending (from token), redirecting to pending-approval')
        return NextResponse.redirect(new URL('/pending-approval', request.url), { status: 302 })
      }

      // For the main dashboard route, allow access if token exists
      if (path === '/dashboard') {
        console.log('Middleware: Access to main dashboard granted')
        return NextResponse.next()
      }

      // For specific dashboard routes, check role-based access if we have decoded token data
      if (decoded && typeof decoded === 'object') {
        console.log('Middleware: Token payload available, checking role-based access')

        // Extract role from token payload
        const userRoleValue = decoded?.role?.slug || decoded?.role || 'student'
        console.log('Middleware: User role from token:', userRoleValue)

        // Validate and normalize role
        const validRoles: UserRole[] = [
          'super-admin',
          'school-admin',
          'teacher',
          'mentor',
          'student',
        ]
        const userRole = validRoles.includes(userRoleValue as UserRole)
          ? (userRoleValue as UserRole)
          : 'student'
        console.log('Middleware: Normalized user role:', userRole)

        // Check if user has access to the route
        console.log('Middleware: Checking access for role:', userRole, 'to path:', path)

        const hasAccess = roleBasedRoutes[userRole]?.some(
          (route: string) => path === route || path.startsWith(`${route}/`),
        )
        console.log('Middleware: User has access:', hasAccess)

        if (!hasAccess) {
          // Redirect to dashboard
          console.log(
            'Middleware: User does not have access to this route, redirecting to dashboard',
          )
          return NextResponse.redirect(new URL('/dashboard', request.url), { status: 302 })
        } else {
          console.log('Middleware: User has access to this route, proceeding')
          return NextResponse.next()
        }
      }

      // If we couldn't decode the token or it doesn't have role information,
      // allow access to the dashboard but not to specific routes
      if (path === '/dashboard') {
        console.log('Middleware: Allowing access to main dashboard without role information')
        return NextResponse.next()
      } else {
        console.log('Middleware: No role information, redirecting to main dashboard')
        return NextResponse.redirect(new URL('/dashboard', request.url), { status: 302 })
      }
    } catch (error) {
      // Error processing the token, redirect to login
      console.error('Middleware: Error processing token:', error)
      console.log('Middleware: Error in middleware, redirecting to login')
      return NextResponse.redirect(new URL('/login', request.url), { status: 302 })
    }
  }

  return NextResponse.next()
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: ['/dashboard/:path*', '/api/dashboard/:path*'],
}
