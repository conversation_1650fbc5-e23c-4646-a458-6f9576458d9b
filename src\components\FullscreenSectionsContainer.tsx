'use client'

import React from 'react'
import { FullscreenSection } from './FullscreenSection'
import { InteractiveDots } from './InteractiveDots'

// Define a common background color for all sections and footer
const commonBgColor = 'hsl(var(--primary))' // cyan-900

export const FullscreenSectionsContainer: React.FC = () => {
  return (
    <div className="scroll-container overflow-hidden" style={{ position: 'relative' }}>
      <FullscreenSection
        title="للطلاب"
        description="قدم مقالاتك واحصل على تعليقات قيمة من المعلمين. طور مهاراتك في الكتابة وشارك صوتك مع العالم."
        buttonText="ابدأ الكتابة"
        buttonLink="/articles/create"
        modelType="book"
        color="black" // cyan-500
        bgColor={commonBgColor} // Using common background color
        textColor="#f0f9ff" // cyan-50
        isReversed={false}
        isFirst={true} // This is the first section, so it will have a gradient
      />

      <FullscreenSection
        title="للمعلمين"
        description="راجع مقالات الطلاب وقدم تعليقات قيمة. ساعد في تشكيل الجيل القادم من الصحفيين والكتاب."
        buttonText="مراجعة المقالات"
        buttonLink="/articles/review"
        modelType="users"
        color="black" // violet-500
        bgColor={commonBgColor} // Using common background color
        textColor="#f5f3ff" // violet-50
        isReversed={true}
      />

      <FullscreenSection
        title="لوحات المتصدرين"
        description="شاهد أفضل المدارس والمعلمين والطلاب أداءً. احتفل بالتميز وألهم الآخرين لتحقيق العظمة."
        buttonText="عرض الإحصائيات"
        buttonLink="/statistics"
        modelType="award"
        color="black" // amber-500
        bgColor={commonBgColor} // Using common background color
        textColor="#fffbeb" // amber-50
        isReversed={false}
      />
    </div>
  )
}
