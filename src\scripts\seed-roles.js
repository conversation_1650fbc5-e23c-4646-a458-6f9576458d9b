// A simple script to seed the database with initial roles
const { MongoClient } = require('mongodb');
require('dotenv').config();

async function seedRoles() {
  // Connect to MongoDB
  const client = new MongoClient(process.env.DATABASE_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const rolesCollection = db.collection('roles');
    
    // Check if roles already exist
    const existingRoles = await rolesCollection.countDocuments();
    
    if (existingRoles > 0) {
      console.log('Roles already exist, skipping role creation');
      return;
    }
    
    // Create roles
    const roles = [
      {
        name: 'Super Admin',
        slug: 'super-admin',
        description: 'Full access to all features and settings',
        permissions: ['full-access'],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'School Admin',
        slug: 'school-admin',
        description: 'Manages teachers, mentors, and students within their school',
        permissions: ['manage-users', 'manage-content'],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: '<PERSON><PERSON>',
        slug: 'mentor',
        description: 'Creates news posts and reviews teacher feedback',
        permissions: ['manage-content'],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Teacher',
        slug: 'teacher',
        description: 'Reviews and approves student articles',
        permissions: ['manage-content'],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Student',
        slug: 'student',
        description: 'Creates and submits articles for review',
        permissions: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    const result = await rolesCollection.insertMany(roles);
    console.log(`${result.insertedCount} roles created successfully`);
    
  } catch (error) {
    console.error('Error seeding roles:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

seedRoles().catch(console.error);
