import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config({
  path: path.resolve(__dirname, '../../.env'),
});

async function examineDBStructure() {
  const uri = process.env.MONGODB_URI;
  
  if (!uri) {
    console.error('MONGODB_URI environment variable is not set');
    return;
  }
  
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    
    // Check users collection structure
    const usersCollection = db.collection('users');
    const totalUsers = await usersCollection.countDocuments();
    console.log(`\nTotal users: ${totalUsers}`);
    
    if (totalUsers > 0) {
      console.log('\nSample user structures:');
      const users = await usersCollection.find({}).limit(5).toArray();
      
      users.forEach((user, index) => {
        console.log(`\n[User ${index + 1}] ${user.email || user.id}`);
        console.log(`  Role: ${JSON.stringify(user.role)}`);
        console.log(`  School: ${JSON.stringify(user.school)}`);
        console.log(`  ID: ${user._id || user.id}`);
      });
    }
    
    // Check articles collection structure
    const articlesCollection = db.collection('articles');
    const totalArticles = await articlesCollection.countDocuments();
    console.log(`\n\nTotal articles: ${totalArticles}`);
    
    if (totalArticles > 0) {
      console.log('\nSample article structures:');
      const articles = await articlesCollection.find({}).limit(3).toArray();
      
      articles.forEach((article, index) => {
        console.log(`\n[Article ${index + 1}] ${article.title || article.id}`);
        console.log(`  Author: ${JSON.stringify(article.author)}`);
        console.log(`  School: ${JSON.stringify(article.school)}`);
        console.log(`  ID: ${article._id || article.id}`);
      });
    }
    
    // Check schools collection structure
    const schoolsCollection = db.collection('schools');
    const totalSchools = await schoolsCollection.countDocuments();
    console.log(`\n\nTotal schools: ${totalSchools}`);
    
    if (totalSchools > 0) {
      console.log('\nSample school structures:');
      const schools = await schoolsCollection.find({}).limit(3).toArray();
      
      schools.forEach((school, index) => {
        console.log(`\n[School ${index + 1}] ${school.name || school.id}`);
        console.log(`  ID: ${school._id || school.id}`);
        console.log(`  Admin: ${JSON.stringify(school.admin)}`);
      });
    }
    
    // Check roles collection structure
    const rolesCollection = db.collection('roles');
    const totalRoles = await rolesCollection.countDocuments();
    console.log(`\n\nTotal roles: ${totalRoles}`);
    
    if (totalRoles > 0) {
      console.log('\nAll roles:');
      const roles = await rolesCollection.find({}).toArray();
      
      roles.forEach((role, index) => {
        console.log(`[Role ${index + 1}] ${role.name} (slug: ${role.slug}, ID: ${role._id || role.id})`);
      });
    }
    
    // Test specific queries that are failing
    console.log('\n\n=== TESTING FAILING QUERIES ===');
    
    // Get a specific school ID to test with
    const sampleSchool = await schoolsCollection.findOne({});
    if (sampleSchool) {
      const schoolId = sampleSchool._id;
      console.log(`\nTesting with school ID: ${schoolId}`);
      
      // Test student count queries
      console.log('\n--- Student Count Tests ---');
      const studentQuery1 = { 'role.slug': 'student', 'school._id': schoolId };
      const studentCount1 = await usersCollection.countDocuments(studentQuery1);
      console.log(`Query 1 (role.slug + school._id): ${studentCount1}`);
      
      const studentQuery2 = { 'role.slug': 'student', school: schoolId };
      const studentCount2 = await usersCollection.countDocuments(studentQuery2);
      console.log(`Query 2 (role.slug + school): ${studentCount2}`);
      
      const studentQuery3 = { 'role.slug': 'student', 'school.id': schoolId.toString() };
      const studentCount3 = await usersCollection.countDocuments(studentQuery3);
      console.log(`Query 3 (role.slug + school.id): ${studentCount3}`);
      
      // Test article count queries
      console.log('\n--- Article Count Tests ---');
      const articleQuery1 = { 'school._id': schoolId };
      const articleCount1 = await articlesCollection.countDocuments(articleQuery1);
      console.log(`Query 1 (school._id): ${articleCount1}`);
      
      const articleQuery2 = { school: schoolId };
      const articleCount2 = await articlesCollection.countDocuments(articleQuery2);
      console.log(`Query 2 (school): ${articleCount2}`);
      
      const articleQuery3 = { 'author.school._id': schoolId };
      const articleCount3 = await articlesCollection.countDocuments(articleQuery3);
      console.log(`Query 3 (author.school._id): ${articleCount3}`);
      
      const articleQuery4 = { 'author.school': schoolId };
      const articleCount4 = await articlesCollection.countDocuments(articleQuery4);
      console.log(`Query 4 (author.school): ${articleCount4}`);
    }
    
  } catch (error) {
    console.error('Error examining database:', error);
  } finally {
    await client.close();
    console.log('\nMongoDB connection closed.');
  }
}

examineDBStructure();
