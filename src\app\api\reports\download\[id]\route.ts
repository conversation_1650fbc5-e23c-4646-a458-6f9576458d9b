import { NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { db } = await connectToDatabase()
    
    // Get report ID from params
    const reportId = params.id
    
    // Get report from database
    const report = await db.collection('reports').findOne({ id: reportId })
    
    if (!report) {
      return NextResponse.json(
        { error: 'Report not found' },
        { status: 404 }
      )
    }
    
    // Generate CSV content
    let csvContent = 'Young Reporter Platform - Activity Report\n\n'
    csvContent += `Report ID: ${report.id}\n`
    csvContent += `Month: ${report.month}/${report.year}\n`
    csvContent += `School: ${report.schoolId === 'all' ? 'All Schools' : report.schoolId}\n`
    csvContent += `Generated At: ${new Date(report.generatedAt).toLocaleString()}\n`
    csvContent += `Total Activities: ${report.totalActivities}\n\n`
    
    // Activity Types Summary
    csvContent += 'Activity Types:\n'
    for (const [type, count] of Object.entries(report.activityTypes)) {
      csvContent += `${type},${count}\n`
    }
    csvContent += '\n'
    
    // User Roles Summary
    csvContent += 'User Roles:\n'
    for (const [role, count] of Object.entries(report.userRoles)) {
      csvContent += `${role},${count}\n`
    }
    csvContent += '\n'
    
    // Schools Summary (if all schools)
    if (report.schoolId === 'all') {
      csvContent += 'Schools:\n'
      for (const [school, count] of Object.entries(report.schools)) {
        csvContent += `${school},${count}\n`
      }
      csvContent += '\n'
    }
    
    // Activities Detail
    csvContent += 'Activities:\n'
    csvContent += 'Date,User,Role,School,Activity Type,Description\n'
    
    for (const activity of report.activities) {
      const date = new Date(activity.date).toLocaleDateString()
      const user = activity.userName
      const role = activity.userRole
      const school = activity.schoolName || ''
      const type = activity.activityType
      const description = activity.description.replace(/,/g, ';') // Replace commas to avoid CSV issues
      
      csvContent += `${date},${user},${role},${school},${type},${description}\n`
    }
    
    // Return CSV file
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="report-${report.year}-${report.month.toString().padStart(2, '0')}-${report.schoolId}.csv"`
      }
    })
  } catch (error) {
    console.error('Error downloading report:', error)
    return NextResponse.json(
      { error: 'Failed to download report' },
      { status: 500 }
    )
  }
}
