import { ThemeProvider } from '@/contexts/ThemeContext'
import { NotificationProvider } from '@/contexts/NotificationContext'
import { PointsProvider } from '@/contexts/PointsContext'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ThemeProvider>
      <NotificationProvider>
        <PointsProvider>
          {children}
        </PointsProvider>
      </NotificationProvider>
    </ThemeProvider>
  )
}
