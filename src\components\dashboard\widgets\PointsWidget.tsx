'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Award, Crown, TrendingUp } from 'lucide-react'

interface PointsWidgetProps {
  points: number
  rank?: number
  totalUsers?: number
}

export function PointsWidget({ points = 0, rank = 0, totalUsers = 0 }: PointsWidgetProps) {
  const [nextRankPoints, setNextRankPoints] = useState(0)
  const [rankLabel, setRankLabel] = useState('')
  
  useEffect(() => {
    // Calculate next rank threshold
    const calculateNextRank = () => {
      if (points < 100) {
        setNextRankPoints(100)
        setRankLabel('مراسل مبتدئ')
      } else if (points < 250) {
        setNextRankPoints(250)
        setRankLabel('مراسل ناشئ')
      } else if (points < 500) {
        setNextRankPoints(500)
        setRankLabel('كاتب صحفي')
      } else if (points < 1000) {
        setNextRankPoints(1000)
        setRankLabel('كاتب أول')
      } else if (points < 2000) {
        setNextRankPoints(2000)
        setRankLabel('محرر')
      } else {
        setNextRankPoints(points + 1000) // Just set a higher goal
        setRankLabel('رئيس التحرير')
      }
    }
    
    calculateNextRank()
  }, [points])
  
  // Calculate progress percentage
  const progress = Math.min(Math.round((points / nextRankPoints) * 100), 100)
  
  return (
    <Card className="shadow-md hover:shadow-lg transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">النقاط والتصنيف</CardTitle>
          <Badge className="bg-purple-100 text-purple-800 border-purple-300 flex items-center gap-1">
            <Award className="h-3.5 w-3.5 ml-1" />
            {rankLabel}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div dir="rtl" className="space-y-4">
          {/* Points display */}
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold">{points}</p>
              <p className="text-muted-foreground text-sm">مجموع النقاط</p>
            </div>
            <div className="flex flex-col items-end">
              {rank > 0 && totalUsers > 0 && (
                <div className="flex items-center gap-1">
                  <Crown className="h-4 w-4 text-yellow-500 ml-1" />
                  <span className="font-medium">المرتبة #{rank}</span>
                </div>
              )}
              <span className="text-sm text-muted-foreground">
                {nextRankPoints > points ? `${nextRankPoints - points} نقطة للمرتبة التالية` : "تم الوصول إلى أعلى مرتبة!"}
              </span>
            </div>
          </div>
          
          {/* Progress bar */}
          <div>
            <Progress value={progress} className="h-2" />
          </div>
          
          {/* Recent point activities placeholder */}
          <div className="pt-2">
            <div className="text-sm font-medium mb-2 flex items-center gap-1">
              <TrendingUp className="h-4 w-4 ml-1" />
              <span>النشاط الأخير</span>
            </div>
            <ul className="space-y-2 text-sm">
              <li className="flex justify-between items-center text-muted-foreground">
                <span>تم نشر مقال</span>
                <span className="text-green-600">+15 نقطة</span>
              </li>
              <li className="flex justify-between items-center text-muted-foreground">
                <span>تقييم جيد</span>
                <span className="text-green-600">+10 نقاط</span>
              </li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 