'use client'

import React, { useState, useRef, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { getImageUrl } from '@/utils/imageUtils'

interface News {
  id: string
  title: string
  slug: string
  featuredImage?: any
  publishedAt?: string
  createdAt: string
  tags?: string[]
}

interface ClientNewsCarouselProps {
  news: News[]
}

export function ClientNewsCarousel({ news }: ClientNewsCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [itemsPerPage, setItemsPerPage] = useState(4)
  const carouselRef = useRef<HTMLDivElement>(null)

  // If no news is provided, create placeholders
  const carouselItems =
    news.length > 0
      ? news
      : Array(4).fill({
          id: '1',
          title: 'Loading news...',
          slug: '#',
          createdAt: new Date().toISOString(),
          tags: ['News'],
        })

  // Determine items per page based on screen width
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setItemsPerPage(1)
      } else if (window.innerWidth < 768) {
        setItemsPerPage(2)
      } else if (window.innerWidth < 1024) {
        setItemsPerPage(3)
      } else {
        setItemsPerPage(4)
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const totalPages = Math.ceil(carouselItems.length / itemsPerPage)

  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % totalPages)
  }

  const goToPrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + totalPages) % totalPages)
  }

  return (
    <div className="relative" dir="ltr">
      <div ref={carouselRef} className="overflow-hidden">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {Array.from({ length: totalPages }).map((_, pageIndex) => (
            <div
              dir="rtl"
              key={pageIndex}
              className="w-full flex-shrink-0 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
            >
              {carouselItems
                .slice(pageIndex * itemsPerPage, (pageIndex + 1) * itemsPerPage)
                .map((item) => {
                  // Get featured image
                  const newsImage = item.featuredImage
                    ? getImageUrl(item.featuredImage)
                    : 'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80'

                  // Format date
                  const publishDate = item.publishedAt
                    ? new Date(item.publishedAt)
                    : new Date(item.createdAt)

                  const formattedDate = publishDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: '2-digit',
                  })

                  // Get category/tag
                  const category =
                    Array.isArray(item.tags) && item.tags.length > 0 ? item.tags[0] : 'News'

                  return (
                    <div key={item.id} className="relative h-[300px] overflow-hidden ">
                      <Image
                        src={newsImage}
                        alt={item.title || 'News image'}
                        fill
                        className="object-cover"
                        sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                      />
                      <div className="absolute inset-0 overlay flex flex-col justify-end p-4">
                        <div className="mb-2 flex items-center">
                          <span className="bg-primary text-primary-foreground uppercase font-semibold px-2 py-1 mr-2 text-xs ">
                            {category}
                          </span>
                          <span className="text-white text-xs">{formattedDate}</span>
                        </div>
                        <Link
                          href={`/news/${item.slug}`}
                          className="text-white uppercase font-semibold text-sm hover:text-primary-foreground/80 transition-colors"
                        >
                          {item.title}
                        </Link>
                      </div>
                    </div>
                  )
                })}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Arrows */}
      {totalPages > 1 && (
        <>
          <button
            onClick={goToPrev}
            className="absolute -left-4 top-1/2 -translate-y-1/2 z-10 bg-primary/80 text-primary-foreground p-2  hover:bg-primary transition-colors"
            aria-label="Previous slide"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>

          <button
            onClick={goToNext}
            className="absolute -right-4 top-1/2 -translate-y-1/2 z-10 bg-primary/80 text-primary-foreground p-2  hover:bg-primary transition-colors"
            aria-label="Next slide"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        </>
      )}

      {/* Dots */}
      {totalPages > 1 && (
        <div className="mt-4 flex justify-center space-x-2">
          {Array.from({ length: totalPages }).map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3  transition-all ${
                index === currentIndex
                  ? 'bg-primary w-6'
                  : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  )
}
