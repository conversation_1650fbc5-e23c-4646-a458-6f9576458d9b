@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    font-family: 'Tajawal', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: #111827;
    -webkit-font-smoothing: antialiased;
  }
  body{
    font-family: 'Tajawal' !important;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    
    /* Default cyan theme */
    --primary: 180 70% 40%;
    --primary-foreground: 0 0% 98%;
    --secondary: 180 20% 96.1%;
    --secondary-foreground: 180 70% 10%;
    --muted: 180 10% 96.1%;
    --muted-foreground: 180 5% 45.1%;
    --accent: 180 30% 96.1%;
    --accent-foreground: 180 70% 10%;
    --border: 180 10% 89.8%;
    --input: 180 10% 89.8%;
    --ring: 180 70% 40%;
    --chart-1: 180 70% 40%;
    --chart-2: 200 70% 50%;
    --chart-3: 160 70% 40%;
    --chart-4: 220 70% 50%;
    --chart-5: 270 70% 50%;

    /* Theme colors */
    --theme-cyan-primary: 180 70% 40%;
    --theme-cyan-ring: 180 70% 40%;
    
    --theme-blue-primary: 220 70% 50%;
    --theme-blue-ring: 220 70% 50%;
    
    --theme-purple-primary: 270 70% 50%;
    --theme-purple-ring: 270 70% 50%;
    
    --theme-green-primary: 150 70% 40%;
    --theme-green-ring: 150 70% 40%;
    
    --theme-amber-primary: 45 90% 50%;
    --theme-amber-ring: 45 90% 50%;
    
    --theme-pink-primary: 330 90% 60%;
    --theme-pink-ring: 330 90% 60%;
    
    --radius: 0.5rem;
  }
  
  /* Apply theme color */
  html[data-theme="cyan"] {
    --primary: var(--theme-cyan-primary);
    --ring: var(--theme-cyan-ring);
  }
  
  html[data-theme="blue"] {
    --primary: var(--theme-blue-primary);
    --ring: var(--theme-blue-ring);
  }
  
  html[data-theme="purple"] {
    --primary: var(--theme-purple-primary);
    --ring: var(--theme-purple-ring);
  }
  
  html[data-theme="green"] {
    --primary: var(--theme-green-primary);
    --ring: var(--theme-green-ring);
  }
  
  html[data-theme="amber"] {
    --primary: var(--theme-amber-primary);
    --ring: var(--theme-amber-ring);
  }
  
  html[data-theme="pink"] {
    --primary: var(--theme-pink-primary);
    --ring: var(--theme-pink-ring);
  }
  
  .dark {
    --background: 180 20% 10%;
    --foreground: 0 0% 98%;
    --card: 180 25% 12%;
    --card-foreground: 0 0% 98%;
    --popover: 180 25% 12%;
    --popover-foreground: 0 0% 98%;
    --primary: 180 70% 50%;
    --primary-foreground: 180 10% 10%;
    --secondary: 180 30% 20%;
    --secondary-foreground: 0 0% 98%;
    --muted: 180 25% 20%;
    --muted-foreground: 180 10% 70%;
    --accent: 180 30% 20%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 180 25% 25%;
    --input: 180 25% 25%;
    --ring: 180 70% 50%;
    --chart-1: 180 70% 50%;
    --chart-2: 200 70% 60%;
    --chart-3: 160 70% 50%;
    --chart-4: 220 70% 60%;
    --chart-5: 270 70% 60%;

    /* Theme colors for dark mode */
    --theme-cyan-primary: 180 70% 50%;
    --theme-cyan-ring: 180 70% 50%;
    
    --theme-blue-primary: 220 70% 60%;
    --theme-blue-ring: 220 70% 60%;
    
    --theme-purple-primary: 270 70% 60%;
    --theme-purple-ring: 270 70% 60%;
    
    --theme-green-primary: 150 70% 50%;
    --theme-green-ring: 150 70% 50%;
    
    --theme-amber-primary: 45 90% 60%;
    --theme-amber-ring: 45 90% 60%;
    
    --theme-pink-primary: 330 90% 70%;
    --theme-pink-ring: 330 90% 70%;
  }
  
  /* Dark mode theme combinations */
  html.dark[data-theme="cyan"] {
    --primary: var(--theme-cyan-primary);
    --ring: var(--theme-cyan-ring);
  }
  
  html.dark[data-theme="blue"] {
    --primary: var(--theme-blue-primary);
    --ring: var(--theme-blue-ring);
  }
  
  html.dark[data-theme="purple"] {
    --primary: var(--theme-purple-primary);
    --ring: var(--theme-purple-ring);
  }
  
  html.dark[data-theme="green"] {
    --primary: var(--theme-green-primary);
    --ring: var(--theme-green-ring);
  }
  
  html.dark[data-theme="amber"] {
    --primary: var(--theme-amber-primary);
    --ring: var(--theme-amber-ring);
  }
  
  html.dark[data-theme="pink"] {
    --primary: var(--theme-pink-primary);
    --ring: var(--theme-pink-ring);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

::-moz-selection { /* Code for Firefox */
  color: black;
  background: cyan;
}

::selection {
  color: black;
  background: cyan;
}
.animate-fadeIn {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-blink {
  animation: blink 1s step-end infinite;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-500 {
  animation-delay: 0.5s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

/* Responsive carousel fixes */
@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Sticky sidebar styles */
@media (min-width: 1024px) {
  #sticky-sidebar {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: auto;
    max-width: 320px;
    margin: 0 auto;
    position: relative;
    z-index: 10;
  }

  #content-sections {
    position: relative;
    z-index: 5;
  }

  /* Ensure the sidebar container stays in place */
  .lg\:sticky {
    position: sticky;
    top: 100px;
    height: auto;
    align-self: flex-start;
  }

  .lg\:self-start {
    align-self: flex-start;
  }

  /* Make sure the grid items align at the start */
  .items-start {
    align-items: flex-start;
  }

  /* New sticky sidebar styles */
  .sticky-sidebar-wrapper {
    position: relative;
    height: 100%;
  }

  .sticky-sidebar-content {
    position: sticky;
    top: 100px;
    padding-top: 1rem;
  }

  /* Make cards fully clickable */
  .card-link {
    position: relative;
    display: block;
    height: 100%;
  }

  .card-link:hover .card {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

/* Image Section Styles */
.image-section-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
}

.image-wrapper {
  width: 400px;
  height: 400px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
  animation: float 6s ease-in-out infinite;
}

.image-wrapper:hover {
  transform: scale(1.05) rotate(2deg);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.section-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.image-wrapper:hover .section-image {
  transform: scale(1.1);
}

@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(2deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}

/* Full-screen sections with scroll snap */
.fullscreen-section {
  width: 100vw;
  height: 100vh;
  scroll-snap-align: start;
  display: flex;
  position: relative;
  overflow: hidden;
}

.scroll-container {
  scroll-snap-type: y mandatory;
  scroll-behavior: smooth;
}

.section-content {
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 2rem;
}

.section-3d {
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

@media (max-width: 768px) {
  .fullscreen-section {
    flex-direction: column;
  }

  .section-content,
  .section-3d {
    width: 100%;
    height: 50%;
  }
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Fix flexbox direction for RTL */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

/* Fix text alignment for table cells in RTL */
[dir="rtl"] th,
[dir="rtl"] td {
  text-align: right;
}
