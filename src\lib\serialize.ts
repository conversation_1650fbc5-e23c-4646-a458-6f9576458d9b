/**
 * Utility functions for serializing rich text content
 */

// Helper function to check if an object is a plain object
export const isPlainObject = (obj: any): boolean => {
  return typeof obj === 'object' && obj !== null && !Array.isArray(obj)
}

// Simple serialize function to help with debugging rich text structures
export const serialize = (content: any): string => {
  if (!content) return ''
  
  if (typeof content === 'string') return content
  
  if (Array.isArray(content)) {
    return content.map(serialize).join('')
  }
  
  if (isPlainObject(content)) {
    // Handle text nodes
    if (content.text) {
      let text = content.text
      return text
    }
    
    // Handle blocks with children
    if (content.children && Array.isArray(content.children)) {
      return content.children.map(serialize).join('')
    }
    
    // Handle other objects
    return Object.values(content).map(val => serialize(val)).join('')
  }
  
  return String(content)
} 