'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import bgImage from '../../../../public/media/isco-ry_ZwyuttGU-unsplash.jpg'
export const ModernLoginForm: React.FC = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      console.log('Attempting login with email:', email)

      // Use the custom login API
      console.log('Sending login request to /api/custom-login')
      const response = await fetch('/api/custom-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        // Ensure credentials are included
        credentials: 'include',
      })

      console.log('Login response status:', response.status)

      // Custom login API doesn't redirect, it returns a JSON response
      // We'll handle the redirection ourselves

      // Otherwise, handle the JSON response
      let data
      try {
        data = await response.json()
        console.log('Login response data:', data)
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError)
        throw new Error('Invalid response from server')
      }

      if (!response.ok) {
        console.error('Login failed:', data.error || 'Unknown error')
        throw new Error(data.error || 'Login failed')
      }

      // Check if user has pending approval status
      if (data.pendingApproval) {
        console.log('User has pending approval status, redirecting to pending-approval page')

        // Check if the pending-approval page exists
        try {
          const routeCheck = await fetch('/api/check-route?path=/pending-approval', {
            method: 'GET',
          })
          const routeData = await routeCheck.json()

          if (routeData.exists) {
            // Page exists, redirect to it
            window.location.href = '/pending-approval'
          } else {
            // Page doesn't exist, show an error message instead
            setError('حسابك في انتظار الموافقة. يرجى انتظار موافقة المدير على حسابك.')
            setIsLoading(false)
          }
        } catch (error) {
          // If check fails, try to redirect anyway
          window.location.href = '/pending-approval'
        }
        return
      }

      console.log('Login successful, redirecting to dashboard')

      // Add a small delay to ensure the cookie is properly set
      setTimeout(() => {
        // Redirect to dashboard
        window.location.href = '/dashboard'
      }, 500)
    } catch (err) {
      console.error('Login error:', err)
      setError(err instanceof Error ? err.message : 'Login failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <section className="flex flex-col md:flex-row h-screen items-center">
      {/* Left side - Image */}
      <div className="bg-primary hidden lg:block w-full md:w-1/2 xl:w-2/3 h-screen relative">
        <Image src={bgImage} alt="الصحفي الصغير" fill className="object-cover" priority />
        <div className="absolute inset-0 bg-primary/30"></div>
        <div className="absolute bottom-0 left-0 right-0 p-12 text-white " dir="rtl">
          <h2 className="text-4xl font-bold mb-4">الصحفي الصغير</h2>
          <p className="text-xl">انضم إلى مجتمع الصحفيين وشارك قصصك مع العالم.</p>
        </div>
      </div>

      {/* Right side - Login Form */}
      <div
        className="bg-background w-full md:max-w-md lg:max-w-full md:mx-auto md:w-1/2 xl:w-1/3 h-screen px-6 lg:px-16 xl:px-12
            flex items-center justify-center"
      >
        <div className="w-full h-100">
          <h1 className="text-xl font-bold text-primary">الصحفي الصغير</h1>

          <h1 className="text-xl md:text-2xl font-bold leading-tight mt-12">
            تسجيل الدخول إلى حسابك
          </h1>

          {error && (
            <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded mt-4 mb-4">
              {error}
            </div>
          )}

          <form className="mt-6" onSubmit={handleSubmit} dir="rtl">
            <div className="mb-4">
              <Label htmlFor="email" className="block text-foreground">
                عنوان البريد الإلكتروني
              </Label>
              <Input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="أدخل عنوان البريد الإلكتروني"
                className="w-full mt-2"
                autoFocus
                required
              />
            </div>

            <div className="mb-4">
              <Label htmlFor="password" className="block text-foreground">
                كلمة المرور
              </Label>
              <Input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="أدخل كلمة المرور"
                className="w-full mt-2"
                required
              />
            </div>

            <div className="text-right mt-2">
              <Link
                href="/forgot-password"
                className="text-sm font-semibold text-primary hover:text-primary/80"
              >
                نسيت كلمة المرور؟
              </Link>
            </div>

            <Button type="submit" className="w-full mt-6" disabled={isLoading}>
              {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
            </Button>
          </form>

          <hr className="my-6 border-border w-full" />

          <Button
            type="button"
            variant="outline"
            className="w-full flex items-center justify-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 mr-2" viewBox="0 0 48 48">
              <defs>
                <path
                  id="a"
                  d="M44.5 20H24v8.5h11.8C34.7 33.9 30.1 37 24 37c-7.2 0-13-5.8-13-13s5.8-13 13-13c3.1 0 5.9 1.1 8.1 2.9l6.4-6.4C34.6 4.1 29.6 2 24 2 11.8 2 2 11.8 2 24s9.8 22 22 22c11 0 21-8 21-22 0-1.3-.2-2.7-.5-4z"
                />
              </defs>
              <clipPath id="b">
                <use xlinkHref="#a" overflow="visible" />
              </clipPath>
              <path clipPath="url(#b)" fill="#FBBC05" d="M0 37V11l17 13z" />
              <path clipPath="url(#b)" fill="#EA4335" d="M0 11l17 13 7-6.1L48 14V0H0z" />
              <path clipPath="url(#b)" fill="#34A853" d="M0 37l30-23 7.9 1L48 0v48H0z" />
              <path clipPath="url(#b)" fill="#4285F4" d="M48 48L17 24l-4-3 35-10z" />
            </svg>
            تسجيل الدخول بواسطة جوجل
          </Button>

          <p className="mt-8">
            تحتاج إلى حساب؟{' '}
            <Link href="/register" className="text-primary hover:text-primary/80 font-semibold">
              إنشاء حساب
            </Link>
          </p>

          <p className="text-sm text-muted-foreground mt-12">
            &copy; {new Date().getFullYear()} الصحفي الصغير - جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </section>
  )
}
