'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Loader2, Calendar, User, Tag, ArrowLeft, ThumbsUp, Share2 } from 'lucide-react'
import Link from 'next/link'
import { renderRichText } from '@/utils/richTextUtils'

interface NewsItem {
  id: string
  title: string
  content: any
  author: {
    id: string
    firstName: string
    lastName: string
    profileImage?: {
      url: string
    }
  }
  category?: string
  publishedDate: string
  status: string
  featuredImage?: {
    url: string
  }
  slug: string
  likes?: number
  views?: number
}

export default function NewsDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [newsItem, setNewsItem] = useState<NewsItem | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [relatedNews, setRelatedNews] = useState<NewsItem[]>([])

  useEffect(() => {
    async function fetchNewsDetail() {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/dashboard/news/${params.id}`, {
          credentials: 'include',
        })

        if (!response.ok) {
          throw new Error('Failed to fetch news detail')
        }

        const data = await response.json()
        setNewsItem(data.news)

        // Fetch related news
        const relatedResponse = await fetch('/api/dashboard/news?limit=3', {
          credentials: 'include',
        })

        if (relatedResponse.ok) {
          const relatedData = await relatedResponse.json()
          // Filter out current news and limit to 3 items
          const filteredRelated = relatedData.news
            .filter((item: NewsItem) => item.id !== params.id)
            .slice(0, 3)
          setRelatedNews(filteredRelated)
        }

        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching news detail:', err)
        setError('Failed to load news detail. Please try again.')
        setIsLoading(false)
      }
    }

    if (params.id) {
      fetchNewsDetail()
    }
  }, [params.id])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: newsItem?.title || 'News Article',
        text: 'Check out this news article',
        url: window.location.href,
      })
    } else {
      // Fallback for browsers that don't support navigator.share
      navigator.clipboard.writeText(window.location.href)
      alert('Link copied to clipboard!')
    }
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col space-y-6">
        <Button variant="ghost" className="w-fit" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to News
        </Button>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4 rounded-md text-red-800 dark:text-red-300">
            {error}
          </div>
        ) : newsItem ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <Card>
                {newsItem.featuredImage && (
                  <div className="w-full h-64 md:h-80 overflow-hidden">
                    <img
                      src={newsItem.featuredImage.url}
                      alt={newsItem.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <CardHeader>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {newsItem.category && <Badge variant="outline">{newsItem.category}</Badge>}
                  </div>
                  <CardTitle className="text-2xl md:text-3xl">{newsItem.title}</CardTitle>
                  <div className="flex flex-wrap items-center text-sm text-gray-500 mt-2 gap-4">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {formatDate(newsItem.publishedDate)}
                    </div>
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      {newsItem.author
                        ? `${newsItem.author.firstName} ${newsItem.author.lastName}`
                        : 'Unknown'}
                    </div>
                    <div className="flex items-center">
                      <ThumbsUp className="h-4 w-4 mr-1" />
                      {newsItem.likes || 0} likes
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="prose dark:prose-invert max-w-none">
                    {newsItem.content ? (
                      <div className="whitespace-pre-wrap">{renderRichText(newsItem.content)}</div>
                    ) : (
                      <p>No content available</p>
                    )}
                  </div>

                  <div className="flex justify-end mt-6">
                    <Button variant="outline" onClick={handleShare}>
                      <Share2 className="mr-2 h-4 w-4" />
                      Share
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Related News</CardTitle>
                </CardHeader>
                <CardContent>
                  {relatedNews.length > 0 ? (
                    <div className="space-y-4">
                      {relatedNews.map((item) => (
                        <Link
                          href={`/dashboard/news/${item.id}`}
                          key={item.id}
                          className="block hover:bg-gray-50 dark:hover:bg-gray-800 p-3 rounded-md transition-colors"
                        >
                          <div className="flex gap-3">
                            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded overflow-hidden flex-shrink-0">
                              {item.featuredImage ? (
                                <img
                                  src={item.featuredImage.url}
                                  alt={item.title}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="flex items-center justify-center h-full">
                                  <span className="text-gray-400 text-xs">No image</span>
                                </div>
                              )}
                            </div>
                            <div>
                              <h3 className="font-medium line-clamp-2">{item.title}</h3>
                              <p className="text-xs text-gray-500 mt-1">
                                {formatDate(item.publishedDate)}
                              </p>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No related news found</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-md">
            <p className="text-gray-500 dark:text-gray-400">News item not found</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
