'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2, ExternalLink, Eye } from 'lucide-react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { EnhancedEditor, textToLexical, lexicalToText } from '@/components/ui/EnhancedEditor'
import { extractPlainText, createRichTextFromPlain } from '@/utils/richTextUtils'
import { ImageUpload } from '@/components/ui/ImageUpload'
import { getImageUrl } from '@/utils/imageUtils'

export default function NewsEditClient({ idOrSlug }: { idOrSlug: string }) {
  const [isLoading, setIsLoading] = useState(true)
  const [newsItem, setNewsItem] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    featuredImage: '',
    status: 'draft',
  })

  useEffect(() => {
    async function fetchNewsItem() {
      try {
        // Function to check if a string is a valid MongoDB ObjectId
        const isObjectId = (str: string) => /^[0-9a-fA-F]{24}$/.test(str)

        let url = ''
        if (isObjectId(idOrSlug)) {
          // Fetch by ID if it's a valid ObjectId
          url = `/api/dashboard/news/${idOrSlug}`
        } else {
          // Fetch by slug otherwise
          url = `/api/dashboard/news/slug/${idOrSlug}`
        }

        const response = await fetch(url, {
          credentials: 'include',
        })

        if (!response.ok) {
          if (response.status === 404) {
            setError('News item not found')
          } else if (response.status === 401) {
            setError('You must be logged in to edit news')
          } else if (response.status === 403) {
            setError('You do not have permission to edit this news item')
          } else {
            setError('An error occurred while fetching the news item')
          }
          setIsLoading(false)
          return
        }

        const data = await response.json()
        const newsData = data.news // Adjust based on your API response structure if needed

        if (!newsData) {
          setError('News item data is missing in the response.')
          setIsLoading(false)
          return
        }

        setNewsItem(newsData)

        // Initialize form data
        setFormData({
          title: newsData.title || '',
          content:
            typeof newsData.content === 'string'
              ? newsData.content
              : extractPlainText(newsData.content) || '',
          featuredImage: newsData.featuredImage ? getImageUrl(newsData.featuredImage) : '',
          status: newsData.status || 'draft',
        })

        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching news item:', err)
        setError('An unexpected error occurred')
        setIsLoading(false)
      }
    }

    fetchNewsItem()
  }, [idOrSlug])

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  // Handle rich text editor changes
  const handleRichTextChange = (value: string) => {
    // Update the content without triggering form submission
    // This prevents the form from submitting while still editing
    setFormData((prev) => ({ ...prev, content: value }))
  }

  // Handle image upload
  const handleImageChange = (url: string) => {
    setFormData((prev) => ({ ...prev, featuredImage: url }))
  }

  // Toggle preview mode
  const togglePreview = () => {
    setIsPreviewMode((prev) => !prev)
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-center text-muted-foreground">Loading news item...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-500">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button variant="outline" className="mt-4" asChild>
              <Link href="/dashboard/news">Back to News</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Get the button that was clicked
      const submitter = (e as any).nativeEvent.submitter
      const status = submitter.value || formData.status

      // Function to check if a string is a valid MongoDB ObjectId
      const isObjectId = (str: string) => /^[0-9a-fA-F]{24}$/.test(str)

      let apiUrl = ''
      if (isObjectId(idOrSlug)) {
        // Use the ID from the route if it's an ObjectId
        apiUrl = `/api/collections/news/${idOrSlug}`
      } else {
        // If it's a slug, you might need a dedicated update-by-slug API route
        // or refetch by slug to get the ID.
        // For now, we'll assume you have a way to update by slug.
        apiUrl = `/api/collections/news/slug/${idOrSlug}` // Replace with your slug update route
      }

      const response = await fetch(apiUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          content: formData.content,
          featuredImage: formData.featuredImage || undefined,
          status,
        }),
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to update news')
      }

      // Redirect to the news page
      window.location.href = '/dashboard/news'
    } catch (err) {
      console.error('Error updating news:', err)
      setError('Failed to update news. Please try again.')
      setIsLoading(false)
    }
  }

  // Create a preview HTML from the content
  const createPreviewHtml = (content: string) => {
    // Simple HTML conversion for preview
    return content
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/__(.*?)__/g, '<u>$1</u>')
      .replace(/# (.*?)(\n|$)/g, '<h1>$1</h1>')
      .replace(/## (.*?)(\n|$)/g, '<h2>$1</h2>')
      .replace(/### (.*?)(\n|$)/g, '<h3>$1</h3>')
      .replace(/> (.*?)(\n|$)/g, '<blockquote>$1</blockquote>')
  }

  return (
    <div className="p-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Edit News: {newsItem?.title}</CardTitle>
          <Button variant="outline" onClick={togglePreview} className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            {isPreviewMode ? 'Edit' : 'Preview'}
          </Button>
        </CardHeader>
        <CardContent>
          {isPreviewMode ? (
            <div className="preview-container">
              <h1 className="text-2xl font-bold mb-4">{formData.title}</h1>
              {formData.featuredImage && (
                <div className="mb-4 relative aspect-video w-full overflow-hidden rounded-md bg-gray-100">
                  <img
                    src={formData.featuredImage}
                    alt={formData.title}
                    className="object-cover w-full h-full"
                  />
                </div>
              )}
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: createPreviewHtml(formData.content) }}
              />
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">News Title</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <ImageUpload value={formData.featuredImage} onChange={handleImageChange} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">News Content</Label>
                <EnhancedEditor
                  value={formData.content}
                  onChange={handleRichTextChange}
                  placeholder="Write your news content here..."
                  className="mb-4"
                />
              </div>

              <div className="flex justify-between">
                <div className="space-x-4">
                  <Button type="submit" name="status" value={formData.status}>
                    Save Changes
                  </Button>
                  {formData.status === 'draft' && (
                    <Button
                      type="submit"
                      name="status"
                      value="published"
                      variant="default"
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Publish Now
                    </Button>
                  )}
                  {formData.status === 'published' && (
                    <Button
                      type="submit"
                      name="status"
                      value="draft"
                      variant="default"
                      className="bg-yellow-600 hover:bg-yellow-700"
                    >
                      Unpublish
                    </Button>
                  )}
                </div>
                <Button variant="outline" asChild>
                  <Link href="/dashboard/news">Cancel</Link>
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>

      <div className="mt-8 bg-blue-50 p-6 rounded-lg">
        <h2 className="text-xl font-bold mb-4">News Post Guidelines</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>Keep news posts clear, concise, and relevant to the Young Reporter community.</li>
          <li>Use a descriptive title that accurately reflects the content.</li>
          <li>Create a simple, readable slug for the URL.</li>
          <li>Include all necessary details such as dates, times, and locations for events.</li>
          <li>Proofread your post before publishing.</li>
        </ul>
      </div>
    </div>
  )
}
