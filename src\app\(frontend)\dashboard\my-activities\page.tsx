'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { PointsDisplay } from '@/components/points/PointsDisplay'
import { PointsHistory } from '@/components/points/PointsHistory'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Award, Calendar, CheckCircle, Clock, Target } from 'lucide-react'

export default function MyActivitiesPage() {
  const router = useRouter()
  const [userRole, setUserRole] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if user is authenticated
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/me')
        const data = await response.json()

        if (!response.ok || !data.user) {
          router.push('/login')
          return
        }

        // Get the role, handling both string and object formats
        const role = typeof data.user.role === 'object' ? data.user.role?.slug : data.user.role
        setUserRole(role)
        setIsLoading(false)
      } catch (error) {
        console.error('Error checking auth:', error)
        router.push('/login')
      }
    }

    checkAuth()
  }, [router])

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
        <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold dark:text-white">My Activities</h1>
        <p className="text-gray-500 dark:text-gray-400">
          Track your activities and points
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <PointsDisplay />
        </div>
        <div className="md:col-span-2">
          <Tabs defaultValue="history">
            <Card>
              <CardHeader className="pb-0">
                <div className="flex justify-between items-center">
                  <CardTitle>Activity Dashboard</CardTitle>
                  <TabsList>
                    <TabsTrigger value="history" className="text-xs">
                      <Clock className="h-3.5 w-3.5 mr-1" />
                      History
                    </TabsTrigger>
                    <TabsTrigger value="tasks" className="text-xs">
                      <CheckCircle className="h-3.5 w-3.5 mr-1" />
                      Tasks
                    </TabsTrigger>
                    <TabsTrigger value="goals" className="text-xs">
                      <Target className="h-3.5 w-3.5 mr-1" />
                      Goals
                    </TabsTrigger>
                  </TabsList>
                </div>
                <CardDescription>View your activity details</CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <TabsContent value="history" className="m-0">
                  <PointsHistory />
                </TabsContent>
                <TabsContent value="tasks" className="m-0">
                  <TasksContent userRole={userRole} />
                </TabsContent>
                <TabsContent value="goals" className="m-0">
                  <GoalsContent userRole={userRole} />
                </TabsContent>
              </CardContent>
            </Card>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

function TasksContent({ userRole }: { userRole: string | null }) {
  const tasks = getTasksForRole(userRole)
  
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Daily Tasks</h3>
      <div className="grid grid-cols-1 gap-3">
        {tasks.daily.map((task, index) => (
          <div key={index} className="flex items-start gap-3 p-3 border rounded-md">
            <div className="p-2 rounded-full bg-blue-100">
              <CheckCircle className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <div className="font-medium">{task.title}</div>
              <div className="text-sm text-muted-foreground">{task.description}</div>
              <div className="text-xs text-muted-foreground mt-1">
                Points: <span className="text-green-600">+{task.points}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <h3 className="text-lg font-medium mt-6">Weekly Tasks</h3>
      <div className="grid grid-cols-1 gap-3">
        {tasks.weekly.map((task, index) => (
          <div key={index} className="flex items-start gap-3 p-3 border rounded-md">
            <div className="p-2 rounded-full bg-purple-100">
              <Calendar className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <div className="font-medium">{task.title}</div>
              <div className="text-sm text-muted-foreground">{task.description}</div>
              <div className="text-xs text-muted-foreground mt-1">
                Points: <span className="text-green-600">+{task.points}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

function GoalsContent({ userRole }: { userRole: string | null }) {
  const ranks = getRanksForRole(userRole)
  
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Rank Progression</h3>
      <div className="grid grid-cols-1 gap-3">
        {ranks.map((rank, index) => (
          <div key={index} className="flex items-start gap-3 p-3 border rounded-md">
            <div className="p-2 rounded-full bg-amber-100">
              <Award className="h-4 w-4 text-amber-600" />
            </div>
            <div className="flex-1">
              <div className="flex justify-between">
                <div className="font-medium">{rank.title}</div>
                <div className="text-xs text-muted-foreground">
                  {rank.points} points
                </div>
              </div>
              <div className="text-sm text-muted-foreground">{rank.description}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

function getTasksForRole(role: string | null) {
  if (role === 'teacher') {
    return {
      daily: [
        {
          title: 'Approve Posts',
          description: 'Approve 3 student posts (5 points each, extra approvals 2 points each)',
          points: 15
        },
        {
          title: 'Approve Users',
          description: 'Approve 2 student registrations (10 points each, extra approvals 2 points each)',
          points: 20
        }
      ],
      weekly: [
        {
          title: 'Complete Daily Tasks',
          description: 'Complete all daily tasks for at least 5 days this week',
          points: 50
        }
      ]
    }
  }
  else if (role === 'mentor') {
    return {
      daily: [
        {
          title: 'Create Posts',
          description: 'Create at least 1 news post (20 points each)',
          points: 20
        },
        {
          title: 'Review Teachers',
          description: 'Review at least 2 teacher activities (15 points each)',
          points: 30
        }
      ],
      weekly: [
        {
          title: 'Complete Daily Tasks',
          description: 'Complete all daily tasks for at least 3 days this week',
          points: 50
        }
      ]
    }
  }
  else if (role === 'student') {
    return {
      daily: [
        {
          title: 'Submit Articles',
          description: 'Submit at least one article for review',
          points: 10
        }
      ],
      weekly: [
        {
          title: 'Quality Articles',
          description: 'Get at least 2 articles rated 3+ stars this week',
          points: 40
        }
      ]
    }
  }
  
  return { daily: [], weekly: [] }
}

function getRanksForRole(role: string | null) {
  if (role === 'teacher') {
    return [
      {
        title: 'Novice Teacher',
        description: 'Just starting out as a teacher',
        points: 0
      },
      {
        title: 'Teacher',
        description: 'Regular teacher with basic responsibilities',
        points: 100
      },
      {
        title: 'Experienced Teacher',
        description: 'Experienced teacher with consistent activity',
        points: 200
      },
      {
        title: 'Senior Teacher',
        description: 'Senior teacher with high engagement',
        points: 500
      },
      {
        title: 'Master Teacher',
        description: 'Master teacher with exceptional contribution',
        points: 1000
      }
    ]
  }
  else if (role === 'mentor') {
    return [
      {
        title: 'Novice Mentor',
        description: 'Just starting out as a mentor',
        points: 0
      },
      {
        title: 'Mentor',
        description: 'Regular mentor with basic responsibilities',
        points: 100
      },
      {
        title: 'Experienced Mentor',
        description: 'Experienced mentor with consistent activity',
        points: 200
      },
      {
        title: 'Senior Mentor',
        description: 'Senior mentor with high engagement',
        points: 500
      },
      {
        title: 'Master Mentor',
        description: 'Master mentor with exceptional contribution',
        points: 1000
      }
    ]
  }
  else if (role === 'student') {
    return [
      {
        title: 'Novice Reporter',
        description: 'Just starting out as a reporter',
        points: 0
      },
      {
        title: 'Reporter',
        description: 'Regular reporter with basic skills',
        points: 100
      },
      {
        title: 'Experienced Reporter',
        description: 'Experienced reporter with consistent quality',
        points: 200
      },
      {
        title: 'Senior Reporter',
        description: 'Senior reporter with high-quality articles',
        points: 500
      },
      {
        title: 'Master Reporter',
        description: 'Master reporter with exceptional articles',
        points: 1000
      }
    ]
  }
  
  return []
}
