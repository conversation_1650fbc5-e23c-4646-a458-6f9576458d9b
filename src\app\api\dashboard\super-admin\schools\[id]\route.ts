import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    if (!token) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
    const userId = typeof decoded === 'object' ? decoded.id : null
    if (!userId) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    const payload = await getPayload({ config })
    const user = await payload.findByID({ collection: 'users', id: userId, depth: 1 })
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'super-admin') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    // Try MongoDB first
    try {
      const { db } = await connectToDatabase()
      let school = null
      try {
        school = await db.collection('schools').findOne({ _id: new ObjectId((await params).id) })
      } catch {
        // If not a valid ObjectId, try string id
        school = await db.collection('schools').findOne({ id: params.id })
      }
      if (school) {
        return NextResponse.json({
          id: school._id?.toString() || school.id,
          name: school.name,
          address: school.address,
          createdAt: school.createdAt,
          updatedAt: school.updatedAt,
        })
      }
    } catch (mongoError) {
      console.warn('Error fetching school from MongoDB, falling back to Payload:', mongoError)
    }
    // Fallback to Payload CMS
    try {
      const school = await payload.findByID({ collection: 'schools', id: params.id, depth: 1 })
      if (school) {
        return NextResponse.json({
          id: school.id,
          name: school.name,
          address: school.address,
          createdAt: school.createdAt,
          updatedAt: school.updatedAt,
        })
      }
    } catch (payloadError) {
      console.warn('Error fetching school from Payload CMS:', payloadError)
    }
    // Not found
    return NextResponse.json({ error: 'Not found' }, { status: 404 })
  } catch (error) {
    console.error('Error fetching school:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
