import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { ModernLoginForm } from './ModernLoginForm'

export default async function LoginPage() {
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // If user is already logged in, redirect to home
  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="max-w-md w-full p-8 bg-card rounded-lg shadow-lg">
          <h1 className="text-2xl font-bold text-center mb-6">مسجل دخول بالفعل</h1>
          <p className="text-muted-foreground mb-6 text-center">
            أنت مسجل دخول بالفعل باسم {user.email}.
          </p>
          <div className="flex justify-center">
            <Link
              href="/"
              className="bg-primary text-primary-foreground px-6 py-3 rounded-lg font-bold hover:bg-primary/90 transition"
            >
              الذهاب للصفحة الرئيسية
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return <ModernLoginForm />
}
