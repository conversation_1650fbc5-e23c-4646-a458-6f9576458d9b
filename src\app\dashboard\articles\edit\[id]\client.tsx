'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2, Eye } from 'lucide-react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { EnhancedEditor } from '@/components/ui/EnhancedEditor'
import { ImageUpload } from '@/components/ui/ImageUpload'
import { getImageUrl } from '@/utils/imageUtils'

export default function ArticleEditClient({ id }: { id: string }) {
  const [isLoading, setIsLoading] = useState(true)
  const [article, setArticle] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    featuredImage: '',
    status: 'draft',
  })
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    async function fetchUser() {
      try {
        const response = await fetch('/api/auth/me', { credentials: 'include' })
        const data = await response.json()
        if (response.ok && data.user) {
          setUser(data.user)
        }
      } catch (err) {
        // Ignore user fetch error
      }
    }
    fetchUser()
  }, [])

  useEffect(() => {
    async function fetchArticle() {
      try {
        const response = await fetch(`/api/dashboard/articles/${id}`, {
          credentials: 'include',
        })
        if (!response.ok) {
          setError('Article not found or you do not have permission to edit this article')
          setIsLoading(false)
          return
        }
        const data = await response.json()
        setArticle(data.data?.article || data.article)
        setFormData({
          title: data.data?.article?.title || data.article?.title || '',
          content: typeof data.data?.article?.content === 'string'
            ? data.data.article.content
            : '',
          featuredImage: data.data?.article?.featuredImage
            ? getImageUrl(data.data.article.featuredImage)
            : '',
          status: data.data?.article?.status || 'draft',
        })
        setIsLoading(false)
      } catch (err) {
        setError('Failed to load article')
        setIsLoading(false)
      }
    }
    fetchArticle()
  }, [id])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleRichTextChange = (value: string) => {
    setFormData(prev => ({ ...prev, content: value }))
  }

  const handleImageChange = (url: string) => {
    setFormData(prev => ({ ...prev, featuredImage: url }))
  }

  const togglePreview = () => {
    setIsPreviewMode(prev => !prev)
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-center text-muted-foreground">Loading article...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-500">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button variant="outline" className="mt-4" asChild>
              <Link href="/dashboard/articles">Back to Articles</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)
    try {
      const submitter = (e as any).nativeEvent.submitter
      const status = submitter.value || formData.status
      // Determine if user is admin
      const isAdmin = user && ['super-admin', 'school-admin'].includes(
        typeof user.role === 'object' ? user.role.slug : user.role
      )
      
      // Ensure featuredImage is in the correct format
      let formattedImage = formData.featuredImage;
      
      // If the image is already an ID or a path, ensure it's in the right format
      if (formattedImage && !formattedImage.includes('/api/media/file/')) {
        // If it's just an ID or a partial path, convert to full path
        const filename = formattedImage.split('/').pop(); // Get the filename or ID
        if (filename) {
          formattedImage = `/api/media/file/${filename}`;
        }
      }
      
      // Build update payload
      const updatePayload: any = {
        title: formData.title,
        content: formData.content,
        featuredImage: formattedImage || undefined,
        status,
      }
      
      // Only include author if NOT admin and author is present in formData
      if (!isAdmin && 'author' in formData && formData.author) {
        updatePayload.author = formData.author
      }
      
      console.log("Submitting with payload:", updatePayload);
      
      const response = await fetch(`/api/collections/articles/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatePayload),
        credentials: 'include',
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to update article')
      }
      window.location.href = '/dashboard/articles'
    } catch (err) {
      setError('Failed to update article. Please try again.')
      setIsLoading(false)
    }
  }

  const createPreviewHtml = (content: string) => {
    return content
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/__(.*?)__/g, '<u>$1</u>')
      .replace(/# (.*?)(\n|$)/g, '<h1>$1</h1>')
      .replace(/## (.*?)(\n|$)/g, '<h2>$1</h2>')
      .replace(/### (.*?)(\n|$)/g, '<h3>$1</h3>')
      .replace(/> (.*?)(\n|$)/g, '<blockquote>$1</blockquote>')
  }

  return (
    <div className="p-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Edit Article: {article?.title}</CardTitle>
          <Button
            variant="outline"
            onClick={togglePreview}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            {isPreviewMode ? 'Edit' : 'Preview'}
          </Button>
        </CardHeader>
        <CardContent>
          {isPreviewMode ? (
            <div className="preview-container">
              <h1 className="text-2xl font-bold mb-4">{formData.title}</h1>
              {formData.featuredImage && (
                <div className="mb-4 relative aspect-video w-full overflow-hidden rounded-md bg-gray-100">
                  <img
                    src={formData.featuredImage}
                    alt={formData.title}
                    className="object-cover w-full h-full"
                  />
                </div>
              )}
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: createPreviewHtml(formData.content) }}
              />
            </div>
          ) :
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">Article Title</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <ImageUpload
                  value={formData.featuredImage}
                  onChange={handleImageChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">Article Content</Label>
                <EnhancedEditor
                  value={formData.content}
                  onChange={handleRichTextChange}
                  placeholder="Write your article content here..."
                  className="mb-4"
                />
              </div>

              <div className="flex justify-between">
                <div className="space-x-4">
                  <Button
                    type="submit"
                    name="status"
                    value={formData.status}
                  >
                    Save Changes
                  </Button>
                  
                  {/* Check if user is admin */}
                  {user && ['super-admin', 'school-admin'].includes(
                    typeof user.role === 'object' ? user.role.slug : user.role
                  ) && (
                    <>
                      {/* Admin-only publish button - always visible regardless of article status */}
                      <Button
                        type="submit"
                        name="status"
                        value="published"
                        variant="default"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Publish
                      </Button>
                      
                      {/* Admin-only unpublish button - only when published */}
                      {formData.status === 'published' && (
                        <Button
                          type="submit"
                          name="status"
                          value="draft"
                          variant="default"
                          className="bg-yellow-600 hover:bg-yellow-700"
                        >
                          Unpublish
                        </Button>
                      )}
                    </>
                  )}
                  
                  {/* Student workflow buttons */}
                  {(!user || !['super-admin', 'school-admin'].includes(
                    typeof user.role === 'object' ? user.role.slug : user.role
                  )) && (
                    <>
                      {formData.status === 'draft' && (
                        <Button
                          type="submit"
                          name="status"
                          value="ready-for-review"
                          variant="default"
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          Submit for Review
                        </Button>
                      )}
                      
                      {formData.status === 'ready-for-review' && (
                        <Button
                          type="submit"
                          name="status"
                          value="draft"
                          variant="outline"
                        >
                          Return to Draft
                        </Button>
                      )}
                    </>
                  )}
                </div>
                <Button variant="outline" asChild>
                  <Link href="/dashboard/articles">
                    Cancel
                  </Link>
                </Button>
              </div>
            </form>
          }
        </CardContent>
      </Card>
    </div>
  )
} 