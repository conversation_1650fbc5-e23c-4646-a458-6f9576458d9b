'use client'

import { useEffect, useState } from 'react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { extractPlainText } from '@/utils/richTextUtils'
import { getArticleId } from '@/lib/id-utils'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { PlusCircle, Eye, Pencil, Trash2, RefreshCw, Loader2, Search, Filter } from 'lucide-react'
import Image from 'next/image'
import { getImageUrl, getImageAlt } from '@/utils/imageUtils'

// Define interface for Article type
interface Article {
  id?: string;
  _id?: string | { $oid: string } | any;
  title: string;
  content?: any;
  status: 'published' | 'pending-review' | 'draft' | string; // Allow string for flexibility
  createdAt: string;
  updatedAt?: string;
  author?: string | { $oid?: string, id?: string } | any;
  slug?: string;
  views?: number;
  // Allow any format for featuredImage
  featuredImage?: string | { $oid?: string } | any;
  image?: string | { $oid?: string } | any;
  teacherReview?: any[];
  category?: string;
  summary?: string;
  tags?: string[];
}

export default function MyArticlesPage() {
  const [articles, setArticles] = useState<Article[]>([])
  const [filteredArticles, setFilteredArticles] = useState<Article[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteId, setDeleteId] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [debug, setDebug] = useState<string>('') // Debug info
  const [showRawData, setShowRawData] = useState(false)
  const [rawArticles, setRawArticles] = useState<any[]>([])

  // Function to fetch articles that can be called multiple times
  const fetchArticles = async () => {
    try {
      console.log('Fetching articles...')
      setIsLoading(true)
      setDebug('') // Clear debug info
      setRawArticles([])

      // First check if we're authenticated
      const authResponse = await fetch('/api/auth/me', {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
      })

      if (!authResponse.ok) {
        console.error('Auth check failed:', await authResponse.text())
        throw new Error('Authentication failed')
      }

      const authData = await authResponse.json()
      console.log('Auth check successful, user:', authData.user ? 'found' : 'not found')

      if (!authData.user) {
        throw new Error('User not authenticated')
      }

      // Now fetch the articles
      console.log('Fetching articles for user:', authData.user.id)
      const response = await fetch('/api/dashboard/my-articles', {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
      })

      console.log('Articles API response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('API error response:', errorText)
        throw new Error(`Failed to fetch articles: ${response.status} ${errorText}`)
      }

      const data = await response.json()
      console.log('Articles data received:', data)
      console.log('Number of articles:', data.articles ? data.articles.length : 0)
      
      // Add extra debugging for API response
      if (data.articles && data.articles.length > 0) {
        console.log('API RESPONSE STRUCTURE CHECK:')
        // Check first article as example
        const firstArticle = data.articles[0]
        console.log('First article keys:', Object.keys(firstArticle))
        console.log('First article direct featuredImage check:', firstArticle.featuredImage)
        
        // Check all articles for featuredImage
        const withImages = data.articles.filter((a: any) => a.featuredImage).length
        const missingImages = data.articles.filter((a: any) => !a.featuredImage).length
        console.log(`Articles with featuredImage: ${withImages}, without: ${missingImages}`)
      }
      
      // Store raw article data for debugging
      setRawArticles(data.articles || [])
      
      // Check for featuredImage in each article
      if (data.articles && data.articles.length > 0) {
        console.log('Image data in articles:')
        data.articles.forEach((article: any, index: number) => {
          console.log(`Article ${index + 1} full data:`, article);
          console.log(`Article ${index + 1} image:`, {
            id: article.id,
            title: article.title,
            hasImage: !!article.featuredImage,
            imageType: article.featuredImage ? typeof article.featuredImage : 'none',
            imageValue: article.featuredImage,
            originalObject: JSON.stringify(article).substring(0, 150) + '...'
          })
        })
      }
      
      // Process articles to normalize their structure - SIMPLIFIED APPROACH
      const articlesData = data.articles ? data.articles.map((article: any) => {
        // Helper function to normalize author IDs from different formats
        const normalizeAuthorId = (author: any): string => {
          if (!author) return '';
          
          // If it's already a string
          if (typeof author === 'string') {
            // Remove surrounding quotes that may have been added during serialization
            const cleaned = author.replace(/^"|"$/g, '');
            console.log(`Normalized string author: "${author}" -> "${cleaned}"`);
            return cleaned;
          }
          
          // If it's an ObjectId in MongoDB extended JSON format
          if (typeof author === 'object' && author.$oid) {
            console.log(`Normalized $oid author: ${JSON.stringify(author)} -> "${author.$oid}"`);
            return author.$oid;
          }
          
          // If it's an object with an id property
          if (typeof author === 'object' && author.id) {
            const cleaned = typeof author.id === 'string' ? author.id.replace(/^"|"$/g, '') : String(author.id);
            console.log(`Normalized object.id author: ${JSON.stringify(author)} -> "${cleaned}"`);
            return cleaned;
          }
          
          // Fallback - try to convert to string
          try {
            const str = String(author);
            const cleaned = str.replace(/^"|"$/g, '');
            console.log(`Normalized fallback author: ${str} -> "${cleaned}"`);
            return cleaned;
          } catch (e) {
            console.log(`Failed to normalize author:`, author);
            return '';
          }
        };
        
        // Helper function to normalize image URL
        const normalizeImage = (img: any): string | null => {
          if (!img) {
            console.log('No image found for article:', article.title);
            return null;
          }
          
          if (typeof img === 'string') {
            console.log('Image is string format:', img);
            
            // Handle media paths directly
            if (img.includes('/api/media/file/')) {
              const filename = img.split('/api/media/file/')[1];
              console.log('Extracted filename from API path:', filename);
              return img; // Return the original path, we'll handle it in the UI
            }
            
            return img;
          }
          
          if (typeof img === 'object') {
            console.log('Image is object format:', JSON.stringify(img));
            if (img.url) {
              console.log('Image has URL property:', img.url);
              return img.url;
            }
            if (img.filename) {
              console.log('Image has filename property:', img.filename);
              return '/media/' + img.filename;
            }
            console.log('Image is object but has no URL or filename:', img);
          }
          
          console.log('Unknown image format:', img);
          return null;
        };
        
        // Create a normalized article with consistent author field
        const normalizedArticle: Article = {
          id: article.id || article._id?.toString?.() || undefined,
          _id: article._id,
          title: article.title || 'Untitled',
          status: article.status,
          createdAt: article.createdAt || new Date().toISOString(),
          content: article.content,
          updatedAt: article.updatedAt,
          slug: article.slug,
          views: article.views || 0,
          // Use the original featuredImage value
          featuredImage: article.featuredImage || article.image || null,
          teacherReview: article.teacherReview || [],
          author: normalizeAuthorId(article.author) // Consistently normalize the author field
        }
        
        // Add debugging
        console.log(`Normalized article "${article.title}":`, {
          id: normalizedArticle.id,
          hasImage: !!normalizedArticle.featuredImage,
          imageValue: normalizedArticle.featuredImage
        });
        
        return normalizedArticle;
      }) : []
      
      console.log('Normalized Articles Count:', articlesData.length);
      console.log('Normalized Articles by Status:', {
        published: articlesData.filter((a: Article) => a.status === 'published').length,
        'pending-review': articlesData.filter((a: Article) => a.status === 'pending-review').length,
        draft: articlesData.filter((a: Article) => a.status === 'draft').length
      });
      
      // Debug logging
      const apiDebugInfo = data.debug ? JSON.stringify(data.debug, null, 2) : '';
      const debugInfo = `
        === API Debug Info ===
        ${apiDebugInfo}
        
        === Processed Articles ===
        Total articles: ${articlesData.length}
        Published: ${articlesData.filter((a: Article) => a.status === 'published').length}
        Pending: ${articlesData.filter((a: Article) => a.status === 'pending-review').length}
        Draft: ${articlesData.filter((a: Article) => a.status === 'draft').length}
        
        Articles: ${JSON.stringify(articlesData.map((a: Article) => ({
          id: a.id || (a._id && typeof a._id === 'string' ? a._id : 
              a._id && typeof a._id === 'object' && a._id.$oid ? a._id.$oid : 'unknown'),
          title: a.title,
          status: a.status,
          author: a.author // Include normalized author ID
        })), null, 2)}
      `
      console.log(debugInfo)
      setDebug(debugInfo)

      setArticles(articlesData)
      setFilteredArticles(articlesData)
      setIsLoading(false)
    } catch (err) {
      console.error('Error fetching articles:', err)
      setError('Failed to load articles. Please try again.')
      setIsLoading(false)
    }
  }

  // Apply search and filters
  useEffect(() => {
    if (articles.length > 0) {
      console.log('Original Articles Count:', articles.length);
      console.log('Search Term:', searchTerm);
      console.log('Status Filter:', statusFilter);
      
      // First, let's double-check that all articles have valid status values
      const articlesWithStatus = articles.map(article => {
        if (!article.status) {
          console.warn('Article missing status:', article.title);
          return { ...article, status: 'draft' as const }; // Type assertion
        }
        return article;
      });
      
      let result = [...articlesWithStatus]
      
      // Log article statuses for debugging
      console.log('Article Statuses:', result.map(a => a.status));
      
      // Apply search filter
      if (searchTerm.trim() !== '') {
        const searchLower = searchTerm.toLowerCase()
        result = result.filter(article => 
          article.title?.toLowerCase().includes(searchLower)
        )
        console.log('After Search Filter:', result.length);
      }
      
      // Apply status filter
      if (statusFilter !== 'all') {
        // More robust status comparison to handle potential mismatches
        result = result.filter(article => {
          const articleStatus = String(article.status).toLowerCase();
          const filterStatus = statusFilter.toLowerCase();
          
          // Exact match
          if (articleStatus === filterStatus) return true;
          
          // Check for semantic matches
          if (filterStatus === 'published' && articleStatus.includes('publish')) return true;
          if (filterStatus === 'pending-review' && 
              (articleStatus.includes('pending') || articleStatus.includes('review'))) return true;
          if (filterStatus === 'draft' && articleStatus.includes('draft')) return true;
          
          return false;
        });
        console.log('After Status Filter:', result.length);
      }
      
      console.log('Final Filtered Articles Count:', result.length);
      console.log('Filtered Articles:', result.map(a => ({title: a.title, status: a.status})));
      
      setFilteredArticles(result)
    }
  }, [articles, searchTerm, statusFilter])

  // Initial fetch on component mount
  useEffect(() => {
    fetchArticles()
  }, [])

  // Add an effect to refresh data when the component gains focus
  useEffect(() => {
    // Function to handle when the window regains focus
    const handleFocus = () => {
      console.log('Window focused, refreshing articles...')
      fetchArticles()
    }

    // Add event listener for focus
    window.addEventListener('focus', handleFocus)

    // Cleanup
    return () => {
      window.removeEventListener('focus', handleFocus)
    }
  }, [])

  const handleDelete = async (id: string) => {
    if (!id) {
      console.error('Cannot delete: Article has no ID')
      alert('Cannot delete: Article has no ID')
      return
    }

    setDeleteId(id)
    setIsDeleting(true)

    try {
      const response = await fetch(`/api/articles/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to delete article: ${response.status} ${errorText}`)
      }

      // Remove the deleted article from the state
      setArticles(articles.filter((article) => article.id !== id))
    } catch (err) {
      console.error('Error deleting article:', err)
      setError('Failed to delete article. Please try again.')
    } finally {
      setIsDeleting(false)
      setDeleteId(null)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center min-h-[50vh]">
          <div className="flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <div>جاري تحميل المقالات...</div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <h1 className="text-2xl font-bold">مقالاتي</h1>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => fetchArticles()}
              className="ml-2"
              title="تحديث المقالات"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          </div>
          <Button asChild>
            <a href="/dashboard/my-articles/new">
              <PlusCircle className="ml-2 h-4 w-4" />
              مقال جديد
            </a>
          </Button>
        </div>

        {/* Search and filter controls */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-grow">
            <Input
              type="text"
              placeholder="بحث عن مقالات بالعنوان..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
              dir="rtl"
            />
            <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
          </div>
          <div className="flex-shrink-0">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              dir="rtl"
            >
              <option value="all">كل الحالات</option>
              <option value="published">منشور</option>
              <option value="pending-review">قيد المراجعة</option>
              <option value="draft">مسودة</option>
            </select>
          </div>
        </div>

        {/* Debug info - only shown in development */}
        {process.env.NODE_ENV === 'development' && debug && (
          <div className="mb-4 p-4 bg-gray-100 text-xs overflow-auto max-h-40 rounded">
            <pre>{debug}</pre>
          </div>
        )}
        
        {/* Raw data debug section (development only) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setShowRawData(!showRawData)}
              className="mb-2"
            >
              {showRawData ? "إخفاء البيانات الخام" : "عرض البيانات الخام"} ({rawArticles.length} مقال)
            </Button>
            
            {showRawData && rawArticles.length > 0 && (
              <div className="bg-gray-100 p-4 rounded overflow-auto max-h-96 text-xs">
                <h3 className="font-bold mb-2">البيانات الخام من API:</h3>
                {rawArticles.map((article, index) => {
                  // Helper function to normalize author for debugging display
                  const normalizeAuthorId = (author: any): string => {
                    if (!author) return 'undefined';
                    
                    if (typeof author === 'string') {
                      return `${author} (cleaned: ${author.replace(/^"|"$/g, '')})`;
                    }
                    
                    if (typeof author === 'object' && author.$oid) {
                      return `$oid: ${author.$oid}`;
                    }
                    
                    if (typeof author === 'object' && author.id) {
                      return `object.id: ${author.id}`;
                    }
                    
                    return String(author);
                  };
                  
                  return (
                    <div key={index} className="mb-4 border-b pb-2">
                      <div><strong>Index:</strong> {index}</div>
                      <div><strong>ID:</strong> {article.id || 'undefined'}</div>
                      <div><strong>_ID:</strong> {article._id ? (typeof article._id === 'string' ? article._id : JSON.stringify(article._id)) : 'undefined'}</div>
                      <div><strong>Title:</strong> {article.title}</div>
                      <div><strong>Status:</strong> {article.status}</div>
                      <div className="bg-yellow-50 p-1 rounded">
                        <div><strong>Original Author:</strong> {typeof article.author === 'string' 
                          ? article.author 
                          : typeof article.author === 'object' 
                            ? JSON.stringify(article.author)
                            : 'undefined'}</div>
                        <div><strong>Normalized Author:</strong> {normalizeAuthorId(article.author)}</div>
                      </div>
                      <div><strong>Created:</strong> {article.createdAt}</div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}

        {/* Add additional debug info right before the card */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-4 p-4 bg-gray-100 text-xs overflow-auto max-h-40 rounded">
            <h3 className="font-bold">معلومات التصفية:</h3>
            <div>المقالات الأصلية: {articles.length}</div>
            <div>المقالات المصفاة: {filteredArticles.length}</div>
            <div>مصطلح البحث: "{searchTerm}"</div>
            <div>تصفية الحالة: {statusFilter}</div>
          </div>
        )}
        
        <Card>
          <CardHeader>
            <CardTitle>كل المقالات ({filteredArticles.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الصورة</TableHead>
                  <TableHead>العنوان</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>المشاهدات</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredArticles.length > 0 ? (
                  filteredArticles.map((article, index) => (
                    <TableRow key={article.id || `article-${index}`}>
                      <TableCell>
                        <div className="h-16 w-24 relative overflow-hidden rounded">
                          <Image
                            src={(() => {
                              // If featuredImage is a string and contains api/media/file
                              if (article.featuredImage && typeof article.featuredImage === 'string') {
                                console.log('Using direct image path for:', article.title, article.featuredImage);
                                return article.featuredImage;
                              }
                              // Fallback
                              else {
                                return 'https://images.unsplash.com/photo-1510442650500-93217e634e4c?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
                              }
                            })()}
                            alt={article.title || 'صورة المقال'}
                            fill
                            className="object-cover"
                            sizes="96px"
                            onError={(e) => {
                              // Try alternative URL if the primary one fails
                              const imgElement = e.currentTarget as HTMLImageElement;
                              
                              // Check if we're already using the fallback to prevent loops
                              if (imgElement.src.includes('placeholder')) return;
                              
                              console.log('Image load error for:', article.title, imgElement.src);
                              
                              if (imgElement.src.includes('/api/media/file/')) {
                                console.log('API path failed, trying media path');
                                const filename = imgElement.src.split('/api/media/file/')[1];
                                imgElement.src = `/media/${filename}`;
                              } else if (imgElement.src.includes('/media/')) {
                                console.log('Media path failed, using placeholder');
                                imgElement.src = '/placeholder-article.png';
                              } else {
                                imgElement.src = '/placeholder-article.png';
                              }
                            }}
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        {article.title}
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            article.status === 'published'
                              ? 'bg-green-100 text-green-800'
                              : article.status === 'pending-review'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-blue-100 text-blue-800'
                          }`}
                        >
                          {article.status === 'published'
                            ? 'منشور'
                            : article.status === 'pending-review'
                              ? 'قيد المراجعة'
                              : 'مسودة'}
                        </span>
                      </TableCell>
                      <TableCell>{new Date(article.createdAt).toLocaleDateString('ar-EG')}</TableCell>
                      <TableCell>{article.views || 0}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" asChild>
                            <a
                              href={(() => {
                                const articleId = getArticleId(article)
                                return articleId ? `/articles/${articleId}` : '#'
                              })()}
                              target="_blank"
                              onClick={(e) => {
                                const articleId = getArticleId(article)
                                if (!articleId) {
                                  e.preventDefault()
                                  console.error('Cannot preview: Article has no valid ID', article)
                                  alert('لا يمكن المعاينة: المقال ليس له معرف صالح')
                                }
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </a>
                          </Button>
                          <Button variant="outline" size="sm" asChild>
                            <a
                              href={(() => {
                                const articleId = getArticleId(article)
                                return articleId ? `/dashboard/my-articles/edit/${articleId}` : '#'
                              })()}
                              onClick={(e) => {
                                const articleId = getArticleId(article)
                                if (!articleId) {
                                  e.preventDefault()
                                  console.error('Cannot edit: Article has no valid ID', article)
                                  alert('لا يمكن التعديل: المقال ليس له معرف صالح')
                                }
                              }}
                            >
                              <Pencil className="h-4 w-4" />
                            </a>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-500"
                            onClick={() => {
                              const articleId = getArticleId(article)
                              if (articleId) {
                                handleDelete(articleId)
                              } else {
                                console.error('Cannot delete: Article has no valid ID', article)
                                alert('لا يمكن الحذف: المقال ليس له معرف صالح')
                              }
                            }}
                            disabled={isDeleting && deleteId === getArticleId(article)}
                          >
                            {isDeleting && deleteId === getArticleId(article) ? (
                              <div className="h-4 w-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      {searchTerm || statusFilter !== 'all' ? 'لا توجد مقالات مطابقة' : 'لا توجد مقالات'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
