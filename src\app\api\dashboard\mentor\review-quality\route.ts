'use server'

import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import jwt from 'jsonwebtoken'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const payload = await getPayload({ config })

    // Verify the token
    const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

    // Get the user ID from the token
    const userId = typeof decoded === 'object' ? decoded.id : null

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Use direct MongoDB access to get mentor reviews
    try {
      const db = payload.db.connection.db
      if (db) {
        const mentorReviewsCollection = db.collection('mentorReviews')

        const mentorReviews = await mentorReviewsCollection
          .find({
            mentor: new ObjectId(userId),
          })
          .toArray()

        // Calculate the average review quality based on mentor reviews
        // Convert evaluationResult to numeric rating: approve = 10, reject = 0
        let totalRating = 0
        let reviewCount = 0

        mentorReviews.forEach((review: any) => {
          if (review.evaluationResult) {
            let rating = 0
            if (review.evaluationResult === 'approve') {
              rating = 10 // Excellent rating for approval
            } else if (review.evaluationResult === 'reject') {
              rating = 0 // Poor rating for rejection
            }

            if (rating >= 0) {
              totalRating += rating
              reviewCount++
            }
          }
        })

        const reviewQuality = reviewCount > 0 ? Math.round((totalRating / reviewCount) * 10) : 0

        return NextResponse.json({ reviewQuality })
      }
    } catch (mongoError) {
      console.error('Direct MongoDB query failed:', mongoError)
    }

    // Fallback: return 0 if no reviews found
    return NextResponse.json({ reviewQuality: 0 })
  } catch (error) {
    console.error('Error fetching review quality:', error)
    return NextResponse.json({ error: 'Failed to fetch review quality' }, { status: 500 })
  }
}
