'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useNotifications } from './NotificationContext'

export interface PointsActivity {
  id: string
  userId: string
  userRole: string
  activityType:
    | 'post_approval'
    | 'user_approval'
    | 'mentor_feedback'
    | 'post_creation'
    | 'teacher_review'
    | 'article_rating'
    | 'article_review'
    | 'student_approval'
    | 'profile_approval'
  points: number
  description: string
  date: string
  isRequired?: boolean
}

interface DailyGoal {
  type: string
  required: number
  completed: number
  points: number
}

interface WeeklyGoal {
  type: string
  required: number
  completed: number
}

interface PointsContextType {
  points: number
  activities: PointsActivity[]
  dailyGoals: {
    teacher: DailyGoal[]
    mentor: DailyGoal[]
    student: DailyGoal[]
  }
  weeklyGoals: {
    teacher: WeeklyGoal[]
    mentor: WeeklyGoal[]
    student: WeeklyGoal[]
  }
  addPoints: (
    amount: number,
    activityType: PointsActivity['activityType'],
    description: string,
  ) => void
  deductPoints: (
    amount: number,
    activityType: PointsActivity['activityType'],
    description: string,
  ) => void
  getPointsForPeriod: (startDate: Date, endDate: Date) => number
  getActivitiesForPeriod: (startDate: Date, endDate: Date) => PointsActivity[]
  getRank: () => string
  getNextRankPoints: () => number
  getTasksCompleted: () => { daily: boolean; weekly: boolean }
  getDailyGoalProgress: (role: string) => number
  getWeeklyGoalProgress: (role: string) => number
}

const PointsContext = createContext<PointsContextType | undefined>(undefined)

export function usePoints() {
  const context = useContext(PointsContext)
  if (context === undefined) {
    throw new Error('usePoints must be used within a PointsProvider')
  }
  return context
}

interface PointsProviderProps {
  children: ReactNode
  userRole?: string
  userId?: string
}

export function PointsProvider({ children, userRole = '', userId = '' }: PointsProviderProps) {
  const [points, setPoints] = useState(0)
  const [activities, setActivities] = useState<PointsActivity[]>([])
  const { addNotification } = useNotifications()

  // Initialize daily goals
  const [dailyGoals, setDailyGoals] = useState<{
    teacher: DailyGoal[]
    mentor: DailyGoal[]
    student: DailyGoal[]
  }>({
    teacher: [
      { type: 'article_review', required: 3, completed: 0, points: 5 },
      { type: 'student_approval', required: 2, completed: 0, points: 10 },
    ],
    mentor: [
      { type: 'post_creation', required: 1, completed: 0, points: 20 },
      { type: 'teacher_review', required: 2, completed: 0, points: 15 },
    ],
    student: [
      { type: 'article_rating', required: 1, completed: 0, points: 0 }, // Points vary by rating
    ],
  })

  // Initialize weekly goals
  const [weeklyGoals, setWeeklyGoals] = useState<{
    teacher: WeeklyGoal[]
    mentor: WeeklyGoal[]
    student: WeeklyGoal[]
  }>({
    teacher: [{ type: 'daily_completions', required: 5, completed: 0 }],
    mentor: [{ type: 'daily_completions', required: 3, completed: 0 }],
    student: [{ type: 'good_ratings', required: 2, completed: 0 }],
  })

  // Load points and activities from localStorage on mount
  useEffect(() => {
    if (!userId) return

    const storedPoints = localStorage.getItem(`points-${userId}`)
    const storedActivities = localStorage.getItem(`points-activities-${userId}`)

    if (storedPoints) {
      try {
        setPoints(JSON.parse(storedPoints))
      } catch (error) {
        console.error('Error parsing stored points:', error)
      }
    }

    if (storedActivities) {
      try {
        setActivities(JSON.parse(storedActivities))
      } catch (error) {
        console.error('Error parsing stored activities:', error)
      }
    }
  }, [userId])

  // Save points and activities to localStorage whenever they change
  useEffect(() => {
    if (!userId) return

    localStorage.setItem(`points-${userId}`, JSON.stringify(points))

    if (activities.length > 0) {
      localStorage.setItem(`points-activities-${userId}`, JSON.stringify(activities))
    }
  }, [points, activities, userId])

  // Initialize with mock data if empty
  useEffect(() => {
    if (!userRole || !userId || activities.length > 0) return

    const mockActivities: PointsActivity[] = []
    let initialPoints = 0

    // Generate mock activities based on user role
    if (userRole === 'teacher') {
      // Post approvals
      for (let i = 0; i < 5; i++) {
        const pointsEarned = i < 3 ? 5 : 2 // First 3 are required (5 points), extras are 2 points
        mockActivities.push({
          id: `post-approval-${i + 1}`,
          userId,
          userRole,
          activityType: 'post_approval',
          points: pointsEarned,
          description: `Approved article "${['Climate Change', 'Local Sports', 'School Events', 'Technology', 'Environment'][i % 5]}"`,
          date: new Date(Date.now() - i * 3600000).toISOString(), // Hours ago
        })
        initialPoints += pointsEarned
      }

      // User approvals
      for (let i = 0; i < 3; i++) {
        const pointsEarned = i < 2 ? 10 : 2 // First 2 are required (10 points), extras are 2 points
        mockActivities.push({
          id: `user-approval-${i + 1}`,
          userId,
          userRole,
          activityType: 'user_approval',
          points: pointsEarned,
          description: `Approved student "${['John Smith', 'Sarah Johnson', 'Michael Brown'][i]}"`,
          date: new Date(Date.now() - i * 7200000).toISOString(), // Hours ago
        })
        initialPoints += pointsEarned
      }

      // Mentor feedback
      mockActivities.push({
        id: 'mentor-feedback-1',
        userId,
        userRole,
        activityType: 'mentor_feedback',
        points: 8,
        description: 'Received positive feedback from mentor',
        date: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      })
      initialPoints += 8

      mockActivities.push({
        id: 'mentor-feedback-2',
        userId,
        userRole,
        activityType: 'mentor_feedback',
        points: -5,
        description: 'Received negative feedback from mentor',
        date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
      })
      initialPoints -= 5
    } else if (userRole === 'mentor') {
      // Post creation
      for (let i = 0; i < 3; i++) {
        mockActivities.push({
          id: `post-creation-${i + 1}`,
          userId,
          userRole,
          activityType: 'post_creation',
          points: 20,
          description: `Created news post "${['School Updates', 'Upcoming Events', 'Student Achievements'][i]}"`,
          date: new Date(Date.now() - i * 86400000).toISOString(), // Days ago
        })
        initialPoints += 20
      }

      // Teacher reviews
      for (let i = 0; i < 4; i++) {
        mockActivities.push({
          id: `teacher-review-${i + 1}`,
          userId,
          userRole,
          activityType: 'teacher_review',
          points: 15,
          description: `Reviewed teacher "${['Robert Johnson', 'Sarah Williams', 'David Miller', 'Emily Wilson'][i]}"`,
          date: new Date(Date.now() - i * 43200000).toISOString(), // Half days ago
        })
        initialPoints += 15
      }
    } else if (userRole === 'student') {
      // Article ratings
      const ratings = [3, 4, 5, 2, 4]
      const pointsPerRating = [5, 10, 20, 30, 50]

      for (let i = 0; i < ratings.length; i++) {
        const rating = ratings[i]
        const pointsEarned = pointsPerRating[rating - 1]

        mockActivities.push({
          id: `article-rating-${i + 1}`,
          userId,
          userRole,
          activityType: 'article_rating',
          points: pointsEarned,
          description: `Article "${['Climate Change', 'Local Sports', 'School Events', 'Technology', 'Environment'][i]}" rated ${rating} stars`,
          date: new Date(Date.now() - i * 86400000 * 2).toISOString(), // 2 days ago each
        })
        initialPoints += pointsEarned
      }
    }

    if (mockActivities.length > 0) {
      setActivities(mockActivities)
      setPoints(initialPoints)
    }
  }, [userRole, userId, activities.length])

  const addPoints = (
    amount: number,
    activityType: PointsActivity['activityType'],
    description: string,
  ) => {
    if (!userId || !userRole) return

    const newActivity: PointsActivity = {
      id: Date.now().toString(),
      userId,
      userRole,
      activityType,
      points: amount,
      description,
      date: new Date().toISOString(),
    }

    setActivities((prev) => [newActivity, ...prev])
    setPoints((prev) => prev + amount)

    // Add notification
    addNotification({
      title: 'Points Earned',
      message: `You earned ${amount} points: ${description}`,
      type: 'success',
      link: `/${userRole}-dashboard/my-activities`,
    })
  }

  const deductPoints = (
    amount: number,
    activityType: PointsActivity['activityType'],
    description: string,
  ) => {
    if (!userId || !userRole) return

    const newActivity: PointsActivity = {
      id: Date.now().toString(),
      userId,
      userRole,
      activityType,
      points: -amount,
      description,
      date: new Date().toISOString(),
    }

    setActivities((prev) => [newActivity, ...prev])
    setPoints((prev) => prev - amount)

    // Add notification
    addNotification({
      title: 'Points Deducted',
      message: `You lost ${amount} points: ${description}`,
      type: 'warning',
      link: `/${userRole}-dashboard/my-activities`,
    })
  }

  const getPointsForPeriod = (startDate: Date, endDate: Date) => {
    return activities
      .filter((activity) => {
        const activityDate = new Date(activity.date)
        return activityDate >= startDate && activityDate <= endDate
      })
      .reduce((total, activity) => total + activity.points, 0)
  }

  const getActivitiesForPeriod = (startDate: Date, endDate: Date) => {
    return activities.filter((activity) => {
      const activityDate = new Date(activity.date)
      return activityDate >= startDate && activityDate <= endDate
    })
  }

  const getRank = () => {
    if (userRole === 'teacher') {
      if (points >= 1000) return 'Master Teacher'
      if (points >= 500) return 'Senior Teacher'
      if (points >= 200) return 'Experienced Teacher'
      if (points >= 100) return 'Teacher'
      return 'Novice Teacher'
    } else if (userRole === 'mentor') {
      if (points >= 1000) return 'Master Mentor'
      if (points >= 500) return 'Senior Mentor'
      if (points >= 200) return 'Experienced Mentor'
      if (points >= 100) return 'Mentor'
      return 'Novice Mentor'
    } else if (userRole === 'student') {
      if (points >= 1000) return 'Master Reporter'
      if (points >= 500) return 'Senior Reporter'
      if (points >= 200) return 'Experienced Reporter'
      if (points >= 100) return 'Reporter'
      return 'Novice Reporter'
    }
    return 'Unranked'
  }

  const getNextRankPoints = () => {
    const currentRank = getRank()

    if (
      currentRank === 'Master Teacher' ||
      currentRank === 'Master Mentor' ||
      currentRank === 'Master Reporter'
    ) {
      return points // Already at max rank
    }

    if (currentRank.includes('Senior')) return 1000
    if (currentRank.includes('Experienced')) return 500
    if (!currentRank.includes('Novice')) return 200
    return 100
  }

  const getTasksCompleted = () => {
    // Get today's activities
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const todayActivities = getActivitiesForPeriod(today, tomorrow)

    // Get this week's activities
    const startOfWeek = new Date(today)
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay())
    const endOfWeek = new Date(startOfWeek)
    endOfWeek.setDate(endOfWeek.getDate() + 7)

    const weekActivities = getActivitiesForPeriod(startOfWeek, endOfWeek)

    // Check daily tasks based on role
    let dailyCompleted = false
    let weeklyCompleted = false

    if (userRole === 'teacher') {
      // Daily: 3 post approvals and 2 user approvals
      const postApprovals = todayActivities.filter((a) => a.activityType === 'post_approval').length
      const userApprovals = todayActivities.filter((a) => a.activityType === 'user_approval').length

      dailyCompleted = postApprovals >= 3 && userApprovals >= 2

      // Weekly: At least 5 days of completed daily tasks
      const dailyTasksByDate = new Map<string, { posts: number; users: number }>()

      weekActivities.forEach((activity) => {
        const date = new Date(activity.date).toDateString()

        if (!dailyTasksByDate.has(date)) {
          dailyTasksByDate.set(date, { posts: 0, users: 0 })
        }

        const dateStats = dailyTasksByDate.get(date)!

        if (activity.activityType === 'post_approval') {
          dateStats.posts++
        } else if (activity.activityType === 'user_approval') {
          dateStats.users++
        }
      })

      const daysCompleted = Array.from(dailyTasksByDate.values()).filter(
        (stats) => stats.posts >= 3 && stats.users >= 2,
      ).length

      weeklyCompleted = daysCompleted >= 5
    } else if (userRole === 'mentor') {
      // Daily: 1 post creation or 2 teacher reviews
      const postCreations = todayActivities.filter((a) => a.activityType === 'post_creation').length
      const teacherReviews = todayActivities.filter(
        (a) => a.activityType === 'teacher_review',
      ).length

      dailyCompleted = postCreations >= 1 || teacherReviews >= 2

      // Weekly: At least 3 days of completed daily tasks
      const dailyTasksByDate = new Map<string, { posts: number; reviews: number }>()

      weekActivities.forEach((activity) => {
        const date = new Date(activity.date).toDateString()

        if (!dailyTasksByDate.has(date)) {
          dailyTasksByDate.set(date, { posts: 0, reviews: 0 })
        }

        const dateStats = dailyTasksByDate.get(date)!

        if (activity.activityType === 'post_creation') {
          dateStats.posts++
        } else if (activity.activityType === 'teacher_review') {
          dateStats.reviews++
        }
      })

      const daysCompleted = Array.from(dailyTasksByDate.values()).filter(
        (stats) => stats.posts >= 1 || stats.reviews >= 2,
      ).length

      weeklyCompleted = daysCompleted >= 3
    } else if (userRole === 'student') {
      // Daily: Submit at least one article or get a rating of 3+ stars
      const hasNewArticle = todayActivities.some(
        (a) => a.activityType === 'article_rating' && a.description.includes('submitted'),
      )

      const hasGoodRating = todayActivities.some(
        (a) =>
          a.activityType === 'article_rating' &&
          (a.description.includes('3 stars') ||
            a.description.includes('4 stars') ||
            a.description.includes('5 stars')),
      )

      dailyCompleted = hasNewArticle || hasGoodRating

      // Weekly: At least 2 articles with 3+ stars
      const goodRatings = weekActivities.filter(
        (a) =>
          a.activityType === 'article_rating' &&
          (a.description.includes('3 stars') ||
            a.description.includes('4 stars') ||
            a.description.includes('5 stars')),
      ).length

      weeklyCompleted = goodRatings >= 2
    }

    return { daily: dailyCompleted, weekly: weeklyCompleted }
  }

  // Update daily goals based on activities
  useEffect(() => {
    if (!userRole) return

    // Get today's activities
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const todayActivities = getActivitiesForPeriod(today, tomorrow)

    // Update daily goals based on role
    if (userRole === 'teacher') {
      const articleReviews = todayActivities.filter(
        (a) => a.activityType === 'article_review' || a.activityType === 'post_approval',
      ).length

      const studentApprovals = todayActivities.filter(
        (a) => a.activityType === 'student_approval' || a.activityType === 'user_approval',
      ).length

      setDailyGoals((prev) => ({
        ...prev,
        teacher: [
          { ...prev.teacher[0], completed: articleReviews },
          { ...prev.teacher[1], completed: studentApprovals },
        ],
      }))
    } else if (userRole === 'mentor') {
      const postCreations = todayActivities.filter((a) => a.activityType === 'post_creation').length
      const teacherReviews = todayActivities.filter(
        (a) => a.activityType === 'teacher_review',
      ).length

      setDailyGoals((prev) => ({
        ...prev,
        mentor: [
          { ...prev.mentor[0], completed: postCreations },
          { ...prev.mentor[1], completed: teacherReviews },
        ],
      }))
    } else if (userRole === 'student') {
      const articleRatings = todayActivities.filter(
        (a) => a.activityType === 'article_rating',
      ).length

      setDailyGoals((prev) => ({
        ...prev,
        student: [{ ...prev.student[0], completed: articleRatings }],
      }))
    }
  }, [activities, userRole])

  // Update weekly goals
  useEffect(() => {
    if (!userRole) return

    // Get this week's activities
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const startOfWeek = new Date(today)
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay())
    const endOfWeek = new Date(startOfWeek)
    endOfWeek.setDate(endOfWeek.getDate() + 7)

    const weekActivities = getActivitiesForPeriod(startOfWeek, endOfWeek)

    // Update weekly goals based on role
    if (userRole === 'teacher') {
      // Count days where daily goals were met
      const dailyTasksByDate = new Map<string, { reviews: number; approvals: number }>()

      weekActivities.forEach((activity) => {
        const date = new Date(activity.date).toDateString()

        if (!dailyTasksByDate.has(date)) {
          dailyTasksByDate.set(date, { reviews: 0, approvals: 0 })
        }

        const dateStats = dailyTasksByDate.get(date)!

        if (
          activity.activityType === 'article_review' ||
          activity.activityType === 'post_approval'
        ) {
          dateStats.reviews++
        } else if (
          activity.activityType === 'student_approval' ||
          activity.activityType === 'user_approval'
        ) {
          dateStats.approvals++
        }
      })

      const daysCompleted = Array.from(dailyTasksByDate.values()).filter(
        (stats) => stats.reviews >= 3 && stats.approvals >= 2,
      ).length

      setWeeklyGoals((prev) => ({
        ...prev,
        teacher: [{ ...prev.teacher[0], completed: daysCompleted }],
      }))
    } else if (userRole === 'mentor') {
      // Count days where daily goals were met
      const dailyTasksByDate = new Map<string, { posts: number; reviews: number }>()

      weekActivities.forEach((activity) => {
        const date = new Date(activity.date).toDateString()

        if (!dailyTasksByDate.has(date)) {
          dailyTasksByDate.set(date, { posts: 0, reviews: 0 })
        }

        const dateStats = dailyTasksByDate.get(date)!

        if (activity.activityType === 'post_creation') {
          dateStats.posts++
        } else if (activity.activityType === 'teacher_review') {
          dateStats.reviews++
        }
      })

      const daysCompleted = Array.from(dailyTasksByDate.values()).filter(
        (stats) => stats.posts >= 1 || stats.reviews >= 2,
      ).length

      setWeeklyGoals((prev) => ({
        ...prev,
        mentor: [{ ...prev.mentor[0], completed: daysCompleted }],
      }))
    } else if (userRole === 'student') {
      // Count good ratings (3+ stars)
      const goodRatings = weekActivities.filter(
        (a) => a.activityType === 'article_rating' && a.points >= 20,
      ).length

      setWeeklyGoals((prev) => ({
        ...prev,
        student: [{ ...prev.student[0], completed: goodRatings }],
      }))
    }
  }, [activities, userRole])

  // Calculate daily goal progress
  const getDailyGoalProgress = (role: string) => {
    if (!role) return 0

    const goals = dailyGoals[role as keyof typeof dailyGoals]
    if (!goals || goals.length === 0) return 0

    const totalRequired = goals.reduce((sum, goal) => sum + goal.required, 0)
    const totalCompleted = goals.reduce(
      (sum, goal) => sum + Math.min(goal.completed, goal.required),
      0,
    )

    return Math.round((totalCompleted / totalRequired) * 100)
  }

  // Calculate weekly goal progress
  const getWeeklyGoalProgress = (role: string) => {
    if (!role) return 0

    const goals = weeklyGoals[role as keyof typeof weeklyGoals]
    if (!goals || goals.length === 0) return 0

    const totalRequired = goals.reduce((sum, goal) => sum + goal.required, 0)
    const totalCompleted = goals.reduce(
      (sum, goal) => sum + Math.min(goal.completed, goal.required),
      0,
    )

    return Math.round((totalCompleted / totalRequired) * 100)
  }

  return (
    <PointsContext.Provider
      value={{
        points,
        activities,
        dailyGoals,
        weeklyGoals,
        addPoints,
        deductPoints,
        getPointsForPeriod,
        getActivitiesForPeriod,
        getRank,
        getNextRankPoints,
        getTasksCompleted,
        getDailyGoalProgress,
        getWeeklyGoalProgress,
      }}
    >
      {children}
    </PointsContext.Provider>
  )
}
