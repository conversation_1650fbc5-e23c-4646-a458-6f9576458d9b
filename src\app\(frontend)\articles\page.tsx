import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { PageLayout } from '@/components/PageLayout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { DeconstructedCard } from '@/components/DeconstructedCard'
import {
  isMediaPath,
  isMediaPathError,
  logMediaPathError,
  safeMediaQuery,
  safePayloadResponse,
} from '@/lib/media-utils'
import { connectToDatabase } from '@/lib/mongodb'
import { getImageUrl, getImageAlt } from '@/utils/imageUtils'

import '@/styles/deconstructed-card.css'
import { ImageSource } from '@/types'

export default async function ArticlesPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // Get search parameters - await them to fix the Next.js warning
  const searchParamsObj = await Promise.resolve(searchParams || {})
  const search = typeof searchParamsObj.search === 'string' ? searchParamsObj.search : ''
  const sort = typeof searchParamsObj.sort === 'string' ? searchParamsObj.sort : 'latest'
  const page = typeof searchParamsObj.page === 'string' ? parseInt(searchParamsObj.page, 10) : 1
  const limit = 12 // Number of articles per page (3 rows of 4)

  // Build the query
  const query: Record<string, any> = {
    status: {
      equals: 'published',
    },
  }

  // Add search term if provided
  if (search) {
    query.title = {
      like: search,
    }
  }

  // Determine sort order
  let sortField = '-createdAt' // Default: latest first
  if (sort === 'oldest') {
    sortField = 'createdAt' // Oldest first
  } else if (sort === 'a-z') {
    sortField = 'title' // Alphabetical A-Z
  } else if (sort === 'z-a') {
    sortField = '-title' // Alphabetical Z-A
  }

  // Apply safe media query to prevent ObjectId casting errors
  const safeQuery = safeMediaQuery(query)

  // Initialize articles with empty values
  let articles = {
    docs: [],
    totalDocs: 0,
    limit,
    totalPages: 0,
    page,
    pagingCounter: 1,
    hasPrevPage: false,
    hasNextPage: false,
    prevPage: null,
    nextPage: null,
  }

  // Skip Payload CMS for articles since we know it has issues with media paths
  // Go directly to MongoDB
  try {
    console.log('Using direct MongoDB query for articles page')
    const { db } = await connectToDatabase()

    // Create a MongoDB query that matches the Payload query
    const mongoQuery: any = { status: 'published' }
    if (search) {
      mongoQuery.title = { $regex: search, $options: 'i' }
    }

    // Determine MongoDB sort order
    const mongoSort: any = {}
    if (sortField === '-createdAt') mongoSort.createdAt = -1
    else if (sortField === 'createdAt') mongoSort.createdAt = 1
    else if (sortField === 'title') mongoSort.title = 1
    else if (sortField === '-title') mongoSort.title = -1

    console.log('MongoDB query:', JSON.stringify(mongoQuery))
    console.log('MongoDB sort:', JSON.stringify(mongoSort))

    // Execute the query - get all articles if there are issues
    let mongoArticles: string | any[] = []
    let totalDocs = 0

    try {
      // First try with the specific query
      mongoArticles = await db
        .collection('articles')
        .find(mongoQuery)
        .sort(mongoSort)
        .skip((page - 1) * limit)
        .limit(limit)
        .toArray()

      totalDocs = await db.collection('articles').countDocuments(mongoQuery)

      console.log(`Found ${mongoArticles.length} articles with specific query`)

      // If no articles found, try a more general query
      if (mongoArticles.length === 0) {
        console.log('No articles found with specific query, trying general query')
        mongoArticles = await db
          .collection('articles')
          .find({})
          .sort(mongoSort)
          .limit(limit)
          .toArray()

        totalDocs = await db.collection('articles').countDocuments({})
        console.log(`Found ${mongoArticles.length} articles with general query`)
      }
    } catch (queryError) {
      console.error('Error executing MongoDB query:', queryError)
      // Try one last attempt with a completely empty query
      try {
        mongoArticles = await db.collection('articles').find({}).limit(limit).toArray()
        totalDocs = mongoArticles.length
        console.log(`Found ${mongoArticles.length} articles with fallback query`)
      } catch (fallbackError) {
        console.error('Error with fallback query:', fallbackError)
      }
    }

    // Format the response to match Payload's format
    articles = {
      docs: mongoArticles,
      totalDocs,
      limit,
      totalPages: Math.ceil(totalDocs / limit),
      page,
      pagingCounter: (page - 1) * limit + 1,
      hasPrevPage: page > 1,
      hasNextPage: page * limit < totalDocs,
      prevPage: page > 1 ? page - 1 : null,
      nextPage: page * limit < totalDocs ? page + 1 : null,
    }
  } catch (mongoError) {
    console.error('Error with MongoDB query:', mongoError)
    // Return empty results instead of throwing an error
    articles = {
      docs: [],
      totalDocs: 0,
      limit,
      totalPages: 0,
      page,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
    }
  }

  return (
    <PageLayout
      title="مقالات الطلاب"
      subtitle="اكتشف المقالات المثيرة للتفكير التي كتبها الصحفيون الطلاب"
      bgImage="bg-[url(https://images.unsplash.com/photo-1509827908497-9d239de281bc?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)]"
    >
      {/* Search and Filters */}
      <div className="bg-card shadow-sm py-6 px-4 border-b">
        <div className="container mx-auto">
          <form
            action="/articles"
            method="get"
            className="flex flex-col md:flex-row justify-between items-center gap-4"
          >
            <div className="w-full md:w-1/2">
              <Input
                type="text"
                name="search"
                placeholder="البحث في المقالات..."
                defaultValue={search}
                className="w-full"
              />
            </div>
            <div className="flex gap-4 w-full md:w-auto">
              <select
                name="sort"
                className="p-2 border border-input rounded bg-background"
                defaultValue={sort}
              >
                <option value="latest">ترتيب حسب: الأحدث</option>
                <option value="oldest">ترتيب حسب: الأقدم</option>
                <option value="a-z">ترتيب حسب: أ-ي</option>
                <option value="z-a">ترتيب حسب: ي-أ</option>
              </select>
              <Button type="submit" className="w-full md:w-auto">
                بحث
              </Button>
            </div>
          </form>
        </div>
      </div>

      {/* Articles List */}
      <div className="py-12 px-4 bg-muted/30">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {articles.docs.map(
              (article: {
                id: string
                title: string
                author: { school: { name: any } }
                featuredImage: ImageSource
                createdAt: string | number | Date
                slug: any
              }) => {
                // Skip articles with media path IDs
                if (article.id && typeof article.id === 'string' && isMediaPath(article.id)) {
                  console.log('Skipping article with media path ID:', article.id)
                  return null
                }

                // Generate a safe ID for articles without a valid ID
                let safeId = article.id

                // If the ID is missing or invalid, generate one from the title
                if (!safeId || typeof safeId !== 'string' || safeId.trim() === '') {
                  safeId = article.title
                    ? article.title
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/(^-|-$)/g, '')
                    : 'article-' + Math.random().toString(36).substring(2, 10)
                  console.log('Generated ID for article without valid ID:', safeId)
                }

                // Get school name
                const schoolName =
                  typeof article.author === 'object' &&
                  article.author.school &&
                  typeof article.author.school === 'object'
                    ? article.author.school.name
                    : 'Unknown'

                // Log the raw featuredImage for debugging
                console.log('Raw featuredImage:', article.featuredImage)

                // Handle different types of featuredImage
                let imageToUse = article.featuredImage

                // If it's a media path string, use it directly
                if (
                  article.featuredImage &&
                  typeof article.featuredImage === 'string' &&
                  isMediaPath(article.featuredImage)
                ) {
                  console.log(
                    'Article has media path as featuredImage, using it directly:',
                    article.featuredImage,
                  )
                  imageToUse = article.featuredImage
                }

                // If it's a MongoDB ID string, try to construct a media URL
                if (
                  article.featuredImage &&
                  typeof article.featuredImage === 'string' &&
                  /^[0-9a-fA-F]{24}$/.test(article.featuredImage) &&
                  !isMediaPath(article.featuredImage)
                ) {
                  console.log('Article has MongoDB ID as featuredImage, constructing URL')
                  imageToUse = { url: `${article.featuredImage}` }
                }

                // Get featured image using the utility function, ensuring it's a valid image object
                const featuredImage = getImageUrl(
                  imageToUse, // Pass the processed featuredImage - getImageUrl will handle all cases
                  'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80',
                )

                // Log the image URL for debugging
                console.log('Card image URL:', featuredImage)

                // Format date for display
                const formattedDate = article.createdAt
                  ? new Date(article.createdAt).toLocaleDateString()
                  : 'No date'

                // Use slug if available, otherwise use ID, but ensure it's not a media path
                // If no valid ID is available, generate a stable one based on the article title or content

                if (
                  !safeId &&
                  article.id &&
                  typeof article.id === 'string' &&
                  !isMediaPath(article.id)
                ) {
                  safeId = article.id
                }

                // If still no valid ID, generate one from the title or use a fallback
                if (!safeId) {
                  // Generate a stable ID from the title if available
                  if (article.title) {
                    // Create a slug from the title
                    safeId = article.title
                      .toLowerCase()
                      .replace(/[^\w\s-]/g, '') // Remove special chars
                      .replace(/\s+/g, '-') // Replace spaces with hyphens
                      .substring(0, 50) // Limit length
                  } else {
                    // Last resort: use a random ID but prefix it to identify it
                    safeId = `article-${Math.random().toString(36).substring(2, 9)}`
                  }

                  console.log('Generated ID for article without valid ID:', safeId)
                }

                return (
                  <DeconstructedCard
                    key={safeId}
                    id={safeId}
                    title={article.title || 'مقال بدون عنوان'}
                    category={schoolName}
                    date={formattedDate}
                    imageUrl={featuredImage}
                    type="article"
                    color="hsl(var(--primary))"
                  />
                )
              },
            )}
          </div>

          {/* Pagination */}
          {articles.totalPages > 1 && (
            <div className="mt-12 flex justify-center">
              <div className="flex flex-wrap gap-2 justify-center">
                {Array.from({ length: articles.totalPages }, (_, i) => {
                  // Build the query string with current search parameters
                  const pageNum = i + 1
                  const queryParams = new URLSearchParams()
                  if (search) queryParams.set('search', search)
                  if (sort !== 'latest') queryParams.set('sort', sort)
                  queryParams.set('page', pageNum.toString())

                  return (
                    <Link
                      key={i}
                      href={`/articles?${queryParams.toString()}`}
                      className={`px-4 py-2 rounded ${
                        articles.page === pageNum
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted text-muted-foreground hover:bg-muted/80'
                      }`}
                    >
                      {pageNum}
                    </Link>
                  )
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* CTA for Students */}
      {user &&
        (typeof user.role === 'object'
          ? user.role?.slug === 'student'
          : user.role === 'student') && (
          <div className="bg-primary/10 py-16 px-4">
            <div className="container mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4">هل أنت مستعد لمشاركة صوتك؟</h2>
              <p className="text-xl mb-6">قدم مقالك الخاص واحصل على تعليقات من المعلمين</p>
              <Button asChild size="lg" className="px-8">
                <Link href="/articles/create">اكتب مقالاً</Link>
              </Button>
            </div>
          </div>
        )}
    </PageLayout>
  )
}
