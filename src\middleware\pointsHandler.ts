import { Payload } from 'payload'
import { addUserPoints, updateSchoolPoints } from '@/utils/points'

/**
 * Award points to a user and update related statistics
 */
export const awardPoints = async ({
  payload,
  userId,
  type,
  points,
  description,
  reference = null,
  createActivity = true,
  updateSchool = true,
}: {
  payload: Payload
  userId: string
  type:
    | 'article_created'
    | 'article_published'
    | 'review_submitted'
    | 'achievement_earned'
    | 'rating_received'
    | 'article_views'
    | 'news_views'
    | 'other'
  points: number
  description: string
  reference?: any
  createActivity?: boolean
  updateSchool?: boolean
}): Promise<{
  success: boolean
  totalPoints: number
  message: string
}> => {
  try {
    // Get the user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })

    if (!user) {
      return {
        success: false,
        totalPoints: 0,
        message: 'User not found',
      }
    }

    // Get user's school ID
    const schoolId = typeof user.school === 'object' ? user.school?.id : user.school

    // Add points to the user
    const newTotalPoints = await addUserPoints({
      payload,
      userId,
      type,
      points,
      description,
      reference,
      createActivity,
      schoolId,
    })

    // Update school points if requested - use incremental update for better performance
    if (updateSchool && schoolId) {
      try {
        await updateSchoolPoints({
          payload,
          schoolId,
          incrementalUpdate: true,
          pointsChange: points,
        })
      } catch (schoolUpdateError) {
        console.error('Failed to update school points:', schoolUpdateError)
        // Continue even if school points update fails
      }
    }

    return {
      success: true,
      totalPoints: newTotalPoints,
      message: `تم إضافة ${points} نقطة`,
    }
  } catch (error) {
    console.error('Error awarding points:', error)
    return {
      success: false,
      totalPoints: 0,
      message: 'Error awarding points',
    }
  }
}

/**
 * Calculate points for article reviews based on daily count
 * First 3 reviews per day: 10 points each
 * Additional reviews: 2 points each
 */
export const calculateReviewPoints = async ({
  payload,
  userId,
}: {
  payload: Payload
  userId: string
}): Promise<number> => {
  try {
    // Set today's date to midnight for consistent comparison
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Query for all reviews done by this user today
    const todayReviews = await payload.find({
      collection: 'activities',
      where: {
        userId: {
          equals: userId,
        },
        activityType: {
          equals: 'article-review',
        },
        createdAt: {
          greater_than_equal: today.toISOString(),
        },
      },
      limit: 100, // Reasonable limit for daily reviews
    })

    // Calculate review count
    const reviewCount = todayReviews.totalDocs

    // Apply points logic:
    // - First 3 reviews: 10 points each
    // - Additional reviews: 2 points each
    const baseReviews = Math.min(reviewCount, 3)
    const additionalReviews = Math.max(0, reviewCount - 3)

    const pointsToAward = baseReviews * 10 + additionalReviews * 2

    console.log(
      `User ${userId} has completed ${reviewCount} reviews today. Awarding ${pointsToAward} points.`,
    )

    return pointsToAward
  } catch (error) {
    console.error('Error calculating review points:', error)
    // Return a safe default value if there's an error
    return 10 // Default to 10 points (equivalent to first review)
  }
}

/**
 * Calculate points for student approvals based on daily count
 * First 2 approvals per day: 10 points each
 * Additional approvals: 2 points each
 */
export const calculateApprovalPoints = async ({
  payload,
  userId,
}: {
  payload: Payload
  userId: string
}): Promise<number> => {
  try {
    // Set today's date to midnight for consistent comparison
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Query for all student approvals done by this user today
    const todayApprovals = await payload.find({
      collection: 'activities',
      where: {
        userId: {
          equals: userId,
        },
        activityType: {
          equals: 'student-approval',
        },
        createdAt: {
          greater_than_equal: today.toISOString(),
        },
      },
      limit: 100, // Reasonable limit for daily approvals
    })

    // Calculate approval count
    const approvalCount = todayApprovals.totalDocs

    // Apply points logic:
    // - First 2 approvals: 10 points each
    // - Additional approvals: 2 points each
    const baseApprovals = Math.min(approvalCount, 2)
    const additionalApprovals = Math.max(0, approvalCount - 2)

    const pointsToAward = baseApprovals * 10 + additionalApprovals * 2

    console.log(
      `User ${userId} has completed ${approvalCount} student approvals today. Awarding ${pointsToAward} points.`,
    )

    return pointsToAward
  } catch (error) {
    console.error('Error calculating approval points:', error)
    // Return a safe default value if there's an error
    return 10 // Default to 10 points (equivalent to first approval)
  }
}
