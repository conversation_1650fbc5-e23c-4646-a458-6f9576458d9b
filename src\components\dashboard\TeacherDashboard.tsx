'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Users, FileText, CheckCircle, XCircle, Clock, Award, Star } from 'lucide-react'
import { isMediaPath } from '@/lib/media-utils'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import TeacherTaskBar from './TeacherTaskBar'
import TeacherApprovalCarousel from './TeacherApprovalCarousel'
import SubmissionCalendar from './SubmissionCalendar'
import ReviewHistoryTimeline from './ReviewHistoryTimeline'
import QuickReportPanel from './QuickReportPanel'

interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  role?:
    | {
        id: string
        name: string
        slug: string
      }
    | string
  school?:
    | {
        id: string
        name: string
      }
    | string
}

interface TeacherStats {
  pendingApprovals: number
  pendingReviews: number
  completedReviews: number
  totalStudents: number
  points: number
}

interface Student {
  id: string
  firstName: string
  lastName: string
  email: string
  status: 'active' | 'pending' | 'inactive'
  articlesCount: number
}

interface Article {
  id: string
  title: string
  author: {
    id: string
    firstName: string
    lastName: string
  }
  status: 'draft' | 'pending-review' | 'published'
  createdAt: string
}

interface TeacherDashboardProps {
  user: User | null
}

export default function TeacherDashboard({ user }: TeacherDashboardProps) {
  const router = useRouter()
  const [stats, setStats] = useState<TeacherStats>({
    pendingApprovals: 0,
    pendingReviews: 0,
    completedReviews: 0,
    totalStudents: 0,
    points: 0,
  })
  const [pendingStudents, setPendingStudents] = useState<Student[]>([])
  const [pendingArticles, setPendingArticles] = useState<Article[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  // Set the default tab to 'teacher-dashboard' to show the Manage Submissions tab by default
  const [activeTab, setActiveTab] = useState('teacher-dashboard')
  const [teacherTabValue, setTeacherTabValue] = useState('overview')

  useEffect(() => {
    async function fetchData() {
      try {
        if (!user) return

        // Get the school ID
        const schoolId = typeof user.school === 'object' ? user.school?.id : user.school

        // Fetch teacher dashboard data
        const response = await fetch(`/api/dashboard/teacher?schoolId=${schoolId}`)

        if (!response.ok) {
          throw new Error('Failed to fetch dashboard data')
        }

        const data = await response.json()

        setStats(data.stats)
        setPendingStudents(data.pendingStudents || [])
        setPendingArticles(data.pendingArticles || [])

        // Preload the teacher dashboard components
        try {
          // Fetch pending articles for the approval carousel
          await fetch('/api/dashboard/teacher/pending-articles')

          // Fetch submissions for the calendar
          await fetch('/api/dashboard/teacher/submissions')

          // Fetch review history
          await fetch('/api/dashboard/teacher/review-history')

          // Fetch report stats
          await fetch('/api/dashboard/teacher/report-stats')
        } catch (preloadError) {
          console.error('Error preloading teacher dashboard components:', preloadError)
          // Continue even if preloading fails
        }

        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError('Failed to load dashboard data. Please try again.')
        setIsLoading(false)
      }
    }

    fetchData()
  }, [user])

  const handleApproveStudent = async (studentId: string) => {
    try {
      const response = await fetch(`/api/students/${studentId}/approve`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to approve student')
      }

      // Update the local state
      setPendingStudents(pendingStudents.filter((student) => student.id !== studentId))
      setStats((prev) => ({
        ...prev,
        pendingApprovals: prev.pendingApprovals - 1,
        totalStudents: prev.totalStudents + 1,
        points: prev.points + 5, // Award points for approving a student
      }))
    } catch (err) {
      console.error('Error approving student:', err)
      alert('Failed to approve student. Please try again.')
    }
  }

  const handleRejectStudent = async (studentId: string) => {
    try {
      const response = await fetch(`/api/students/${studentId}/reject`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to reject student')
      }

      // Update the local state
      setPendingStudents(pendingStudents.filter((student) => student.id !== studentId))
      setStats((prev) => ({
        ...prev,
        pendingApprovals: prev.pendingApprovals - 1,
      }))
    } catch (err) {
      console.error('Error rejecting student:', err)
      alert('Failed to reject student. Please try again.')
    }
  }

  const statCards = [
    {
      title: 'موافقات منتظرة',
      value: stats.pendingApprovals,
      icon: <Clock className="w-6 h-6" />,
      color: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-300',
      link: '/dashboard/students?filter=pending',
    },
    {
      title: 'مراجعات منتظرة',
      value: stats.pendingReviews,
      icon: <FileText className="w-6 h-6" />,
      color: 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300',
      link: '/dashboard/articles?filter=pending-review',
    },
    {
      title: 'إجمالي الطلاب',
      value: stats.totalStudents,
      icon: <Users className="w-6 h-6" />,
      color: 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300',
      link: '/dashboard/students',
    },
    {
      title: 'نقاط المعلم',
      value: stats.points,
      icon: <Award className="w-6 h-6" />,
      color: 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300',
      link: '/dashboard/points',
    },
  ]

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-80 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-80 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4">
        {error}
      </div>
    )
  }

  return (
    <>
      <div className="mb-8" dir="rtl">
        <h1 className="text-3xl font-bold dark:text-white">لوحة تحكم المعلم</h1>
        <p className="text-gray-500 dark:text-gray-400">
          مرحباً بك مرة أخرى، {user?.firstName || 'المعلم'}
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6" dir="rtl">
        <TabsList className="mb-4 w-full">
          <TabsTrigger value="dashboard" className="flex-1">
            نظرة عامة على لوحة التحكم
          </TabsTrigger>
          <TabsTrigger value="teacher-dashboard" className="flex-1">
            إدارة تقديمات الطلاب والمراجعات والتقارير
          </TabsTrigger>
        </TabsList>

        {/* Dashboard Overview Tab */}
        <TabsContent value="dashboard" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {statCards.map((stat, index) => (
              <Card key={index} className="p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between">
                  <div className={`p-3 rounded-lg ${stat.color}`}>{stat.icon}</div>
                  <div className="text-right">
                    <h3 className="text-2xl font-bold dark:text-white">{stat.value}</h3>
                    <p className="text-gray-500 dark:text-gray-400">{stat.title}</p>
                  </div>
                </div>
                <div className="mt-4">
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-primary hover:text-primary/80"
                    onClick={() => router.push(stat.link)}
                  >
                    عرض التفاصيل
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          {/* Teacher Task Bar */}
          <TeacherTaskBar />

          {/* Progress */}
          <Card className="p-6 mb-8" dir="rtl">
            <h2 className="text-xl font-bold mb-4 dark:text-white">تقدمك</h2>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">المراجعات المكتملة</span>
                  <span className="text-sm font-medium">{stats.completedReviews} / 50</span>
                </div>
                <Progress value={(stats.completedReviews / 50) * 100} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">النقاط المكتسبة</span>
                  <span className="text-sm font-medium">{stats.points} / 500</span>
                </div>
                <Progress value={(stats.points / 500) * 100} className="h-2" />
              </div>
            </div>
          </Card>

          {/* Tables Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Pending Students */}
            <Card className="p-6" dir="rtl">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold dark:text-white">موافقات الطلاب المنتظرة</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/dashboard/students?filter=pending')}
                >
                  عرض الكل
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الاسم</TableHead>
                    <TableHead>البريد الإلكتروني</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingStudents.length > 0 ? (
                    pendingStudents.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell className="font-medium">
                          {student.firstName} {student.lastName}
                        </TableCell>
                        <TableCell>{student.email}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-green-600 border-green-600 hover:bg-green-50 mx-1"
                              onClick={() => handleApproveStudent(student.id)}
                            >
                              <CheckCircle className="h-4 w-4 ml-1" />
                              موافقة
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 border-red-600 hover:bg-red-50 mx-1"
                              onClick={() => handleRejectStudent(student.id)}
                            >
                              <XCircle className="h-4 w-4 ml-1" />
                              رفض
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-4 text-gray-500">
                        لا توجد موافقات منتظرة
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </Card>

            {/* Pending Articles */}
            <Card className="p-6" dir="rtl">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold dark:text-white">المقالات قيد المراجعة</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/dashboard/articles?filter=pending-review')}
                >
                  عرض الكل
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>العنوان</TableHead>
                    <TableHead>الكاتب</TableHead>
                    <TableHead>التاريخ</TableHead>
                    <TableHead>الإجراء</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingArticles.length > 0 ? (
                    pendingArticles
                      .filter(article => article.id && !isMediaPath(article.id))
                      .map((article) => (
                        <TableRow key={article.id}>
                          <TableCell className="font-medium">
                            {article.title || 'مقال بدون عنوان'}
                          </TableCell>
                          <TableCell>
                            {/* Anonymize student author for privacy */}
                            {article.author
                              ? `طالب_${article.author.id.substring(0, 8)}`
                              : 'طالب مجهول'}
                          </TableCell>
                          <TableCell>
                            {article.createdAt
                              ? new Date(article.createdAt).toLocaleDateString('ar-EG')
                              : 'تاريخ غير معروف'}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/dashboard/articles/review/${article.id}`)}
                            >
                              <Star className="h-4 w-4 ml-1" />
                              مراجعة
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                        لا توجد مقالات قيد المراجعة
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </Card>
          </div>
        </TabsContent>

        {/* Teacher Dashboard Tab - Manage Submissions, Reviews, and Reports */}
        <TabsContent value="teacher-dashboard" className="space-y-6">
          <div className="space-y-8">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-2xl font-bold">لوحة تحكم المعلم</h2>
                <p className="text-gray-500 dark:text-gray-400">
                  إدارة تقديمات الطلاب والمراجعات والتقارير
                </p>
              </div>
            </div>

            <Tabs value={teacherTabValue} onValueChange={setTeacherTabValue} className="space-y-4"
         
            >
              <TabsList className="w-full"
              dir="rtl" 
              >
                <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
                <TabsTrigger value="submissions">التقديمات</TabsTrigger>
                <TabsTrigger value="reviews">المراجعات</TabsTrigger>
                <TabsTrigger value="reports">التقارير</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <TeacherApprovalCarousel />
                  <QuickReportPanel />
                </div>
              </TabsContent>

              <TabsContent value="submissions" className="space-y-4">
                <SubmissionCalendar />
              </TabsContent>

              <TabsContent value="reviews" className="space-y-4">
                <ReviewHistoryTimeline />
              </TabsContent>

              <TabsContent value="reports" className="space-y-4">
                <Card>
                  <CardContent className="pt-6">
                    <QuickReportPanel />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </TabsContent>
      </Tabs>
    </>
  )
}
