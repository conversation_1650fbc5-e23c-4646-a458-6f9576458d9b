import * as jwt from 'jsonwebtoken'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { createAuditLog } from './auditTrail'
import { getPayload } from 'payload'
import payloadConfig from '@/payload.config'
import { verify, sign } from 'jsonwebtoken'
import { isMediaPath } from '@/lib/media-utils'

/**
 * Secure Token Management System
 * Handles JWT creation, validation, and rotation
 */

export interface TokenPayload {
  id: string
  collection: string
  email: string
  role?: string
  school?: string
  exp?: number
  iat?: number
  aud?: string
  iss?: string
}

export interface TokenConfig {
  secret: string
  expiresIn: number // seconds
  audience: string
  issuer: string
  cookieName: string
  cookieOptions: {
    httpOnly: boolean
    secure: boolean
    sameSite: 'strict' | 'lax' | 'none'
    path: string
    maxAge?: number
  }
}

// Default configuration
const DEFAULT_CONFIG: TokenConfig = {
  secret: process.env.PAYLOAD_SECRET || 'secret',
  expiresIn: 7 * 24 * 60 * 60, // 7 days
  audience: 'young-reporter',
  issuer: 'young-reporter-api',
  cookieName: 'payload-token',
  cookieOptions: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
  },
}

/**
 * Creates a JWT token
 * @param payload The token payload
 * @param config Optional configuration overrides
 * @returns The JWT token
 */
export function createToken(
  payload: Omit<TokenPayload, 'exp' | 'iat' | 'aud' | 'iss'>,
  config: Partial<TokenConfig> = {},
): string {
  const effectiveConfig = { ...DEFAULT_CONFIG, ...config }

  // Create the token payload
  const tokenPayload: TokenPayload = {
    ...payload,
    exp: Math.floor(Date.now() / 1000) + effectiveConfig.expiresIn,
    iat: Math.floor(Date.now() / 1000),
    aud: effectiveConfig.audience,
    iss: effectiveConfig.issuer,
  }

  // Sign the token
  return jwt.sign(tokenPayload, effectiveConfig.secret)
}

/**
 * Verifies a JWT token
 * @param token The token to verify
 * @param config Optional configuration overrides
 * @returns The decoded token payload if valid, null otherwise
 */
export function verifyToken(token: string, config: Partial<TokenConfig> = {}): TokenPayload | null {
  const effectiveConfig = { ...DEFAULT_CONFIG, ...config }

  try {
    // Verify the token with flexible audience validation
    // Accept both 'young-reporter' and 'payload-cms' as valid audiences
    try {
      // First try with the configured audience
      const decoded = jwt.verify(token, effectiveConfig.secret, {
        audience: effectiveConfig.audience,
        issuer: effectiveConfig.issuer,
      })

      // Validate the decoded token
      if (!decoded || typeof decoded !== 'object') {
        console.error('Invalid token format:', decoded)
        return null
      }

      // Ensure the token has the required fields
      const payload = decoded as TokenPayload
      if (!payload.id || !payload.collection || !payload.email) {
        console.error('Token missing required fields:', payload)
        return null
      }

      return payload
    } catch (audienceError) {
      // If that fails, try with 'payload-cms' audience
      if (String(audienceError).includes('jwt audience invalid')) {
        console.log('Trying alternative audience validation for backward compatibility')
        const decoded = jwt.verify(token, effectiveConfig.secret, {
          audience: 'payload-cms',
          issuer: 'payload-cms',
        })

        // Validate the decoded token
        if (!decoded || typeof decoded !== 'object') {
          console.error('Invalid token format with alternative audience:', decoded)
          return null
        }

        // Ensure the token has the required fields
        const payload = decoded as TokenPayload
        if (!payload.id || !payload.collection || !payload.email) {
          console.error('Token with alternative audience missing required fields:', payload)
          return null
        }

        return payload
      }
      // If it's not an audience error, rethrow
      throw audienceError
    }
  } catch (error) {
    console.error('Error verifying token:', error)
    return null
  }
}

/**
 * Sets a token as a cookie in the response
 * @param response The NextResponse object
 * @param token The token to set
 * @param config Optional configuration overrides
 * @returns The updated response
 */
export function setTokenCookie(
  response: NextResponse,
  token: string,
  config: Partial<TokenConfig> = {},
): NextResponse {
  const effectiveConfig = { ...DEFAULT_CONFIG, ...config }

  // Set the cookie
  response.cookies.set(effectiveConfig.cookieName, token, {
    ...effectiveConfig.cookieOptions,
    maxAge: effectiveConfig.expiresIn,
  })

  return response
}

/**
 * Gets a token from the request cookies
 * @param req The NextRequest object
 * @param config Optional configuration overrides
 * @returns The token if found, null otherwise
 */
export function getTokenFromRequest(
  req: NextRequest,
  config: Partial<TokenConfig> = {},
): string | null {
  const effectiveConfig = { ...DEFAULT_CONFIG, ...config }

  // Get the token from cookies
  const token = req.cookies.get(effectiveConfig.cookieName)?.value

  return token || null
}

/**
 * Gets a token from the server-side cookies
 * @param config Optional configuration overrides
 * @returns The token if found, null otherwise
 */
export async function getTokenFromServerCookies(
  config: Partial<TokenConfig> = {},
): Promise<string | null> {
  const effectiveConfig = { ...DEFAULT_CONFIG, ...config }

  // Get the cookie store
  const cookieStore = await cookies()

  // Get the token
  const token = cookieStore.get(effectiveConfig.cookieName)?.value

  return token || null
}

/**
 * Clears the token cookie
 * @param response The NextResponse object
 * @param config Optional configuration overrides
 * @returns The updated response
 */
export function clearTokenCookie(
  response: NextResponse,
  config: Partial<TokenConfig> = {},
): NextResponse {
  const effectiveConfig = { ...DEFAULT_CONFIG, ...config }

  // Delete the cookie
  response.cookies.delete(effectiveConfig.cookieName)

  return response
}

/**
 * Rotates a token if it's close to expiration
 * @param token The current token
 * @param thresholdSeconds Threshold in seconds before expiration to rotate
 * @param config Optional configuration overrides
 * @returns A new token if rotation is needed, null otherwise
 */
export function rotateTokenIfNeeded(
  token: string,
  thresholdSeconds: number = 24 * 60 * 60, // 1 day by default
  config: Partial<TokenConfig> = {},
): string | null {
  try {
    // Verify the token
    const decoded = verifyToken(token, config)

    if (!decoded || !decoded.exp) {
      return null
    }

    // Check if the token is close to expiration
    const now = Math.floor(Date.now() / 1000)
    const timeUntilExpiration = decoded.exp - now

    if (timeUntilExpiration <= thresholdSeconds) {
      // Create a new token with the same payload
      const { exp, iat, aud, iss, ...payloadWithoutMetadata } = decoded

      return createToken(payloadWithoutMetadata, config)
    }

    return null
  } catch (error) {
    console.error('Error rotating token:', error)
    return null
  }
}

/**
 * Middleware to verify token and extract user information
 * @param req The NextRequest object
 * @param config Optional configuration overrides
 * @returns Object containing the user ID and role if token is valid
 */
/**
 * Extracts user ID and role from a token
 * @param token The token to extract from
 * @param config Optional configuration overrides
 * @returns Object containing the user ID and role
 */
export async function extractUserFromToken(
  token: string,
  config: Partial<TokenConfig> = {},
): Promise<{
  id: string | null
  role: string | null
  school: string | null
}> {
  try {
    // Import media utilities
    const { isMediaPath } = await import('@/lib/media-utils')

    // Verify the token
    const decoded = verifyToken(token, config)

    if (!decoded) {
      console.warn('Token verification failed or returned null.')
      return {
        id: null,
        role: null,
        school: null,
      }
    }

    // Check if the ID is a media path
    if (typeof decoded.id === 'string' && isMediaPath(decoded.id)) {
      console.error('Media path detected in token ID:', decoded.id)
      return {
        id: null,
        role: null,
        school: null,
      }
    }

    // Validate that the ID is a proper MongoDB ObjectId (if id is present)
    if (decoded.id && typeof decoded.id === 'string' && !/^[0-9a-fA-F]{24}$/.test(decoded.id)) {
      console.error('Invalid ObjectId format in token ID:', decoded.id)
      return {
        id: null,
        role: null,
        school: null,
      }
    }

    // Return extracted user information
    try {
      // Try with default payload import first
      try {
        const payload = await getPayload({ config: payloadConfig })
        const user = await payload.findByID({
          collection: 'users',
          id: decoded.id,
          depth: 1, // Fetch relationships like role and school
        })

        if (user) {
          // Extract role and school from the fetched user object
          const role = typeof user.role === 'object' ? user.role?.slug : user.role
          const school = typeof user.school === 'object' ? user.school?.id : user.school

          return {
            id: user.id || null,
            role: role || null,
            school: school || null,
          }
        }
      } catch (payloadError) {
        console.warn('Failed to get payload with default config:', payloadError)
        // Continue to fallback approach
      }

      // Fallback: Use the stored token data since we couldn't retrieve from database
      console.warn(`Failed to retrieve user data from database. Using token data for user ${decoded.id}.`)
      return {
        id: decoded.id || null,
        role: decoded.role || null,
        school: decoded.school || null,
      }
    } catch (dbError) {
      console.error('Error fetching user from database after token verification:', dbError)
      // Fallback to token data if database fetch fails
      return {
        id: decoded.id || null,
        role: decoded.role || null,
        school: decoded.school || null,
      }
    }
  } catch (error) {
    console.error('Error extracting user from token:', error)
    return {
      id: null,
      role: null,
      school: null,
    }
  }
}

export async function verifyTokenMiddleware(
  req: NextRequest,
  config: Partial<TokenConfig> = {},
): Promise<{
  userId: string | null
  userRole: string | null
  userEmail: string | null
  isValid: boolean
  response?: NextResponse
}> {
  try {
    // Get the token from the request
    const token = getTokenFromRequest(req, config)

    if (!token) {
      return {
        userId: null,
        userRole: null,
        userEmail: null,
        isValid: false,
      }
    }

    // Verify the token
    const decoded = verifyToken(token, config)

    if (!decoded) {
      // Token is invalid, clear it
      const response = NextResponse.next()
      clearTokenCookie(response, config)

      return {
        userId: null,
        userRole: null,
        userEmail: null,
        isValid: false,
        response,
      }
    }

    // Check if token needs rotation
    const newToken = rotateTokenIfNeeded(token, 24 * 60 * 60, config)

    if (newToken) {
      // Token was rotated, set the new token
      const response = NextResponse.next()
      setTokenCookie(response, newToken, config)

      // Log the token rotation
      await createAuditLog(decoded.id, 'token-rotation', 'auth', undefined, {
        email: decoded.email,
      })

      return {
        userId: decoded.id,
        userRole: decoded.role || null,
        userEmail: decoded.email,
        isValid: true,
        response,
      }
    }

    // Token is valid and doesn't need rotation
    return {
      userId: decoded.id,
      userRole: decoded.role || null,
      userEmail: decoded.email,
      isValid: true,
    }
  } catch (error) {
    console.error('Error in token middleware:', error)
    return {
      userId: null,
      userRole: null,
      userEmail: null,
      isValid: false,
    }
  }
}
