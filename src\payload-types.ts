/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    roles: Role;
    schools: School;
    articles: Article;
    news: News;
    statistics: Statistic;
    notifications: Notification;
    achievements: Achievement;
    'user-achievements': UserAchievement;
    activities: Activity;
    systemicIssues: SystemicIssue;
    reports: Report;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    roles: RolesSelect<false> | RolesSelect<true>;
    schools: SchoolsSelect<false> | SchoolsSelect<true>;
    articles: ArticlesSelect<false> | ArticlesSelect<true>;
    news: NewsSelect<false> | NewsSelect<true>;
    statistics: StatisticsSelect<false> | StatisticsSelect<true>;
    notifications: NotificationsSelect<false> | NotificationsSelect<true>;
    achievements: AchievementsSelect<false> | AchievementsSelect<true>;
    'user-achievements': UserAchievementsSelect<false> | UserAchievementsSelect<true>;
    activities: ActivitiesSelect<false> | ActivitiesSelect<true>;
    systemicIssues: SystemicIssuesSelect<false> | SystemicIssuesSelect<true>;
    reports: ReportsSelect<false> | ReportsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  /**
   * First name waiting for approval
   */
  pendingFirstName?: string | null;
  /**
   * Last name waiting for approval
   */
  pendingLastName?: string | null;
  /**
   * Status of name change request
   */
  nameChangeStatus?: ('none' | 'pending' | 'approved' | 'rejected') | null;
  profileImage?: (string | null) | Media;
  /**
   * Profile image waiting for approval
   */
  pendingProfileImage?: (string | null) | Media;
  /**
   * Status of the profile image
   */
  profileImageStatus?: ('none' | 'approved' | 'pending' | 'rejected') | null;
  /**
   * Account status
   */
  status?: ('pending' | 'active' | 'suspended') | null;
  role: string | Role;
  school?: (string | null) | School;
  /**
   * Total points earned by the user
   */
  points?: number | null;
  /**
   * User's current rank among peers with the same role
   */
  rank?: number | null;
  /**
   * History of point-earning activities
   */
  pointsActivities?:
    | {
        type:
          | 'article_created'
          | 'article_published'
          | 'review_submitted'
          | 'achievement_earned'
          | 'rating_received'
          | 'other';
        points: number;
        description: string;
        /**
         * Reference to related entity (article, review, etc.)
         */
        reference?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        timestamp: string;
        id?: string | null;
      }[]
    | null;
  /**
   * The teacher or admin who approved this user
   */
  approvedBy?: (string | null) | User;
  grade?: ('1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | '11' | '12') | null;
  notificationPreferences?: {
    emailNotifications?: boolean | null;
    emailTypes?: ('articleSubmissions' | 'articleReviews' | 'reviewEvaluations' | 'systemAnnouncements')[] | null;
  };
  theme?: {
    darkMode?: boolean | null;
    themeColor?: ('cyan' | 'blue' | 'purple' | 'green' | 'amber' | 'pink') | null;
  };
  preferences?: {
    showCompletedTasks?: boolean | null;
    /**
     * Automatically approve new students from your school
     */
    autoApproveStudents?: boolean | null;
    /**
     * Number of pending reviews before receiving a notification
     */
    reviewNotificationThreshold?: number | null;
  };
  stats?: {
    reviewsRated?: number | null;
    totalMentorRating?: number | null;
    averageMentorRating?: number | null;
    positiveRatings?: number | null;
    negativeRatings?: number | null;
    totalRatings?: number | null;
    approvalRate?: number | null;
  };
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string;
  alt: string;
  caption?: string | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    card?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    hero?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "roles".
 */
export interface Role {
  id: string;
  name: string;
  /**
   * This is used for programmatic access to roles
   */
  slug: string;
  permissions: ('full-access' | 'manage-users' | 'manage-content')[];
  description?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "schools".
 */
export interface School {
  id: string;
  name: string;
  address?: string | null;
  /**
   * City where the school is located
   */
  city?: string | null;
  /**
   * State/province where the school is located
   */
  state?: string | null;
  /**
   * Postal/ZIP code
   */
  zipCode?: string | null;
  /**
   * Country where the school is located
   */
  country?: string | null;
  /**
   * Contact phone number
   */
  phone?: string | null;
  /**
   * Contact email address
   */
  email?: string | null;
  /**
   * School website URL
   */
  website?: string | null;
  /**
   * Description or mission statement of the school
   */
  description?: string | null;
  image?: (string | null) | Media;
  /**
   * Direct URL to the school image (alternative to the media relationship)
   */
  imageUrl?: string | null;
  admin?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "articles".
 */
export interface Article {
  id: string;
  title: string;
  /**
   * Auto-generated from title. Used in the URL.
   */
  slug?: string | null;
  /**
   * Featured image URL for the article (displayed at the top)
   */
  featuredImage?: string | null;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Short summary of the article (max 200 characters)
   */
  summary?: string | null;
  /**
   * Author of the article (student only)
   */
  author?: (string | null) | User;
  /**
   * Anonymized author name
   */
  authorAlias?: string | null;
  status: 'draft' | 'ready-for-review' | 'pending-review' | 'published';
  teacherReview?:
    | {
        reviewer: string | User;
        comment: string;
        rating: number;
        /**
         * Must add a comment before approving
         */
        approved?: boolean | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Number of times this article has been viewed
   */
  views?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "news".
 */
export interface News {
  id: string;
  title: string;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * This will be used in the URL
   */
  slug: string;
  /**
   * Featured image for the news post (displayed at the top)
   */
  featuredImage?: (string | null) | Media;
  publishedAt?: string | null;
  /**
   * Number of times this news item has been viewed
   */
  views?: number | null;
  author: string | User;
  status: 'draft' | 'published';
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "statistics".
 */
export interface Statistic {
  id: string;
  name: string;
  type: 'summary' | 'schoolRanking' | 'mentorRanking' | 'teacherRanking' | 'studentRanking' | 'schoolPoints';
  /**
   * School related to these points (only for school points type)
   */
  school?: (string | null) | School;
  /**
   * This field stores the ranking data
   */
  data:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  lastUpdated?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "notifications".
 */
export interface Notification {
  id: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  /**
   * Optional link to navigate to when notification is clicked
   */
  link?: string | null;
  /**
   * The user who will receive this notification
   */
  user: string | User;
  /**
   * Whether the notification has been read by the user
   */
  read?: boolean | null;
  createdAt: string;
  /**
   * Optional details related to the notification
   */
  details?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "achievements".
 */
export interface Achievement {
  id: string;
  name: string;
  description: string;
  type: 'article' | 'review' | 'engagement' | 'special';
  icon: 'star' | 'trophy' | 'medal' | 'certificate' | 'badge' | 'crown';
  color: 'blue' | 'green' | 'purple' | 'yellow' | 'red' | 'gray';
  /**
   * Criteria for earning this achievement (e.g., {"articlesPublished": 5})
   */
  criteria:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Points awarded for earning this achievement
   */
  points: number;
  createdAt: string;
  updatedAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-achievements".
 */
export interface UserAchievement {
  id: string;
  user: string | User;
  achievement: string | Achievement;
  earnedAt: string;
  /**
   * Additional metadata about how the achievement was earned
   */
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "activities".
 */
export interface Activity {
  id: string;
  userId: string | User;
  targetUserId?: (string | null) | User;
  activityType:
    | 'article-review'
    | 'article-comment'
    | 'student-approval'
    | 'profile-image-approval'
    | 'name-change-approval'
    | 'news-post'
    | 'achievement-earned'
    | 'login';
  details?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  school?: (string | null) | School;
  points?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "systemicIssues".
 */
export interface SystemicIssue {
  id: string;
  title: string;
  description: string;
  type:
    | 'inappropriate-content'
    | 'review-quality'
    | 'student-behavior'
    | 'teacher-behavior'
    | 'technical-issue'
    | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  reporter: string | User;
  school: string | School;
  relatedTeachers?: (string | User)[] | null;
  relatedArticles?: (string | Article)[] | null;
  adminNotes?: string | null;
  resolution?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "reports".
 */
export interface Report {
  id: string;
  articleTitle: string;
  article: string | Article;
  author?: (string | null) | User;
  /**
   * School of the article author (for filtering)
   */
  authorSchool?: (string | null) | School;
  reportedBy: string | User;
  reason: string;
  status: 'pending' | 'reviewed' | 'ignored';
  resolved?: boolean | null;
  resolvedBy?: (string | null) | User;
  resolvedAt?: string | null;
  actions?:
    | {
        action: 'article-removed' | 'content-edited' | 'warning-issued' | 'no-action';
        actionBy: string | User;
        notes?: string | null;
        actionDate: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'media';
        value: string | Media;
      } | null)
    | ({
        relationTo: 'roles';
        value: string | Role;
      } | null)
    | ({
        relationTo: 'schools';
        value: string | School;
      } | null)
    | ({
        relationTo: 'articles';
        value: string | Article;
      } | null)
    | ({
        relationTo: 'news';
        value: string | News;
      } | null)
    | ({
        relationTo: 'statistics';
        value: string | Statistic;
      } | null)
    | ({
        relationTo: 'notifications';
        value: string | Notification;
      } | null)
    | ({
        relationTo: 'achievements';
        value: string | Achievement;
      } | null)
    | ({
        relationTo: 'user-achievements';
        value: string | UserAchievement;
      } | null)
    | ({
        relationTo: 'activities';
        value: string | Activity;
      } | null)
    | ({
        relationTo: 'systemicIssues';
        value: string | SystemicIssue;
      } | null)
    | ({
        relationTo: 'reports';
        value: string | Report;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  firstName?: T;
  lastName?: T;
  pendingFirstName?: T;
  pendingLastName?: T;
  nameChangeStatus?: T;
  profileImage?: T;
  pendingProfileImage?: T;
  profileImageStatus?: T;
  status?: T;
  role?: T;
  school?: T;
  points?: T;
  rank?: T;
  pointsActivities?:
    | T
    | {
        type?: T;
        points?: T;
        description?: T;
        reference?: T;
        timestamp?: T;
        id?: T;
      };
  approvedBy?: T;
  grade?: T;
  notificationPreferences?:
    | T
    | {
        emailNotifications?: T;
        emailTypes?: T;
      };
  theme?:
    | T
    | {
        darkMode?: T;
        themeColor?: T;
      };
  preferences?:
    | T
    | {
        showCompletedTasks?: T;
        autoApproveStudents?: T;
        reviewNotificationThreshold?: T;
      };
  stats?:
    | T
    | {
        reviewsRated?: T;
        totalMentorRating?: T;
        averageMentorRating?: T;
        positiveRatings?: T;
        negativeRatings?: T;
        totalRatings?: T;
        approvalRate?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        card?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        hero?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "roles_select".
 */
export interface RolesSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  permissions?: T;
  description?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "schools_select".
 */
export interface SchoolsSelect<T extends boolean = true> {
  name?: T;
  address?: T;
  city?: T;
  state?: T;
  zipCode?: T;
  country?: T;
  phone?: T;
  email?: T;
  website?: T;
  description?: T;
  image?: T;
  imageUrl?: T;
  admin?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "articles_select".
 */
export interface ArticlesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  featuredImage?: T;
  content?: T;
  summary?: T;
  author?: T;
  authorAlias?: T;
  status?: T;
  teacherReview?:
    | T
    | {
        reviewer?: T;
        comment?: T;
        rating?: T;
        approved?: T;
        id?: T;
      };
  views?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "news_select".
 */
export interface NewsSelect<T extends boolean = true> {
  title?: T;
  content?: T;
  slug?: T;
  featuredImage?: T;
  publishedAt?: T;
  views?: T;
  author?: T;
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "statistics_select".
 */
export interface StatisticsSelect<T extends boolean = true> {
  name?: T;
  type?: T;
  school?: T;
  data?: T;
  lastUpdated?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "notifications_select".
 */
export interface NotificationsSelect<T extends boolean = true> {
  message?: T;
  type?: T;
  link?: T;
  user?: T;
  read?: T;
  createdAt?: T;
  details?: T;
  updatedAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "achievements_select".
 */
export interface AchievementsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  type?: T;
  icon?: T;
  color?: T;
  criteria?: T;
  points?: T;
  createdAt?: T;
  updatedAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-achievements_select".
 */
export interface UserAchievementsSelect<T extends boolean = true> {
  user?: T;
  achievement?: T;
  earnedAt?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "activities_select".
 */
export interface ActivitiesSelect<T extends boolean = true> {
  userId?: T;
  targetUserId?: T;
  activityType?: T;
  details?: T;
  school?: T;
  points?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "systemicIssues_select".
 */
export interface SystemicIssuesSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  type?: T;
  severity?: T;
  status?: T;
  reporter?: T;
  school?: T;
  relatedTeachers?: T;
  relatedArticles?: T;
  adminNotes?: T;
  resolution?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "reports_select".
 */
export interface ReportsSelect<T extends boolean = true> {
  articleTitle?: T;
  article?: T;
  author?: T;
  authorSchool?: T;
  reportedBy?: T;
  reason?: T;
  status?: T;
  resolved?: T;
  resolvedBy?: T;
  resolvedAt?: T;
  actions?:
    | T
    | {
        action?: T;
        actionBy?: T;
        notes?: T;
        actionDate?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}