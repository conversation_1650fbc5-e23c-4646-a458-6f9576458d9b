import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function POST(req: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token and get user info
      const { userId, role } = await extractUserFromToken(token)

      console.log('User ID:', userId, 'Role:', role)

      // Get the payload instance to verify the role
      const payload = await getPayload({ config })

      // Get the user from Payload CMS
      let userRole = role

      if (userId) {
        try {
          const user = await payload.findByID({
            collection: 'users',
            id: userId,
            depth: 1,
          })

          // Check if the user has a role object
          if (user && user.role) {
            if (typeof user.role === 'object' && user.role.slug) {
              userRole = user.role.slug
            } else if (typeof user.role === 'string') {
              // Try to get the role by ID
              try {
                const roleDoc = await payload.findByID({
                  collection: 'roles',
                  id: user.role,
                })
                if (roleDoc && roleDoc.slug) {
                  userRole = roleDoc.slug
                }
              } catch (roleError) {
                console.error('Error fetching role:', roleError)
              }
            }
          }

          console.log('User role from Payload:', userRole)
        } catch (userError) {
          console.error('Error fetching user:', userError)
        }
      }

      // Allow teachers, mentors, school admins, and super admins to access this endpoint
      if (
        userRole !== 'teacher' &&
        userRole !== 'mentor' &&
        userRole !== 'school-admin' &&
        userRole !== 'super-admin'
      ) {
        return NextResponse.json(
          {
            error: 'Forbidden',
            message: 'Only teachers, mentors, and admins can access this endpoint',
            providedRole: userRole,
          },
          { status: 403 },
        )
      }
      const { db } = await connectToDatabase()

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      // Get report data from request
      const data = await req.json()
      const { reason, type } = data

      if (!reason || !type) {
        return NextResponse.json({ error: 'Reason and type are required' }, { status: 400 })
      }

      // Create an activity record for the escalation
      const activity = await payload.create({
        collection: 'activities',
        data: {
          userId,
          activityType: 'escalation',
          details: {
            reason,
            type,
            reportedBy: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
            reportedByRole: 'teacher',
          },
        },
      })

      // Create notifications for school admins and super admins
      try {
        // Find all admins
        const admins = await payload.find({
          collection: 'users',
          where: {
            'role.slug': {
              in: ['school-admin', 'super-admin'],
            },
          },
        })

        // Create notifications for each admin
        for (const admin of admins.docs) {
          await payload.create({
            collection: 'notifications',
            data: {
              userId: admin.id,
              title: 'New Escalation Report',
              message: `A teacher has escalated an issue: ${type}`,
              type: 'escalation',
              date: new Date().toISOString(),
              read: false,
            },
          })
        }
      } catch (notificationError) {
        console.error('Error creating notifications:', notificationError)
        // Continue even if notification creation fails
      }

      return NextResponse.json({
        success: true,
        message: 'Report escalated successfully',
        activityId: activity.id,
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error escalating report:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
