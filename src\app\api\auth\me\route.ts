import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import config from '@/payload.config'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    // Also check for token in Authorization header
    const authHeader = req.headers.get('Authorization')
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null

    // Use token from cookie or header
    const finalToken = token || headerToken

    if (!finalToken) {
      return NextResponse.json({ user: null }, { status: 401 })
    }

    try {
      // Verify the token
      const secret = process.env.PAYLOAD_SECRET || 'secret'

      let decoded
      try {
        console.log('Verifying token...')
        decoded = jwt.verify(finalToken, secret, {
          // Use the correct audience and issuer values
          audience: 'payload-cms',
          issuer: 'payload-cms',
          // Add some leeway for clock skew
          clockTolerance: 60, // 60 seconds
        })
        console.log('Token verified successfully')
      } catch (jwtError) {
        console.error('JWT verification error:', jwtError)
        // Clear the invalid token
        cookieStore.delete('payload-token')

        // Check for specific JWT errors
        const errorMessage = String(jwtError)
        if (errorMessage.includes('jwt expired')) {
          return NextResponse.json(
            {
              user: null,
              error: 'Token expired',
              tokenCleared: true,
              expired: true,
            },
            { status: 401 },
          )
        } else if (errorMessage.includes('invalid signature')) {
          return NextResponse.json(
            {
              user: null,
              error: 'Invalid token signature',
              tokenCleared: true,
            },
            { status: 401 },
          )
        } else if (errorMessage.includes('audience')) {
          return NextResponse.json(
            {
              user: null,
              error: 'Invalid token audience',
              tokenCleared: true,
            },
            { status: 401 },
          )
        }

        return NextResponse.json(
          {
            user: null,
            error: 'Invalid token',
            tokenCleared: true,
          },
          { status: 401 },
        )
      }

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token')
        cookieStore.delete('payload-token')
        return NextResponse.json(
          {
            user: null,
            error: 'Invalid token: no user ID',
            tokenCleared: true,
          },
          { status: 401 },
        )
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the user with relationships populated
      try {
        console.log('Looking up user with ID:', userId)

        // First check if the user exists
        const userExists = await payload.find({
          collection: 'users',
          where: {
            id: {
              equals: userId,
            },
          },
          limit: 1,
        })

        if (userExists.totalDocs === 0) {
          console.log('User not found in database, token may be stale')
          // Clear the invalid token
          cookieStore.delete('payload-token')
          return NextResponse.json(
            {
              user: null,
              error: 'User not found in database',
              tokenCleared: true,
            },
            { status: 401 },
          )
        }

        // User exists, now get full details
        const user = await payload.findByID({
          collection: 'users',
          id: userId,
          depth: 1,
        })

        // Include role information in the response
        const role = typeof user.role === 'object' ? user.role?.slug : user.role

        // Also get the role from the token for comparison
        const tokenRole =
          typeof decoded === 'object' && decoded.role
            ? typeof decoded.role === 'object'
              ? decoded.role?.slug
              : decoded.role
            : null

        // Use the role from the user object if available, otherwise use the role from the token
        const finalRole = role || tokenRole || 'student'

        console.log('User found successfully, role:', finalRole)

        // Add cache-busting headers to prevent caching
        const response = NextResponse.json({
          user,
          role: finalRole,
          isAdmin: finalRole === 'super-admin' || finalRole === 'school-admin',
          isAuthenticated: true,
        })

        // Add cache control headers
        response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
        response.headers.set('Pragma', 'no-cache')
        response.headers.set('Expires', '0')

        return response
      } catch (userError) {
        console.error('Error finding user:', userError)

        // Check if this is a database connection error
        const errorMessage = String(userError)
        if (errorMessage.includes('ETIMEDOUT') || errorMessage.includes('connect failed')) {
          console.log('Database connection error detected')
          return NextResponse.json(
            {
              user: null,
              error: 'Database connection error',
              dbError: true,
            },
            { status: 503 },
          )
        }

        // For other errors, clear the token and return 401
        cookieStore.delete('payload-token')
        return NextResponse.json(
          {
            user: null,
            error: 'User not found or access denied',
            tokenCleared: true,
          },
          { status: 401 },
        )
      }
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)

      // Clear the invalid token
      cookieStore.delete('payload-token')

      return NextResponse.json({ user: null, error: 'Invalid token' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error in /api/auth/me:', error)
    return NextResponse.json({ user: null, error: 'Server error' }, { status: 500 })
  }
}
