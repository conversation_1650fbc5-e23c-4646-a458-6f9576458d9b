import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import config from '@/payload.config'

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Await params before destructuring
    const resolvedParams = await params
    const { id } = resolvedParams
    const payload = await getPayload({
      config,
    })

    // Get the current user from cookies
    const cookieStore = req.cookies
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication token not found' },
        { status: 401 }
      )
    }

    // Get the current user
    const { user } = await payload.auth({ token })

    if (!user) {
      return NextResponse.json(
        { error: 'You must be logged in to update a news post' },
        { status: 401 }
      )
    }

    // Check if user is a mentor
    const isMentor = typeof user.role === 'object'
      ? user.role?.slug === 'mentor'
      : user.role === 'mentor'

    if (!isMentor) {
      return NextResponse.json(
        { error: 'Only mentors can update news posts' },
        { status: 403 }
      )
    }

    // Get the news post
    const newsPost = await payload.findByID({
      collection: 'news',
      id,
    })

    // Check if the user is the author
    const isAuthor = newsPost.author === user.id ||
      (typeof newsPost.author === 'object' && newsPost.author?.id === user.id)

    if (!isAuthor) {
      return NextResponse.json(
        { error: 'You can only update your own news posts' },
        { status: 403 }
      )
    }

    // Check if the request is JSON or form data
    let title, slug, content, status;

    if (req.headers.get('content-type')?.includes('application/json')) {
      // Handle JSON request
      const body = await req.json();
      title = body.title;
      slug = body.slug;
      content = body.content;
      status = body.status;
    } else {
      // Handle form data request
      const formData = await req.formData();
      title = formData.get('title') as string;
      slug = formData.get('slug') as string;
      content = formData.get('content') as string;
      status = formData.get('status') as string;
    }

    // Validate inputs
    if (!title || !slug || !content || !status) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      )
    }

    // Validate status
    if (status !== 'draft' && status !== 'published') {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      )
    }

    // Update the news post
    const updateData = {
      title,
      slug,
      content,
      status,
    }

    // If publishing for the first time, add publishedAt date
    if (status === 'published' && newsPost.status === 'draft' && !newsPost.publishedAt) {
      updateData.publishedAt = new Date().toISOString()
    }

    await payload.update({
      collection: 'news',
      id,
      data: updateData,
    })

    // Redirect to the dashboard news page
    return NextResponse.redirect(new URL('/dashboard/news', req.url))
  } catch (error) {
    console.error('Error updating news post:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred. The slug may already be in use.' },
      { status: 500 }
    )
  }
}
