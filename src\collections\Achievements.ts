import type { CollectionConfig } from 'payload'

export const Achievements: CollectionConfig = {
  slug: 'achievements',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'description', 'type', 'createdAt'],
  },
  access: {
    read: () => true,
    create: ({ req }) => (req.user && req.user.role === 'super-admin' ? true : false),
    update: ({ req }) => (req.user && req.user.role === 'super-admin' ? true : false),
    delete: ({ req }) => (req.user && req.user.role === 'super-admin' ? true : false),
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Article', value: 'article' },
        { label: 'Review', value: 'review' },
        { label: 'Engagement', value: 'engagement' },
        { label: 'Special', value: 'special' },
      ],
    },
    {
      name: 'icon',
      type: 'select',
      required: true,
      options: [
        { label: 'Star', value: 'star' },
        { label: 'Trophy', value: 'trophy' },
        { label: 'Medal', value: 'medal' },
        { label: 'Certificate', value: 'certificate' },
        { label: 'Badge', value: 'badge' },
        { label: 'Crown', value: 'crown' },
      ],
    },
    {
      name: 'color',
      type: 'select',
      required: true,
      options: [
        { label: 'Blue', value: 'blue' },
        { label: 'Green', value: 'green' },
        { label: 'Purple', value: 'purple' },
        { label: 'Yellow', value: 'yellow' },
        { label: 'Red', value: 'red' },
        { label: 'Gray', value: 'gray' },
      ],
    },
    {
      name: 'criteria',
      type: 'json',
      required: true,
      admin: {
        description: 'Criteria for earning this achievement (e.g., {"articlesPublished": 5})',
      },
    },
    {
      name: 'points',
      type: 'number',
      required: true,
      min: 1,
      admin: {
        description: 'Points awarded for earning this achievement',
      },
    },
    {
      name: 'createdAt',
      type: 'date',
      admin: {
        position: 'sidebar',
        date: {
          pickerAppearance: 'dayAndTime',
        },
        readOnly: true,
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        return {
          ...data,
          createdAt: data.createdAt || new Date().toISOString(),
        }
      },
    ],
  },
}
