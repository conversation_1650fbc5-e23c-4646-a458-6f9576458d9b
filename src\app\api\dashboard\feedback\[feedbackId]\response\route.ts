import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

// Helper function to handle Payload CMS feedback updates
async function handlePayloadFeedback(payload: any, feedbackId: string, body: any) {
  try {
    const existingPayloadFeedback = await payload.findByID({
      collection: 'systemicIssues',
      id: feedbackId,
    })
    
    if (!existingPayloadFeedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 })
    }
    
    // Update the feedback in Payload CMS
    await payload.update({
      collection: 'systemicIssues',
      id: feedbackId,
      data: {
        resolution: body.response,
        status: body.status || existingPayloadFeedback.status,
      },
    })
    
    // Get the updated feedback item
    const updatedFeedback = await payload.findByID({
      collection: 'systemicIssues',
      id: feedbackId,
    })
    
    if (!updatedFeedback) {
      return NextResponse.json({ error: 'Failed to retrieve updated feedback' }, { status: 500 })
    }
    
    // Format the response
    const formattedFeedback = {
      id: updatedFeedback.id,
      type: updatedFeedback.type,
      subject: updatedFeedback.title,
      message: updatedFeedback.description,
      status: updatedFeedback.status,
      priority: updatedFeedback.severity,
      schoolId: typeof updatedFeedback.school === 'object' ? updatedFeedback.school?.id : updatedFeedback.school,
      schoolName: typeof updatedFeedback.school === 'object' ? updatedFeedback.school?.name : '',
      submitterId: typeof updatedFeedback.reporter === 'object' ? updatedFeedback.reporter?.id : updatedFeedback.reporter,
      submitterName: typeof updatedFeedback.reporter === 'object' ? 
        `${updatedFeedback.reporter?.firstName || ''} ${updatedFeedback.reporter?.lastName || ''}`.trim() : '',
      submitterRole: typeof updatedFeedback.reporter === 'object' && updatedFeedback.reporter.role ? 
        (typeof updatedFeedback.reporter.role === 'object' ? updatedFeedback.reporter.role.slug : updatedFeedback.reporter.role) : '',
      dateSubmitted: updatedFeedback.createdAt,
      lastUpdated: updatedFeedback.updatedAt,
      response: updatedFeedback.resolution,
    }
    
    return NextResponse.json({ feedback: formattedFeedback })
  } catch (payloadError) {
    console.error('[Feedback API - Response] Error updating feedback in Payload CMS:', payloadError)
    return NextResponse.json({ error: 'Feedback not found' }, { status: 404 })
  }
}

// PATCH endpoint for feedback response updates
export async function PATCH(
  req: NextRequest,
  { params }: { params: { feedbackId: string } }
) {
  try {
    const { feedbackId } = params;
    console.log(`[Dynamic Feedback API - Response] Processing: feedbackId=${feedbackId}`);
    
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      const userId = typeof decoded === 'object' ? decoded.id : null
      
      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })
      
      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })
      
      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      // Extract user role
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role

      // Only specific roles can update feedback
      const canUpdateFeedback = userRole === 'super-admin' || userRole === 'school-admin'
      if (!canUpdateFeedback) {
        return NextResponse.json({ error: 'Forbidden - Insufficient permissions to update feedback' }, { status: 403 })
      }

      // Parse request body
      const body = await req.json()

      // Connect to the database
      const { db } = await connectToDatabase()
      const feedbackCollection = db.collection('feedback')
      
      try {
        // Try to convert feedbackId to ObjectId
        let objectId: ObjectId
        try {
          objectId = new ObjectId(feedbackId)
        } catch (e) {
          // If feedbackId is not a valid ObjectId, try to find it in Payload CMS
          return handlePayloadFeedback(payload, feedbackId, body)
        }
        
        // Check if feedback exists in MongoDB
        const existingFeedback = await feedbackCollection.findOne({ _id: objectId })
        
        if (!existingFeedback) {
          // Try to find it in Payload CMS if not in MongoDB
          return handlePayloadFeedback(payload, feedbackId, body)
        }
        
        // Update fields
        const updateData: Record<string, any> = {
          updatedAt: new Date(),
          lastUpdated: new Date(),
          response: body.response
        }
        
        if (body.status) {
          updateData.status = body.status
        }
        
        // Update the feedback in MongoDB
        const result = await feedbackCollection.updateOne(
          { _id: objectId },
          { $set: updateData }
        )
        
        if (result.modifiedCount === 0) {
          return NextResponse.json({ error: 'Failed to update feedback' }, { status: 500 })
        }
        
        // Get the updated feedback item
        const updatedFeedback = await feedbackCollection.findOne({ _id: objectId })
        
        if (!updatedFeedback) {
          return NextResponse.json({ error: 'Failed to retrieve updated feedback' }, { status: 500 })
        }
        
        // Format the response
        const formattedFeedback = {
          id: updatedFeedback._id.toString(),
          type: updatedFeedback.type || 'issue',
          subject: updatedFeedback.subject || updatedFeedback.title || 'Feedback',
          message: updatedFeedback.message || updatedFeedback.comment || updatedFeedback.description || '',
          status: updatedFeedback.status || 'new',
          priority: updatedFeedback.priority || 'medium',
          schoolId: updatedFeedback.schoolId?.toString() || 'unknown',
          schoolName: updatedFeedback.schoolName || 'Unknown School',
          submitterId: updatedFeedback.submitterId?.toString() || updatedFeedback.userId?.toString() || 'unknown',
          submitterName: updatedFeedback.submitterName || 'Unknown User',
          submitterRole: updatedFeedback.submitterRole || 'unknown',
          dateSubmitted: updatedFeedback.dateSubmitted || updatedFeedback.createdAt || new Date().toISOString(),
          lastUpdated: updatedFeedback.updatedAt?.toISOString() || new Date().toISOString(),
          response: updatedFeedback.response || null,
        }
        
        return NextResponse.json({ feedback: formattedFeedback })
      } catch (dbError) {
        console.error('[Feedback API - Response] Database error:', dbError)
        return NextResponse.json({ error: 'Database error' }, { status: 500 })
      }
    } catch (error) {
      console.error('[Feedback API - Response] Token verification error:', error)
      return NextResponse.json(
        { error: 'Unauthorized', details: error instanceof Error ? error.message : String(error) },
        { status: 401 },
      )
    }
  } catch (error) {
    console.error('[Feedback API - Response] Error updating feedback:', error)
    return NextResponse.json(
      {
        error: 'Internal Server Error',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
} 