'use client'

import { useState, useEffect } from 'react'
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Link as LinkIcon,
  Image,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Quote,
  Code,
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface EnhancedEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  readOnly?: boolean
  className?: string
}

export function EnhancedEditor({
  value,
  onChange,
  placeholder = 'Write something...',
  readOnly = false,
  className = '',
}: EnhancedEditorProps) {
  const [editorValue, setEditorValue] = useState('')
  const [selectionStart, setSelectionStart] = useState(0)
  const [selectionEnd, setSelectionEnd] = useState(0)
  const textareaRef = useState<HTMLTextAreaElement | null>(null)

  // Set initial value
  useEffect(() => {
    setEditorValue(value)
  }, [value])

  // Handle editor change
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const content = e.target.value
    setEditorValue(content)
    onChange(content)

    // Save selection
    setSelectionStart(e.target.selectionStart)
    setSelectionEnd(e.target.selectionEnd)
  }

  // Handle selection change
  const handleSelect = (e: React.SyntheticEvent<HTMLTextAreaElement>) => {
    const target = e.target as HTMLTextAreaElement
    setSelectionStart(target.selectionStart)
    setSelectionEnd(target.selectionEnd)
  }

  // Apply formatting
  const applyFormatting = (prefix: string, suffix: string = prefix) => {
    if (readOnly) return

    const textarea = textareaRef[0]
    if (!textarea) return

    const start = selectionStart
    const end = selectionEnd
    const selectedText = editorValue.substring(start, end)

    // Apply formatting
    const newText =
      editorValue.substring(0, start) + prefix + selectedText + suffix + editorValue.substring(end)

    // Update value
    setEditorValue(newText)
    onChange(newText)

    // Focus and set selection
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + prefix.length, end + prefix.length)
    }, 0)
  }

  // Format handlers
  const handleBold = () => applyFormatting('**', '**')
  const handleItalic = () => applyFormatting('*', '*')
  const handleUnderline = () => applyFormatting('__', '__')
  const handleH1 = () => applyFormatting('# ', '')
  const handleH2 = () => applyFormatting('## ', '')
  const handleBulletList = () => applyFormatting('- ', '')
  const handleNumberedList = () => applyFormatting('1. ', '')
  const handleLink = () => applyFormatting('[', '](url)')
  const handleImage = () => applyFormatting('![alt text](', ')')
  const handleQuote = () => applyFormatting('> ', '')
  const handleCode = () => applyFormatting('`', '`')

  return (
    <div className={`enhanced-editor ${className}`}>
      {/* Toolbar */}
      <div className="flex flex-wrap gap-1 p-2 bg-gray-100 border border-gray-300 rounded-t-md sticky top-0 z-10 shadow-sm">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleBold}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <Bold className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Bold</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleItalic}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <Italic className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Italic</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleUnderline}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <Underline className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Underline</p>
            </TooltipContent>
          </Tooltip>

          <div className="w-px h-6 bg-gray-300 mx-1 self-center"></div>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleH1}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <Heading1 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Heading 1</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleH2}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <Heading2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Heading 2</p>
            </TooltipContent>
          </Tooltip>

          <div className="w-px h-6 bg-gray-300 mx-1 self-center"></div>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleBulletList}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <List className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Bullet List</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleNumberedList}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <ListOrdered className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Numbered List</p>
            </TooltipContent>
          </Tooltip>

          <div className="w-px h-6 bg-gray-300 mx-1 self-center"></div>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleLink}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <LinkIcon className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Insert Link</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleImage}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <Image className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Insert Image</p>
            </TooltipContent>
          </Tooltip>

          <div className="w-px h-6 bg-gray-300 mx-1 self-center"></div>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleQuote}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <Quote className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Quote</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleCode}
                disabled={readOnly}
                className="h-8 w-8"
              >
                <Code className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Code</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Editor */}
      <textarea
        ref={(ref) => (textareaRef[0] = ref)}
        value={editorValue}
        onChange={handleChange}
        onSelect={handleSelect}
        placeholder={placeholder}
        readOnly={readOnly}
        className="w-full min-h-[400px] p-4 border border-gray-300 border-t-0 rounded-b-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-sans text-base leading-relaxed"
        style={{
          resize: 'vertical',
          lineHeight: '1.6',
          color: '#333',
          backgroundColor: '#fff',
          boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)',
        }}
      />
    </div>
  )
}

// Helper function to convert plain text to Lexical format
export function textToLexical(text: string) {
  return {
    root: {
      type: 'root',
      children: [
        {
          type: 'paragraph',
          children: [
            {
              type: 'text',
              text: text || '',
              version: 1,
            },
          ],
          direction: null,
          format: '',
          indent: 0,
          version: 1,
        },
      ],
      direction: null,
      format: '',
      indent: 0,
      version: 1,
    },
  }
}

// Helper function to convert Lexical format to plain text
export function lexicalToText(lexical: any) {
  try {
    if (!lexical || !lexical.root || !lexical.root.children) {
      return ''
    }

    // Extract text from children
    let text = ''
    lexical.root.children.forEach((child: any) => {
      if (child.type === 'paragraph') {
        const paragraphText = child.children.map((textNode: any) => textNode.text || '').join('')
        text += paragraphText + '\n\n'
      }
    })

    return text.trim()
  } catch (error) {
    console.error('Error converting Lexical to text:', error)
    return ''
  }
}
