import 'dotenv/config'

import CopyWebpackPlugin from 'copy-webpack-plugin'

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Other Next.js config options
  reactStrictMode: true,

  // Fix for the EPERM error
  onDemandEntries: {
    // Keep pages in memory for longer
    maxInactiveAge: 60 * 60 * 1000, // 1 hour
    // Have more pages loaded at once
    pagesBufferLength: 5,
  },

  // Image configuration
  images: {
    domains: ['images.unsplash.com', 'placehold.co'],
    unoptimized: process.env.NODE_ENV === 'development',
  },

  // Disable experimental features
  experimental: {
    // Disable telemetry
    disableOptimizedLoading: true,
    optimizeCss: false,
  },
}

export default {
  ...nextConfig,
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.plugins.push(
        new CopyWebpackPlugin({
          patterns: [
            {
              from: 'node_modules/pdfkit/js/data',
              to: '.next/server/vendor-chunks/data',
            },
          ],
        }),
      )
    }

    return config
  },
}
