import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
// Removed MongoDB imports as we will rely on Payload API

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies - still needed for auth check by Payload
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    // Although we don't manually verify the token here,
    // Payload's access control in the collection definition will use it.
    // If no token is present, Payload access control might deny the request.

    const payload = await getPayload({ config })

    // Get all activities using Payload API
    const activitiesResponse = await payload.find({
      collection: 'activities',
      depth: 1, // Populate userId relationship
      sort: '-createdAt', // Sort by creation date descending
      // Add pagination if needed in the future
      // limit: 10,
      // page: 1,
    })

    // Return the documents directly as expected by the frontend structure { activities: [...] }
    // Note: The frontend currently expects activity.user, but the API returns activity.userId (populated).
    // We will adjust the frontend in the next step.
    return NextResponse.json({ activities: activitiesResponse.docs })
  } catch (error: any) {
    console.error('Error fetching activities via Payload:', error)
    // Attempt to return a more specific error message if available
    const errorMessage = error?.message || 'Failed to fetch activities'
    const errorStatus = error?.status || 500
    return NextResponse.json({ error: errorMessage }, { status: errorStatus })
  }
}
