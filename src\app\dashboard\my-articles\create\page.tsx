'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'

export default function CreateArticleRedirectPage() {
  const router = useRouter()
  
  useEffect(() => {
    // Redirect to the new page
    router.push('/dashboard/my-articles/new')
  }, [router])
  
  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <p className="ml-4">Redirecting to new article page...</p>
        </div>
      </div>
    </DashboardLayout>
  )
}
