'use client'

import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, Palette } from 'lucide-react'
import { useState } from 'react'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { useTheme, ThemeColor } from '@/contexts/ThemeContext'

type ColorOption = {
  value: ThemeColor
  label: string
  color: string
}

export default function ThemeSelector() {
  const { darkMode, themeColor, toggleDarkMode, setThemeColor } = useTheme()
  const [open, setOpen] = useState(false)

  // Define the available color themes
  const colorOptions: ColorOption[] = [
    { value: 'cyan', label: 'أزرق سماوي', color: 'rgb(14, 165, 175)' },
    { value: 'blue', label: 'أزرق', color: 'rgb(59, 130, 246)' },
    { value: 'purple', label: 'بنفسجي', color: 'rgb(139, 92, 246)' },
    { value: 'green', label: 'أخضر', color: 'rgb(34, 197, 94)' },
    { value: 'amber', label: 'كهرماني', color: 'rgb(245, 158, 11)' },
    { value: 'pink', label: 'وردي', color: 'rgb(236, 72, 153)' },
  ]

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="rounded-full">
          <Palette className="h-5 w-5" />
          <span className="sr-only">تغيير السمة</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={toggleDarkMode} className="flex items-center justify-between">
          {darkMode ? (
            <>
              <div className="flex items-center">
                <Sun className="ml-2 h-4 w-4" />
                <span>الوضع النهاري</span>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center">
                <Moon className="ml-2 h-4 w-4" />
                <span>الوضع الليلي</span>
              </div>
            </>
          )}
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <div className="px-2 py-1.5 text-sm font-semibold">ألوان السمة</div>
        
        {colorOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => {
              setThemeColor(option.value)
              document.documentElement.setAttribute('data-theme', option.value)
            }}
            className="flex items-center justify-between"
          >
            <div className="flex items-center">
              <Circle
                className="ml-2 h-4 w-4"
                style={{ fill: option.color, stroke: option.color }}
              />
              <span>{option.label}</span>
            </div>
            {themeColor === option.value && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 