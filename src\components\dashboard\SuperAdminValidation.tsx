'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Loader2, CheckCircle, XCircle, AlertTriangle, Download } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

interface ValidationResult {
  status: 'passed' | 'failed' | 'warning'
  message: string
  data?: any
}

interface ValidationResults {
  checks: Record<string, ValidationResult>
  actions: Record<string, ValidationResult>
  summary: {
    totalChecks: number
    passedChecks: number
    totalActions: number
    passedActions: number
  }
}

interface SuperAdminValidationProps {
  checks?: string[]
  actions?: string[]
}

export default function SuperAdminValidation({
  checks = [
    'global activity heatmap',
    'cross-school statistics',
    'system health monitor',
    'role distribution pie chart',
    'raw audit logs',
    'system configurations',
  ],
  actions = ['create/manage schools', 'force publish content', 'export global reports'],
}: SuperAdminValidationProps) {
  const { toast } = useToast()
  const [isValidating, setIsValidating] = useState(false)
  const [results, setResults] = useState<ValidationResults | null>(null)
  const [activeTab, setActiveTab] = useState('checks')

  const runValidation = async () => {
    setIsValidating(true)
    setResults(null)

    try {
      const response = await fetch('/api/dashboard/super-admin/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          checks,
          actions,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to validate super-admin dashboard')
      }

      const data = await response.json()
      setResults(data)

      // Show toast with summary
      const totalPassed = data.summary.passedChecks + data.summary.passedActions
      const total = data.summary.totalChecks + data.summary.totalActions
      const percentPassed = Math.round((totalPassed / total) * 100)

      toast({
        title: 'Validation Complete',
        description: `${totalPassed} of ${total} checks passed (${percentPassed}%)`,
        variant: percentPassed >= 80 ? 'default' : percentPassed >= 50 ? 'warning' : 'destructive',
      })
    } catch (error) {
      console.error('Error validating super-admin dashboard:', error)
      toast({
        title: 'Validation Failed',
        description: error.message || 'An error occurred during validation',
        variant: 'destructive',
      })
    } finally {
      setIsValidating(false)
    }
  }

  const exportResults = () => {
    if (!results) return

    // Create a JSON blob
    const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    // Create a link and click it
    const a = document.createElement('a')
    a.href = url
    a.download = `super-admin-validation-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()

    // Clean up
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: 'Export Complete',
      description: 'Validation results have been exported',
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      default:
        return null
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'passed':
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
            Passed
          </Badge>
        )
      case 'failed':
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
            Failed
          </Badge>
        )
      case 'warning':
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
            Warning
          </Badge>
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Super Admin Dashboard Validation</h2>
          <p className="text-gray-500">
            Validate the presence and functionality of super-admin dashboard features
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={runValidation} disabled={isValidating}>
            {isValidating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Validating...
              </>
            ) : (
              'Run Validation'
            )}
          </Button>
          {results && (
            <Button variant="outline" onClick={exportResults}>
              <Download className="mr-2 h-4 w-4" />
              Export Results
            </Button>
          )}
        </div>
      </div>

      {results && (
        <>
          <Card>
            <CardHeader>
              <CardTitle>Validation Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span>Checks: {results.summary.passedChecks}/{results.summary.totalChecks}</span>
                    <span>{Math.round((results.summary.passedChecks / results.summary.totalChecks) * 100)}%</span>
                  </div>
                  <Progress
                    value={(results.summary.passedChecks / results.summary.totalChecks) * 100}
                    className="h-2"
                  />
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span>Actions: {results.summary.passedActions}/{results.summary.totalActions}</span>
                    <span>{Math.round((results.summary.passedActions / results.summary.totalActions) * 100)}%</span>
                  </div>
                  <Progress
                    value={(results.summary.passedActions / results.summary.totalActions) * 100}
                    className="h-2"
                  />
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span>Overall: {results.summary.passedChecks + results.summary.passedActions}/{results.summary.totalChecks + results.summary.totalActions}</span>
                    <span>{Math.round(((results.summary.passedChecks + results.summary.passedActions) / (results.summary.totalChecks + results.summary.totalActions)) * 100)}%</span>
                  </div>
                  <Progress
                    value={((results.summary.passedChecks + results.summary.passedActions) / (results.summary.totalChecks + results.summary.totalActions)) * 100}
                    className="h-2"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="checks">Feature Checks</TabsTrigger>
              <TabsTrigger value="actions">Action Checks</TabsTrigger>
              <TabsTrigger value="data">Raw Data</TabsTrigger>
            </TabsList>

            <TabsContent value="checks">
              <Card>
                <CardHeader>
                  <CardTitle>Feature Checks</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">Status</TableHead>
                        <TableHead>Feature</TableHead>
                        <TableHead>Message</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {Object.entries(results.checks).map(([feature, result]) => (
                        <TableRow key={feature}>
                          <TableCell>{getStatusIcon(result.status)}</TableCell>
                          <TableCell className="font-medium">
                            {feature.charAt(0).toUpperCase() + feature.slice(1)}
                          </TableCell>
                          <TableCell>{result.message}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="actions">
              <Card>
                <CardHeader>
                  <CardTitle>Action Checks</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">Status</TableHead>
                        <TableHead>Action</TableHead>
                        <TableHead>Message</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {Object.entries(results.actions).map(([action, result]) => (
                        <TableRow key={action}>
                          <TableCell>{getStatusIcon(result.status)}</TableCell>
                          <TableCell className="font-medium">
                            {action.charAt(0).toUpperCase() + action.slice(1)}
                          </TableCell>
                          <TableCell>{result.message}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="data">
              <Card>
                <CardHeader>
                  <CardTitle>Raw Validation Data</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-auto max-h-[500px]">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}

      {!results && !isValidating && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center p-6 text-center">
              <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
              <h3 className="text-lg font-medium">No Validation Results</h3>
              <p className="text-gray-500 mt-2">
                Click the "Run Validation" button to check the super-admin dashboard features
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {isValidating && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center p-6 text-center">
              <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
              <h3 className="text-lg font-medium">Validating Dashboard Features</h3>
              <p className="text-gray-500 mt-2">
                This may take a few moments...
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
