import { toast as toastFunction } from '@/components/ui/use-toast'

type ToastVariant = 'default' | 'destructive'

interface ToastOptions {
  title: string
  description: string
  variant?: ToastVariant
}

export function showToast(options: ToastOptions) {
  return toastFunction({
    title: options.title,
    description: options.description,
    variant: options.variant,
  })
}

export const toast = {
  success: (message: string) => showToast({ 
    title: 'Success', 
    description: message 
  }),
  error: (message: string) => showToast({ 
    title: 'Error', 
    description: message, 
    variant: 'destructive' 
  }),
  info: (message: string) => showToast({ 
    title: 'Info', 
    description: message 
  }),
  warning: (message: string) => showToast({ 
    title: 'Warning', 
    description: message, 
    variant: 'destructive' 
  }),
}
