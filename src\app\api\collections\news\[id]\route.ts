import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { isMediaPath } from '@/lib/media-utils'
import payloadConfig from '@/payload.config'
import { extractUserFromToken } from '@/lib/security'
import { incrementViewCount } from '@/utils/viewUtils'
import jwt from 'jsonwebtoken'
import { ObjectId } from 'mongodb'

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the ID from the URL
    const { id } = await Promise.resolve(params)

    // Check if the ID is a media path
    if (isMediaPath(id)) {
      return NextResponse.json({ error: 'Invalid news ID' }, { status: 400 })
    }

    // Get the payload instance
    const payload = await getPayload({ config: payloadConfig })

    // Get the news item
    const news = await payload.findByID({
      collection: 'news',
      id,
      depth: 2, // Populate relationships
    })

    // Only increment view count if it's a GET request from a client
    // We check the Accept header to determine if it's a browser request
    const acceptHeader = request.headers.get('Accept') || ''
    if (acceptHeader.includes('text/html') || acceptHeader.includes('application/json')) {
      // Increment view count asynchronously
      incrementViewCount('news', id).catch(error => {
        console.error('Error incrementing view count:', error)
      })
    }

    return NextResponse.json(news)
  } catch (error) {
    console.error('Error fetching news item:', error)
    return NextResponse.json({ error: 'Failed to fetch news item' }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the ID from the URL
    const { id } = await Promise.resolve(params)

    // Check if the ID is a media path
    if (isMediaPath(id)) {
      return NextResponse.json({ error: 'Invalid news ID' }, { status: 400 })
    }

    // Get the current user from cookies - await cookies() properly
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Authentication token not found' }, { status: 401 })
    }

    // Get the user using extractUserFromToken
    const userInfo = await extractUserFromToken(token)

    if (!userInfo || !userInfo.id) {
      return NextResponse.json(
        { error: 'You must be logged in to update a news post' },
        { status: 401 },
      )
    }

    // Get the payload instance with config
    const payload = await getPayload({ config: payloadConfig })

    // Fetch the full user details from the database
    const user = await payload.findByID({
      collection: 'users',
      id: userInfo.id,
      depth: 1,
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 })
    }

    // Get the news post with populated author
    const newsPost = await payload.findByID({
      collection: 'news',
      id,
      depth: 1, // Add depth to populate relationships
    })

    // Get the user role
    const userRole = typeof user.role === 'object' ? user.role?.slug : user.role;
    const isSuperAdmin = userRole === 'super-admin';
    const isSchoolAdmin = userRole === 'school-admin';
    const isMentor = userRole === 'mentor';

    const isAuthor =
      newsPost.author === user.id ||
      (typeof newsPost.author === 'object' && newsPost.author?.id === user.id);

    if (!(isSuperAdmin || isSchoolAdmin || isAuthor)) {
      return NextResponse.json(
        { error: 'You do not have permission to update this news post' },
        { status: 403 },
      );
    }

    // Get the request body
    const body = await request.json()
    const { title, content, featuredImage, status, slug: bodySlug } = body // Destructure slug from body as bodySlug

    // Validate inputs
    if (!title || !status) {
      return NextResponse.json({ error: 'Title and status are required' }, { status: 400 })
    }

    // Validate status
    if (status !== 'draft' && status !== 'published') {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 })
    }

    // {{change 1}} Determine the slug for the update based on provided values
    let finalSlug = newsPost.slug // Default to existing slug

    // If a slug is provided in the body and is not an empty string after trimming, prioritize it.
    // Otherwise, if title is provided, generate slug from the title.
    const trimmedBodySlug = typeof bodySlug === 'string' ? bodySlug.trim() : ''
    function slugify(raw = '') {
      return raw
        .toLowerCase()
        .trim()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-+|-+$/g, '')
    }

    // …

    if (trimmedBodySlug) {
      finalSlug = slugify(trimmedBodySlug)
    } else if (title) {
      finalSlug = slugify(title)
    }

    if (!finalSlug && title) {
      finalSlug = slugify(title)
      console.warn('Generated slug was empty, falling back to generating from title.')
    }
    // Update the news post data
    const updateData: any = {
      title,
      slug: finalSlug, // Use the determined slug
      status,
    }

    // Set the author field to the original author to maintain consistency
    // Extract the author ID properly based on its format
    if (typeof newsPost.author === 'object' && newsPost.author?.id) {
      updateData.author = newsPost.author.id
    } else if (typeof newsPost.author === 'string') {
      updateData.author = newsPost.author
    }
    // If author is undefined or null, don't include it in the update

    // Validate that the author is a mentor (re-check for safety)
    const authorId =
      typeof newsPost.author === 'object' && newsPost.author?.id
        ? newsPost.author.id
        : typeof newsPost.author === 'string'
          ? newsPost.author
          : null

    if (!authorId) {
      return NextResponse.json({ error: 'Author ID not found' }, { status: 400 })
    }

    const authorUser = await payload.findByID({
      collection: 'users',
      id: authorId,
      depth: 1,
    })

    if (!authorUser) {
      return NextResponse.json({ error: 'Author not found' }, { status: 400 })
    }

    // Handle content field - ensure it\'s in the exact format Payload CMS expects
    if (content) {
      // If content is already a stringified JSON, parse it
      if (typeof content === 'string') {
        try {
          updateData.content = JSON.parse(content)
        } catch (e) {
          // If parsing fails, create a valid minimal structure
          updateData.content = {
            root: {
              type: 'root',
              children: [
                {
                  type: 'paragraph',
                  children: [{ text: content || '' }],
                  direction: null,
                  format: '',
                  indent: 0,
                  version: 1,
                },
              ],
              direction: null,
              format: '',
              indent: 0,
              version: 1,
            },
          }
        }
      } else if (content.root) {
        // Use the object directly if it has the proper structure
        updateData.content = content
      } else {
        // Create a valid structure with the content as text
        updateData.content = {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [{ text: JSON.stringify(content) || '' }],
                direction: null,
                format: '',
                indent: 0,
                version: 1,
              },
            ],
            direction: null,
            format: '',
            indent: 0,
            version: 1,
          },
        }
      }
    } else {
      // Provide a minimal valid content structure
      updateData.content = {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [{ text: '' }],
              direction: null,
              format: '',
              indent: 0,
              version: 1,
            },
          ],
          direction: null,
          format: '',
          indent: 0,
          version: 1,
        },
      }
    }

    // Handle featuredImage - convert path to ID if necessary
    if (featuredImage !== undefined) {
      // Check for undefined specifically to allow setting to null
      // Check if it's already an ObjectId string
      if (
        featuredImage === null ||
        (typeof featuredImage === 'string' && ObjectId.isValid(featuredImage))
      ) {
        // Allow null or valid ObjectId string
        updateData.featuredImage = featuredImage
      } else if (typeof featuredImage === 'string' && isMediaPath(featuredImage)) {
        // If it's a media path string, try to find the media document
        try {
          // Extract filename from path (simple approach, might need refinement)
          const filename = featuredImage.split('/').pop()
          if (filename) {
            // Find media document by filename
            const mediaDoc = await payload.find({
              collection: 'media',
              where: {
                filename: {
                  equals: filename,
                },
              },
              limit: 1,
            })

            if (mediaDoc.docs.length > 0) {
              updateData.featuredImage = mediaDoc.docs[0].id
            } else {
              console.warn(
                `Media document not found for path: ${featuredImage}. Setting featuredImage to null.`,
              )
              updateData.featuredImage = null // Set to null if media not found
            }
          } else {
            console.warn(
              `Could not extract filename from path: ${featuredImage}. Setting featuredImage to null.`,
            )
            updateData.featuredImage = null
          }
        } catch (mediaError) {
          console.error(`Error finding media document for path: ${featuredImage}`, mediaError)
          updateData.featuredImage = null // Set to null on error
        }
      } else {
        // If it's not a valid ObjectId string or a recognizable media path string,
        // assume it's an invalid value and set to null or handle as an error.
        console.warn(
          `Invalid value provided for featuredImage: ${featuredImage}. Setting featuredImage to null.`,
        )
        updateData.featuredImage = null
      }
    }

    // If publishing for the first time, add publishedAt date
    if (status === 'published' && newsPost.status === 'draft' && !newsPost.publishedAt) {
      updateData.publishedAt = new Date().toISOString()
    }

    // Log the update data for debugging
    console.log('Updating news post with data:', JSON.stringify(updateData, null, 2))

    try {
      // Create a clean copy of the update data to avoid any reference issues
      const cleanUpdateData = JSON.parse(JSON.stringify(updateData))

      // Update news with explicit parameter format
      const updatedNews = await payload.update({
        collection: 'news',
        id,
        data: cleanUpdateData,
      })

      console.log('News post updated successfully:', updatedNews.id)

      return NextResponse.json({
        success: true,
        message: 'News post updated successfully',
        data: updatedNews,
      })
    } catch (updateError) {
      console.error('Failed to update news post:', updateError)
      // Log more details of the error
      if (updateError && typeof updateError === 'object') {
        console.error('Error details:', JSON.stringify(updateError, null, 2))
        const errorObj = updateError as Record<string, any>
        if ('data' in errorObj) {
          console.error('Error data:', JSON.stringify(errorObj.data, null, 2))
          if (errorObj.data && typeof errorObj.data === 'object' && 'errors' in errorObj.data) {
            console.error('Field errors:', JSON.stringify(errorObj.data.errors, null, 2))
          }
        }
      }

      // Enhanced error handling for Payload update errors
      if (updateError && typeof updateError === 'object' && 'data' in updateError) {
        const errorData = updateError.data as any
        if (errorData && errorData.errors) {
          return NextResponse.json(
            {
              error: 'Validation Error',
              message:
                updateError instanceof Error ? updateError.message : 'Unknown validation error',
              fields: errorData.errors,
            },
            { status: 400 },
          )
        }
      }

      // Re-throw to be caught by the outer catch block
      throw updateError
    }
  } catch (error) {
    console.error('Error updating news post:', error)

    // Check for validation errors
    if (error && typeof error === 'object' && 'data' in error) {
      // For Payload validation errors
      const errorData = error.data as any

      if (errorData && errorData.errors) {
        return NextResponse.json(
          {
            error: 'Validation Error',
            details: 'Field validation failed',
            fields: errorData.errors,
          },
          { status: 400 },
        )
      }

      return NextResponse.json(
        {
          error: 'Validation Error',
          details: errorData || 'Invalid field values',
          message: error instanceof Error ? error.message : 'Unknown validation error',
        },
        { status: 400 },
      )
    }

    return NextResponse.json(
      {
        error: 'An unexpected error occurred. The slug may already be in use.',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}
