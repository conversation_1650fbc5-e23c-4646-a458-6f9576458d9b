import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import payloadConfig from '@/payload.config'
import { extractUserFromToken } from '@/lib/security'
import jwt from 'jsonwebtoken'

export async function POST(request: NextRequest) {
  try {
    // Get the current user from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Authentication token not found' }, { status: 401 })
    }

    // Get the user using extractUserFromToken
    const userInfo = await extractUserFromToken(token)

    if (!userInfo || !userInfo.id) {
      return NextResponse.json(
        { error: 'You must be logged in to create a news post' },
        { status: 401 },
      )
    }

    // Get the payload instance with config
    const payload = await getPayload({ config: payloadConfig })

    // Fetch the full user details from the database
    const user = await payload.findByID({
      collection: 'users',
      id: userInfo.id,
      depth: 1,
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 })
    }

    // Get the user role
    const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
    const isSuperAdmin = userRole === 'super-admin'
    const isSchoolAdmin = userRole === 'school-admin'
    const isMentor = userRole === 'mentor'

    // Only mentors and admins can create news
    if (!(isSuperAdmin || isSchoolAdmin || isMentor)) {
      return NextResponse.json(
        { error: 'Only mentors and admins can create news posts' },
        { status: 403 },
      )
    }

    // Get the request body
    const body = await request.json()
    const { title, slug, content, featuredImage, status } = body

    // Validate inputs
    if (!title || !slug) {
      return NextResponse.json({ error: 'Title and slug are required' }, { status: 400 })
    }

    // Validate status
    if (status !== 'draft' && status !== 'published') {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 })
    }

    // Create news data object
    const newsData: any = {
      title,
      slug,
      status,
      author: user.id,
    }

    // Add content if provided
    if (content) {
      newsData.content = content
    }

    // Add featuredImage if provided
    if (featuredImage) {
      // Handle different formats of featuredImage
      if (typeof featuredImage === 'string') {
        // Check if it's a MongoDB ID (used for media relationships)
        if (/^[0-9a-fA-F]{24}$/.test(featuredImage)) {
          newsData.featuredImage = featuredImage
        } else {
          // For URLs or paths, we need to decide how to handle them based on your system
          // For now, we'll just pass it as is and let Payload handle it
          newsData.featuredImage = featuredImage
        }
      } else if (typeof featuredImage === 'object' && featuredImage !== null) {
        // If it's an object with an ID property, use that
        if (featuredImage.id) {
          newsData.featuredImage = featuredImage.id
        } else if (featuredImage.url) {
          // Handle cases where we get a URL instead of an ID
          newsData.featuredImage = featuredImage.url
        }
      }
    }

    // If publishing now, add publishedAt date
    if (status === 'published') {
      newsData.publishedAt = new Date().toISOString()
    }

    try {
      // Create the news post
      const createdNews = await payload.create({
        collection: 'news',
        data: newsData,
      })

      return NextResponse.json({
        success: true,
        message: 'News post created successfully',
        data: createdNews,
      })
    } catch (error) {
      console.error('Error creating news post:', error)
      
      // Check for validation errors
      if (error && typeof error === 'object' && 'errors' in error) {
        return NextResponse.json(
          {
            error: 'Validation Error',
            details: error.errors,
          },
          { status: 400 },
        )
      }
      
      return NextResponse.json(
        {
          error: 'An unexpected error occurred. The slug may already be in use.',
          details: error instanceof Error ? error.message : String(error),
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error('Error in news create API:', error)
    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
} 