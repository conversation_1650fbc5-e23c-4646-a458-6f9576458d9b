import { connectToDatabase } from '../lib/mongodb';
import { ObjectId } from 'mongodb';

/**
 * Migration script to fix image paths in articles
 * This script will convert all article featuredImage values from IDs to proper paths
 * Run with: npx ts-node -r tsconfig-paths/register src/scripts/fixImagePaths.ts
 */
async function fixImagePaths() {
  console.log('Starting migration to fix article image paths...');
  
  try {
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    const articlesCollection = db.collection('articles');
    
    // Find all articles with featuredImage that's not in the proper format
    const articles = await articlesCollection.find({
      featuredImage: { $ne: null },
      $nor: [
        { featuredImage: { $regex: '^/api/media/file/' } },
        { featuredImage: { $regex: '^/media/' } }
      ]
    }).toArray();
    
    console.log(`Found ${articles.length} articles with image paths to fix`);
    
    let successCount = 0;
    let errorCount = 0;
    let skippedCount = 0;
    
    // Process each article
    for (const article of articles) {
      try {
        const featuredImage = article.featuredImage;
        
        // Skip if null or undefined
        if (!featuredImage) {
          skippedCount++;
          continue;
        }
        
        let newPath = null;
        
        // Check if it's already a filename with extension but missing the path prefix
        if (typeof featuredImage === 'string' && /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(featuredImage)) {
          // It's already a filename, just add the prefix
          newPath = `/api/media/file/${featuredImage}`;
          console.log(`Converting filename to path: ${featuredImage} -> ${newPath}`);
        }
        // Check if it's a MongoDB ObjectId
        else if (typeof featuredImage === 'string' && /^[0-9a-fA-F]{24}$/.test(featuredImage)) {
          // It's likely a MongoDB ID
          newPath = `/api/media/file/${featuredImage}`;
          console.log(`Converting ObjectId to path: ${featuredImage} -> ${newPath}`);
        } 
        // Handle object format (should be rare in database)
        else if (typeof featuredImage === 'object' && featuredImage !== null) {
          if (featuredImage.id) {
            newPath = `/api/media/file/${featuredImage.id}`;
            console.log(`Converting object with ID to path: ${featuredImage.id} -> ${newPath}`);
          } else if (featuredImage.filename) {
            newPath = `/api/media/file/${featuredImage.filename}`;
            console.log(`Converting object with filename to path: ${featuredImage.filename} -> ${newPath}`);
          }
        }
        
        if (newPath) {
          // Update the article
          const result = await articlesCollection.updateOne(
            { _id: article._id },
            { $set: { featuredImage: newPath } }
          );
          
          if (result.modifiedCount === 1) {
            console.log(`Updated article ${article._id}: ${article.title || 'Untitled'}`);
            console.log(`  Changed ${typeof featuredImage === 'string' ? featuredImage : 'object'} -> ${newPath}`);
            successCount++;
          } else {
            console.log(`No changes for article ${article._id}: ${article.title || 'Untitled'}`);
            skippedCount++;
          }
        } else {
          console.log(`Skipped article ${article._id}: ${article.title || 'Untitled'} - featuredImage format not recognized:`, featuredImage);
          skippedCount++;
        }
      } catch (articleError) {
        console.error(`Error processing article ${article._id}:`, articleError);
        errorCount++;
      }
    }
    
    console.log(`Migration complete:`);
    console.log(`- ${successCount} articles updated`);
    console.log(`- ${skippedCount} articles skipped`);
    if (errorCount > 0) {
      console.log(`- ${errorCount} errors occurred. See logs for details.`);
    }
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Execute the function
fixImagePaths().catch(console.error); 