import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  // Await params before accessing its properties
  const resolvedParams = await params
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the news item by ID
      const newsItem = await payload.findByID({
        collection: 'news',
        id: resolvedParams.id,
        depth: 2, // Populate relationships
      })

      if (!newsItem) {
        return NextResponse.json({ error: 'News item not found' }, { status: 404 })
      }

      // Note: Views count update is disabled due to validation issues
      // We'll implement a proper solution for tracking views in a future update

      return NextResponse.json({ news: newsItem })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching news item:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
