'use client'

import React, { useEffect, useState } from 'react'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { SearchIcon, FilterIcon, RefreshCw, AlertTriangle, Calendar, ChevronDown, Lock, FileText } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

// Define BadgeVariant type
type BadgeVariant = React.ComponentProps<typeof Badge>['variant']

interface AuditLog {
  id: string
  action: string
  collection: string
  documentId: string
  userId: string
  userName: string
  timestamp: string
  details?: string
  schoolId?: string
  schoolName?: string
}

export function RawAuditLogs() {
  const [loading, setLoading] = useState(true)
  const [logs, setLogs] = useState<AuditLog[]>([])
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState({
    collection: 'all',
    action: 'all',
    search: '',
  })

  const fetchLogs = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/super-admin/audit-logs')
      if (!response.ok) throw new Error('Failed to fetch audit logs')
      const data = await response.json()
      
      // Process the logs to make them more readable
      const processedLogs = (data.logs || data || []).map((log: AuditLog) => {
        // Create a normalized action name from any action string
        let normalizedAction = log.action || '';
        
        // Handle specific action types with better formatting
        if (normalizedAction?.includes('article-review')) {
          normalizedAction = 'article-review';
        } else if (normalizedAction?.includes('notification')) {
          normalizedAction = 'notification';
        } else if (normalizedAction?.includes('test-article')) {
          normalizedAction = 'test-article';
        }
        
        // Add a timestamp if missing (helps with sorting)
        if (!log.timestamp) {
          log.timestamp = new Date().toISOString();
        }
        
        // For logs without proper timestamps, try to extract date from details if possible
        if (log.details && log.details.includes('timestamp')) {
          try {
            const match = log.details.match(/timestamp[:=](\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})/);
            if (match && match[1]) {
              log.timestamp = match[1];
            }
          } catch (e) {
            // If extraction fails, leave timestamp as is
          }
        }
        
        return {
          ...log,
          action: normalizedAction
        };
      });
      
      // Sort by most recent first
      processedLogs.sort((a: AuditLog, b: AuditLog) => {
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      });
      
      setLogs(processedLogs)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching audit logs:', error)
      setError('Failed to load audit logs')
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchLogs()
  }, [])

  // Filter logs based on current filter settings
  const filteredLogs = logs.filter((log) => {
    if (filter.collection !== 'all' && log.collection !== filter.collection) return false
    if (filter.action !== 'all' && log.action !== filter.action) return false
    if (filter.search && !Object.values(log).some(value => 
      value && typeof value === 'string' && value.toLowerCase().includes(filter.search.toLowerCase())
    )) return false
    
    return true
  })

  const handleRefresh = () => {
    fetchLogs()
  }

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilter(prev => ({ ...prev, search: event.target.value }))
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown date';
    
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }
    
    try {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Format error';
    }
  }

  // Format action for display
  const formatAction = (action: string) => {
    const actionMap: Record<string, { label: string, variant: BadgeVariant }> = {
      'create': { label: 'إنشاء', variant: 'default' },
      'update': { label: 'تحديث', variant: 'outline' },
      'delete': { label: 'حذف', variant: 'destructive' },
      'approve': { label: 'موافقة', variant: 'default' },
      'reject': { label: 'رفض', variant: 'destructive' },
      'publish': { label: 'نشر', variant: 'default' },
      'unpublish': { label: 'إلغاء النشر', variant: 'secondary' },
      'login': { label: 'تسجيل دخول', variant: 'secondary' },
      'logout': { label: 'تسجيل خروج', variant: 'outline' },
      'force-publish': { label: 'نشر إجباري', variant: 'secondary' }
    }
    
    return actionMap[action] || { label: action, variant: 'outline' }
  }
  
  // Format the timestamp cell with a tooltip showing full date information
  const formatTimestampCell = (timestamp: string) => {
    const dateText = formatDate(timestamp);
    
    if (dateText === 'Unknown date' || dateText === 'Invalid date') {
      return (
        <div className="flex items-center text-amber-500">
          <Calendar className="h-3 w-3 mr-1" />
          <span className="text-xs">No date</span>
        </div>
      );
    }
    
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="font-mono text-xs">
              {dateText}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs">Original timestamp: {timestamp}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Format timestamp for display
  const formatTimestamp = (timestamp: string | null | undefined) => {
    if (!timestamp) return 'لا يوجد تاريخ'
    
    try {
      const date = new Date(timestamp)
      return new Intl.DateTimeFormat('ar-EG', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).format(date)
    } catch (e) {
      return 'تاريخ غير صالح'
    }
  }

  // Format details for display
  const formatDetails = (details: any) => {
    if (!details) return <span className="text-muted-foreground">-</span>
    
    if (details.startsWith?.('enc:')) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <span className="flex items-center text-muted-foreground">
                <Lock className="h-3 w-3 mr-1" />
                <span>بيانات مشفرة</span>
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs font-mono">{details}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }
    
    if (typeof details === 'object') {
      try {
        const jsonStr = JSON.stringify(details, null, 2)
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <span className="flex items-center text-muted-foreground">
                  <FileText className="h-3 w-3 mr-1" />
                  <span>بيانات JSON</span>
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <pre className="text-xs font-mono max-h-[200px] overflow-auto">{jsonStr}</pre>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      } catch (e) {
        return String(details)
      }
    }
    
    return details
  }

  if (loading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-[400px] w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 border border-red-200 bg-red-50 rounded-md text-red-700 flex items-center">
        <AlertTriangle className="h-5 w-5 mr-2" />
        Error: {error}
      </div>
    )
  }

  // Get unique actions for filter dropdown
  const uniqueActions = Array.from(new Set(logs.map(log => log.action))).filter(Boolean);
  // Get unique collections for filter dropdown
  const uniqueCollections = Array.from(new Set(logs.map(log => log.collection))).filter(Boolean);
  
  return (
    <div className="space-y-4" dir="rtl">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <SearchIcon className="w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="بحث في السجلات..."
            className="max-w-xs"
            value={filter.search}
            onChange={handleSearchChange}
          />
        </div>
        
        <div className="flex flex-wrap items-center gap-2">
          <div className="flex items-center gap-2">
            <FilterIcon className="w-4 h-4 text-muted-foreground" />
            <Select 
              value={filter.collection} 
              onValueChange={(value) => setFilter(prev => ({ ...prev, collection: value }))}
            >
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="جميع المجموعات" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المجموعات</SelectItem>
                {uniqueCollections.map(collection => (
                  <SelectItem key={collection} value={collection}>{collection || 'Uncategorized'}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <Select 
            value={filter.action} 
            onValueChange={(value) => setFilter(prev => ({ ...prev, action: value }))}
          >
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="جميع الإجراءات" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الإجراءات</SelectItem>
              {uniqueActions.map(action => (
                <SelectItem key={action} value={action}>{action || 'Unknown'}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="icon" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>الوقت</TableHead>
              <TableHead>الإجراء</TableHead>
              <TableHead>المجموعة</TableHead>
              <TableHead>المستخدم</TableHead>
              <TableHead>المدرسة</TableHead>
              <TableHead>التفاصيل</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLogs.length > 0 ? (
              filteredLogs.map((log) => (
                <TableRow key={log.id || Math.random().toString()}>
                  <TableCell>{formatTimestampCell(log.timestamp)}</TableCell>
                  <TableCell>{formatAction(log.action).label}</TableCell>
                  <TableCell>{log.collection || '-'}</TableCell>
                  <TableCell>{log.userName || 'System'}</TableCell>
                  <TableCell>{log.schoolName || '-'}</TableCell>
                  <TableCell className="max-w-xs truncate" title={log.details}>
                    {formatDetails(log.details)}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                  لا توجد سجلات مطابقة للبحث.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      <div className="text-xs text-muted-foreground">
        Showing {filteredLogs.length} of {logs.length} logs
      </div>
    </div>
  )
} 