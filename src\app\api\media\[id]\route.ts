import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { ObjectId } from 'mongodb'
import path from 'path'
import fs from 'fs/promises'

/**
 * API endpoint to serve media files by ID
 * This handles both files stored in the media collection and directly references to files
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    
    if (!id) {
      return NextResponse.json({ error: 'Media ID is required' }, { status: 400 })
    }
    
    // Get the payload instance
    const payload = await getPayload({ config })
    
    // Try to find the media in the Payload CMS media collection
    try {
      const media = await payload.findByID({
        collection: 'media',
        id,
      })
      
      if (media && media.url) {
        // If media has a URL, redirect to it
        return NextResponse.redirect(new URL(media.url, request.url))
      }
      
      // If media has a filename but no URL, try to serve the file from the uploads directory
      if (media && media.filename) {
        // Construct the file path relative to the project root
        const filePath = path.join(process.cwd(), 'media', media.filename)
        
        try {
          // Check if the file exists
          await fs.access(filePath)
          
          // Read the file
          const fileBuffer = await fs.readFile(filePath)
          
          // Determine MIME type based on file extension
          const ext = path.extname(media.filename).toLowerCase()
          let contentType = 'application/octet-stream'
          
          if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg'
          else if (ext === '.png') contentType = 'image/png'
          else if (ext === '.gif') contentType = 'image/gif'
          else if (ext === '.webp') contentType = 'image/webp'
          else if (ext === '.svg') contentType = 'image/svg+xml'
          else if (ext === '.pdf') contentType = 'application/pdf'
          
          // Return the file with appropriate headers
          return new NextResponse(fileBuffer, {
            headers: {
              'Content-Type': contentType,
              'Cache-Control': 'public, max-age=31536000',
            },
          })
        } catch (fileError) {
          console.error('Error reading media file:', fileError)
        }
      }
    } catch (mediaError) {
      console.log('Media not found in database, will try fallback methods:', mediaError)
    }
    
    // Fallback: Check if the ID might be a path to a file in /public/uploads
    try {
      // Check if the ID looks like a path and normalize it
      let filePath = id
      if (filePath.startsWith('/uploads/')) {
        filePath = path.join(process.cwd(), 'public', filePath)
      } else {
        filePath = path.join(process.cwd(), 'public/uploads', filePath)
      }
      
      // Try to access the file
      await fs.access(filePath)
      
      // Read the file
      const fileBuffer = await fs.readFile(filePath)
      
      // Determine MIME type based on file extension
      const ext = path.extname(filePath).toLowerCase()
      let contentType = 'application/octet-stream'
      
      if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg'
      else if (ext === '.png') contentType = 'image/png'
      else if (ext === '.gif') contentType = 'image/gif'
      else if (ext === '.webp') contentType = 'image/webp'
      else if (ext === '.svg') contentType = 'image/svg+xml'
      else if (ext === '.pdf') contentType = 'application/pdf'
      
      // Return the file with appropriate headers
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=31536000',
        },
      })
    } catch (fileError) {
      console.error('Error reading file from uploads directory:', fileError)
    }
    
    // If we've reached this point, we couldn't find the media
    return NextResponse.json(
      { error: 'Media not found' }, 
      { status: 404 }
    )
  } catch (error) {
    console.error('Error serving media:', error)
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    )
  }
} 