import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import { isMediaPath, safeMediaQuery } from '@/lib/media-utils'
import payloadConfig from '@/payload.config'

export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized: Please log in' }, { status: 401 })
    }

    const user = await extractUserFromToken(token)

    if (!user || user.role !== 'mentor') {
      return NextResponse.json(
        { error: 'Unauthorized: Only mentors can access this endpoint' },
        { status: 403 },
      )
    }

    // Get the school ID from the user
    const schoolId = user.school

    if (!schoolId) {
      return NextResponse.json(
        { error: 'You must be associated with a school to access this endpoint' },
        { status: 400 },
      )
    }

    // Get articles with teacher reviews
    const payload = await getPayload({ config: payloadConfig })

    try {
      // First, simply get all articles from this school
      const articles = await payload.find({
        collection: 'articles',
        where: {
          // Use a simple query with proper equals operator
          'author.school': { equals: schoolId },
          // {{change 1}} Add condition to exclude articles with media paths in featuredImage using not_like
          featuredImage: {
            not_like: /(\/api\/media\/|\/file\/|\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff))/i, // Exclude strings matching media path patterns
          },
        },
        depth: 2, // To get teacher and article details
      })

      // Filter in JavaScript instead of MongoDB query to avoid ObjectParameterError
      const validArticles = articles.docs
        // Remove articles with media path IDs
        .filter((article) => article && article.id && !isMediaPath(article.id))
        // Only include articles with teacher reviews
        .filter(
          (article) =>
            article &&
            article.teacherReview &&
            Array.isArray(article.teacherReview) &&
            article.teacherReview.length > 0,
        )

      // Extract teacher reviews
      const recentReviews = []

      for (const article of validArticles) {
        if (article.teacherReview && article.teacherReview.length > 0) {
          for (let index = 0; index < article.teacherReview.length; index++) {
            const review = article.teacherReview[index]

            // Get teacher name
            let teacherName = 'Unknown Teacher'
            if (typeof review.reviewer === 'object' && review.reviewer) {
              const firstName = review.reviewer.firstName || ''
              const lastName = review.reviewer.lastName || ''
              const email = review.reviewer.email || ''
              teacherName = `${firstName} ${lastName}`.trim() || email
            }

            recentReviews.push({
              id: `${article.id}-${index}`,
              articleId: article.id,
              articleTitle: article.title || 'Untitled Article',
              teacherId: typeof review.reviewer === 'object' ? review.reviewer.id : review.reviewer,
              teacherName,
              rating: review.rating || 0,
              comment: review.comment || '',
              approved: review.approved || false,
              createdAt: review.createdAt || article.createdAt,
              reviewIndex: index,
              hasBeenRated:
                (review.mentorFeedback && typeof review.mentorFeedback === 'object') ||
                review.mentorReview === true,
            })
          }
        }
      }

      // Sort by createdAt (most recent first)
      recentReviews.sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0
        const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0
        return dateB - dateA
      })

      // Limit to 20 most recent reviews
      const limitedReviews = recentReviews.slice(0, 20)

      return NextResponse.json(
        {
          success: true,
          reviews: limitedReviews,
        },
        { status: 200 },
      )
    } catch (error) {
      console.error('Error fetching recent teacher reviews:', error)
      return NextResponse.json(
        {
          error: 'Failed to fetch recent teacher reviews',
          details: error instanceof Error ? error.message : String(error),
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error('Error fetching recent teacher reviews:', error)
    return NextResponse.json({ error: 'Failed to fetch recent teacher reviews' }, { status: 500 })
  }
}
