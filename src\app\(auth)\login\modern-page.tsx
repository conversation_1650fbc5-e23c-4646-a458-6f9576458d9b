import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import bgImage from '@/public/images/isco-ry_ZwyuttGU-unsplash.jpg'
import { Label } from '@/components/ui/label'

export default function ModernLoginPage() {
  return (
    <section className="flex flex-col md:flex-row h-screen items-center">
      {/* Left side - Image */}
      <div className="bg-primary hidden lg:block w-full md:w-1/2 xl:w-2/3 h-screen relative">
        <Image
          src="https://images.unsplash.com/photo-1444313431167-e7921088a9d3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1441&q=100"
          alt="Young Reporter"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-primary/30"></div>
        <div className="absolute bottom-0 left-0 right-0 p-12 text-white">
          <h2 className="text-4xl font-bold mb-4">Young Reporter</h2>
          <p className="text-xl">
            Join our community of young journalists and share your stories with the world.
          </p>
        </div>
      </div>

      {/* Right side - Login Form */}
      <div
        className="bg-background w-full md:max-w-md lg:max-w-full md:mx-auto md:w-1/2 xl:w-1/3 h-screen px-6 lg:px-16 xl:px-12
            flex items-center justify-center"
      >
        <div className="w-full h-100">
          <h1 className="text-xl font-bold text-primary">Young Reporter</h1>

          <h1 className="text-xl md:text-2xl font-bold leading-tight mt-12">
            Log in to your account
          </h1>

          <form className="mt-6" action="/api/custom-login" method="POST">
            <div className="mb-4">
              <Label htmlFor="email" className="block text-foreground">
                Email Address
              </Label>
              <Input
                type="email"
                id="email"
                name="email"
                placeholder="Enter Email Address"
                className="w-full mt-2"
                autoFocus
                required
              />
            </div>

            <div className="mb-4">
              <Label htmlFor="password" className="block text-foreground">
                Password
              </Label>
              <Input
                type="password"
                id="password"
                name="password"
                placeholder="Enter Password"
                className="w-full mt-2"
                required
              />
            </div>

            <div className="text-right mt-2">
              <Link
                href="/forgot-password"
                className="text-sm font-semibold text-primary hover:text-primary/80"
              >
                Forgot Password?
              </Link>
            </div>

            <Button type="submit" className="w-full mt-6">
              Log In
            </Button>
          </form>

          <hr className="my-6 border-border w-full" />

          <Button
            type="button"
            variant="outline"
            className="w-full flex items-center justify-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 mr-2" viewBox="0 0 48 48">
              <defs>
                <path
                  id="a"
                  d="M44.5 20H24v8.5h11.8C34.7 33.9 30.1 37 24 37c-7.2 0-13-5.8-13-13s5.8-13 13-13c3.1 0 5.9 1.1 8.1 2.9l6.4-6.4C34.6 4.1 29.6 2 24 2 11.8 2 2 11.8 2 24s9.8 22 22 22c11 0 21-8 21-22 0-1.3-.2-2.7-.5-4z"
                />
              </defs>
              <clipPath id="b">
                <use xlinkHref="#a" overflow="visible" />
              </clipPath>
              <path clipPath="url(#b)" fill="#FBBC05" d="M0 37V11l17 13z" />
              <path clipPath="url(#b)" fill="#EA4335" d="M0 11l17 13 7-6.1L48 14V0H0z" />
              <path clipPath="url(#b)" fill="#34A853" d="M0 37l30-23 7.9 1L48 0v48H0z" />
              <path clipPath="url(#b)" fill="#4285F4" d="M48 48L17 24l-4-3 35-10z" />
            </svg>
            Log in with Google
          </Button>

          <p className="mt-8">
            Need an account?{' '}
            <Link href="/register" className="text-primary hover:text-primary/80 font-semibold">
              Create an account
            </Link>
          </p>

          <p className="text-sm text-muted-foreground mt-12">
            &copy; {new Date().getFullYear()} Young Reporter - All Rights Reserved.
          </p>
        </div>
      </div>
    </section>
  )
}
