import { NextResponse } from 'next/server';

/**
 * Standard API response interface
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  status?: number;
  message?: string;
}

/**
 * Creates a standardized success response
 * @param data The data to include in the response
 * @param message Optional success message
 * @param status HTTP status code (default: 200)
 * @returns A NextResponse with standardized format
 */
export function createSuccessResponse<T = any>(
  data: T, 
  message?: string, 
  status = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json(
    { 
      success: true, 
      data, 
      ...(message && { message })
    }, 
    { status }
  );
}

/**
 * Creates a standardized error response
 * @param error Error message
 * @param status HTTP status code (default: 400)
 * @returns A NextResponse with standardized format
 */
export function createErrorResponse(
  error: string, 
  status = 400
): NextResponse<ApiResponse<null>> {
  return NextResponse.json(
    { 
      success: false, 
      error 
    }, 
    { status }
  );
}

/**
 * Handles common API errors and returns a standardized response
 * @param error The error object
 * @param defaultMessage Default error message if none can be extracted
 * @returns A standardized error response
 */
export function handleApiError(
  error: any, 
  defaultMessage = 'An unexpected error occurred'
): NextResponse<ApiResponse<null>> {
  console.error('API Error:', error);
  
  // Extract error message if possible
  const errorMessage = error?.message || defaultMessage;
  
  // Determine status code
  let status = 500;
  if (error?.status) {
    status = error.status;
  } else if (errorMessage.includes('not found')) {
    status = 404;
  } else if (errorMessage.includes('unauthorized') || errorMessage.includes('unauthenticated')) {
    status = 401;
  } else if (errorMessage.includes('forbidden')) {
    status = 403;
  } else if (errorMessage.includes('validation')) {
    status = 400;
  }
  
  return createErrorResponse(errorMessage, status);
}

/**
 * Checks if we're in development mode
 * @returns True if in development mode, false otherwise
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';
}
