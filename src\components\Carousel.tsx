'use client'

import React, { useState, useEffect, useRef } from 'react'

interface CarouselProps {
  title: string
  items: React.ReactNode[]
  className?: string
}

export const Carousel: React.FC<CarouselProps> = ({ title, items, className = '' }) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [touchStart, setTouchStart] = useState(0)
  const [touchEnd, setTouchEnd] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null)
  
  // Calculate how many items to show based on screen size
  const [itemsToShow, setItemsToShow] = useState(1)
  
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setItemsToShow(1)
      } else if (window.innerWidth < 1024) {
        setItemsToShow(2)
      } else {
        setItemsToShow(3)
      }
    }
    
    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  // Auto-play functionality
  useEffect(() => {
    const play = () => {
      autoPlayRef.current = setTimeout(() => {
        nextSlide()
      }, 5000)
    }
    
    play()
    
    return () => {
      if (autoPlayRef.current) {
        clearTimeout(autoPlayRef.current)
      }
    }
  }, [currentIndex])
  
  // Handle touch events for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX)
  }
  
  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }
  
  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 50) {
      // Swipe left
      nextSlide()
    }
    
    if (touchStart - touchEnd < -50) {
      // Swipe right
      prevSlide()
    }
  }
  
  const nextSlide = () => {
    if (isAnimating) return
    
    setIsAnimating(true)
    setCurrentIndex((prevIndex) => 
      prevIndex === items.length - itemsToShow ? 0 : prevIndex + 1
    )
    
    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }
  
  const prevSlide = () => {
    if (isAnimating) return
    
    setIsAnimating(true)
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? items.length - itemsToShow : prevIndex - 1
    )
    
    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }
  
  // If we don't have enough items, don't show navigation
  const showNavigation = items.length > itemsToShow
  
  return (
    <div className={`w-full ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">{title}</h2>
        {showNavigation && (
          <div className="flex space-x-2">
            <button
              onClick={prevSlide}
              className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 transition"
              aria-label="Previous slide"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <button
              onClick={nextSlide}
              className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 transition"
              aria-label="Next slide"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        )}
      </div>
      
      <div
        className="relative overflow-hidden"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{
            transform: `translateX(-${currentIndex * (100 / itemsToShow)}%)`,
            width: `${(items.length / itemsToShow) * 100}%`,
          }}
        >
          {items.map((item, index) => (
            <div
              key={index}
              className="px-2"
              style={{ width: `${100 / items.length}%` }}
            >
              {item}
            </div>
          ))}
        </div>
        
        {/* Dots indicator */}
        {showNavigation && (
          <div className="flex justify-center mt-4 space-x-2">
            {Array.from({ length: items.length - itemsToShow + 1 }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  currentIndex === index ? 'bg-blue-600' : 'bg-gray-300'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
