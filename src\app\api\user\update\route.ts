import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import { ObjectId } from 'mongodb' // Added ObjectId import

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import type { User } from '../../../../payload-types' // Adjusted path

// Fix issues in the file:
// 1. Fix ObjectId signature deprecation warnings
// 2. Remove unused variables
// 3. Fix missing try/catch blocks
// 4. Fix 'catch' or 'finally' expected at EOF

export async function POST(req: NextRequest) {
  try {
    console.log('User update API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    console.log('Token found:', token ? 'Yes' : 'No')

    if (!token) {
      console.log('No token found, returning 401')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      let body
      try {
        body = await req.json()
        console.log('Request body parsed as JSON:', body)
      } catch (error) {
        console.log('Error parsing JSON, trying formData')
        // Fallback to formData if JSON parsing fails
        const formData = await req.formData()
        body = {
          firstName: formData.get('firstName'),
          lastName: formData.get('lastName'),
          bio: formData.get('bio'),
          grade: formData.get('grade'),
          profileImageId: formData.get('profileImageId'),
        }
        console.log('Request body parsed as formData:', body)
      }

      const { firstName, lastName, bio, grade, profileImageId } = body

      // Validate required fields
      if (!firstName || !lastName) {
        console.log('Missing required fields')
        return NextResponse.json(
          { error: 'First name and last name are required' },
          { status: 400 },
        )
      }

      // Try to update in MongoDB first
      try {
        console.log('Connecting to MongoDB...')
        const { db } = await connectToDatabase()
        console.log('MongoDB connection successful')

        // Get user to determine role - try different ID formats with a comprehensive query
        console.log('Looking up user in MongoDB with ID:', userId)

        // Create a comprehensive query that tries all possible ID formats
        const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(userId)

        const query: { $or: Array<{ id?: any; email?: any; _id?: any }> } = {
          $or: [
            { id: userId },
            { id: userId.toString() },
            { email: userId }, // In case userId is an email
          ],
        }

        // Add ObjectId query if the format is valid
        if (isValidObjectId) {
          try {
            query.$or.push({ _id: new ObjectId(userId.toString()) })
          } catch (err) {
            console.log('Error creating ObjectId:', err)
          }
        }

        console.log('MongoDB query:', JSON.stringify(query))
        const users = await db.collection('users').find(query).toArray()

        let user: any = null // Use 'any' for now, or ideally the User type from MongoDB
        if (users.length > 0) {
          user = users[0]
          console.log('Found user in MongoDB:', user._id || user.id)
          console.log('User details:', {
            id: user.id,
            _id: user._id ? user._id.toString() : 'N/A',
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: typeof user.role === 'object' ? user.role.slug : user.role,
          })
        } else {
          console.log('User not found in MongoDB with any ID format')
        }

        if (!user) {
          console.log('User not found in MongoDB, will try Payload CMS')
          // Don't return error here, continue to Payload CMS
        } else {
          console.log('User found in MongoDB')
          const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
          console.log('User role:', userRole)

          // Build update data - properly handle relationship fields
          const updateData: Partial<User> & { updatedAt?: string; bio?: string | null } = {
            // Added bio to type
            bio,
            updatedAt: new Date().toISOString(),
          }

          // Handle name changes with approval workflow
          // For teachers, mentors, school admins, and super admins, update name directly
          if (['teacher', 'mentor', 'school-admin', 'super-admin'].includes(userRole)) {
            console.log('User is authorized to update name directly')
            updateData.firstName = firstName
            updateData.lastName = lastName
          } else {
            // For students, name changes require approval
            console.log('Name changes for students require approval')

            // Check if the name is actually changing
            const currentFirstName = user.firstName || ''
            const currentLastName = user.lastName || ''

            if (firstName !== currentFirstName || lastName !== currentLastName) {
              console.log('Name is changing, setting pending name fields')
              updateData.pendingFirstName = firstName
              updateData.pendingLastName = lastName
              updateData.nameChangeStatus = 'pending'
            } else {
              console.log('Name is not changing, keeping current name')
              updateData.firstName = firstName
              updateData.lastName = lastName
            }
          }

          // Handle role relationship properly
          if (user.role) {
            // For MongoDB, we can keep the role as is
            updateData.role = user.role // Assuming user.role is already in the correct format for MongoDB (ID or object)
          }

          // Only include grade if provided and user is a student
          if (grade && userRole === 'student') {
            updateData.grade = grade as User['grade'] // Cast to User type
          }

          // Handle profile image upload
          if (profileImageId) {
            console.log('Profile image ID provided:', profileImageId)

            // Set the pending profile image and status
            updateData.pendingProfileImage = profileImageId
            updateData.profileImageStatus = 'pending'

            // For teachers, mentors, school admins, and super admins, auto-approve
            if (['teacher', 'mentor', 'school-admin', 'super-admin'].includes(userRole)) {
              console.log('User is authorized to auto-approve profile image')
              updateData.profileImage = profileImageId
              updateData.pendingProfileImage = null // Clear pending
              updateData.profileImageStatus = 'approved'
            }
          }

          // Preserve other required fields if they exist in the user object
          if (user.school) {
            // For MongoDB, we can keep the school as is
            updateData.school = user.school // Assuming user.school is in correct format
          }

          console.log('Updating user in MongoDB with data:', updateData)

          // Create a query that matches the user we found
          const updateQuery: any = {} // Use 'any' for MongoDB query flexibility

          // Use the _id if available, otherwise use the id
          if (user._id) {
            updateQuery._id = user._id
          } else if (user.id) {
            updateQuery.id = user.id
          }

          console.log('MongoDB update query:', JSON.stringify(updateQuery))

          // Update user in MongoDB
          const result = await db.collection('users').updateOne(updateQuery, { $set: updateData })

          console.log('MongoDB update result:', {
            matchedCount: result.matchedCount,
            modifiedCount: result.modifiedCount,
            upsertedCount: result.upsertedCount,
          })

          if (result.matchedCount > 0) {
            console.log('User updated in MongoDB')
            // Don't return yet - continue to update in Payload CMS to ensure sync
          } else {
            console.log('No user matched in MongoDB update')
          }
        }
      } catch (mongoError: any) {
        // Typed mongoError
        console.warn('Error updating user in MongoDB, falling back to Payload:', mongoError)
      }

      // Always update in Payload CMS to ensure sync
      console.log('Updating in Payload CMS')
      const payload = await getPayload({ config })

      // Get user to determine role
      console.log('Looking up user in Payload with ID:', userId)
      let payloadUser: User | undefined // Type payloadUser

      try {
        payloadUser = await payload.findByID({
          collection: 'users',
          id: userId,
        })

        console.log('User found in Payload CMS')
      } catch (findError: any) {
        // Typed findError
        console.error('Error finding user in Payload:', findError)

        // Try to find the user by email if ID lookup fails
        try {
          console.log('Trying to find user by email in Payload CMS')
          const usersByEmail = await payload.find({
            collection: 'users',
            where: {
              email: {
                equals: userId, // In case userId is an email
              },
            },
          })

          if (usersByEmail.docs.length > 0) {
            payloadUser = usersByEmail.docs[0]
            console.log('User found by email in Payload CMS')
          }
        } catch (emailFindError: any) {
          // Typed emailFindError
          console.error('Error finding user by email in Payload:', emailFindError)
        }
      }

      if (!payloadUser) {
        console.log('User not found in Payload CMS')
        return NextResponse.json({ error: 'User not found in any system' }, { status: 404 })
      }

      const userRolePayload =
        typeof payloadUser.role === 'object' ? payloadUser.role?.slug : payloadUser.role // Renamed to avoid conflict
      console.log('User role from Payload:', userRolePayload)

      // Build update data - properly handle relationship fields
      const payloadUpdateData: Partial<User> & { bio?: string | null } = {
        // Added bio to type
        bio,
      }

      // Handle name changes with approval workflow
      // userRolePayload is already defined above

      // For teachers, mentors, school admins, and super admins, update name directly
      if (['teacher', 'mentor', 'school-admin', 'super-admin'].includes(userRolePayload)) {
        console.log('User is authorized to update name directly')
        payloadUpdateData.firstName = firstName
        payloadUpdateData.lastName = lastName
      } else {
        // For students, name changes require approval
        console.log('Name changes for students require approval')

        // Check if the name is actually changing
        const currentFirstName = payloadUser.firstName || ''
        const currentLastName = payloadUser.lastName || ''

        if (firstName !== currentFirstName || lastName !== currentLastName) {
          console.log('Name is changing, setting pending name fields')
          payloadUpdateData.pendingFirstName = firstName
          payloadUpdateData.pendingLastName = lastName
          payloadUpdateData.nameChangeStatus = 'pending'
        } else {
          console.log('Name is not changing, keeping current name')
          payloadUpdateData.firstName = firstName
          payloadUpdateData.lastName = lastName
        }
      }

      // Handle role relationship properly
      try {
        // Get the role ID directly from the database
        if (payloadUser.role) {
          let roleId: string | null = null

          // If role is an object with id, use the id
          if (
            typeof payloadUser.role === 'object' &&
            payloadUser.role !== null &&
            (payloadUser.role as any).id
          ) {
            console.log('Using role ID from object:', (payloadUser.role as any).id)
            roleId = (payloadUser.role as any).id
          }
          // If role is a string, use it directly
          else if (typeof payloadUser.role === 'string') {
            console.log('Using role ID from string:', payloadUser.role)
            roleId = payloadUser.role
          }
          // If role is an object with slug, look up the ID
          else if (
            typeof payloadUser.role === 'object' &&
            payloadUser.role !== null &&
            (payloadUser.role as any).slug
          ) {
            console.log('Looking up role ID by slug:', (payloadUser.role as any).slug)

            // Find the role by slug
            const roleDoc = await payload.find({
              collection: 'roles',
              where: {
                slug: {
                  equals: (payloadUser.role as any).slug,
                },
              },
            })

            if (roleDoc.docs && roleDoc.docs.length > 0) {
              roleId = roleDoc.docs[0].id
              console.log('Found role ID by slug:', roleId)
            } else {
              console.error('Role not found by slug:', (payloadUser.role as any).slug)
            }
          }

          if (roleId) {
            payloadUpdateData.role = roleId
          } else {
            console.error('Could not determine role ID')
          }
        }
      } catch (roleError: any) {
        // Typed roleError
        console.error('Error handling role relationship:', roleError)
      }

      // Only include grade if provided and user is a student
      if (grade && userRolePayload === 'student') {
        payloadUpdateData.grade = grade as User['grade'] // Cast to User type
      }

      // Handle profile image upload
      if (profileImageId) {
        console.log('Profile image ID provided:', profileImageId)

        // Set the pending profile image and status
        payloadUpdateData.pendingProfileImage = profileImageId
        payloadUpdateData.profileImageStatus = 'pending'

        // For teachers, mentors, school admins, and super admins, auto-approve
        if (['teacher', 'mentor', 'school-admin', 'super-admin'].includes(userRolePayload)) {
          console.log('User is authorized to auto-approve profile image')
          payloadUpdateData.profileImage = profileImageId
          payloadUpdateData.pendingProfileImage = null // Clear pending
          payloadUpdateData.profileImageStatus = 'approved'
        }
      }

      // Handle school relationship properly
      if (payloadUser.school) {
        // If school is an object with id, use the id
        if (
          typeof payloadUser.school === 'object' &&
          payloadUser.school !== null &&
          (payloadUser.school as any).id
        ) {
          console.log('Using school ID from object:', (payloadUser.school as any).id)
          payloadUpdateData.school = (payloadUser.school as any).id
        }
        // If school is a string, use it directly
        else if (typeof payloadUser.school === 'string') {
          console.log('Using school ID from string:', payloadUser.school)
          payloadUpdateData.school = payloadUser.school
        }
        // If we can't determine the school ID, log an error
        else {
          console.error('Could not determine school ID from:', payloadUser.school)
          // Don't include school in this case to avoid validation errors
        }
      }

      console.log('Updating user in Payload with data:', payloadUpdateData)

      // Update user in Payload
      try {
        console.log('Attempting to update user in Payload with ID:', userId)

        // Log the exact data being sent to Payload
        console.log('Update data for Payload:', JSON.stringify(payloadUpdateData, null, 2))

        await payload.update({
          collection: 'users',
          id: userId,
          data: payloadUpdateData,
        })

        console.log('User updated successfully in Payload')
      } catch (payloadError: any) {
        // Typed payloadError
        console.error('Error updating user in Payload:', payloadError)

        // Try a more minimal update as a fallback
        try {
          console.log('Trying minimal update as fallback')

          // Create a minimal update with only the essential fields
          const minimalUpdate: Partial<User> = {
            // Typed minimalUpdate
            firstName: firstName || payloadUser.firstName,
            lastName: lastName || payloadUser.lastName,
          }

          // Always include role ID
          try {
            if (payloadUser.role) {
              let roleId: string | null = null

              // If role is an object with id, use the id
              if (
                typeof payloadUser.role === 'object' &&
                payloadUser.role !== null &&
                (payloadUser.role as any).id
              ) {
                console.log('Fallback: Using role ID from object:', (payloadUser.role as any).id)
                roleId = (payloadUser.role as any).id
              }
              // If role is a string, use it directly
              else if (typeof payloadUser.role === 'string') {
                console.log('Fallback: Using role ID from string:', payloadUser.role)
                roleId = payloadUser.role
              }
              // If role is an object with slug, look up the ID
              else if (
                typeof payloadUser.role === 'object' &&
                payloadUser.role !== null &&
                (payloadUser.role as any).slug
              ) {
                console.log('Fallback: Looking up role ID by slug:', (payloadUser.role as any).slug)

                // Find the role by slug
                const roleDoc = await payload.find({
                  collection: 'roles',
                  where: {
                    slug: {
                      equals: (payloadUser.role as any).slug,
                    },
                  },
                })

                if (roleDoc.docs && roleDoc.docs.length > 0) {
                  roleId = roleDoc.docs[0].id
                  console.log('Fallback: Found role ID by slug:', roleId)
                } else {
                  console.error('Fallback: Role not found by slug:', (payloadUser.role as any).slug)

                  // As a last resort, try to find any student role
                  console.log('Fallback: Trying to find any student role')
                  const studentRoleDoc = await payload.find({
                    collection: 'roles',
                    where: {
                      slug: {
                        equals: 'student',
                      },
                    },
                  })

                  if (studentRoleDoc.docs && studentRoleDoc.docs.length > 0) {
                    roleId = studentRoleDoc.docs[0].id
                    console.log('Fallback: Found student role ID:', roleId)
                  }
                }
              }

              if (roleId) {
                minimalUpdate.role = roleId
              } else {
                console.error('Fallback: Could not determine role ID')
              }
            }
          } catch (roleError: any) {
            // Typed roleError
            console.error('Fallback: Error handling role relationship:', roleError)
          }

          // Include school ID if needed
          if (
            payloadUser.school &&
            typeof payloadUser.school === 'object' &&
            payloadUser.school !== null &&
            (payloadUser.school as any).id
          ) {
            minimalUpdate.school = (payloadUser.school as any).id
          } else if (payloadUser.school && typeof payloadUser.school === 'string') {
            minimalUpdate.school = payloadUser.school
          }

          console.log('Minimal update data:', JSON.stringify(minimalUpdate, null, 2))

          const minimalUpdatedUser = await payload.update({
            collection: 'users',
            id: userId,
            data: minimalUpdate,
          })

          console.log('User updated with minimal data')

          // If we have a profile image ID, try to update just that in a separate call
          if (profileImageId) {
            console.log('Attempting to update profile image separately')

            const imageUpdate: Partial<User> = {
              // Typed imageUpdate
              pendingProfileImage: profileImageId,
              profileImageStatus: 'pending',
            }

            // For teachers, mentors, school admins, and super admins, auto-approve
            if (['teacher', 'mentor', 'school-admin', 'super-admin'].includes(userRolePayload)) {
              imageUpdate.profileImage = profileImageId
              imageUpdate.pendingProfileImage = null // Clear pending
              imageUpdate.profileImageStatus = 'approved'
            }

            try {
              await payload.update({
                collection: 'users',
                id: userId,
                data: imageUpdate,
              })
              console.log('Profile image updated separately')
            } catch (imageError: any) {
              // Typed imageError
              console.error('Error updating profile image separately:', imageError)
              // Continue anyway, at least the basic profile was updated
            }
          }
        } catch (fallbackError: any) {
          // Typed fallbackError
          console.error('Fallback update also failed:', fallbackError)

          // Final fallback: try to update directly in MongoDB
          try {
            console.log('Attempting direct MongoDB update as final fallback')

            const { db } = await connectToDatabase()

            // Create a minimal update with just the profile image
            const directUpdate: Partial<User> & { updatedAt?: string; bio?: string | null } = {
              // Added bio to type
              pendingProfileImage: profileImageId,
              profileImageStatus: 'pending',
              updatedAt: new Date().toISOString(),
            }

            // For teachers, mentors, school admins, and super admins, auto-approve
            if (['teacher', 'mentor', 'school-admin', 'super-admin'].includes(userRolePayload)) {
              directUpdate.profileImage = profileImageId
              directUpdate.pendingProfileImage = null // Clear pending
              directUpdate.profileImageStatus = 'approved'
            }

            console.log('Direct MongoDB update data:', directUpdate)

            // Try different ID formats
            let updateResult = await db
              .collection('users')
              .updateOne({ id: userId }, { $set: directUpdate })

            if (updateResult.matchedCount === 0) {
              // Try with string ID
              updateResult = await db
                .collection('users')
                .updateOne({ id: userId.toString() }, { $set: directUpdate })
            }

            if (updateResult.matchedCount === 0) {
              // Try with ObjectId
              try {
                // const ObjectId = require('mongodb').ObjectId; // Removed this line
                if (ObjectId.isValid(userId)) {
                  // ObjectId is now from top-level import
                  updateResult = await db
                    .collection('users')
                    .updateOne({ _id: new ObjectId(userId) }, { $set: directUpdate })
                }
              } catch (err: any) {
                // Typed err
                console.log('Error with ObjectId:', err)
              }
            }

            if (updateResult.matchedCount > 0) {
              console.log('Direct MongoDB update successful')

              // Add cache-busting headers to prevent caching
              const response = NextResponse.json({
                success: true,
                message: 'Profile image updated successfully',
                note: 'Used direct database update as fallback',
                timestamp: new Date().toISOString(), // Add timestamp to force client to recognize change
              })

              // Add cache control headers
              response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
              response.headers.set('Pragma', 'no-cache')
              response.headers.set('Expires', '0')

              return response
            } else {
              console.error('Direct MongoDB update failed')
              throw new Error('No matching user found in MongoDB')
            }
          } catch (directError: any) {
            // Typed directError
            console.error('Direct MongoDB update failed:', directError)
            return NextResponse.json(
              {
                error: 'Failed to update profile',
                details: directError.message, // Corrected to directError
              },
              { status: 500 },
            )
          } // Closes catch (directError)
        } // Closes catch (fallbackError) - THIS WAS THE MISSING BRACE
      } // Closes catch (payloadError)

      console.log('User updated in Payload')

      // Add cache-busting headers to prevent caching
      const response = NextResponse.json({
        success: true,
        message: 'Profile updated successfully',
        timestamp: new Date().toISOString(), // Add timestamp to force client to recognize change
      })

      // Add cache control headers
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
      response.headers.set('Pragma', 'no-cache')
      response.headers.set('Expires', '0')

      return response
    } catch (tokenError: any) {
      // Typed tokenError
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error: any) {
    // Typed error
    console.error('Error updating profile:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
