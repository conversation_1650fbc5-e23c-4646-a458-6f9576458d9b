import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    const payload = await getPayload({
      config,
    })

    // Get the current user
    const { user } = await payload.auth({ req })

    if (!user) {
      return NextResponse.json(
        { error: 'You must be logged in to change your password' },
        { status: 401 }
      )
    }

    const formData = await req.formData()
    const currentPassword = formData.get('currentPassword') as string
    const newPassword = formData.get('newPassword') as string
    const confirmPassword = formData.get('confirmPassword') as string

    // Validate inputs
    if (!currentPassword || !newPassword || !confirmPassword) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      )
    }

    if (newPassword !== confirmPassword) {
      return NextResponse.json(
        { error: 'New passwords do not match' },
        { status: 400 }
      )
    }

    // Determine redirect URL based on user role
    let redirectUrl = '/'
    const userRole = typeof user.role === 'object' 
      ? user.role?.slug 
      : user.role

    if (userRole === 'student') {
      redirectUrl = '/dashboard/student/profile'
    } else if (userRole === 'teacher') {
      redirectUrl = '/dashboard/teacher/profile'
    } else if (userRole === 'mentor') {
      redirectUrl = '/dashboard/mentor/profile'
    }

    try {
      // Verify current password by attempting to login
      await payload.login({
        collection: 'users',
        data: {
          email: user.email,
          password: currentPassword,
        },
      })

      // Update the password
      await payload.update({
        collection: 'users',
        id: user.id,
        data: {
          password: newPassword,
        },
      })

      // Redirect back to profile page
      return NextResponse.redirect(new URL(redirectUrl, req.url))
    } catch (error) {
      console.error('Password change error:', error)
      return NextResponse.json(
        { error: 'Current password is incorrect' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Error changing password:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
