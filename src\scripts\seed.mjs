import { getPayload } from 'payload'
import path from 'path'
import { fileURLToPath } from 'url'
import dotenv from 'dotenv'
import { buildConfig } from 'payload'
import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import sharp from 'sharp'

// Load environment variables
dotenv.config()

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// We'll use a simpler approach without importing collections directly

// Create config
const config = buildConfig({
  admin: {
    user: Users.slug,
  },
  collections: [
    Users,
    Media,
    Roles,
    Schools,
    Articles,
    News,
    Statistics,
    Notifications,
    Achievements,
    UserAchievements,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || '',
  }),
  sharp,
  plugins: [payloadCloudPlugin()],
})

async function seed() {
  try {
    // Initialize Payload
    const payload = await getPayload({
      config,
    })

    console.log('Seeding database...')

    // Create roles
    console.log('Creating roles...')

    // Check if roles already exist
    const existingRoles = await payload.find({
      collection: 'roles',
      limit: 1,
    })

    if (existingRoles.docs.length > 0) {
      console.log('Roles already exist, skipping role creation')
    } else {
      // Create roles
      const superAdminRole = await payload.create({
        collection: 'roles',
        data: {
          name: 'Super Admin',
          slug: 'super-admin',
          description: 'Full access to all features and settings',
        },
      })

      const schoolAdminRole = await payload.create({
        collection: 'roles',
        data: {
          name: 'School Admin',
          slug: 'school-admin',
          description: 'Manages teachers, mentors, and students within their school',
        },
      })

      const mentorRole = await payload.create({
        collection: 'roles',
        data: {
          name: 'Mentor',
          slug: 'mentor',
          description: 'Creates news posts and reviews teacher feedback',
        },
      })

      const teacherRole = await payload.create({
        collection: 'roles',
        data: {
          name: 'Teacher',
          slug: 'teacher',
          description: 'Reviews and approves student articles',
        },
      })

      const studentRole = await payload.create({
        collection: 'roles',
        data: {
          name: 'Student',
          slug: 'student',
          description: 'Creates and submits articles for review',
        },
      })

      console.log('Roles created successfully')
    }

    // Check if users already exist
    const existingUsers = await payload.find({
      collection: 'users',
      limit: 1,
    })

    if (existingUsers.docs.length > 0) {
      console.log('Users already exist, skipping user creation')
    } else {
      // Get roles
      const roles = await payload.find({
        collection: 'roles',
        limit: 100,
      })

      const superAdminRole = roles.docs.find((role) => role.slug === 'super-admin')
      const schoolAdminRole = roles.docs.find((role) => role.slug === 'school-admin')
      const mentorRole = roles.docs.find((role) => role.slug === 'mentor')
      const teacherRole = roles.docs.find((role) => role.slug === 'teacher')
      const studentRole = roles.docs.find((role) => role.slug === 'student')

      if (!superAdminRole || !schoolAdminRole || !mentorRole || !teacherRole || !studentRole) {
        console.error('Not all roles were found')
        process.exit(1)
      }

      // Create a demo school
      console.log('Creating demo school...')
      const demoSchool = await payload.create({
        collection: 'schools',
        data: {
          name: 'Demo High School',
          address: '123 Education Lane, Learning City, LC 12345',
        },
      })

      console.log('Demo school created successfully')

      // Create a super admin user
      console.log('Creating super admin user...')
      const superAdmin = await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Admin User',
          role: superAdminRole.id,
          school: demoSchool.id,
        },
      })

      console.log('Super admin created successfully')

      // Update the school with the admin
      await payload.update({
        collection: 'schools',
        id: demoSchool.id,
        data: {
          admin: superAdmin.id,
        },
      })

      console.log('School updated with admin')

      // Create a school admin user
      console.log('Creating school admin user...')
      await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'School Admin',
          role: schoolAdminRole.id,
          school: demoSchool.id,
        },
      })

      console.log('School admin created successfully')

      // Create a mentor user
      console.log('Creating mentor user...')
      const mentor = await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Mentor User',
          role: mentorRole.id,
          school: demoSchool.id,
        },
      })

      console.log('Mentor created successfully')

      // Create a teacher user
      console.log('Creating teacher user...')
      const teacher = await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Teacher User',
          role: teacherRole.id,
          school: demoSchool.id,
        },
      })

      console.log('Teacher created successfully')

      // Create a student user
      console.log('Creating student user...')
      const student = await payload.create({
        collection: 'users',
        data: {
          email: '<EMAIL>',
          password: 'Password123!',
          name: 'Student User',
          role: studentRole.id,
          school: demoSchool.id,
          grade: '10',
        },
      })

      console.log('Student created successfully')

      // Create a sample news post
      console.log('Creating sample news post...')
      await payload.create({
        collection: 'news',
        data: {
          title: 'Welcome to Young Reporter',
          content:
            "<p>Welcome to Young Reporter, a platform for student journalists to share their voices with the world. We're excited to have you join our community!</p><p>This platform allows students to write articles, receive feedback from teachers, and get their work published for all to see. Teachers can review and approve articles, while mentors can create news posts like this one.</p><p>Get started by exploring the articles section or, if you're a student, by writing your first article!</p>",
          slug: 'welcome-to-young-reporter',
          publishedAt: new Date().toISOString(),
          author: mentor.id,
          status: 'published',
        },
      })

      console.log('Sample news post created successfully')

      // Create a sample article
      console.log('Creating sample article...')
      const article = await payload.create({
        collection: 'articles',
        data: {
          title: 'My First Article',
          content:
            '<p>This is a sample article written by a student. In a real scenario, this would be a thoughtful piece on a topic the student is passionate about.</p><p>Articles go through a review process where teachers provide feedback and ratings. Once approved, they become visible to everyone on the platform.</p><p>This system helps students improve their writing skills while building a portfolio of published work.</p>',
          author: student.id,
          status: 'pending-review',
        },
      })

      console.log('Sample article created successfully')

      // Add a teacher review to the article
      console.log('Adding teacher review to article...')
      await payload.update({
        collection: 'articles',
        id: article.id,
        data: {
          teacherReview: [
            {
              reviewer: teacher.id,
              comment:
                'Great first article! I like your clear writing style. Consider adding more specific examples to strengthen your points.',
              rating: 8,
              approved: true,
            },
          ],
          status: 'published',
        },
      })

      console.log('Teacher review added successfully')
    }

    console.log('Seed completed successfully!')
  } catch (error) {
    console.error('Error seeding database:', error)
  }

  // Exit the process
  process.exit(0)
}

seed()
