'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Loader2, Eye, Trash, Check } from 'lucide-react'
import { showToast } from '@/lib/toast-utils'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

interface Report {
  id: string
  articleId: string
  articleTitle: string
  authorId: string
  reportedBy: string
  reason: string
  status: 'pending' | 'reviewed' | 'ignored'
  createdAt: string
  resolved: boolean
}

export default function ReportsPage() {
  const router = useRouter()
  const [reports, setReports] = useState<Report[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)
  const [isReportDetailsOpen, setIsReportDetailsOpen] = useState(false)
  const [userRole, setUserRole] = useState<string>('')
  const [debugInfo, setDebugInfo] = useState<Record<string, any>>({})
  const [showDebug, setShowDebug] = useState(false)

  // Fetch user role on component mount
  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        const res = await fetch('/api/auth/me', {
          credentials: 'include',
        })
        const data = await res.json()
        
        // Make sure the user is a school admin or super admin
        const role = data.role || ''
        setUserRole(role)
        
        // Redirect if not an admin
        if (role !== 'school-admin' && role !== 'super-admin') {
          router.push('/dashboard')
        }
      } catch (err) {
        console.error('Error fetching user role:', err)
        setError('Error fetching user role. Please try again.')
      }
    }
    fetchUserRole()
  }, [router])

  // Fetch reports on component mount
  useEffect(() => {
    const fetchReports = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/dashboard/reports', {
          credentials: 'include',
        })

        // Add debug information for error cases
        if (!response.ok) {
          console.error('Reports API Error:', {
            status: response.status,
            statusText: response.statusText
          });
          
          // Try to get more detailed error information
          try {
            const errorData = await response.json();
            console.error('Error data:', errorData);
            throw new Error(errorData.error || 'Failed to fetch reports');
          } catch (jsonError) {
            throw new Error(`Failed to fetch reports: ${response.status} ${response.statusText}`);
          }
        }

        const data = await response.json()
        
        if (data.success && data.reports) {
          console.log(`Loaded ${data.reports.length} reports`);
          setReports(data.reports)
        } else {
          throw new Error(data.error || 'Failed to fetch reports')
        }
      } catch (err) {
        console.error('Error fetching reports:', err)
        setError('Failed to load reports. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    if (userRole === 'school-admin' || userRole === 'super-admin') {
      fetchReports()
    }
  }, [userRole])

  // Collect debug info
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;
    
    const collectDebugInfo = async () => {
      try {
        // Get current user info
        const userRes = await fetch('/api/auth/me', { credentials: 'include' });
        const userData = await userRes.json();
        
        setDebugInfo({
          currentUser: userData,
          userRole,
          tokenExists: document.cookie.includes('payload-token'),
          browserInfo: navigator.userAgent,
          timestamp: new Date().toISOString()
        });
      } catch (e) {
        console.error('Error collecting debug info', e);
        setDebugInfo({ error: 'Failed to collect debug info' });
      }
    };
    
    collectDebugInfo();
  }, [userRole]);

  // Helper function for handling loading state in async operations
  const withLoading = async <T,>(action: string, fn: () => Promise<T>): Promise<T> => {
    setActionLoading(action)
    try {
      return await fn()
    } finally {
      setActionLoading(null)
    }
  }

  // Handle view article
  const handleViewArticle = async (articleId: string) => {
    await withLoading(`view-${articleId}`, async () => {
      router.push(`/dashboard/articles/${articleId}`)
    })
  }

  // Handle remove article
  const handleRemoveArticle = async (report: Report) => {
    if (!confirm('Are you sure you want to remove this article?')) return

    await withLoading(`remove-${report.id}`, async () => {
      try {
        const response = await fetch(`/api/dashboard/reports/${report.id}/remove-article`, {
          method: 'POST',
          credentials: 'include',
        })

        if (!response.ok) {
          throw new Error('Failed to remove article')
        }

        const data = await response.json()
        
        if (data.success) {
          showToast({
            title: 'Success',
            description: 'Article removed successfully',
          })
          
          // Update reports list
          setReports(reports.map(r => 
            r.id === report.id ? { ...r, status: 'reviewed', resolved: true } : r
          ))
        } else {
          throw new Error(data.error || 'Failed to remove article')
        }
      } catch (err) {
        console.error('Error removing article:', err)
        showToast({
          title: 'Error',
          description: 'Failed to remove article. Please try again.',
          variant: 'destructive',
        })
      }
    })
  }

  // Handle ignore report
  const handleIgnoreReport = async (report: Report) => {
    await withLoading(`ignore-${report.id}`, async () => {
      try {
        const response = await fetch(`/api/dashboard/reports/${report.id}/ignore`, {
          method: 'POST',
          credentials: 'include',
        })

        if (!response.ok) {
          throw new Error('Failed to ignore report')
        }

        const data = await response.json()
        
        if (data.success) {
          showToast({
            title: 'Success',
            description: 'Report ignored successfully',
          })
          
          // Update reports list
          setReports(reports.map(r => 
            r.id === report.id ? { ...r, status: 'ignored', resolved: true } : r
          ))
        } else {
          throw new Error(data.error || 'Failed to ignore report')
        }
      } catch (err) {
        console.error('Error ignoring report:', err)
        showToast({
          title: 'Error',
          description: 'Failed to ignore report. Please try again.',
          variant: 'destructive',
        })
      }
    })
  }

  // View report details
  const viewReportDetails = (report: Report) => {
    setSelectedReport(report)
    setIsReportDetailsOpen(true)
  }

  // Add debug information panel (only in development)
  const DebugPanel = process.env.NODE_ENV === 'development' ? (
    <div className="mt-8">
      <Button 
        variant="outline" 
        onClick={() => setShowDebug(!showDebug)}
        className="mb-2"
      >
        {showDebug ? 'Hide Debug Info' : 'Show Debug Info'}
      </Button>
      
      {showDebug && (
        <Card className="p-4 bg-slate-50 border border-slate-200">
          <CardHeader>
            <CardTitle className="text-sm font-mono">Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xs font-mono whitespace-pre overflow-auto max-h-96">
              {JSON.stringify(debugInfo, null, 2)}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  ) : null;

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">المقالات المُبلّغ عنها</h1>
          <Button 
            variant="outline" 
            onClick={() => router.refresh()}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              'تحديث'
            )}
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>جميع البلاغات</CardTitle>
          </CardHeader>
          <CardContent>
            {reports.length === 0 ? (
              <div className="text-center py-6 text-gray-500">
                لم يتم العثور على بلاغات
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>المقال</TableHead>
                    <TableHead>تاريخ الإبلاغ</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow key={report.id} className={report.resolved ? 'bg-gray-50' : ''}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{report.articleTitle}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {report.reason.length > 100 
                              ? `${report.reason.slice(0, 100)}...` 
                              : report.reason}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {new Date(report.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            report.status === 'reviewed'
                              ? 'bg-green-100 text-green-800'
                              : report.status === 'ignored'
                              ? 'bg-gray-100 text-gray-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {report.status === 'reviewed' ? 'تمت المراجعة' : 
                           report.status === 'ignored' ? 'تم التجاهل' : 'قيد الانتظار'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => viewReportDetails(report)}
                          >
                            التفاصيل
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewArticle(report.articleId)}
                            disabled={actionLoading === `view-${report.id}`}
                          >
                            {actionLoading === `view-${report.id}` ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                          {!report.resolved && (
                            <>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleRemoveArticle(report)}
                                disabled={actionLoading === `remove-${report.id}`}
                              >
                                {actionLoading === `remove-${report.id}` ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Trash className="h-4 w-4" />
                                )}
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleIgnoreReport(report)}
                                disabled={actionLoading === `ignore-${report.id}`}
                              >
                                {actionLoading === `ignore-${report.id}` ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Check className="h-4 w-4" />
                                )}
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Report Details Dialog */}
      {selectedReport && (
        <Dialog open={isReportDetailsOpen} onOpenChange={setIsReportDetailsOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تفاصيل البلاغ</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">المقال</h3>
                  <p className="text-base">{selectedReport.articleTitle}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">سبب الإبلاغ</h3>
                  <p className="text-base whitespace-pre-wrap">{selectedReport.reason}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">الحالة</h3>
                  <p className="text-base capitalize">
                    {selectedReport.status === 'reviewed' ? 'تمت المراجعة' : 
                     selectedReport.status === 'ignored' ? 'تم التجاهل' : 'قيد الانتظار'}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">تاريخ الإبلاغ</h3>
                  <p className="text-base">
                    {new Date(selectedReport.createdAt).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsReportDetailsOpen(false)}>
                إغلاق
              </Button>
              {!selectedReport.resolved && (
                <div className="flex space-x-2">
                  <Button
                    variant="destructive"
                    onClick={() => {
                      handleRemoveArticle(selectedReport)
                      setIsReportDetailsOpen(false)
                    }}
                  >
                    إزالة المقال
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleIgnoreReport(selectedReport)
                      setIsReportDetailsOpen(false)
                    }}
                  >
                    تجاهل البلاغ
                  </Button>
                </div>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {DebugPanel}
    </DashboardLayout>
  )
}
