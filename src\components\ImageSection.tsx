'use client'

import React from 'react'
import Image from 'next/image'
import im from '../../public/media/4.png'
import im2 from '../../public/media/10.png'
import im3 from '../../public/media/7.png'

interface ImageSectionProps {
  type: 'book' | 'users' | 'award'
  color: string
}

export const ImageSection: React.FC<ImageSectionProps> = ({ type, color }) => {
  // Define image paths based on type
  const getImagePath = () => {
    switch (type) {
      case 'book':
        return im // Replace with your actual image path
      case 'users':
        return im2 // Replace with your actual image path
      case 'award':
        return im3 // Replace with your actual image path
      default:
        return '/images/default-image.jpg' // Replace with your actual image path
    }
  }

  return (
    <div className="image-section-container">
      <div
        className="image-wrapper"
        style={{
          boxShadow: `0 10px 30px -5px ${color}80`,
          border: `2px solid ${color}`,
        }}
      >
        <Image
          src={getImagePath()}
          alt={`${type} illustration`}
          width={400}
          height={400}
          className="section-image"
          style={{
            objectFit: 'contain',
            borderRadius: '8px',
          }}
          // Fallback to placeholder if image doesn't exist
          onError={(e) => {
            const target = e.target as HTMLImageElement
            target.src = `https://placehold.co/400x400/0ea5e9/ffffff?text=${type.charAt(0).toUpperCase() + type.slice(1)}`
          }}
        />
      </div>
    </div>
  )
}
