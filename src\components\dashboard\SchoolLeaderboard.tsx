'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Award, Users, Trophy } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { useRouter } from 'next/navigation'

interface Leader {
  id: string
  name: string
  anonymizedName?: string
  score: number
  type: 'student' | 'teacher' | 'mentor'
}

interface SchoolLeaderboardProps {
  schoolId: string
}

export default function SchoolLeaderboard({ schoolId }: SchoolLeaderboardProps) {
  const router = useRouter()
  const [leaderboard, setLeaderboard] = useState<Leader[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('students')

  useEffect(() => {
    async function fetchLeaderboard() {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/dashboard/school-admin/leaderboard?schoolId=${schoolId}`)

        if (!response.ok) {
          throw new Error('فشل في جلب بيانات قائمة المتصدرين')
        }

        const data = await response.json()
        setLeaderboard(data.leaderboard || [])
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching leaderboard:', err)
        setError('فشل في تحميل بيانات قائمة المتصدرين')
        setIsLoading(false)
      }
    }

    fetchLeaderboard()
  }, [schoolId])

  const studentLeaders = leaderboard.filter((leader) => leader.type === 'student')
  const teacherLeaders = leaderboard.filter((leader) => leader.type === 'teacher')
  const mentorLeaders = leaderboard.filter((leader) => leader.type === 'mentor')

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center" dir="rtl">
            <Award className="ml-2 h-5 w-5" />
            قائمة المتصدرين في المدرسة
          </CardTitle>
        </CardHeader>
        <CardContent dir="rtl">
          <div className="bg-red-50 text-red-500 p-4 rounded-md">{error}</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center" dir="rtl">
          <Award className="ml-2 h-5 w-5" />
          قائمة المتصدرين في المدرسة
        </CardTitle>
      </CardHeader>
      <CardContent dir="rtl">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="students">الطلاب</TabsTrigger>
            <TabsTrigger value="teachers">المعلمون</TabsTrigger>
            <TabsTrigger value="mentors">الموجهون</TabsTrigger>
          </TabsList>

          <TabsContent value="students">
            {isLoading ? (
              <LeaderboardSkeleton />
            ) : studentLeaders.length > 0 ? (
              <LeaderboardTable leaders={studentLeaders} title="أفضل الطلاب" />
            ) : (
              <EmptyState message="لا توجد بيانات للطلاب" />
            )}
          </TabsContent>

          <TabsContent value="teachers">
            {isLoading ? (
              <LeaderboardSkeleton />
            ) : teacherLeaders.length > 0 ? (
              <LeaderboardTable leaders={teacherLeaders} title="أفضل المعلمين" />
            ) : (
              <EmptyState message="لا توجد بيانات للمعلمين" />
            )}
          </TabsContent>

          <TabsContent value="mentors">
            {isLoading ? (
              <LeaderboardSkeleton />
            ) : mentorLeaders.length > 0 ? (
              <LeaderboardTable leaders={mentorLeaders} title="أفضل الموجهين" />
            ) : (
              <EmptyState message="لا توجد بيانات للموجهين" />
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

function LeaderboardTable({ leaders, title }: { leaders: Leader[]; title: string }) {
  // School admins should always see real names, no anonymization needed
  const shouldAnonymize = false;
  
  return (
    <div>
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="text-right py-2 px-4">المرتبة</th>
              <th className="text-right py-2 px-4">الاسم</th>
              <th className="text-left py-2 px-4">النقاط</th>
            </tr>
          </thead>
          <tbody>
            {leaders.map((leader, index) => (
              <tr key={leader.id} className="border-b hover:bg-gray-50">
                <td className="py-2 px-4">
                  {index === 0 ? (
                    <Trophy className="h-5 w-5 text-yellow-500" />
                  ) : index === 1 ? (
                    <Trophy className="h-5 w-5 text-gray-400" />
                  ) : index === 2 ? (
                    <Trophy className="h-5 w-5 text-amber-700" />
                  ) : (
                    `#${index + 1}`
                  )}
                </td>
                <td className="py-2 px-4">
                  {leader.name || (leader.anonymizedName || `الطالب ${leader.id.substring(0, 4)}`)}
                </td>
                <td className="py-2 px-4 text-left font-semibold">{leader.score}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

function LeaderboardSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-6 w-40" />
      <div className="space-y-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded-full" />
              <Skeleton className="h-4 w-40" />
            </div>
            <Skeleton className="h-4 w-16" />
          </div>
        ))}
      </div>
    </div>
  )
}

function EmptyState({ message }: { message: string }) {
  return (
    <div className="flex flex-col items-center justify-center py-8 text-center">
      <Users className="h-12 w-12 text-gray-300 mb-4" />
      <p className="text-gray-500">{message}</p>
    </div>
  )
}
