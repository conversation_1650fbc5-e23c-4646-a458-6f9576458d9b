'use client'

import { useEffect, useState } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { School, Phone, Globe, MapPin, Mail, Users, FileText, Calendar, Edit, Save } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { formatDate } from '@/lib/date-utils'

interface SchoolData {
  id: string
  name: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  phone: string
  email: string
  website: string
  description: string
  logo?: string
  image?: {
    id: string
    url: string
    alt: string
  } | null
  imageUrl?: string
  createdAt: string
  updatedAt: string
  studentCount: number
  teacherCount: number
  mentorCount: number
  articleCount: number
}

export default function SchoolProfilePage() {
  const router = useRouter()
  const { toast } = useToast()
  const [schoolData, setSchoolData] = useState<SchoolData | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [editedData, setEditedData] = useState<Partial<SchoolData>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [isUploading, setIsUploading] = useState(false)

  useEffect(() => {
    async function fetchSchoolData() {
      try {
        setIsLoading(true)
        const response = await fetch('/api/dashboard/school-profile')
        
        const data = await response.json()
        
        if (!response.ok) {
          // Extract the error message from the server response if available
          const errorMessage = data.message || data.error || 'Failed to fetch school profile data'
          throw new Error(errorMessage)
        }
        
        setSchoolData(data.school)
        setEditedData(data.school)
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching school profile:', err)
        const errorMessage = err instanceof Error ? err.message : 'Failed to load school profile. Please try again.'
        setError(errorMessage)
        setIsLoading(false)
      }
    }
    
    fetchSchoolData()
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setEditedData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSave = async () => {
    try {
      setIsSaving(true)
      
      // Log what we're sending to help debug
      console.log('Sending updated school data:', editedData);
      
      const response = await fetch('/api/dashboard/school-profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editedData),
      })
      
      const data = await response.json();
      
      if (!response.ok) {
        console.error('Failed to update school profile:', data);
        throw new Error(data.message || data.error || 'Failed to update school profile');
      }
      
      console.log('Update response:', data);
      setSchoolData(data.school)
      setIsEditing(false)
      
      toast({
        title: 'Profile Updated',
        description: 'School profile has been successfully updated.',
      })
    } catch (err) {
      console.error('Error updating school profile:', err)
      toast({
        title: 'Update Failed',
        description: err instanceof Error ? err.message : 'Failed to update school profile. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;
    
    const file = e.target.files[0];
    if (!file) return;
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Invalid File',
        description: 'Please select an image file.',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      setIsUploading(true);
      
      const formData = new FormData();
      formData.append('image', file);
      
      const response = await fetch('/api/dashboard/school-profile/image', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload image');
      }
      
      const data = await response.json();
      console.log('Image upload response:', data);
      
      // Update the school data with the new image URL
      setSchoolData(prev => {
        if (!prev) return null;
        return { 
          ...prev, 
          imageUrl: data.school.imageUrl,
          // Keep other image data if it exists
          image: prev.image 
        };
      });
      
      toast({
        title: 'Image Uploaded',
        description: 'School image has been successfully updated.',
      });
    } catch (err) {
      console.error('Error uploading school image:', err);
      toast({
        title: 'Upload Failed',
        description: 'Failed to upload school image. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">ملف المدرسة</h1>
        </div>

        <Tabs defaultValue="profile" className="space-y-6"
        dir='rtl'>
          <TabsList>
            <TabsTrigger value="profile">الملف التعريفي</TabsTrigger>
            <TabsTrigger value="stats">الإحصائيات</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <School className="mr-2 h-5 w-5" />
                  معلومات المدرسة
                </CardTitle>
                <CardDescription>
                  عرض وتحديث معلومات المدرسة الأساسية
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col md:flex-row gap-6">
                  {/* School Image/Logo */}
                  <div className="flex flex-col items-center space-y-4 mb-6 md:mb-0">
                    <Avatar className="w-32 h-32">
                      <AvatarImage 
                        src={schoolData?.imageUrl || ''}
                        alt={schoolData?.name || 'School logo'}
                      />
                      <AvatarFallback className="text-2xl">
                        {schoolData?.name?.substring(0, 2) || 'SC'}
                      </AvatarFallback>
                    </Avatar>
                    {isEditing && (
                      <div className="flex flex-col items-center">
                        <Label htmlFor="logo-upload" className="cursor-pointer text-primary hover:underline">
                          {isUploading ? 'جاري الرفع...' : 'تغيير الشعار'}
                        </Label>
                        <Input 
                          id="logo-upload"
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleImageUpload}
                          disabled={isUploading}
                        />
                      </div>
                    )}
                  </div>

                  {/* School Information */}
                  <div className="flex-1 space-y-4">
                    {!isEditing ? (
                      <>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label className="text-muted-foreground">اسم المدرسة</Label>
                            <p className="text-lg font-medium">{schoolData?.name}</p>
                          </div>
                          <div>
                            <Label className="text-muted-foreground">البريد الإلكتروني</Label>
                            <div className="flex items-center">
                              <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                              <p>{schoolData?.email || 'غير متوفر'}</p>
                            </div>
                          </div>
                          <div>
                            <Label className="text-muted-foreground">الهاتف</Label>
                            <div className="flex items-center">
                              <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                              <p>{schoolData?.phone || 'غير متوفر'}</p>
                            </div>
                          </div>
                          <div>
                            <Label className="text-muted-foreground">الموقع الإلكتروني</Label>
                            <div className="flex items-center">
                              <Globe className="h-4 w-4 mr-2 text-muted-foreground" />
                              <p>
                                {schoolData?.website ? (
                                  <a 
                                    href={schoolData.website} 
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-primary hover:underline"
                                  >
                                    {schoolData.website}
                                  </a>
                                ) : (
                                  'غير متوفر'
                                )}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div>
                          <Label className="text-muted-foreground">العنوان</Label>
                          <div className="flex items-start mt-1">
                            <MapPin className="h-4 w-4 mr-2 text-muted-foreground mt-1" />
                            <p>
                              {schoolData?.address}
                              {schoolData?.city && schoolData?.address && <span>, </span>}
                              {schoolData?.city}
                              {schoolData?.state && <span>, </span>}
                              {schoolData?.state}
                              {schoolData?.zipCode && <span> </span>}
                              {schoolData?.zipCode}
                              {schoolData?.country && <span>, </span>}
                              {schoolData?.country}
                            </p>
                          </div>
                        </div>

                        <div>
                          <Label className="text-muted-foreground">الوصف</Label>
                          <p className="mt-1 whitespace-pre-wrap">
                            {schoolData?.description || 'لا يوجد وصف متاح.'}
                          </p>
                        </div>

                        <div>
                          <Label className="text-muted-foreground">تاريخ التسجيل</Label>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                            <p>{formatDate(schoolData?.createdAt || '')}</p>
                          </div>
                        </div>
                      </>
                    ) : (
                      <form className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="name">اسم المدرسة</Label>
                            <Input
                              id="name"
                              name="name"
                              value={editedData.name || ''}
                              onChange={handleInputChange}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="email">البريد الإلكتروني</Label>
                            <Input
                              id="email"
                              name="email"
                              type="email"
                              value={editedData.email || ''}
                              onChange={handleInputChange}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="phone">الهاتف</Label>
                            <Input
                              id="phone"
                              name="phone"
                              value={editedData.phone || ''}
                              onChange={handleInputChange}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="website">الموقع الإلكتروني</Label>
                            <Input
                              id="website"
                              name="website"
                              value={editedData.website || ''}
                              onChange={handleInputChange}
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="address">العنوان</Label>
                            <Input
                              id="address"
                              name="address"
                              value={editedData.address || ''}
                              onChange={handleInputChange}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="city">المدينة</Label>
                            <Input
                              id="city"
                              name="city"
                              value={editedData.city || ''}
                              onChange={handleInputChange}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="state">المحافظة/الولاية</Label>
                            <Input
                              id="state"
                              name="state"
                              value={editedData.state || ''}
                              onChange={handleInputChange}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="zipCode">الرمز البريدي</Label>
                            <Input
                              id="zipCode"
                              name="zipCode"
                              value={editedData.zipCode || ''}
                              onChange={handleInputChange}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="country">الدولة</Label>
                            <Input
                              id="country"
                              name="country"
                              value={editedData.country || ''}
                              onChange={handleInputChange}
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="description">الوصف</Label>
                          <Textarea
                            id="description"
                            name="description"
                            value={editedData.description || ''}
                            onChange={handleInputChange}
                            rows={5}
                          />
                        </div>
                      </form>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                {!isEditing ? (
                  <Button onClick={() => setIsEditing(true)} className="flex items-center">
                    <Edit className="mr-2 h-4 w-4" />
                    تعديل الملف التعريفي
                  </Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button variant="outline" onClick={() => setIsEditing(false)} disabled={isSaving}>
                      إلغاء
                    </Button>
                    <Button onClick={handleSave} disabled={isSaving} className="flex items-center">
                      {isSaving ? (
                        <>
                          <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full" />
                          جاري الحفظ...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          حفظ التغييرات
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="stats" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <FileText className="mr-2 h-5 w-5" />
                  إحصائيات المدرسة
                </CardTitle>
                <CardDescription>
                  معلومات حول مستخدمي ومقالات المدرسة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">الطلاب</p>
                          <p className="text-2xl font-bold">{schoolData?.studentCount || 0}</p>
                        </div>
                        <Users className="h-8 w-8 text-blue-500 opacity-80" />
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">المعلمون</p>
                          <p className="text-2xl font-bold">{schoolData?.teacherCount || 0}</p>
                        </div>
                        <Users className="h-8 w-8 text-green-500 opacity-80" />
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">الموجهون</p>
                          <p className="text-2xl font-bold">{schoolData?.mentorCount || 0}</p>
                        </div>
                        <Users className="h-8 w-8 text-purple-500 opacity-80" />
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">المقالات</p>
                          <p className="text-2xl font-bold">{schoolData?.articleCount || 0}</p>
                        </div>
                        <FileText className="h-8 w-8 text-amber-500 opacity-80" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
