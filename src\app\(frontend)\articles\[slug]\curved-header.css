@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400&display=swap');

/* Curved Header Styles */
.intro {
  position: relative;
  height: auto;
  transform: translateZ(0);
  background-image: linear-gradient(35deg, hsl(var(--primary)), hsl(var(--secondary)));
  overflow: hidden;
}

.intro img {
  position: relative;
  display: block;
  width: 100%;
  height: auto;
  z-index: 1;
  mix-blend-mode: multiply;
  object-fit: cover;
  max-height: 70vh;
}

.intro .caption {
  position: absolute;
  bottom: 25%;
  left: 0;
  width: 100%;
  text-align: center;
  z-index: 3;
  color: white;
  padding: 0 1rem;
}

.intro .caption h1 {
  display: inline-block;
  width: 90%;
  max-width: 1200px;
  font-size: clamp(2rem, 6vw, 5rem);
  font-weight: 300;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.intro .overlay {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.intro .overlay svg {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: auto;
}

/* Article Content Styles */
.article-content {
  width: 100%;
  max-width: 60rem;
  margin: 0 auto;
  padding: 3rem 1.5rem;
  color: hsl(var(--foreground));
  font-weight: 300;
}

.article-content .teaser {
  font-size: 1.4rem;
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 300;
  color: hsl(var(--primary));
  max-width: 50rem;
  margin-left: auto;
  margin-right: auto;
}

/* Article metadata */
.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid hsl(var(--border));
  font-size: 0.9rem;
  color: hsl(var(--muted-foreground));
}

.article-meta .author {
  font-weight: 500;
}

.article-meta .date {
  font-style: italic;
}

/* Teacher reviews section */
.teacher-reviews {
  margin-top: 3rem;
  padding: 1.5rem;
  background-color: hsl(var(--muted) / 30%);
  border-radius: 0.5rem;
  border-left: 4px solid hsl(var(--primary));
}

.teacher-reviews h2 {
  color: hsl(var(--primary));
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 500;
}

.review-item {
  padding: 1rem 0;
  border-bottom: 1px solid hsl(var(--border) / 50%);
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.review-rating {
  font-weight: bold;
  color: hsl(var(--primary));
}

.review-status {
  font-size: 0.85rem;
  padding: 0.2rem 0.5rem;
  border-radius: 0.25rem;
}

.status-approved {
  background-color: hsl(var(--success) / 20%);
  color: hsl(var(--success));
}

.status-not-approved {
  background-color: hsl(var(--destructive) / 20%);
  color: hsl(var(--destructive));
}

/* Share buttons */
.share-buttons {
  margin-top: 2rem;
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

/* Back link */
.back-link {
  display: block;
  margin-top: 3rem;
  text-align: center;
  color: hsl(var(--primary));
  font-weight: 500;
  text-decoration: none;
}

.back-link:hover {
  text-decoration: underline;
}

/* Status indicator */
.status-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background-color: hsl(var(--warning));
  color: hsl(var(--warning-foreground));
  padding: 0.5rem 1rem;
  text-align: center;
  font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .intro .caption h1 {
    width: 95%;
    font-size: clamp(1.5rem, 8vw, 3rem);
  }
  
  .article-content .teaser {
    font-size: 1.2rem;
  }
  
  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Rich text content styling */
.rich-text-content {
  line-height: 1.8;
}

.rich-text-content p {
  margin-bottom: 1.5rem;
}

.rich-text-content h2 {
  font-size: 1.8rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: hsl(var(--primary));
}

.rich-text-content h3 {
  font-size: 1.4rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: hsl(var(--primary));
}

.rich-text-content a {
  color: hsl(var(--primary));
  text-decoration: underline;
}

.rich-text-content blockquote {
  border-left: 3px solid hsl(var(--primary));
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.rich-text-content ul, 
.rich-text-content ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.rich-text-content li {
  margin-bottom: 0.5rem;
}

/* First letter styling */
.rich-text-content > div > p:first-of-type::first-letter {
  color: hsl(var(--primary));
  float: left;
  font-size: 5em;
  line-height: 0.8;
  margin-right: 0.1em;
  margin-top: 0.1em;
}
