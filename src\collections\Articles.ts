import type { CollectionConfig } from 'payload'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { articleAccess } from '../access'
import { notifyArticleSubmitted, notifyArticleReviewed } from '../utils/notifications'
import { checkAndAwardAchievements } from '../utils/achievements'
import { addUserPoints, updateUserRankings, updatePointsAndRankings } from '../utils/points'

export const Articles: CollectionConfig = {
  slug: 'articles',
  admin: {
    useAsTitle: 'title',
  },
  access: {
    read: articleAccess,
    create: articleAccess,
    update: articleAccess,
    delete: articleAccess,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'slug',
      type: 'text',
      unique: true,
      admin: {
        description: 'Auto-generated from title. Used in the URL.',
        position: 'sidebar',
      },
      hooks: {
        beforeValidate: [
          ({ data }) => {
            // Auto-generate slug from title if not provided
            if (data && data.title && !data.slug) {
              return data.title
                .toLowerCase()
                .replace(/[^\w\s]/g, '')
                .replace(/\s+/g, '-')
            }
            return data && data.slug ? data.slug : ''
          },
        ],
      },
    },
    {
      name: 'featuredImage',
      type: 'text',
      admin: {
        description: 'Featured image URL for the article (displayed at the top)',
      },
      hooks: {
        beforeChange: [
          ({ value }) => {
            // If it's null or undefined, just return it
            if (value === null || value === undefined) return value

            // If it's a string but doesn't include the path prefix, add it
            if (
              typeof value === 'string' &&
              !value.includes('/api/media/file/') &&
              !value.startsWith('/media/')
            ) {
              // Convert to path format
              return `/api/media/file/${value}`
            }

            // Otherwise return as is
            return value
          },
        ],
      },
    },
    {
      name: 'content',
      type: 'richText',
      editor: lexicalEditor({}),
      required: true,
    },
    {
      name: 'summary',
      type: 'text',
      required: false,
      maxLength: 200,
      admin: {
        description: 'Short summary of the article (max 200 characters)',
      },
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        readOnly: true,
        description: 'Author of the article (student only)',
        condition: (data, siblingData, { user }) => {
          if (user && user.role) {
            const role = typeof user.role === 'object' ? user.role.slug : user.role
            // Hide author from teachers
            return role !== 'teacher'
          }
          return true
        },
      },
      filterOptions: () => ({
        role: {
          equals: 'student',
        },
      }),
    },
    {
      name: 'authorAlias',
      type: 'text',
      admin: {
        readOnly: true,
        description: 'Anonymized author name',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Ready for Review', value: 'ready-for-review' },
        { label: 'Pending Review', value: 'pending-review' },
        { label: 'Published', value: 'published' },
      ],
    },
    {
      name: 'teacherReview',
      type: 'array',
      fields: [
        {
          name: 'reviewer',
          type: 'relationship',
          relationTo: 'users',
          required: true,
          filterOptions: () => {
            // Only allow teachers to be selected as reviewers
            return {
              role: {
                equals: 'teacher',
              },
            }
          },
        },
        {
          name: 'comment',
          type: 'textarea',
          required: true,
        },
        {
          name: 'rating',
          type: 'number',
          required: true,
          min: 1,
          max: 10,
        },
        {
          name: 'approved',
          type: 'checkbox',
          required: true,
          admin: {
            condition: (data, siblingData) => {
              return !!siblingData?.comment
            },
            description: 'Must add a comment before approving',
          },
        },
      ],
    },
    {
      name: 'views',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Number of times this article has been viewed',
        position: 'sidebar',
      },
    },
  ],
  hooks: {
    // Update status when student marks as ready for review
    beforeChange: [
      async ({ data, originalDoc, operation, req }) => {
        console.log('Articles beforeChange hook - incoming data:', data as any)
        console.log('Articles beforeChange hook - data.teacherReview:', (data as any).teacherReview)
        console.log('Articles beforeChange hook - data.author:', (data as any).author)
        // Handle relationship references that might be causing validation errors
        // When author is passed as a string ID but the relationship expects an object
        if (data.author && typeof data.author === 'string') {
          try {
            // Verify the author exists
            await req.payload.findByID({
              collection: 'users',
              id: data.author,
            })
            // Author is valid, no need to change
          } catch (error) {
            // If author lookup fails, maintain the original author
            if (originalDoc?.author) {
              data.author = originalDoc.author
            }
            console.log('Author validation failed, keeping original author')
          }
        }

        // Handle teacherReview relationship references
        if (data.teacherReview && Array.isArray(data.teacherReview)) {
          for (let i = 0; i < data.teacherReview.length; i++) {
            const review = data.teacherReview[i]
            // If reviewer is a string ID but the relationship expects an object
            if (review.reviewer && typeof review.reviewer === 'string') {
              try {
                // Fetch the User document
                const user = await req.payload.findByID({
                  collection: 'users',
                  id: review.reviewer,
                })
                // Replace the string ID with a minimal object for the relationship
                review.reviewer = { id: user.id, collection: 'users' }
                console.log('Reviewer populated successfully with minimal object:', review.reviewer)
              } catch (error) {
                console.error('Error fetching reviewer:', error)
              }
            }
          }
        }

        // If status changed from draft to ready-for-review
        if (originalDoc?.status === 'draft' && data.status === 'ready-for-review') {
          // Update to pending-review
          data.status = 'pending-review'
          // We'll send notifications in afterChange when we have the ID
        }

        // If any teacher review with approved=true, set status to published
        if (
          data.teacherReview &&
          data.teacherReview.some((review: { approved: boolean }) => review.approved)
        ) {
          data.status = 'published'
          // We'll send notifications in afterChange when we have the ID
        }

        if (operation === 'create' || operation === 'update') {
          let author = data.author as
            | string
            | { relationTo: string; value: string }
            | { id: string }

          // Handle different author formats
          if (typeof author === 'object' && author !== null) {
            if ('value' in author) {
              author = author.value
            } else if ('id' in author) {
              author = author.id
            }
          }

          // Only proceed if we have a valid string ID
          if (typeof author === 'string' && author) {
            const user = await req.payload.findByID({
              collection: 'users',
              id: author,
            })

            if (user && user.school) {
              const schoolId = typeof user.school === 'object' ? user.school.id : user.school
              const salt = `school-specific-secret-${schoolId}`
              const authorAlias = `Student_${Math.random().toString(36).substring(2, 8)}`
              data.authorAlias = authorAlias
            }
          }
        }

        return data
      },
    ],
    // Update statistics and send notifications after article changes
    afterChange: [
      async ({ doc, req, operation, previousDoc }) => {
        // Update summary statistics regardless of article status
        try {
          const { payload } = req

          // Get counts for summary statistics
          const totalArticles = await payload.count({
            collection: 'articles',
          })

          const publishedArticles = await payload.count({
            collection: 'articles',
            where: {
              status: {
                equals: 'published',
              },
            },
          })

          const totalStudents = await payload.count({
            collection: 'users',
            where: {
              'role.slug': {
                equals: 'student',
              },
            },
          })

          const totalTeachers = await payload.count({
            collection: 'users',
            where: {
              'role.slug': {
                equals: 'teacher',
              },
            },
          })

          const totalMentors = await payload.count({
            collection: 'users',
            where: {
              'role.slug': {
                equals: 'mentor',
              },
            },
          })

          const totalSchools = await payload.count({
            collection: 'schools',
          })

          // Find existing summary or create new one
          const existingSummary = await payload.find({
            collection: 'statistics',
            where: {
              type: {
                equals: 'summary',
              },
            },
          })

          if (existingSummary.docs.length > 0) {
            // Update existing
            await payload.update({
              collection: 'statistics',
              id: existingSummary.docs[0].id,
              data: {
                data: {
                  totalArticles,
                  publishedArticles,
                  totalStudents,
                  totalTeachers,
                  totalMentors,
                  totalSchools,
                },
              },
            })
          } else {
            // Create new
            await payload.create({
              collection: 'statistics',
              data: {
                name: 'Summary Statistics',
                type: 'summary',
                data: {
                  totalArticles,
                  publishedArticles,
                  totalStudents,
                  totalTeachers,
                  totalMentors,
                  totalSchools,
                },
              },
            })
          }
        } catch (error) {
          console.error('Error updating summary statistics:', error)
        }

        // Only update detailed stats for published articles
        if (doc.status === 'published') {
          try {
            const { payload } = req

            // Get all articles with populated author and school
            const articles = await payload.find({
              collection: 'articles',
              where: {
                status: {
                  equals: 'published',
                },
              },
              depth: 2, // Populate relationships
            })

            // Count published articles per school
            interface SchoolCount {
              count: number
              name: string
            }

            const schoolCounts: Record<string, SchoolCount> = {}

            articles.docs.forEach((article) => {
              if (article.author && typeof article.author === 'object' && article.author.school) {
                const schoolId =
                  typeof article.author.school === 'object'
                    ? article.author.school.id
                    : (article.author.school as string)

                if (!schoolCounts[schoolId]) {
                  schoolCounts[schoolId] = {
                    count: 0,
                    name:
                      typeof article.author.school === 'object'
                        ? article.author.school.name
                        : 'Unknown School',
                  }
                }

                schoolCounts[schoolId].count++
              }
            })

            // Convert to array and sort
            const rankings = Object.entries(schoolCounts)
              .map(([id, data]: [string, SchoolCount]) => ({
                id,
                name: data.name,
                count: data.count,
              }))
              .sort((a, b) => b.count - a.count)

            // Find existing school ranking or create new one
            const existingStats = await payload.find({
              collection: 'statistics',
              where: {
                type: {
                  equals: 'schoolRanking',
                },
              },
            })

            if (existingStats.docs.length > 0) {
              // Update existing
              await payload.update({
                collection: 'statistics',
                id: existingStats.docs[0].id,
                data: {
                  data: rankings,
                },
              })
            } else {
              // Create new
              await payload.create({
                collection: 'statistics',
                data: {
                  name: 'School Rankings',
                  type: 'schoolRanking',
                  data: rankings,
                },
              })
            }

            // Update student rankings
            // Calculate average rating per student
            interface StudentStat {
              totalRating: number
              articleCount: number
              name: string
            }

            const studentStats: Record<string, StudentStat> = {}

            articles.docs.forEach((article) => {
              if (article.author && article.teacherReview && article.teacherReview.length > 0) {
                const studentId =
                  typeof article.author === 'object'
                    ? article.author.id
                    : (article.author as string)

                if (!studentStats[studentId]) {
                  studentStats[studentId] = {
                    totalRating: 0,
                    articleCount: 0,
                    name:
                      typeof article.author === 'object' ? article.author.email : 'Unknown Student',
                  }
                }

                // Calculate average rating for this article
                const ratings = article.teacherReview.map(
                  (review: { rating: number }) => review.rating,
                )
                const avgRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length

                studentStats[studentId].totalRating += avgRating
                studentStats[studentId].articleCount++
              }
            })

            // Calculate average rating and convert to array
            const studentRankings = Object.entries(studentStats)
              .map(([id, data]: [string, StudentStat]) => ({
                id,
                name: data.name,
                articleCount: data.articleCount,
                averageRating: data.totalRating / data.articleCount,
              }))
              .sort((a, b) => b.averageRating - a.averageRating)

            // Find existing student ranking or create new one
            const existingStudentStats = await payload.find({
              collection: 'statistics',
              where: {
                type: {
                  equals: 'studentRanking',
                },
              },
            })

            if (existingStudentStats.docs.length > 0) {
              // Update existing
              await payload.update({
                collection: 'statistics',
                id: existingStudentStats.docs[0].id,
                data: {
                  data: studentRankings,
                },
              })
            } else {
              // Create new
              await payload.create({
                collection: 'statistics',
                data: {
                  name: 'Student Rankings',
                  type: 'studentRanking',
                  data: studentRankings,
                },
              })
            }

            // Update teacher rankings
            // Count reviews per teacher
            interface TeacherStat {
              reviewCount: number
              name: string
            }

            const teacherStats: Record<string, TeacherStat> = {}

            articles.docs.forEach((article) => {
              if (article.teacherReview && article.teacherReview.length > 0) {
                article.teacherReview.forEach(
                  (review: { reviewer?: { id: string; email: string } | string }) => {
                    if (review.reviewer) {
                      const teacherId =
                        typeof review.reviewer === 'object'
                          ? review.reviewer.id
                          : (review.reviewer as string)

                      if (!teacherStats[teacherId]) {
                        teacherStats[teacherId] = {
                          reviewCount: 0,
                          name:
                            typeof review.reviewer === 'object'
                              ? review.reviewer.email
                              : 'Unknown Teacher',
                        }
                      }

                      teacherStats[teacherId].reviewCount++
                    }
                  },
                )
              }
            })

            // Convert to array and sort
            const teacherRankings = Object.entries(teacherStats)
              .map(([id, data]: [string, TeacherStat]) => ({
                id,
                name: data.name,
                reviewCount: data.reviewCount,
              }))
              .sort((a, b) => b.reviewCount - a.reviewCount)

            // Find existing teacher ranking or create new one
            const existingTeacherStats = await payload.find({
              collection: 'statistics',
              where: {
                type: {
                  equals: 'teacherRanking',
                },
              },
            })

            if (existingTeacherStats.docs.length > 0) {
              // Update existing
              await payload.update({
                collection: 'statistics',
                id: existingTeacherStats.docs[0].id,
                data: {
                  data: teacherRankings,
                },
              })
            } else {
              // Create new
              await payload.create({
                collection: 'statistics',
                data: {
                  name: 'Teacher Rankings',
                  type: 'teacherRanking',
                  data: teacherRankings,
                },
              })
            }
          } catch (error) {
            console.error('Error updating statistics:', error)
          }
        }

        // Send notifications based on status changes
        try {
          const { payload } = req

          // If article was just submitted for review
          if (
            operation === 'create' ||
            (previousDoc?.status !== 'pending-review' && doc.status === 'pending-review')
          ) {
            // Notify teachers about new article for review
            await notifyArticleSubmitted({
              payload,
              articleId: doc.id,
              articleTitle: doc.title,
              authorId: typeof doc.author === 'object' ? doc.author.id : doc.author,
            })
          }

          // If article was just published (approved by teacher)
          if (previousDoc?.status !== 'published' && doc.status === 'published') {
            // Find the teacher review that approved the article
            const approvedReview = doc.teacherReview?.find(
              (review: { approved: boolean }) => review.approved,
            )

            if (approvedReview) {
              // Get the student ID
              const studentId = typeof doc.author === 'object' ? doc.author.id : doc.author

              // Notify student about article approval
              await notifyArticleReviewed({
                payload,
                articleId: doc.id,
                articleTitle: doc.title,
                studentId,
                approved: true,
              })

              // Check if student has earned any achievements
              await checkAndAwardAchievements({
                payload,
                userId: studentId,
              })

              // If there's a teacher who reviewed, check their achievements too
              if (approvedReview.reviewer) {
                const teacherId =
                  typeof approvedReview.reviewer === 'object'
                    ? approvedReview.reviewer.id
                    : approvedReview.reviewer

                await checkAndAwardAchievements({
                  payload,
                  userId: teacherId,
                })
              }
            }
          }
        } catch (error) {
          console.error('Error sending notifications:', error)
        }

        // Update points when an article changes
        try {
          if (!req.payload) return

          // When a new article is created
          if (operation === 'create') {
            // Award points to the author for creating an article
            const authorId = typeof doc.author === 'object' ? doc.author.id : doc.author

            if (authorId) {
              // Add points for creating an article (5 points)
              await addUserPoints({
                payload: req.payload,
                userId: authorId,
                type: 'article_created',
                points: 5,
                description: `Created article "${doc.title || 'Untitled'}"`,
                reference: {
                  collection: 'articles',
                  id: doc.id,
                },
              })

              // Update rankings after point changes
              await updateUserRankings({
                payload: req.payload,
                role: 'student',
              })

              // Update statistics
              await updatePointsAndRankings({
                payload: req.payload,
              })
            }
          }

          // When an article is published
          if (previousDoc?.status !== 'published' && doc.status === 'published') {
            // Award points to the author for getting published
            const authorId = typeof doc.author === 'object' ? doc.author.id : doc.author

            if (authorId) {
              // Add points for article being published (15 points)
              await addUserPoints({
                payload: req.payload,
                userId: authorId,
                type: 'article_published',
                points: 15,
                description: `Article "${doc.title || 'Untitled'}" was published`,
                reference: {
                  collection: 'articles',
                  id: doc.id,
                },
              })

              // Update rankings after point changes
              await updateUserRankings({
                payload: req.payload,
                role: 'student',
              })

              // Update statistics
              await updatePointsAndRankings({
                payload: req.payload,
              })
            }

            // Add rating points if there are reviews with ratings
            if (doc.teacherReview && Array.isArray(doc.teacherReview)) {
              for (const review of doc.teacherReview) {
                if (typeof review === 'object' && review.rating) {
                  // Add points based on rating (2 points per rating point)
                  const ratingPoints = Math.round(review.rating * 2)

                  await addUserPoints({
                    payload: req.payload,
                    userId: authorId,
                    type: 'rating_received',
                    points: ratingPoints,
                    description: `Received rating of ${review.rating}/10 for article "${doc.title || 'Untitled'}"`,
                    reference: {
                      collection: 'articles',
                      id: doc.id,
                      review: review.id,
                    },
                  })
                }
              }
            }
          }

          // When a new review is added
          if (
            doc.teacherReview &&
            Array.isArray(doc.teacherReview) &&
            (!previousDoc?.teacherReview ||
              !Array.isArray(previousDoc.teacherReview) ||
              doc.teacherReview.length > previousDoc.teacherReview.length)
          ) {
            // Find the new review(s) by comparing with previous doc
            const newReviews = doc.teacherReview.filter((current: any) => {
              const currentId = typeof current === 'object' ? current.id : null

              // If no previous reviews, all are new
              if (!previousDoc?.teacherReview || !Array.isArray(previousDoc.teacherReview)) {
                return true
              }

              // Check if this review exists in the previous document
              return !previousDoc.teacherReview.some((prev: any) => {
                const prevId = typeof prev === 'object' ? prev.id : null
                return currentId && prevId && currentId === prevId
              })
            })

            // Award points for each new review
            for (const review of newReviews) {
              if (typeof review === 'object' && review.reviewer) {
                const reviewerId =
                  typeof review.reviewer === 'object' ? review.reviewer.id : review.reviewer

                // Add points for submitting a review (10 points)
                await addUserPoints({
                  payload: req.payload,
                  userId: reviewerId,
                  type: 'review_submitted',
                  points: 10,
                  description: `Submitted review for article "${doc.title || 'Untitled'}"`,
                  reference: {
                    collection: 'articles',
                    id: doc.id,
                    review: review.id,
                  },
                })
              }
            }

            // Update rankings after point changes
            await updateUserRankings({
              payload: req.payload,
              role: 'teacher',
            })

            // Update statistics
            await updatePointsAndRankings({
              payload: req.payload,
            })
          }
        } catch (error) {
          console.error('Error updating points after article change:', error)
        }
      },
    ],
  },
}
