'use client'

import Link from 'next/link'
import React from 'react'

import { useNotifications } from '@/components/NotificationProvider'

export const ClientNotificationsPage: React.FC = () => {
  const { notifications, loading, error, markAsRead } = useNotifications()

  // Get background color based on notification type
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-100 border-green-500'
      case 'warning':
        return 'bg-yellow-100 border-yellow-500'
      case 'error':
        return 'bg-red-100 border-red-500'
      default:
        return 'bg-blue-100 border-blue-500'
    }
  }

  if (loading) {
    return (
      <div className="py-12 px-4">
        <div className="container mx-auto">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="p-6 text-center">
              <p className="text-gray-500">Loading notifications...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="py-12 px-4">
        <div className="container mx-auto">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="p-6 text-center">
              <p className="text-red-500">{error}</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="py-12 px-4">
      <div className="container mx-auto">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-6">All Notifications</h2>

            {notifications.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">You have no notifications.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`border-l-4 ${getTypeColor(notification.type)} p-4 rounded-md ${
                      !notification.read ? 'bg-gray-50' : ''
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-gray-800">{notification.message}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(notification.createdAt).toLocaleString()}
                        </p>
                      </div>
                      {!notification.read && (
                        <button
                          onClick={() => markAsRead(notification.id)}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Mark as read
                        </button>
                      )}
                    </div>
                    {notification.link && (
                      <div className="mt-2">
                        <Link
                          href={notification.link}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          View Details →
                        </Link>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
