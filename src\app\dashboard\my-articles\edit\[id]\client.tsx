'use client'

import { useEffect, useState } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'

import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { getArticleId } from '@/lib/id-utils'
import {
  extractPlainText,
  createRichTextFromPlain,
  renderRichText,
  isRichTextObject,
} from '@/utils/richTextUtils'
import { EnhancedEditor, textToLexical, lexicalToText } from '@/components/ui/EnhancedEditor'
import { ImageUpload } from '@/components/ui/ImageUpload'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Loader2, <PERSON><PERSON>ef<PERSON> } from 'lucide-react'
import Link from 'next/link'
import { useToast } from '@/components/ui/use-toast'

interface Article {
  id: string
  title: string
  content:
    | {
        [key: string]: unknown
      }
    | string
  summary?: string
  status: string
  category?: string
  tags?: string[] | string
  featuredImage?: {
    url: string
    alt?: string
    [key: string]: unknown
  }
  author?: {
    id: string
    firstName?: string
    lastName?: string
    email?: string
    [key: string]: unknown
  }
  slug?: string
  createdAt: string
  updatedAt?: string
}

interface ArticleEditClientProps {
  id: string
}

export default function ArticleEditClient({ id }: ArticleEditClientProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [article, setArticle] = useState<Article | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    summary: '',
    status: 'draft',
    category: '',
    tags: '',
    featuredImage: '',
  })

  // Function to fetch article data
  const fetchArticle = async () => {
    try {
      console.log('Fetching article with ID:', id)
      setIsLoading(true)
      setError('')

      // First check if we're authenticated
      const authResponse = await fetch('/api/auth/me', {
        credentials: 'include',
      })

      if (!authResponse.ok) {
        console.error('Auth check failed:', await authResponse.text())
        throw new Error('Authentication failed')
      }

      const authData = await authResponse.json()
      console.log('Auth check successful, user:', authData.user ? 'found' : 'not found')

      if (!authData.user) {
        throw new Error('User not authenticated')
      }

      // Now fetch the article
      console.log('Fetching article data...')
      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime()
      const response = await fetch(`/api/articles/${id}?t=${timestamp}`, {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
      } as RequestInit)

      console.log('Article API response status:', response.status)

      if (!response.ok) {
        let errorMessage = `Failed to fetch article: ${response.status}`
        try {
          const errorData = await response.json()
          console.error('Error response:', errorData)
          errorMessage = errorData.error || errorMessage
        } catch (parseError) {
          const errorText = await response.text()
          console.error('Error response text:', errorText)
          errorMessage = errorText || errorMessage
        }
        throw new Error(errorMessage)
      }

      const data = await response.json()
      console.log('Article data received:', data)

      if (!data.article) {
        throw new Error('Article not found')
      }

      setArticle(data.article)

      // Extract plain text from rich text content if needed
      let contentText = ''
      if (data.article.content) {
        if (isRichTextObject(data.article.content)) {
          // Convert Lexical format to plain text
          contentText = lexicalToText(data.article.content)
          console.log('Converted Lexical content to plain text')
        } else if (typeof data.article.content === 'string') {
          contentText = data.article.content
          console.log('Content is already a string')
        } else {
          console.log('Unknown content format:', typeof data.article.content)
          contentText = JSON.stringify(data.article.content)
        }
      }

      // Get featured image URL if it exists
      let featuredImageUrl = ''
      if (data.article.featuredImage) {
        if (typeof data.article.featuredImage === 'object') {
          featuredImageUrl = data.article.featuredImage.url || ''
        } else {
          featuredImageUrl = data.article.featuredImage || ''
        }
      }

      setFormData({
        title: data.article.title || '',
        content: contentText,
        summary: data.article.summary || '',
        status: data.article.status || 'draft',
        category: data.article.category || '',
        tags: Array.isArray(data.article.tags)
          ? data.article.tags.join(', ')
          : data.article.tags || '',
        featuredImage: featuredImageUrl,
      })
    } catch (err) {
      console.error('Error fetching article:', err)
      setError('Failed to load article. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Check if ID is a media path
  const isMediaPath = (path: string): boolean => {
    if (!path || typeof path !== 'string') return false

    return (
      path.includes('/api/media') ||
      path.includes('/media') ||
      path.includes('/file/') ||
      /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(path)
    )
  }

  // Fetch article data on component mount
  useEffect(() => {
    if (id && id !== 'undefined' && !isMediaPath(id)) {
      fetchArticle()
    } else {
      setIsLoading(false)
      if (isMediaPath(id)) {
        setError(
          'Invalid article ID: Media path detected. Please go back and select a valid article.',
        )
        console.error('Invalid article ID (media path):', id)
      } else {
        setError('Invalid article ID. Please go back and select a valid article.')
        console.error('Invalid article ID:', id)
      }
    }
  }, [id])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleRichTextChange = (value: string) => {
    setFormData((prev) => ({ ...prev, content: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleImageChange = (url: string) => {
    setFormData((prev) => ({ ...prev, featuredImage: url }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Prevent editing published articles
    if (article?.status === 'published') {
      setError('Published articles cannot be edited')
      return
    }

    setIsSaving(true)
    setError('')
    setSuccess('')

    try {
      // Prepare tags as array
      const tagsArray = formData.tags
        .split(',')
        .map((tag) => tag.trim())
        .filter((tag) => tag)

      // Convert plain text content to Lexical rich text format
      const richTextContent = textToLexical(formData.content)
      console.log('Created rich text content from plain text')

      console.log('Submitting article update with ID:', id)
      console.log('Content preview:', formData.content.substring(0, 100))

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime()

      // Prepare the request body
      const requestBody = {
        ...formData,
        content: richTextContent, // Use the rich text format
        tags: tagsArray,
        featuredImage: formData.featuredImage || null,
      }

      console.log('Sending PUT request to:', `/api/articles/${id}?t=${timestamp}`)
      console.log('Request body:', JSON.stringify(requestBody).substring(0, 200) + '...')

      const response = await fetch(`/api/articles/${id}?t=${timestamp}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody),
      })

      console.log('Response status:', response.status)

      if (!response.ok) {
        let errorMessage = `Failed to update article: ${response.status}`
        try {
          const errorData = await response.json()
          console.error('Error response:', errorData)
          errorMessage = errorData.error || errorMessage
        } catch (parseError) {
          const errorText = await response.text()
          console.error('Error response text:', errorText)
          errorMessage = errorText || errorMessage
        }
        throw new Error(errorMessage)
      }

      // Try to parse the response as JSON
      let responseData
      try {
        responseData = await response.json()
        console.log('Update response data:', responseData)

        if (!responseData.success) {
          throw new Error(responseData.error || 'Update failed on the server')
        }

        setSuccess('Article updated successfully')

        // If status changed to pending-review, show different message
        if (formData.status === 'pending-review' && article.status !== 'pending-review') {
          setSuccess('Article submitted for review')
          toast({
            title: 'Success',
            description: 'Article submitted for review',
          })
        } else {
          toast({
            title: 'Success',
            description: 'Article updated successfully',
          })
        }

        // Update the article state with the response data
        if (responseData.article) {
          setArticle(responseData.article)
        } else {
          // If no article in response, refresh the data
          await fetchArticle()
        }
      } catch (parseError) {
        console.error('Error parsing response:', parseError)
        // Even if we can't parse the response, consider it a success if status was OK
        setSuccess('Article updated successfully, but there was an issue refreshing the data')
      }
    } catch (err) {
      console.error('Error updating article:', err)
      const errorMessage = 'Failed to update article. Please try again.'
      setError(errorMessage)
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <Link href="/dashboard/my-articles" className="mr-4">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Articles
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Edit Article</h1>
      </div>

      {article?.status === 'published' && (
        <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 rounded mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium">
                This article has been published and is now in read-only mode.
              </p>
              <p className="text-xs mt-1">
                Published articles cannot be edited to maintain the integrity of the review process.
                You can view the article but cannot make changes.
              </p>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
          {(id === 'undefined' || !id) && (
            <div className="mt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dashboard/my-articles')}
              >
                Return to My Articles
              </Button>
            </div>
          )}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Article Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                required
                disabled={article?.status === 'published'}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="summary">Summary</Label>
              <Textarea
                id="summary"
                name="summary"
                value={formData.summary}
                onChange={handleInputChange}
                placeholder="Brief summary of your article"
                rows={3}
                disabled={article?.status === 'published'}
              />
            </div>

            <div className="space-y-2">
              <ImageUpload
                value={formData.featuredImage}
                onChange={handleImageChange}
                disabled={article?.status === 'published'}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <EnhancedEditor
                value={formData.content}
                onChange={handleRichTextChange}
                placeholder="Write your article here"
                className="mb-4"
                readOnly={article?.status === 'published'}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Input
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  placeholder="e.g. News, Sports, Opinion"
                  disabled={article?.status === 'published'}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  name="tags"
                  value={formData.tags}
                  onChange={handleInputChange}
                  placeholder="Comma-separated tags"
                  disabled={article?.status === 'published'}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange('status', value)}
                disabled={article?.status === 'published'}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="pending-review">Submit for Review</SelectItem>
                  {article?.status === 'published' && (
                    <SelectItem value="published">Published</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {article?.status === 'published' ? (
                <div className="bg-amber-100 border border-amber-400 text-amber-700 px-4 py-3 rounded mt-2">
                  <p className="font-medium">Published Article - Locked for Editing</p>
                  <p className="text-sm mt-1">
                    Once an article is published, it cannot be edited. This ensures the integrity of
                    the review process and maintains consistency in published content. If you need
                    to make important corrections, please contact your teacher directly.
                  </p>
                </div>
              ) : (
                formData.status === 'pending-review' && (
                  <p className="text-sm text-amber-600">
                    Your article will be submitted for review by a teacher.
                  </p>
                )
              )}
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/dashboard/my-articles')}
              >
                Cancel
              </Button>

              {article && (
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => {
                    const articleId = getArticleId(article)
                    if (articleId) {
                      window.open(`/articles/${articleId}`, '_blank')
                    } else {
                      console.error('Cannot preview: Article has no valid ID', article)
                      alert('Cannot preview: Article has no valid ID')
                    }
                  }}
                >
                  Preview
                </Button>
              )}

              <Button
                type="button"
                variant="default"
                disabled={isSaving || article?.status === 'published'}
                onClick={async (e) => {
                  await handleSubmit(e)
                  // Show a success message
                  setSuccess('Article saved successfully. Redirecting to dashboard...')
                  // Show toast notification
                  toast({
                    title: 'Success',
                    description: 'Article saved successfully. Redirecting to dashboard...',
                  })
                  // Wait a moment for the save to complete, then redirect
                  setTimeout(() => {
                    router.push('/dashboard/my-articles')
                  }, 1500)
                }}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save & Return'
                )}
              </Button>

              <Button type="submit" disabled={isSaving || article?.status === 'published'}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : article?.status === 'published' ? (
                  'Cannot Edit Published Article'
                ) : (
                  'Save Changes'
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
