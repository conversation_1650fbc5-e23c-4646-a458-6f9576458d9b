import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ApiResponse, ArticlesResponse } from '@/types'
import { ObjectId } from 'mongodb'

// Helper function for standardized error responses
function errorResponse(error: string, status: number): NextResponse<ApiResponse<null>> {
  return NextResponse.json(
    {
      success: false,
      error,
    },
    { status },
  )
}

// Helper function for standardized success responses
function successResponse<T>(data: T): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
  })
}

// Helper to check if we're in development mode
function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test'
}

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    // Also check for token in Authorization header
    const authHeader = req.headers.get('Authorization')
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null

    // Use token from cookie or header
    const finalToken = token || headerToken

    // For development, if token is not available, use a fallback approach
    // In production, this should be removed and proper authentication enforced
    if (!finalToken) {
      if (isDevelopment()) {
        console.warn(
          'No token found in cookies or headers, proceeding without authentication (development only)',
        )
      } else {
        // In production, require authentication
        return errorResponse('Unauthorized', 401)
      }
    }

    // If we have a token, verify it
    let userId: string | null = null
    let userRole: string | null = null
    if (finalToken) {
      try {
        const decoded = jwt.verify(finalToken, process.env.PAYLOAD_SECRET || 'secret') as any
        userId = typeof decoded === 'object' ? decoded.id : null
        userRole = typeof decoded === 'object' ? decoded.role : null

        if (!userId && !isDevelopment()) {
          return errorResponse('Unauthorized', 401)
        }
      } catch (tokenError) {
        console.error('Token verification error:', tokenError)
        if (!isDevelopment()) {
          return errorResponse('Unauthorized', 401)
        }
      }
    }

    // Get depth parameter from URL
    const url = new URL(req.url)
    const depthParam = url.searchParams.get('depth')
    const depth = depthParam ? parseInt(depthParam, 10) : 1

    // Try to get articles from MongoDB first
    try {
      const { db } = await connectToDatabase()
      
      // Create query based on user role
      let query = {};
      
      // For teachers, only show pending-review and published articles
      if (userRole === 'teacher') {
        query = { status: { $in: ['pending-review', 'published'] } };
      }
      
      const mongoArticles = await db.collection('articles').find(query).toArray()

      if (mongoArticles && mongoArticles.length > 0) {
        console.log('Fetched articles from MongoDB:', mongoArticles.length)
        // Convert MongoDB documents to our Article type
        const articles = await Promise.all(mongoArticles.map(async (doc) => {
          // Debug the author field
          console.log(`Article ${doc._id}: Author type:`, typeof doc.author, 'Value:', doc.author);
          
          // Try to get author information
          let authorInfo: any = doc.author || 'Unknown';
          let authorLookupAttempted = false;
          
          // Handle native MongoDB ObjectId instances directly
          if (doc.author && doc.author.constructor && doc.author.constructor.name === 'ObjectId') {
            try {
              authorLookupAttempted = true;
              console.log(`Processing native ObjectId author: ${doc.author.toString()}`);
              
              // Convert to string ID for lookup
              const authorId = doc.author.toString();
              
              // Try to find the user
              const user = await db.collection('users').findOne({
                _id: doc.author // Use the ObjectId directly
              });
              
              if (user) {
                console.log(`Found user for ObjectId: ${user.firstName} ${user.lastName}, Grade: ${user.grade || 'N/A'}`);
                
                // Log exact user data to see if grade is present
                console.log(`User data for native ObjectId:`, JSON.stringify({
                  id: user._id.toString(),
                  firstName: user.firstName,
                  lastName: user.lastName,
                  grade: user.grade
                }));
                
                authorInfo = {
                  id: user._id.toString(),
                  firstName: user.firstName || '',
                  lastName: user.lastName || '',
                  profileImage: user.profileImage,
                  role: user.role,
                  grade: user.grade || 'N/A',
                  email: user.email || '<EMAIL>',
                  createdAt: user.createdAt || new Date().toISOString(),
                  updatedAt: user.updatedAt || undefined
                };
                
                console.log(`Final grade for native ObjectId: ${authorInfo.grade}`);
              } else {
                console.log(`No user found for native ObjectId: ${authorId}`);
              }
            } catch (error) {
              console.error(`Error processing native ObjectId author for article ${doc._id}:`, error);
            }
          }
          // Check for string ID
          else if (doc.author && typeof doc.author === 'string' && doc.author !== 'Unknown') {
            try {
              authorLookupAttempted = true;
              console.log(`Looking up author with string ID: ${doc.author}`);
              
              // Try to find the user by ID, carefully handling different ID formats
              try {
                let user;
                
                // If string ID looks like a valid ObjectId, try that first
                if (/^[0-9a-fA-F]{24}$/.test(doc.author)) {
                  user = await db.collection('users').findOne({ 
                    _id: new ObjectId(doc.author)
                  });
                }
                
                // If no user found, try more ways to match
                if (!user) {
                  user = await db.collection('users').findOne({ 
                    $or: [
                      { id: doc.author }, // In case there's an id field
                      { userId: doc.author }, // In case there's a userId field
                      { email: doc.author } // In case author is stored as email
                    ]
                  });
                }
                
                if (user) {
                  console.log(`Found user for ID ${doc.author}: ${user.firstName} ${user.lastName}, Grade: ${user.grade || 'N/A'}`);
                  authorInfo = {
                    id: typeof user._id === 'string' ? user._id : user._id.toString(),
                    firstName: user.firstName || '',
                    lastName: user.lastName || '',
                    profileImage: user.profileImage,
                    role: user.role,
                    // Add grade info with clear log
                    grade: user.grade || 'N/A',
                    // These fields are required by User interface but can be placeholders
                    // since they're not used in the articles display
                    email: user.email || '<EMAIL>',
                    createdAt: user.createdAt || new Date().toISOString(),
                    updatedAt: user.updatedAt || undefined
                  };
                  console.log(`Final grade for ${doc._id}: ${authorInfo.grade}`);
                } else {
                  console.log(`No user found for ID ${doc.author}`);
                }
              } catch (error) {
                console.error(`Error fetching author for article ${doc._id}:`, error);
              }
            } catch (error) {
              console.error(`Error processing string author ID for article ${doc._id}:`, error);
            }
          } 
          // Check for MongoDB object with _id
          else if (doc.author && typeof doc.author === 'object' && doc.author._id) {
            try {
              authorLookupAttempted = true;
              const authorId = typeof doc.author._id === 'string' ? doc.author._id : doc.author._id.toString();
              console.log(`Processing author with _id: ${authorId}`);
              
              // If we only have the ID but not name info, try to find more user details
              if (!doc.author.firstName && !doc.author.lastName && /^[0-9a-fA-F]{24}$/.test(authorId)) {
                const user = await db.collection('users').findOne({
                  _id: new ObjectId(authorId)
                });
                
                if (user) {
                  console.log(`Found additional user info for ID ${authorId}`);
                  authorInfo = {
                    id: authorId,
                    firstName: user.firstName || '',
                    lastName: user.lastName || '',
                    profileImage: user.profileImage || doc.author.profileImage,
                    role: user.role || doc.author.role,
                    grade: user.grade || 'N/A',
                    email: user.email || '<EMAIL>',
                    createdAt: user.createdAt || new Date().toISOString(),
                    updatedAt: user.updatedAt || undefined
                  };
                } else {
                  // Just use what we have
                  authorInfo = {
                    id: authorId,
                    firstName: doc.author.firstName || '',
                    lastName: doc.author.lastName || '',
                    profileImage: doc.author.profileImage,
                    role: doc.author.role,
                    grade: 'N/A',
                    email: doc.author.email || '<EMAIL>',
                    createdAt: doc.author.createdAt || new Date().toISOString(),
                    updatedAt: doc.author.updatedAt || undefined
                  };
                }
              } else {
                // Just use what we have
                authorInfo = {
                  id: authorId,
                  firstName: doc.author.firstName || '',
                  lastName: doc.author.lastName || '',
                  profileImage: doc.author.profileImage,
                  role: doc.author.role,
                  grade: doc.author.grade || 'N/A',
                  email: doc.author.email || '<EMAIL>',
                  createdAt: doc.author.createdAt || new Date().toISOString(),
                  updatedAt: doc.author.updatedAt || undefined
                };
              }
            } catch (error) {
              console.error(`Error processing author object with _id for article ${doc._id}:`, error);
            }
          }
          // Check for MongoDB object with $oid format
          else if (doc.author && typeof doc.author === 'object' && doc.author.$oid) {
            try {
              authorLookupAttempted = true;
              const oidValue = doc.author.$oid;
              console.log(`Processing author with $oid format: ${oidValue}`, doc.author);
              
              let user;
              // Try to find the user by ID
              if (/^[0-9a-fA-F]{24}$/.test(oidValue)) {
                user = await db.collection('users').findOne({ 
                  _id: new ObjectId(oidValue)
                });
              }
              
              if (!user) {
                // Try to match by string ID or other fields
                user = await db.collection('users').findOne({ 
                  $or: [
                    { id: oidValue },
                    { userId: oidValue },
                    { email: oidValue }
                  ]
                });
              }
              
              if (user) {
                console.log(`Found user for $oid ${oidValue}:`, { 
                  firstName: user.firstName, 
                  lastName: user.lastName, 
                  id: user._id.toString(),
                  grade: user.grade || 'N/A'
                });
                
                // Log exact user data to see if grade is present
                console.log(`User data for debugging:`, JSON.stringify(user));
                
                authorInfo = {
                  id: typeof user._id === 'string' ? user._id : user._id.toString(),
                  firstName: user.firstName || '',
                  lastName: user.lastName || '',
                  profileImage: user.profileImage,
                  role: user.role,
                  grade: user.grade || 'N/A',
                  email: user.email || '<EMAIL>',
                  createdAt: user.createdAt || new Date().toISOString(),
                  updatedAt: user.updatedAt || undefined
                };
                
                console.log(`Final grade from $oid user lookup: ${authorInfo.grade}`);
              } else {
                // Create a placeholder author object with the ID we have
                console.log(`No user found for $oid ${oidValue}, creating placeholder`);
                authorInfo = {
                  id: oidValue,
                  firstName: 'Student',
                  lastName: oidValue.substring(0, 6),
                  profileImage: null,
                  role: 'student',
                  grade: 'N/A',
                  email: `student-${oidValue.substring(0, 6)}@example.com`,
                  createdAt: new Date().toISOString(),
                  updatedAt: undefined
                };
              }
            } catch (error) {
              console.error(`Error processing author with $oid format for article ${doc._id}:`, error);
            }
          }
          
          // Log the result of our author lookup
          if (authorLookupAttempted) {
            if (typeof authorInfo === 'object') {
              console.log(`Final author for article ${doc._id}: ${authorInfo.firstName} ${authorInfo.lastName}, Grade: ${authorInfo.grade || 'N/A'}`);
            } else {
              console.log(`Failed to resolve author for article ${doc._id}, using: ${authorInfo}`);
            }
          }
          
          return {
            id: doc._id.toString(),
            title: doc.title || 'Untitled',
            content: doc.content || {},
            author: authorInfo,
            status: doc.status || 'draft',
            createdAt: doc.createdAt || new Date().toISOString(),
            updatedAt: doc.updatedAt,
            slug: doc.slug,
            // For consistency, include these fields as they may exist in MongoDB
            featuredImage: doc.featuredImage,
            teacherReview: doc.teacherReview || [],
          };
        }));
        return successResponse<ArticlesResponse>({ articles: articles as any })
      }
    } catch (mongoError) {
      console.warn('Error fetching articles from MongoDB, falling back to Payload:', mongoError)
    }

    // Fallback to Payload CMS if MongoDB fails
    const payload = await getPayload({ config })

    // Get all articles with the requested depth
    const articlesResponse = await payload.find({
      collection: 'articles',
      depth: depth, // Use the depth from query params
    })

    console.log(`Fetched ${articlesResponse.docs.length} articles with depth=${depth}`);

    // Convert Payload docs to our Article type with type assertion
    const articles = articlesResponse.docs.map((doc) => {
      // Process author information
      let authorInfo: any = doc.author || 'Unknown';
      
      // Handle author info if it exists
      if (doc.author && typeof doc.author === 'object') {
        // Payload already populates related fields when depth > 0
        authorInfo = {
          id: doc.author.id || '',
          firstName: doc.author.firstName || '',
          lastName: doc.author.lastName || '',
          profileImage: doc.author.profileImage,
          role: doc.author.role,
          // Add grade info
          grade: doc.author.grade || 'N/A',
          // These fields are required by User interface
          email: doc.author.email || '<EMAIL>',
          createdAt: doc.author.createdAt || new Date().toISOString(),
          updatedAt: doc.author.updatedAt || undefined
        };
        console.log(`Author info for ${doc.title}: ${authorInfo.firstName} ${authorInfo.lastName}, Grade: ${authorInfo.grade || 'N/A'}`);
      }
      
      return {
        id: doc.id,
        title: doc.title || 'Untitled',
        content: doc.content || {},
        author: authorInfo,
        status: doc.status || 'draft',
        createdAt: doc.createdAt || new Date().toISOString(),
        updatedAt: doc.updatedAt,
        slug: doc.slug || undefined,
        featuredImage: doc.featuredImage || undefined,
        teacherReview: doc.teacherReview || [],
      };
    })

    return successResponse<ArticlesResponse>({ articles: articles as any })
  } catch (error) {
    console.error('Error fetching articles:', error)
    return errorResponse('Failed to fetch articles', 500)
  }
}
