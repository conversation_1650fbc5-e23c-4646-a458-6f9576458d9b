import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    if (!token) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
    const userId = typeof decoded === 'object' ? decoded.id : null
    if (!userId) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    const payload = await getPayload({ config })
    const user = await payload.findByID({ collection: 'users', id: userId, depth: 1 })
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'super-admin') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    try {
      const { db } = await connectToDatabase()
      const { ObjectId } = await import('mongodb')

      // First, get all roles with their IDs
      const rolesSlugs = ['super-admin', 'school-admin', 'teacher', 'mentor', 'student']
      const rolesData = await db
        .collection('roles')
        .find({ slug: { $in: rolesSlugs } })
        .toArray()
      console.log('Found roles:', rolesData)

      // Create a map of slug to ObjectId
      const roleMap = new Map()
      rolesData.forEach((role) => {
        roleMap.set(role.slug, role._id)
      })

      const counts = await Promise.all(
        rolesSlugs.map(async (roleSlug) => {
          const roleId = roleMap.get(roleSlug)
          if (!roleId) {
            console.warn(`Role not found for slug: ${roleSlug}`)
            return { role: roleSlug, count: 0 }
          }

          // Count users with this role ID
          const count = await db.collection('users').countDocuments({
            $or: [
              { role: roleId },
              { role: roleId.toString() },
              { role: roleSlug }, // fallback for string roles
              { 'role.slug': roleSlug }, // fallback for object roles
            ],
          })
          console.log(`Role ${roleSlug} (${roleId}): ${count} users`)
          return { role: roleSlug, count }
        }),
      )
      console.log('Role distribution counts:', counts)
      if (counts.some((c) => c.count > 0)) return NextResponse.json({ roleDistribution: counts })
    } catch (mongoError) {
      console.warn(
        'Error fetching role distribution from MongoDB, falling back to Payload:',
        mongoError,
      )
    }
    // Fallback to Payload CMS
    try {
      const rolesSlugs = ['super-admin', 'school-admin', 'teacher', 'mentor', 'student']

      // First get all roles to get their IDs
      const rolesResult = await payload.find({
        collection: 'roles',
        where: { slug: { in: rolesSlugs } },
        limit: 10,
      })

      console.log('Payload roles found:', rolesResult.docs)

      const counts = await Promise.all(
        rolesSlugs.map(async (roleSlug) => {
          // Find the role document for this slug
          const roleDoc = rolesResult.docs.find((r) => r.slug === roleSlug)
          if (!roleDoc) {
            console.warn(`Role not found for slug: ${roleSlug}`)
            return { role: roleSlug, count: 0 }
          }

          const result = await payload.count({
            collection: 'users',
            where: { role: { equals: roleDoc.id } },
          })
          console.log(`Payload role ${roleSlug} (${roleDoc.id}): ${result.totalDocs} users`)
          return { role: roleSlug, count: result.totalDocs }
        }),
      )
      if (counts.some((c) => c.count > 0)) return NextResponse.json({ roleDistribution: counts })
    } catch (payloadError) {
      console.warn('Error fetching role distribution from Payload CMS:', payloadError)
    }
    // Fallback mock data
    return NextResponse.json({
      roleDistribution: [
        { role: 'super-admin', count: 1 },
        { role: 'school-admin', count: 5 },
        { role: 'teacher', count: 20 },
        { role: 'mentor', count: 10 },
        { role: 'student', count: 100 },
      ],
    })
  } catch (error) {
    console.error('Error fetching role distribution:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
