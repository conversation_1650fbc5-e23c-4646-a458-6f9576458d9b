import { Access, PayloadRequest } from 'payload'

// Helper to check if user has a specific role
export const hasRole = async (req: PayloadRequest, roleSlug: string): Promise<boolean> => {
  const { user, payload } = req

  if (!user) return false

  // If role is populated and has a slug property
  if (typeof user.role === 'object' && user.role !== null && 'slug' in user.role) {
    console.log('hasRole: user.role is object', { userRole: user.role, expected: roleSlug })
    return user.role.slug === roleSlug
  }

  // If role is just an ID, fetch the role
  if (user.role) {
    try {
      const role = await payload.findByID({
        collection: 'roles',
        id: user.role,
      })
      console.log('hasRole: fetched role for user', { userRoleId: user.role, fetchedRole: role, expected: roleSlug })
      return role?.slug === roleSlug
    } catch (error) {
      console.error('Error fetching role:', error)
      return false
    }
  }

  return false
}

// Check if user is a super admin
export const isSuperAdmin: Access = async ({ req }) => {
  return await hasRole(req, 'super-admin')
}

// Check if user is a school admin
export const isSchoolAdmin: Access = async ({ req }) => {
  return await hasRole(req, 'school-admin')
}

// Check if user is a mentor
export const isMentor: Access = async ({ req }) => {
  return await hasRole(req, 'mentor')
}

// Check if user is a teacher
export const isTeacher: Access = async ({ req }) => {
  return await hasRole(req, 'teacher')
}

// Check if user is a student
export const isStudent: Access = async ({ req }) => {
  return await hasRole(req, 'student')
}

// Check if user is from the same school
export const isSameSchool = async (req: PayloadRequest, schoolId: string): Promise<boolean> => {
  const { user } = req

  if (!user) return false

  // If school is populated
  if (typeof user.school === 'object' && user.school !== null) {
    return user.school.id === schoolId
  }

  // If school is just an ID
  return user.school === schoolId
}

// Access control for school admins to manage their school's users
export const canManageSchoolUsers: Access = async ({ req, data }) => {
  const isAdmin = await isSchoolAdmin({ req })
  if (!isAdmin) return false

  // If no data, this is a list operation - only show users from their school
  if (!data) {
    return {
      school: {
        equals: req.user!.school,
      },
    }
  }

  // For create/update operations, check if the user belongs to the admin's school
  return await isSameSchool(req, data.school)
}

// Check if user is the author of an article or news post
export const isAuthor = (user: any, document: any): boolean => {
  if (!user || !document) return false

  // If author is populated
  if (typeof document.author === 'object' && document.author !== null) {
    return document.author.id === user.id
  }

  // If author is just an ID
  return document.author === user.id
}

// Access control for articles

export const articleAccess: Access = async ({ req, id, data }) => {
  const { user, payload } = req

  // Anyone can read published articles
  if (req.method === 'GET') {
    // If no ID, this is a list operation - only show published articles to the public
    if (!id) {
      if (!user) {
        return {
          status: {
            equals: 'published',
          },
        }
      }

      // Super admins and school admins can see all articles
      const isSuperAdminUser = await isSuperAdmin({ req })
      const isSchoolAdminUser = await isSchoolAdmin({ req })
      if (isSuperAdminUser || isSchoolAdminUser) return true

      // Teachers can see pending review articles but not draft articles
      // and not articles they have already reviewed
      const isTeacherUser = await isTeacher({ req })
      if (isTeacherUser) {
        return {
          and: [
            {
              status: {
                equals: 'pending-review',
              },
            },
            {
              or: [
                {
                  teacherReview: {
                    exists: false,
                  },
                },
                {
                  and: [
                    {
                      teacherReview: {
                        exists: true,
                      },
                    },
                    {
                      'teacherReview.reviewer': {
                        not_equals: user.id,
                      },
                    },
                  ],
                },
              ],
            },
          ],
        }
      }

      // Mentors can see all published articles
      const isMentorUser = await isMentor({ req })
      if (isMentorUser) {
        return {
          status: {
            equals: 'published',
          },
        }
      }

      // Students can see their own articles and all published articles
      const isStudentUser = await isStudent({ req })
      if (isStudentUser) {
        return {
          or: [
            {
              status: {
                equals: 'published',
              },
            },
            {
              and: [
                {
                  author: {
                    equals: user.id,
                  },
                },
              ],
            },
          ],
        }
      }

      // Default to only showing published articles
      return {
        status: {
          equals: 'published',
        },
      }
    }

    // If there is an ID, fetch the article to check access
    try {
      const article = await payload.findByID({
        collection: 'articles',
        id,
      })

      // Published articles are public
      if (article.status === 'published') return true

      // If not published, only allow access to appropriate users
      if (!user) return false

      // Super admins and school admins can do anything
      const isSuperAdminUser = await isSuperAdmin({ req })
      const isSchoolAdminUser = await isSchoolAdmin({ req })
      if (isSuperAdminUser || isSchoolAdminUser) return true

      // Teachers can see pending review articles
      const isTeacherUser = await isTeacher({ req })
      if (isTeacherUser && article.status === 'pending-review') return true

      // Students can only see their own unpublished articles
      const isStudentUser = await isStudent({ req })
      if (isStudentUser && isAuthor(user, article)) return true

      return false
    } catch (error) {
      console.error('Error checking article access:', error)
      return false
    }
  }

  // Create, update, and delete operations
  if (!user) return false

  // Super admins and school admins can do anything
  const isSuperAdminUser = await isSuperAdmin({ req })
  const isSchoolAdminUser = await isSchoolAdmin({ req })
  if (isSuperAdminUser || isSchoolAdminUser) return true

  // Create operations
  if (req.method === 'POST' && !id) {
    // Only students can create articles
    const isStudentUser = await isStudent({ req })
    return isStudentUser
  }

  // Update and delete operations require fetching the article
  if (id) {
    try {
      const article = await payload.findByID({
        collection: 'articles',
        id,
      })

      // Students can only update/delete their own articles that aren't published
      const isStudentUser = await isStudent({ req })
      if (isStudentUser && isAuthor(user, article) && article.status !== 'published') {
        return true
      }

      // Teachers can update articles to add reviews
      const isTeacherUser = await isTeacher({ req })
      if (isTeacherUser && article.status === 'pending-review') {
        // Only allow updating the teacherReview field
        if (req.method === 'PATCH' && data && 'teacherReview' in data) {
          return true
        }
      }

      return false
    } catch (error) {
      console.error('Error checking article access:', error)
      return false
    }
  }

  return false
}

// Access control for news posts
export const newsAccess: Access = async ({ req, id }) => {
  // If no user, only allow access to published news
  const { user, payload } = req

  // Anyone can read published news
  if (req.method === 'GET') {
    // If no ID, this is a list operation - only show published news to the public
    if (!id) {
      if (!user) {
        return {
          status: {
            equals: 'published',
          },
        }
      }

      // Super admins and school admins can see all news
      const isSuperAdminUser = await isSuperAdmin({ req })
      const isSchoolAdminUser = await isSchoolAdmin({ req })
      if (isSuperAdminUser || isSchoolAdminUser) return true

      // Teachers and Mentors can see all published news
      const isTeacherUser = await isTeacher({ req })
      const isMentorUser = await isMentor({ req })
      if (isTeacherUser || isMentorUser) {
        return {
          status: {
            equals: 'published',
          },
        }
      }

      // Mentors can see their own draft news
      if (isMentorUser) {
        return {
          or: [
            {
              and: [
                {
                  author: {
                    equals: user.id,
                  },
                },
                {
                  status: {
                    equals: 'draft',
                  },
                },
              ],
            },
          ],
        }
      }

      // Default to only showing published news
      return {
        status: {
          equals: 'published',
        },
      }
    }

    // If there is an ID, fetch the news post to check access
    try {
      const news = await payload.findByID({
        collection: 'news',
        id,
      })

      // Published news is public
      if (news.status === 'published') return true

      // If not published, only allow access to appropriate users
      if (!user) return false

      // Super admins and school admins can see all news
      const isSuperAdminUser = await isSuperAdmin({ req })
      const isSchoolAdminUser = await isSchoolAdmin({ req })
      if (isSuperAdminUser || isSchoolAdminUser) return true

      // Mentors can see their own draft news
      const isMentorUser = await isMentor({ req })
      if (isMentorUser && isAuthor(user, news)) return true

      return false
    } catch (error) {
      console.error('Error checking news access:', error)
      return false
    }
  }

  // Create, update, and delete operations
  if (!user) return false

  // Super admins and school admins can do anything
  const isSuperAdminUser = await isSuperAdmin({ req })
  const isSchoolAdminUser = await isSchoolAdmin({ req })
  if (isSuperAdminUser || isSchoolAdminUser) return true

  // Create operations
  if (req.method === 'POST' && !id) {
    // Only mentors can create news
    const isMentorUser = await isMentor({ req })
    return isMentorUser
  }

  // Update and delete operations require fetching the news post
  if (id) {
    try {
      const news = await payload.findByID({
        collection: 'news',
        id,
      })

      // Mentors can only update/delete their own news
      const isMentorUser = await isMentor({ req })
      if (isMentorUser && isAuthor(user, news)) {
        return true
      }

      return false
    } catch (error) {
      console.error('Error checking news access:', error)
      return false
    }
  }

  return false
}

// Access control for statistics
export const statisticsAccess: Access = async ({ req }: { req: PayloadRequest }) => {
  // Anyone can read statistics
  if (req.method === 'GET') return true

  // Only system can create/update/delete statistics
  return false
}
