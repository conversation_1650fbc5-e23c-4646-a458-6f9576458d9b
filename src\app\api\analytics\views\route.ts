import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import { getViewCount, updateBatchViewCounts } from '@/utils/viewUtils'
import config from '@/payload.config'
    import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

/**
 * GET request to retrieve view statistics for news or articles
 * Query parameters:
 * - type: 'news' or 'articles' (required)
 * - id: item ID (required for single item, omit for top items)
 * - limit: number of top items to return (default: 10, only used when id is not provided)
 */
export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(req.url)
    const type = url.searchParams.get('type')
    const id = url.searchParams.get('id')
    const limit = parseInt(url.searchParams.get('limit') || '10', 10)

    console.log(`Analytics API: Received request for ${type} views, limit: ${limit}, id: ${id || 'none'}`)

    // Validate type parameter
    if (!type || (type !== 'news' && type !== 'articles')) {
      return NextResponse.json(
        { error: 'Invalid type parameter. Must be "news" or "articles"' },
        { status: 400 }
      )
    }

    // If ID is provided, get view count for a specific item
    if (id) {
      const viewCount = await getViewCount(type as 'news' | 'articles', id)
      console.log(`Analytics API: Returning view count ${viewCount} for ${type} item ${id}`)
      return NextResponse.json({ views: viewCount })
    }

    try {
      // Get the payload instance
      const payload = await getPayload({ config })

      // Try using Payload API first
      console.log(`Analytics API: Attempting to fetch top ${limit} ${type} by views using Payload`)
      
      // Make sure to only get published items
      const query = {
        status: {
          equals: 'published',
        },
      }

      const items = await payload.find({
        collection: type,
        where: query,
        sort: '-views', // Sort by views in descending order
        limit,
        depth: 1, // We don't need full depth for this query
      })

      console.log(`Analytics API: Found ${items.docs.length} ${type} items using Payload`)

      // Check if we have enough results, if not try MongoDB directly
      if (items.docs.length < limit) {
        console.log(`Analytics API: Not enough results from Payload, trying MongoDB directly`)
        const { db } = await connectToDatabase()
        
        const mongoItems = await db.collection(type)
          .find({ status: 'published' })
          .sort({ views: -1 })
          .limit(limit)
          .toArray()
          
        console.log(`Analytics API: Found ${mongoItems.length} ${type} items using MongoDB`)
        
        // Combine results if needed
        if (mongoItems.length > items.docs.length) {
          return NextResponse.json({
            items: mongoItems.map(item => ({
              id: item.id || (item._id ? item._id.toString() : ''),
              title: item.title || 'Untitled',
              views: item.views || 0,
              slug: item.slug || item.id || (item._id ? item._id.toString() : ''),
            })),
          })
        }
      }

      // Return the top items with their view counts from Payload
      return NextResponse.json({
        items: items.docs.map(item => ({
          id: item.id,
          title: item.title || 'Untitled',
          views: item.views || 0,
          slug: item.slug || item.id,
        })),
      })
    } catch (payloadError) {
      // If Payload fails, try MongoDB directly
      console.error('Analytics API: Payload error, falling back to MongoDB:', payloadError)
      
      const { db } = await connectToDatabase()
      
      const mongoItems = await db.collection(type)
        .find({ status: 'published' })
        .sort({ views: -1 })
        .limit(limit)
        .toArray()
        
      console.log(`Analytics API: Found ${mongoItems.length} ${type} items using MongoDB fallback`)
      
      return NextResponse.json({
        items: mongoItems.map(item => ({
          id: item.id || (item._id ? item._id.toString() : ''),
          title: item.title || 'Untitled',
          views: item.views || 0,
          slug: item.slug || item.id || (item._id ? item._id.toString() : ''),
        })),
      })
    }
  } catch (error) {
    console.error('Error fetching view statistics:', error)
    return NextResponse.json({ error: 'Internal Server Error', items: [] }, { status: 500 })
  }
}

/**
 * POST request to update view counts in batch
 * Requires admin authentication
 * Body format:
 * {
 *   type: 'news' | 'articles',
 *   viewCounts: {
 *     [itemId]: count,
 *     ...
 *   }
 * }
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Extract user from token
    const userInfo = await extractUserFromToken(token)

    if (!userInfo || !userInfo.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the payload instance
    const payload = await getPayload({ config })

    // Get the user with role
    const user = await payload.findByID({
      collection: 'users',
      id: userInfo.id,
      depth: 1,
    })

    // Check if user is an admin
    const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
    const isAdmin = userRole === 'super-admin' || userRole === 'school-admin'

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get request body
    const body = await req.json()
    const { type, viewCounts } = body

    // Validate parameters
    if (!type || (type !== 'news' && type !== 'articles')) {
      return NextResponse.json(
        { error: 'Invalid type parameter. Must be "news" or "articles"' },
        { status: 400 }
      )
    }

    if (!viewCounts || typeof viewCounts !== 'object') {
      return NextResponse.json(
        { error: 'Invalid viewCounts parameter. Must be an object mapping IDs to counts' },
        { status: 400 }
      )
    }

    // Update view counts
    await updateBatchViewCounts(type as 'news' | 'articles', viewCounts)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating view counts:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 