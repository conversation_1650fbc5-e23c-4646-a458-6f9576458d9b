import { S3Client } from '@aws-sdk/client-s3';

// Create a custom S3 client with Backblaze B2 compatibility
export const createS3Client = () => {
  return new S3Client({
    endpoint: 'https://s3.us-east-005.backblazeb2.com',
    credentials: {
      accessKeyId: process.env.BACKBLAZE_KEY_ID || '',
      secretAccessKey: process.env.BACKBLAZE_API || '',
    },
    region: 'us-east-005',
    forcePathStyle: true,
    // Disable host prefix injection
    disableHostPrefix: true,
  });
};
