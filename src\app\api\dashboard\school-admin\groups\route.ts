import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const schoolId = url.searchParams.get('schoolId')
      const limit = parseInt(url.searchParams.get('limit') || '50')

      if (!schoolId) {
        return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
      }

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the user from MongoDB
        const mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })

        if (mongoUser) {
          // Check if user is a school admin
          const userRole = typeof mongoUser.role === 'object' ? mongoUser.role.slug : mongoUser.role
          if (userRole !== 'school-admin') {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get user's school ID
          const userSchoolId =
            typeof mongoUser.school === 'object' ? mongoUser.school.id : mongoUser.school

          // Verify the school ID matches the user's school
          if (userSchoolId !== schoolId) {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get classes for the school
          const classes = await db.collection('classes').find({ schoolId }).limit(limit).toArray()

          // Get student groups for the school
          const studentGroups = await db
            .collection('studentGroups')
            .find({ schoolId })
            .limit(limit)
            .toArray()

          // Combine classes and student groups
          const groups = [
            ...classes.map((cls) => ({
              id: cls._id.toString(),
              name: cls.name,
              type: 'class',
            })),
            ...studentGroups.map((group) => ({
              id: group._id.toString(),
              name: group.name,
              type: 'student-group',
            })),
          ]

          return NextResponse.json({ groups })
        }
      } catch (mongoError) {
        console.warn('Error fetching groups from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user is a school admin
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
      if (userRole !== 'school-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get user's school ID
      const userSchoolId = typeof user.school === 'object' ? user.school?.id : user.school

      // Verify the school ID matches the user's school
      if (userSchoolId !== schoolId) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get classes for the school
      const classes = await payload.find({
        collection: 'classes',
        where: {
          schoolId: { equals: schoolId },
        },
        limit,
      })

      // Get student groups for the school
      const studentGroups = await payload.find({
        collection: 'studentGroups',
        where: {
          schoolId: { equals: schoolId },
        },
        limit,
      })

      // Combine classes and student groups
      const groups = [
        ...classes.docs.map((cls) => ({
          id: cls.id,
          name: cls.name,
          type: 'class',
        })),
        ...studentGroups.docs.map((group) => ({
          id: group.id,
          name: group.name,
          type: 'student-group',
        })),
      ]

      return NextResponse.json({ groups })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching groups:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
