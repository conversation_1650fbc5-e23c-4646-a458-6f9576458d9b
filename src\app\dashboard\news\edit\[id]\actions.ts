'use server'

import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { getPayload } from 'payload'
import config from '@/payload.config'
import jwt from 'jsonwebtoken'

export async function updateNews(formData: FormData) {
  try {
    // Get the payload instance
    const payload = await getPayload({
      config,
    })

    // Get the current user from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      throw new Error('Authentication token not found')
    }

    // Decode the user ID from the token
    const secret = process.env.PAYLOAD_SECRET || 'secret'
    const decoded = jwt.verify(token, secret)
    const userId = typeof decoded === 'object' ? decoded.id : null
    if (!userId) throw new Error('Invalid token: no user ID')
    // Fetch the user
    const user = await payload.findByID({ collection: 'users', id: userId })

    if (!user) {
      throw new Error('You must be logged in to update a news post')
    }

    // Check if user is a mentor, school-admin, or super-admin
    const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
    const isMentor = userRole === 'mentor'
    const isSchoolAdmin = userRole === 'school-admin'
    const isSuperAdmin = userRole === 'super-admin'

    if (!isMentor && !isSchoolAdmin && !isSuperAdmin) {
      throw new Error('Only mentors, school-admins, or super-admins can update news posts')
    }

    // Get form data
    const id = formData.get('id') as string
    const title = formData.get('title') as string
    const slug = formData.get('slug') as string
    const content = formData.get('content') as string
    const status = formData.get('status') as string

    if (!id || !title || !slug || !content || !status) {
      throw new Error('All fields are required')
    }

    // Get the news post
    const newsPost = await payload.findByID({
      collection: 'news',
      id,
    })

    // Check if the user is the author (for mentors), or allow super-admins/school-admins to edit any
    let isAllowed = false;
    if (isSuperAdmin || isSchoolAdmin) {
      isAllowed = true;
    } else if (isMentor) {
      isAllowed = newsPost.author === user.id || (typeof newsPost.author === 'object' && newsPost.author?.id === user.id);
    }
    if (!isAllowed) {
      throw new Error('You do not have permission to update this news post');
    }

    // Validate status
    if (status !== 'draft' && status !== 'published') {
      throw new Error('Invalid status')
    }

    // Update the news post
    const updateData: any = {
      title,
      slug,
      content,
      status: status as 'draft' | 'published',
    }

    // If publishing for the first time, add publishedAt date
    if (status === 'published' && newsPost.status === 'draft' && !newsPost.publishedAt) {
      updateData.publishedAt = new Date().toISOString()
    }

    await payload.update({
      collection: 'news',
      id,
      data: updateData,
    })

    // Redirect to the news page
    redirect('/dashboard/news')
  } catch (error) {
    console.error('Error updating news post:', error)
    throw error
  }
}
