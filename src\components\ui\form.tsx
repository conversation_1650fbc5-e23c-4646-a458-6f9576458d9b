// This is a shadcn/ui-compatible form wrapper for React Hook Form
'use client'

import * as React from 'react'
import { useFormContext, FormProvider, UseFormReturn, FieldValues, Controller } from 'react-hook-form'

export function Form({ children, ...props }: { children: React.ReactNode } & React.ComponentProps<'form'>) {
  const methods = useFormContext()
  return (
    <form {...props} onSubmit={props.onSubmit || methods.handleSubmit(() => {})}>
      {children}
    </form>
  )
}

export function FormField({
  name,
  control,
  render,
}: {
  name: string
  control: any
  render: (props: { field: any }) => React.ReactElement
}) {
  return <Controller name={name} control={control} render={render} />
}

export function FormItem({ children }: { children: React.ReactNode }) {
  return <div className="space-y-2">{children}</div>
}

export function FormLabel({ children }: { children: React.ReactNode }) {
  return <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">{children}</label>
}

export function FormControl({ children }: { children: React.ReactNode }) {
  return <div>{children}</div>
}

export function FormMessage({ children }: { children?: React.ReactNode }) {
  return children ? <div className="text-xs text-red-500 mt-1">{children}</div> : null
}

export function FormDescription({ children }: { children: React.ReactNode }) {
  return <div className="text-xs text-muted-foreground">{children}</div>
} 