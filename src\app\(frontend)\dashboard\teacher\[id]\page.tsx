'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog'
import { ArrowLeft, User, FileText, CheckCircle, Clock, X } from 'lucide-react'
import TeacherReviewsTable from '@/components/dashboard/TeacherReviewsTable'

interface TeacherDetailProps {
  params: {
    id: string
  }
}

export default function TeacherDetail({ params }: TeacherDetailProps) {
  // Access params directly, ignoring the warning for now
  const teacherId = params.id
  const router = useRouter()
  const [teacher, setTeacher] = useState<any>(null)
  const [articles, setArticles] = useState<any[]>([])
  const [students, setStudents] = useState<any[]>([])
  const [selectedArticle, setSelectedArticle] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  // Function to fetch article details
  const fetchArticleDetails = async (articleId: string) => {
    try {
      const response = await fetch(`/api/dashboard/articles/${articleId}`, {
        credentials: 'include',
      })

      if (!response.ok) {
        console.error('Failed to fetch article details:', await response.text())
        return null
      }

      const data = await response.json()
      return data.data?.article || null
    } catch (err) {
      console.error('Error fetching article details:', err)
      return null
    }
  }

  // Function to view article details
  const handleViewArticleDetails = async (articleId: string) => {
    const article = await fetchArticleDetails(articleId)
    setSelectedArticle(article)
  }

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true)

        // Fetch teacher data
        const teacherResponse = await fetch(`/api/users/${teacherId}`, {
          credentials: 'include',
        })

        if (!teacherResponse.ok) {
          throw new Error('Failed to fetch teacher data')
        }

        const teacherData = await teacherResponse.json()
        setTeacher(teacherData)

        // Fetch articles reviewed by this teacher
        const articlesResponse = await fetch(`/api/articles?reviewerId=${teacherId}`, {
          credentials: 'include',
        })

        if (articlesResponse.ok) {
          const articlesData = await articlesResponse.json()
          setArticles(articlesData.docs || [])
        }

        // Fetch students approved by this teacher
        const studentsResponse = await fetch(`/api/users?approvedBy=${teacherId}`, {
          credentials: 'include',
        })

        if (studentsResponse.ok) {
          const studentsData = await studentsResponse.json()
          setStudents(studentsData.docs || [])
        }

        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching teacher data:', err)
        setError('Failed to load teacher data. Please try again.')
        setIsLoading(false)
      }
    }

    if (teacherId) {
      fetchData()
    }
  }, [teacherId])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <Button variant="outline" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back
        </Button>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      </div>
    )
  }

  if (!teacher) {
    return (
      <div className="p-6">
        <Button variant="outline" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back
        </Button>
        <div className="bg-amber-100 border border-amber-400 text-amber-700 px-4 py-3 rounded mb-4">
          Teacher not found.
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <Button variant="outline" onClick={() => router.back()} className="mb-4">
        <ArrowLeft className="mr-2 h-4 w-4" /> Back
      </Button>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Teacher Profile */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              Teacher Profile
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Name</h3>
                <p className="text-lg font-semibold">
                  {teacher.firstName} {teacher.lastName}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Email</h3>
                <p>{teacher.email}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">School</h3>
                <p>{typeof teacher.school === 'object' ? teacher.school?.name : 'Unknown'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Joined</h3>
                <p>{new Date(teacher.createdAt).toLocaleDateString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Articles Reviewed */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5" />
              Articles Reviewed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-4">{articles.length}</div>
            {articles.length > 0 ? (
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {articles.slice(0, 5).map((article) => (
                  <div key={article.id} className="border-b pb-2">
                    <p className="font-medium">{article.title}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="mr-1 h-3 w-3" />
                        {new Date(article.updatedAt).toLocaleDateString()}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-2 text-xs"
                        onClick={() => handleViewArticleDetails(article.id)}
                      >
                        View Details
                      </Button>
                    </div>
                  </div>
                ))}
                {articles.length > 5 && (
                  <Button
                    variant="link"
                    className="p-0"
                    onClick={() => router.push(`/dashboard/articles?teacherId=${teacherId}`)}
                  >
                    View all {articles.length} articles
                  </Button>
                )}
              </div>
            ) : (
              <p className="text-gray-500">No articles reviewed yet.</p>
            )}
          </CardContent>
        </Card>

        {/* Students Approved */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="mr-2 h-5 w-5" />
              Students Approved
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-4">{students.length}</div>
            {students.length > 0 ? (
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {students.slice(0, 5).map((student) => (
                  <div key={student.id} className="border-b pb-2">
                    <p className="font-medium">
                      {student.firstName} {student.lastName}
                    </p>
                    <p className="text-sm text-gray-500">{student.email}</p>
                  </div>
                ))}
                {students.length > 5 && (
                  <Button
                    variant="link"
                    className="p-0"
                    onClick={() => router.push(`/dashboard/students?approvedBy=${teacherId}`)}
                  >
                    View all {students.length} students
                  </Button>
                )}
              </div>
            ) : (
              <p className="text-gray-500">No students approved yet.</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Article Details Dialog */}
      {selectedArticle && (
        <Dialog open={!!selectedArticle} onOpenChange={(open) => !open && setSelectedArticle(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>Article Details</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => setSelectedArticle(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-6">
              {/* Article Info */}
              <Card>
                <CardHeader>
                  <CardTitle>{selectedArticle.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Author</h3>
                      <p>
                        {typeof selectedArticle.author === 'object'
                          ? `${selectedArticle.author.firstName || ''} ${selectedArticle.author.lastName || ''}`.trim() || selectedArticle.author.email
                          : 'Unknown Author'}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Status</h3>
                      <p className="capitalize">{selectedArticle.status}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Created</h3>
                      <p>{new Date(selectedArticle.createdAt).toLocaleDateString()}</p>
                    </div>
                    {selectedArticle.content && (
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Content Preview</h3>
                        <div className="mt-2 p-4 bg-gray-50 rounded-md max-h-40 overflow-y-auto">
                          {typeof selectedArticle.content === 'string' ? (
                            <div dangerouslySetInnerHTML={{ __html: selectedArticle.content.substring(0, 500) + '...' }} />
                          ) : (
                            <p>Content not available in text format</p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Teacher Reviews */}
              {selectedArticle.teacherReview && selectedArticle.teacherReview.length > 0 && (
                <TeacherReviewsTable
                  reviews={selectedArticle.teacherReview}
                  articleId={selectedArticle.id}
                  articleTitle={selectedArticle.title}
                />
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
