import nodemailer from 'nodemailer'

// Create a test account for development
// In production, you would use real SMTP credentials
let transporter: nodemailer.Transporter

// Initialize the transporter
async function initializeTransporter() {
  if (transporter) return transporter

  // For development, use Ethereal (fake SMTP service)
  if (process.env.NODE_ENV !== 'production') {
    const testAccount = await nodemailer.createTestAccount()
    
    transporter = nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass,
      },
    })
  } else {
    // For production, use real SMTP credentials
    transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    })
  }

  return transporter
}

// Email templates
const emailTemplates = {
  articleSubmitted: (articleTitle: string, articleUrl: string) => ({
    subject: `New Article Submitted: "${articleTitle}"`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3b82f6;">New Article Submitted for Review</h2>
        <p>A new article titled <strong>"${articleTitle}"</strong> has been submitted for review.</p>
        <p>Please review this article at your earliest convenience.</p>
        <div style="margin: 30px 0;">
          <a href="${articleUrl}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
            Review Article
          </a>
        </div>
        <p>Thank you for your contribution to Young Reporter!</p>
        <hr style="border: 1px solid #e5e7eb; margin: 20px 0;" />
        <p style="color: #6b7280; font-size: 12px;">
          This is an automated message from the Young Reporter platform. Please do not reply to this email.
        </p>
      </div>
    `,
  }),

  articleReviewed: (articleTitle: string, articleUrl: string, approved: boolean) => ({
    subject: `Article ${approved ? 'Approved' : 'Reviewed'}: "${articleTitle}"`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: ${approved ? '#10b981' : '#3b82f6'};">Your Article Has Been ${
      approved ? 'Approved and Published' : 'Reviewed'
    }</h2>
        <p>Your article titled <strong>"${articleTitle}"</strong> has been ${
      approved ? 'approved and is now published' : 'reviewed by a teacher'
    }.</p>
        ${
          approved
            ? `<p>Congratulations! Your work is now visible to the public.</p>`
            : `<p>Please check the feedback and make any necessary revisions.</p>`
        }
        <div style="margin: 30px 0;">
          <a href="${articleUrl}" style="background-color: ${
      approved ? '#10b981' : '#3b82f6'
    }; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
            ${approved ? 'View Published Article' : 'View Feedback'}
          </a>
        </div>
        <p>Thank you for your contribution to Young Reporter!</p>
        <hr style="border: 1px solid #e5e7eb; margin: 20px 0;" />
        <p style="color: #6b7280; font-size: 12px;">
          This is an automated message from the Young Reporter platform. Please do not reply to this email.
        </p>
      </div>
    `,
  }),

  reviewEvaluated: (articleTitle: string, articleUrl: string, rating: number) => ({
    subject: `Your Review Has Been Evaluated: "${articleTitle}"`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #8b5cf6;">Your Review Has Been Evaluated</h2>
        <p>Your review of the article <strong>"${articleTitle}"</strong> has been evaluated by a mentor.</p>
        <p>Rating: <strong>${rating}/10</strong></p>
        ${
          rating >= 8
            ? '<p style="color: #10b981;">Great job! Your feedback was excellent.</p>'
            : rating <= 3
            ? '<p style="color: #ef4444;">There are some areas for improvement in your review approach.</p>'
            : '<p>Thank you for your contribution to the review process.</p>'
        }
        <div style="margin: 30px 0;">
          <a href="${articleUrl}" style="background-color: #8b5cf6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
            View Article
          </a>
        </div>
        <p>Thank you for your contribution to Young Reporter!</p>
        <hr style="border: 1px solid #e5e7eb; margin: 20px 0;" />
        <p style="color: #6b7280; font-size: 12px;">
          This is an automated message from the Young Reporter platform. Please do not reply to this email.
        </p>
      </div>
    `,
  }),
}

// Send email function
export async function sendEmail({
  to,
  template,
  data,
}: {
  to: string
  template: 'articleSubmitted' | 'articleReviewed' | 'reviewEvaluated'
  data: any
}) {
  try {
    // Initialize transporter if not already done
    const emailTransporter = await initializeTransporter()

    // Get the template
    let emailContent
    switch (template) {
      case 'articleSubmitted':
        emailContent = emailTemplates.articleSubmitted(data.articleTitle, data.articleUrl)
        break
      case 'articleReviewed':
        emailContent = emailTemplates.articleReviewed(
          data.articleTitle,
          data.articleUrl,
          data.approved
        )
        break
      case 'reviewEvaluated':
        emailContent = emailTemplates.reviewEvaluated(
          data.articleTitle,
          data.articleUrl,
          data.rating
        )
        break
      default:
        throw new Error(`Unknown email template: ${template}`)
    }

    // Send the email
    const info = await emailTransporter.sendMail({
      from: '"Young Reporter" <<EMAIL>>',
      to,
      subject: emailContent.subject,
      html: emailContent.html,
    })

    // For development, log the URL where the email can be previewed
    if (process.env.NODE_ENV !== 'production') {
      console.log('Email preview URL: %s', nodemailer.getTestMessageUrl(info))
    }

    return { success: true, messageId: info.messageId }
  } catch (error) {
    console.error('Error sending email:', error)
    return { success: false, error }
  }
}
