'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { getImageUrl, getImageAlt } from '@/utils/imageUtils'
import { renderRichText } from '@/utils/richTextUtils'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Loader2, Calendar, User, ArrowLeft, Star, Send } from 'lucide-react'
import { use } from 'react'

interface Article {
  id: string
  title: string
  content: Record<string, unknown> // Rich text content from Payload CMS
  author:
    | {
        id: string
        firstName: string
        lastName: string
        profileImage?:
          | {
              url: string
            }
          | string
      }
    | string
  category?: string
  createdAt: string
  status: string
  featuredImage?:
    | {
        url: string
        width?: number
        height?: number
        alt?: string
      }
    | string
  teacherReview?: Array<{
    reviewer: string | { id: string; firstName: string; lastName: string }
    reviewerName?: string
    comment: string
    rating: number
    approved: boolean
    reviewDate?: string
  }>
}

// For client components in Next.js App Router, we need to handle params differently
// In newer versions of Next.js, params is a Promise that needs to be unwrapped with use()
export default function ArticleDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter()
  const [article, setArticle] = useState<Article | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [reviewData, setReviewData] = useState({
    comment: '',
    rating: 5,
    approved: false,
  })
  const [isSubmittingReview, setIsSubmittingReview] = useState(false)
  const [reviewSuccess, setReviewSuccess] = useState(false)
  const [reviewError, setReviewError] = useState('')

  // Unwrap params using React.use() to avoid the warning
  const unwrappedParams = use(params)
  const articleId = unwrappedParams.id

  // Check if ID is a media path
  const isMediaPath = (path: string): boolean => {
    if (!path || typeof path !== 'string') return false

    return (
      path.includes('/api/media') ||
      path.includes('/media') ||
      path.includes('/file/') ||
      /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(path)
    )
  }

  // Define fetchArticleDetail outside useEffect so it can be called from other functions
  const fetchArticleDetail = async () => {
    try {
      // Check if articleId is a media path
      if (isMediaPath(articleId)) {
        console.error('Invalid article ID (media path):', articleId)
        setError(
          'Invalid article ID: Media path detected. Please go back and select a valid article.',
        )
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      setReviewSuccess(false) // Reset review success state when refreshing data
      console.log('Fetching article with ID:', articleId)

      // Use the API endpoint to fetch the article
      // This avoids importing server-only modules in the client component
      const response = await fetch(`/api/dashboard/articles/${articleId}`, {
        credentials: 'include',
      })

      if (!response.ok) {
        console.error('API response not OK:', response.status, response.statusText)
        throw new Error('Failed to fetch article detail')
      }

      const data = await response.json()
      console.log('Article data received:', data)

      // Handle the standardized API response format
      if (data.success && data.data?.article) {
        setArticle(data.data.article)
      } else {
        console.error('Invalid article data format:', data)
        throw new Error(data.error || 'Failed to fetch article data')
      }
      setIsLoading(false)
    } catch (err) {
      console.error('Error fetching article detail:', err)
      setError('Failed to load article detail. Please try again.')
      setIsLoading(false)
    }
  }

  // Fetch article details when the component mounts or articleId changes
  useEffect(() => {
    fetchArticleDetail()
  }, [articleId])

  // Store current user data in state for checking if the user has already reviewed the article
  const [currentUser, setCurrentUser] = useState<any>(null)

  useEffect(() => {
    // Fetch the current user
    const fetchCurrentUser = async () => {
      try {
        const response = await fetch('/api/auth/me', {
          credentials: 'include',
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success && data.data?.user) {
            // Store the user in state
            setCurrentUser(data.data.user)
          }
        }
      } catch (error) {
        console.error('Error fetching current user:', error)
      }
    }

    fetchCurrentUser()
  }, [])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const handleReviewSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Submitting review...')

    if (!article) {
      console.error('No article available for review')
      setReviewError('No article available for review')
      return
    }

    // Validate form
    if (!reviewData.comment.trim()) {
      console.log('Missing comment')
      setReviewError('Please provide feedback comments')
      return
    }

    if (reviewData.rating < 1 || reviewData.rating > 10) {
      console.log('Invalid rating:', reviewData.rating)
      setReviewError('Rating must be between 1 and 10')
      return
    }

    try {
      setIsSubmittingReview(true)
      setReviewError('')
      console.log('Review data:', reviewData)

      // Create form data for submission
      const formData = new FormData()
      formData.append('rating', reviewData.rating.toString())
      formData.append('comment', reviewData.comment)
      formData.append('approved', reviewData.approved.toString())
      console.log('Form data created')

      // We'll use the articleId from the URL directly
      // This ensures we're using the same ID that was used to fetch the article
      console.log('Submitting review for article ID from URL:', articleId)

      // Log the article object for debugging
      console.log('Article object:', {
        id: article.id,
        title: article.title,
        status: article.status,
      })

      // Use the API endpoint that matches the URL structure
      // This ensures we're using the same article ID as in the URL
      console.log('Using article-specific review endpoint')

      // Don't need to append articleId since it's in the URL
      const response = await fetch(`/api/dashboard/articles/${articleId}/review`, {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          // Add headers to ensure cookies are sent
          Accept: 'application/json',
        },
      })

      console.log('Response received:', response.status, response.statusText)

      // Parse the response
      let responseData
      try {
        responseData = await response.json()
        console.log('Response data:', responseData)
      } catch (parseError) {
        console.error('Error parsing response:', parseError)
        throw new Error('Failed to parse server response')
      }

      if (!response.ok) {
        console.error('Response not OK:', response.status, responseData)

        // Check if the error is due to article ID mismatch
        if (response.status === 400 && responseData.correctId) {
          console.log('Article ID mismatch. Correct ID:', responseData.correctId)

          // Update the form with the correct ID and try again
          console.log('Retrying with correct article ID')

          const retryFormData = new FormData()
          retryFormData.append('rating', reviewData.rating.toString())
          retryFormData.append('comment', reviewData.comment)
          retryFormData.append('approved', reviewData.approved.toString())
          retryFormData.append('articleId', responseData.correctId)

          const retryResponse = await fetch('/api/submit-review', {
            method: 'POST',
            body: retryFormData,
            credentials: 'include',
            headers: {
              Accept: 'application/json',
            },
          })

          const retryData = await retryResponse.json()

          if (retryResponse.ok && retryData.success) {
            console.log('Retry successful')

            // Show success message
            setReviewSuccess(true)

            // Reset form
            setReviewData({
              comment: '',
              rating: 5,
              approved: false,
            })

            // If the API returned the updated article, use it directly
            if (retryData.data?.article) {
              console.log('Using article from response')
              setArticle(retryData.data.article)
            } else {
              // Otherwise, refresh article data after a short delay
              console.log('Refreshing article data')
              setTimeout(() => {
                fetchArticleDetail()
              }, 1000)
            }

            return
          } else {
            console.error('Retry failed:', retryData)
            throw new Error(retryData.error || 'Failed to submit review')
          }
        }

        throw new Error(responseData.error || 'Failed to submit review')
      }

      if (responseData.success) {
        console.log('Review submitted successfully:', responseData.message)

        // Show success message
        setReviewSuccess(true)

        // Reset form
        setReviewData({
          comment: '',
          rating: 5,
          approved: false,
        })

        // If the API returned the updated article, use it directly
        if (responseData.data?.article) {
          console.log('Using article from response')
          setArticle(responseData.data.article)
        } else {
          // Otherwise, refresh article data after a short delay
          console.log('Refreshing article data')
          setTimeout(() => {
            fetchArticleDetail()
          }, 1000)
        }
      } else {
        console.error('Response indicates failure:', responseData)
        throw new Error(responseData.error || 'Failed to submit review')
      }
    } catch (err) {
      console.error('Error submitting review:', err)
      setReviewError(err instanceof Error ? err.message : 'Failed to submit review')
    } finally {
      setIsSubmittingReview(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col space-y-6">
        <Button variant="ghost" className="w-fit" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Articles
        </Button>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4 rounded-md text-red-800 dark:text-red-300">
            {error}
          </div>
        ) : article ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <Card>
                {article.featuredImage && (
                  <div className="w-full h-64 md:h-80 overflow-hidden">
                    <Image
                      src={getImageUrl(article.featuredImage)}
                      alt={getImageAlt(article.featuredImage, article.title)}
                      className="w-full h-full object-cover"
                      width={800}
                      height={400}
                      onError={(e) => {
                        // Fallback to a placeholder image on error
                        const target = e.target as HTMLImageElement
                        target.src = 'https://placehold.co/800x400?text=Image+Not+Available'
                      }}
                    />
                  </div>
                )}
                <CardHeader>
                  <div className="flex flex-wrap gap-2 mb-2">
                    <Badge
                      className={
                        article.status === 'published'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                          : article.status === 'pending-review'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                            : ''
                      }
                    >
                      {article.status}
                    </Badge>
                  </div>
                  <CardTitle className="text-2xl md:text-3xl">{article.title}</CardTitle>
                  <div className="flex flex-wrap items-center text-sm text-gray-500 mt-2 gap-4">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {formatDate(article.createdAt)}
                    </div>
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      {article.author
                        ? typeof article.author === 'object'
                          ? `${article.author.firstName || ''} ${article.author.lastName || ''}`.trim() ||
                            'Unknown'
                          : 'Unknown'
                        : 'Unknown'}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="prose dark:prose-invert max-w-none">
                    {article.content ? (
                      <div className="whitespace-pre-wrap">{renderRichText(article.content)}</div>
                    ) : (
                      <p>No content available</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Article Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Author</h3>
                      <div className="flex items-center mt-1">
                        <Avatar className="h-8 w-8 mr-2">
                          {typeof article.author === 'object' && article.author?.profileImage ? (
                            <AvatarImage
                              src={getImageUrl(article.author.profileImage)}
                              alt={getImageAlt(
                                article.author.profileImage,
                                typeof article.author === 'object'
                                  ? `${article.author.firstName || ''} ${article.author.lastName || ''}`.trim() ||
                                      'Author'
                                  : 'Author',
                              )}
                            />
                          ) : (
                            <AvatarFallback>
                              {typeof article.author === 'object' && article.author.firstName
                                ? article.author.firstName[0]
                                : 'A'}
                              {typeof article.author === 'object' && article.author.lastName
                                ? article.author.lastName[0]
                                : ''}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <span>
                          {article.author
                            ? typeof article.author === 'object'
                              ? `${article.author.firstName || ''} ${article.author.lastName || ''}`.trim() ||
                                'Unknown'
                              : 'Unknown'
                            : 'Unknown'}
                        </span>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Status</h3>
                      <Badge
                        className={`mt-1 ${
                          article.status === 'published'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                            : article.status === 'pending-review'
                              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                              : ''
                        }`}
                      >
                        {article.status}
                      </Badge>
                    </div>

                    {article.teacherReview && article.teacherReview.length > 0 && currentUser && (
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Your Reviews</h3>
                        <div className="space-y-3 mt-2">
                          {/* Filter reviews to only show the current user's reviews */}
                          {article.teacherReview
                            .filter(
                              (review) =>
                                (typeof review.reviewer === 'object' &&
                                  review.reviewer?.id === currentUser.id) ||
                                review.reviewer === currentUser.id,
                            )
                            .map((review, index) => (
                              <div
                                key={index}
                                className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md"
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center">
                                    <Star className="h-4 w-4 text-yellow-500 mr-1" />
                                    <span className="font-medium">{review.rating}/10</span>
                                  </div>
                                  <Badge
                                    className={
                                      review.approved
                                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                    }
                                  >
                                    {review.approved ? 'Approved' : 'Not Approved'}
                                  </Badge>
                                </div>
                                <p className="text-sm mt-2">{review.comment}</p>
                              </div>
                            ))}
                        </div>
                      </div>
                    )}

                    {/* Add Review Form - Show for pending-review and published articles */}
                    {(article.status === 'pending-review' || article.status === 'published') && (
                      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h3 className="text-lg font-medium mb-4">Add Your Review</h3>

                        {/* Check if the teacher has already reviewed this article */}
                        {currentUser &&
                        article.teacherReview &&
                        article.teacherReview.some((review) => {
                          // Check if the current user is the reviewer
                          return (
                            (typeof review.reviewer === 'object' &&
                              review.reviewer?.id === currentUser.id) ||
                            review.reviewer === currentUser.id
                          )
                        }) ? (
                          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md text-yellow-800 dark:text-yellow-300 mb-4">
                            You have already reviewed this article.
                          </div>
                        ) : reviewSuccess ? (
                          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-md text-green-800 dark:text-green-300 mb-4">
                            Review submitted successfully!
                          </div>
                        ) : (
                          <form onSubmit={handleReviewSubmit} className="space-y-4">
                            {reviewError && (
                              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md text-red-800 dark:text-red-300 mb-4">
                                {reviewError}
                              </div>
                            )}

                            <div>
                              <label
                                htmlFor="rating"
                                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                              >
                                Rating (1-10)
                              </label>
                              <input
                                type="number"
                                id="rating"
                                min="1"
                                max="10"
                                value={reviewData.rating}
                                onChange={(e) =>
                                  setReviewData({
                                    ...reviewData,
                                    rating: parseInt(e.target.value) || 5,
                                  })
                                }
                                className="w-24 p-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-800"
                                required
                              />
                            </div>

                            <div>
                              <label
                                htmlFor="comment"
                                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                              >
                                Feedback
                              </label>
                              <Textarea
                                id="comment"
                                value={reviewData.comment}
                                onChange={(e) =>
                                  setReviewData({ ...reviewData, comment: e.target.value })
                                }
                                className="w-full min-h-[100px]"
                                placeholder="Provide constructive feedback for the author..."
                                required
                              />
                            </div>

                            {/* Only show approval checkbox for pending-review articles */}
                            {article.status === 'pending-review' && (
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  id="approved"
                                  checked={reviewData.approved}
                                  onChange={(e) =>
                                    setReviewData({ ...reviewData, approved: e.target.checked })
                                  }
                                  className="h-4 w-4 text-primary border-gray-300 rounded"
                                />
                                <label
                                  htmlFor="approved"
                                  className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
                                >
                                  Approve for publication
                                </label>
                              </div>
                            )}

                            <Button type="submit" className="w-full" disabled={isSubmittingReview}>
                              {isSubmittingReview ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Submitting...
                                </>
                              ) : (
                                <>
                                  <Send className="mr-2 h-4 w-4" />
                                  Submit Review
                                </>
                              )}
                            </Button>
                          </form>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-md">
            <p className="text-gray-500 dark:text-gray-400">Article not found</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
