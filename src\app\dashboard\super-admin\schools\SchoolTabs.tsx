"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from '@/components/ui/tabs';
import { ContentManager } from '../components/content-manager';
import { FeedbackManager } from '../components/feedback-manager';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { toast } from '@/components/ui/use-toast';

// User type for users array
interface User {
  id: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  email: string;
  role: string | { slug: string };
  status: string;
  grade?: string;
  createdAt?: string;
}

interface School {
  id: string;
  name: string;
  address?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Fetch roles and provide lookup to UsersManager
function useRolesLookup() {
  const [roles, setRoles] = useState<{ [id: string]: string }>({});
  useEffect(() => {
    fetch('/api/dashboard/super-admin/roles')
      .then(res => res.json())
      .then(data => {
        if (data.roles) {
          const lookup: { [id: string]: string } = {};
          data.roles.forEach((role: any) => {
            if (role.id) lookup[role.id] = role.slug;
            if (role._id) lookup[role._id] = role.slug;
            if (role.slug) lookup[role.slug] = role.slug;
          });
          setRoles(lookup);
        }
      });
  }, []);
  return roles;
}

function UsersManager({ schoolId, rolesLookup }: { schoolId: string, rolesLookup: { [id: string]: string } }) {
  const [loading, setLoading] = useState<boolean>(true);
  const [users, setUsers] = useState<User[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<{message: string, password?: string} | null>(null);
  
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const res = await fetch(`/api/dashboard/super-admin/users?schoolId=${schoolId}`);
      if (!res.ok) throw new Error('فشل في جلب المستخدمين');
      const data = await res.json();
      setUsers(data.users || []);
      setLoading(false);
    } catch (e) {
      setError('فشل في تحميل المستخدمين');
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchUsers();
  }, [schoolId]);
  
  if (loading) return <Skeleton className="h-[300px] w-full" />;
  if (error) return <div className="text-red-500">{error}</div>;

  // Helper to display role
  const getRoleDisplay = (user: any) => {
    if (user.role && typeof user.role === 'object') {
      return user.role.slug || user.role.id || 'دور غير معروف';
    }
    if (typeof user.role === 'string') {
      if (rolesLookup[user.role]) return rolesLookup[user.role];
      // If it's an ObjectId string, show as 'Unknown Role'
      if (/^[a-f\d]{24}$/i.test(user.role)) return rolesLookup[user.role] || 'دور غير معروف';
      return user.role;
    }
    return 'دور غير معروف';
  };

  // Function to translate role names to Arabic
  const translateRole = (role: string) => {
    const roleMap: Record<string, string> = {
      'student': 'طالب',
      'teacher': 'معلم',
      'mentor': 'موجه',
      'school-admin': 'مدير مدرسة',
      'super-admin': 'مدير النظام',
      'admin': 'مدير',
      'editor': 'محرر',
      'Unknown Role': 'دور غير معروف'
    };
    return roleMap[role] || role;
  };

  // Separate students from other users
  const students = users.filter(
    (user) => getRoleDisplay(user) === 'student'
  );
  const others = users.filter(
    (user) => getRoleDisplay(user) !== 'student'
  );
  
  // Handle adding new user
  const handleAddUser = (role: string) => {
    setSelectedRole(role);
    setIsAddingUser(true);
    setSuccessMessage(null);
  };
  
  const handleSubmitNewUser = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const formData = new FormData(e.currentTarget);
      const userData = {
        firstName: formData.get('firstName') as string,
        lastName: formData.get('lastName') as string,
        email: formData.get('email') as string,
        role: selectedRole,
        schoolId: schoolId,
        grade: formData.get('grade') as string || undefined
      };
      
      const response = await fetch('/api/dashboard/super-admin/schools/add-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ أثناء إنشاء المستخدم');
      }
      
      // Show success message with temporary password
      setSuccessMessage({
        message: data.message,
        password: data.tempPassword
      });
      
      // Refresh user list
      fetchUsers();
      
      // Reset form
      e.currentTarget.reset();
      
      // Close dialog after a delay
      setTimeout(() => {
        setIsAddingUser(false);
      }, 5000);
      
    } catch (error) {
      console.error('Error adding user:', error);
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء المستخدم';
      toast({
        title: 'خطأ',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-8" dir="rtl">
      <div className="flex flex-wrap gap-2 mb-4">
        <Button onClick={() => handleAddUser('student')} variant="outline" size="sm">
          <Plus className="h-4 w-4 ml-2" /> إضافة طالب
        </Button>
        <Button onClick={() => handleAddUser('teacher')} variant="outline" size="sm">
          <Plus className="h-4 w-4 ml-2" /> إضافة معلم
        </Button>
        <Button onClick={() => handleAddUser('mentor')} variant="outline" size="sm">
          <Plus className="h-4 w-4 ml-2" /> إضافة موجه
        </Button>
        <Button onClick={() => handleAddUser('school-admin')} variant="outline" size="sm">
          <Plus className="h-4 w-4 ml-2" /> إضافة مدير مدرسة
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>الطلاب</CardTitle>
        </CardHeader>
        <CardContent>
          <table className="w-full text-sm">
            <thead>
              <tr>
                <th>الاسم</th>
                <th>البريد الإلكتروني</th>
                <th>الصف</th>
                <th>الحالة</th>
                <th>تاريخ الإنشاء</th>
                <th>الدور</th>
              </tr>
            </thead>
            <tbody>
              {students.length > 0 ? students.map((user) => (
                <tr key={user.id} className="border-b">
                  <td>{user.name || `${user.firstName || ''} ${user.lastName || ''}`}</td>
                  <td>{user.email}</td>
                  <td>{user.grade || '-'}</td>
                  <td><Badge>{user.status === 'active' ? 'نشط' : user.status === 'pending' ? 'معلق' : user.status}</Badge></td>
                  <td>{user.createdAt ? new Date(user.createdAt).toLocaleDateString('ar-EG') : '-'}</td>
                  <td>{translateRole(getRoleDisplay(user))}</td>
                </tr>
              )) : (
                <tr><td colSpan={6} className="text-center text-muted-foreground">لم يتم العثور على طلاب</td></tr>
              )}
            </tbody>
          </table>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>المستخدمون الآخرون</CardTitle>
        </CardHeader>
        <CardContent>
          <table className="w-full text-sm">
            <thead>
              <tr>
                <th>الاسم</th>
                <th>البريد الإلكتروني</th>
                <th>الدور</th>
                <th>الحالة</th>
                <th>تاريخ الإنشاء</th>
              </tr>
            </thead>
            <tbody>
              {others.length > 0 ? others.map((user) => (
                <tr key={user.id} className="border-b">
                  <td>{user.name || `${user.firstName || ''} ${user.lastName || ''}`}</td>
                  <td>{user.email}</td>
                  <td>{translateRole(getRoleDisplay(user))}</td>
                  <td><Badge>{user.status === 'active' ? 'نشط' : user.status === 'pending' ? 'معلق' : user.status}</Badge></td>
                  <td>{user.createdAt ? new Date(user.createdAt).toLocaleDateString('ar-EG') : '-'}</td>
                </tr>
              )) : (
                <tr><td colSpan={5} className="text-center text-muted-foreground">لم يتم العثور على مستخدمين آخرين</td></tr>
              )}
            </tbody>
          </table>
        </CardContent>
      </Card>
      
      {/* Add User Dialog */}
      <Dialog open={isAddingUser} onOpenChange={setIsAddingUser}>
        <DialogContent dir="rtl">
          <DialogHeader>
            <DialogTitle>إضافة {translateRole(selectedRole)} جديد</DialogTitle>
            <DialogDescription>
              أدخل معلومات المستخدم الجديد للمدرسة
            </DialogDescription>
          </DialogHeader>
          
          {successMessage ? (
            <div className="p-4 rounded-md bg-green-50 text-green-700 mb-4">
              <h3 className="font-bold mb-2">{successMessage.message}</h3>
              {successMessage.password && (
                <div className="mt-2">
                  <p className="font-medium">كلمة المرور المؤقتة:</p>
                  <code className="bg-green-100 p-1 rounded">{successMessage.password}</code>
                  <p className="text-xs mt-1">يرجى تدوين كلمة المرور هذه، فلن يتم عرضها مرة أخرى.</p>
                </div>
              )}
            </div>
          ) : (
            <form onSubmit={handleSubmitNewUser} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="firstName" className="text-sm font-medium">الاسم الأول</label>
                  <Input id="firstName" name="firstName" required />
                </div>
                <div className="space-y-2">
                  <label htmlFor="lastName" className="text-sm font-medium">اسم العائلة</label>
                  <Input id="lastName" name="lastName" required />
                </div>
              </div>
              
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">البريد الإلكتروني</label>
                <Input id="email" name="email" type="email" required />
              </div>
              
              {selectedRole === 'student' && (
                <div className="space-y-2">
                  <label htmlFor="grade" className="text-sm font-medium">الصف</label>
                  <Input id="grade" name="grade" />
                </div>
              )}
              
              <DialogFooter className="mt-6">
                <Button type="button" variant="outline" onClick={() => setIsAddingUser(false)} disabled={isSubmitting}>إلغاء</Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <span className="animate-spin ml-2">⏳</span>
                      جاري الإنشاء...
                    </>
                  ) : (
                    <>إضافة المستخدم</>
                  )}
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

function ActivitiesManager({ schoolId }: { schoolId: string }) {
  const [loading, setLoading] = useState(true);
  const [activities, setActivities] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  useEffect(() => {
    setLoading(true);
    const url = showDetails
      ? `/api/dashboard/super-admin/activities?schoolId=${schoolId}&raw=true`
      : `/api/dashboard/super-admin/activities?schoolId=${schoolId}`;
    fetch(url)
      .then(res => res.json())
      .then(res => { setActivities(res.activities || []); setLoading(false); })
      .catch(e => { setError('فشل في تحميل الأنشطة'); setLoading(false); });
  }, [schoolId, showDetails]);
  if (loading) return <Skeleton className="h-[300px] w-full" />;
  if (error) return <div className="text-red-500">{error}</div>;
  
  // Helper to format details as a pretty list
  function renderDetails(details: any) {
    if (!details || typeof details !== 'object') return '-';
    return (
      <ul className="text-xs text-muted-foreground space-y-1">
        {Object.entries(details).map(([key, value]) => (
          <li key={key}><span className="font-medium text-foreground">{translateKey(key)}:</span> {typeof value === 'string' ? value : JSON.stringify(value)}</li>
        ))}
      </ul>
    );
  }
  
  // Helper to translate activity data keys
  function translateKey(key: string) {
    const keyMap: Record<string, string> = {
      'userId': 'معرف المستخدم',
      'userName': 'اسم المستخدم',
      'userRole': 'دور المستخدم',
      'description': 'الوصف',
      'contentId': 'معرف المحتوى',
      'contentType': 'نوع المحتوى',
      'contentTitle': 'عنوان المحتوى',
      'action': 'الإجراء',
      'status': 'الحالة',
      'schoolId': 'معرف المدرسة',
      'schoolName': 'اسم المدرسة',
      'timestamp': 'الطابع الزمني',
      'date': 'التاريخ',
      'details': 'التفاصيل',
      'count': 'العدد',
      'id': 'المعرف',
      'type': 'النوع'
    };
    return keyMap[key] || key;
  }
  
  // Translate activity types
  function translateActivityType(type: string) {
    const typeMap: Record<string, string> = {
      'login': 'تسجيل الدخول',
      'logout': 'تسجيل الخروج',
      'article-create': 'إنشاء مقال',
      'article-edit': 'تعديل مقال',
      'article-publish': 'نشر مقال',
      'article-delete': 'حذف مقال',
      'news-create': 'إنشاء خبر',
      'news-edit': 'تعديل خبر',
      'news-publish': 'نشر خبر',
      'news-delete': 'حذف خبر',
      'comment-create': 'إنشاء تعليق',
      'comment-edit': 'تعديل تعليق',
      'comment-delete': 'حذف تعليق',
      'user-create': 'إنشاء مستخدم',
      'user-edit': 'تعديل مستخدم',
      'user-delete': 'حذف مستخدم',
      'feedback-create': 'إنشاء تعليق',
      'feedback-respond': 'الرد على تعليق',
      'admin-action': 'إجراء إداري'
    };
    return typeMap[type] || type;
  }
  
  function formatDate(date: string) {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleString('ar-EG');
  }
  
  return (
    <Card dir="rtl">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>سجل الأنشطة</CardTitle>
        <button
          className="text-xs underline text-blue-600 hover:text-blue-800"
          onClick={() => setShowDetails((v) => !v)}
        >
          {showDetails ? 'عرض الملخص' : 'عرض التفاصيل'}
        </button>
      </CardHeader>
      <CardContent>
        {showDetails ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {activities.length > 0 ? activities.map((a, idx) => (
              <div key={a._id?.toString?.() || a._id || idx} className="border rounded-lg p-4 bg-muted/50">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs text-muted-foreground">{formatDate(a.date || a.createdAt)}</span>
                  <span className="text-xs px-2 py-1 rounded bg-gray-200 text-gray-700">{translateActivityType(a.activityType) || '-'}</span>
                </div>
                <div className="font-medium text-sm mb-1">{a.userName || a.userId || '-'} <span className="text-xs text-muted-foreground">({a.userRole || '-'})</span></div>
                <div className="text-xs mb-2 text-muted-foreground">{a.description || '-'}</div>
                <div className="text-xs font-semibold mb-1">التفاصيل:</div>
                {renderDetails(a.details)}
              </div>
            )) : (
              <div className="col-span-2 text-center text-muted-foreground">لم يتم العثور على أنشطة</div>
            )}
          </div>
        ) : (
          <table className="w-full text-sm">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>العدد</th>
                <th>المدرسة</th>
              </tr>
            </thead>
            <tbody>
              {activities.length > 0 ? activities.map((activity, idx) => (
                <tr key={idx} className="border-b">
                  <td>{activity.date}</td>
                  <td>{activity.count}</td>
                  <td>{activity.schoolName || '-'}</td>
                </tr>
              )) : (
                <tr><td colSpan={3} className="text-center text-muted-foreground">لم يتم العثور على أنشطة</td></tr>
              )}
            </tbody>
          </table>
        )}
      </CardContent>
    </Card>
  );
}

// For debugging: Add debug output for ContentManager tabs
// (You can remove this after confirming data loads)
function DebugContent({ schoolId, type }: { schoolId: string; type: string }) {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    setLoading(true);
    fetch(`/api/dashboard/super-admin/content?schoolId=${schoolId}&type=${type}`)
      .then(res => res.json())
      .then(res => { setData(res.content || []); setLoading(false); })
      .catch(e => { setError('فشل في التحميل'); setLoading(false); });
  }, [schoolId, type]);
  if (loading) return <div>جاري تحميل {type === 'article' ? 'المقالات' : type === 'news' ? 'الأخبار' : 'التعليقات'}...</div>;
  if (error) return <div>خطأ: {error}</div>;
  return <pre style={{ maxHeight: 200, overflow: 'auto', background: '#eee', fontSize: 12 }}>{JSON.stringify(data, null, 2)}</pre>;
}

export default function SchoolTabs({ schoolId, school }: { schoolId: string; school: School }) {
  const rolesLookup = useRolesLookup();
  return (
    <Tabs defaultValue="overview" className="w-full" dir="rtl">
      <TabsList>
        <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
        <TabsTrigger value="users">المستخدمون</TabsTrigger>
        <TabsTrigger value="article">المقالات</TabsTrigger>
        <TabsTrigger value="news">الأخبار</TabsTrigger>
        <TabsTrigger value="comment">التعليقات</TabsTrigger>
        <TabsTrigger value="feedback">تقييمات المعلمين والموجهين</TabsTrigger>
        <TabsTrigger value="activities">الأنشطة</TabsTrigger>
      </TabsList>
      <TabsContent value="overview">
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>نظرة عامة عن المدرسة</CardTitle>
          </CardHeader>
          <CardContent>
            <div><strong>العنوان:</strong> {school.address}</div>
            <div><strong>تاريخ الإنشاء:</strong> {school.createdAt ? new Date(school.createdAt).toLocaleString('ar-EG') : '-'}</div>
            <div><strong>تاريخ التحديث:</strong> {school.updatedAt ? new Date(school.updatedAt).toLocaleString('ar-EG') : '-'}</div>
            {/* Add stats and recent activity here if desired */}
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="users">
        <UsersManager schoolId={schoolId} rolesLookup={rolesLookup} />
      </TabsContent>
      <TabsContent value="article">
        <ContentManager schoolId={schoolId} type="article" />
        <DebugContent schoolId={schoolId} type="article" />
      </TabsContent>
      <TabsContent value="news">
        <ContentManager schoolId={schoolId} type="news" />
        <DebugContent schoolId={schoolId} type="news" />
      </TabsContent>
      <TabsContent value="comment">
        <ContentManager schoolId={schoolId} type="comment" />
        <DebugContent schoolId={schoolId} type="comment" />
      </TabsContent>
      <TabsContent value="feedback">
        <FeedbackManager schoolId={schoolId} />
      </TabsContent>
      <TabsContent value="activities">
        <ActivitiesManager schoolId={schoolId} />
      </TabsContent>
    </Tabs>
  );
} 