import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { cookies } from 'next/headers'

import config from '@/payload.config'

export async function POST(req: NextRequest, { params }: { params: any }) {
  try {
    // In Next.js App Router, we need to properly handle params
    // We must await context.params before accessing its properties
    const resolvedParams = await params
    const { id } = resolvedParams

    const payload = await getPayload({
      config,
    })

    // Use payload's built-in auth method instead of manually handling cookies
    let user: any = null
    try {
      // Get the token from cookies
      const cookieStore = await cookies()
      const token = cookieStore.get('payload-token')?.value

      // Use the token to get the user
      if (token) {
        try {
          // Use findByID to get the user from the token
          const jwt = await import('jsonwebtoken')
          const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

          if (decoded && typeof decoded === 'object' && decoded.id) {
            user = await payload.findByID({
              collection: 'users',
              id: decoded.id,
            })
          }
        } catch (error) {
          console.error('Error verifying token:', error)
        }
      }

      if (!user) {
        console.log('No authenticated user found')
        return NextResponse.json(
          { error: 'You must be logged in to review an article' },
          { status: 401 },
        )
      }

      console.log('Authenticated user:', user.id, user.email)
    } catch (authError) {
      console.error('Authentication error:', authError)
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 })
    }

    // Check if user is a teacher or mentor
    const userRole = typeof user.role === 'object' ? user.role?.slug : user.role

    const isTeacher = userRole === 'teacher'
    const isMentor = userRole === 'mentor'
    const isAdmin = userRole === 'super-admin' || userRole === 'school-admin'

    if (!isTeacher && !isMentor && !isAdmin) {
      console.log('User does not have permission to review articles:', userRole)
      return NextResponse.json(
        { error: 'Only teachers, mentors, and admins can review articles' },
        { status: 403 },
      )
    }

    // For mentors and admins, we'll add a special flag to the review
    const isTeacherReview = isTeacher

    // Get the article
    console.log('Looking for article with ID:', id)

    try {
      // First try to find the article using find() to avoid NotFound errors
      const articlesResult = await payload.find({
        collection: 'articles',
        where: {
          or: [{ id: { equals: id } }, { slug: { equals: id } }],
        },
        limit: 1,
      })

      if (articlesResult.docs.length === 0) {
        console.log('Article not found with ID:', id)
        return NextResponse.json({ error: 'Article not found' }, { status: 404 })
      }

      const article = articlesResult.docs[0]
      console.log('Found article:', article.id, article.title)

      // Check if article is pending review
      if (article.status !== 'pending-review') {
        return NextResponse.json({ error: 'This article is not pending review' }, { status: 400 })
      }

      const formData = await req.formData()
      const rating = parseInt(formData.get('rating') as string, 10)
      const comment = formData.get('comment') as string
      const approved = formData.has('approved') && formData.get('approved') === 'true'

      // Validate inputs
      if (!rating || !comment) {
        return NextResponse.json({ error: 'Rating and comment are required' }, { status: 400 })
      }

      if (rating < 1 || rating > 10) {
        return NextResponse.json({ error: 'Rating must be between 1 and 10' }, { status: 400 })
      }

      // Create the review
      const teacherReview = {
        reviewer: user.id,
        rating,
        comment,
        approved,
        reviewerRole: userRole,
        isMentorReview: !isTeacherReview,
        createdAt: new Date().toISOString(),
      }

      // Update the article with the review
      const updatedArticle = await payload.update({
        collection: 'articles',
        id: article.id,
        data: {
          teacherReview: [...(article.teacherReview || []), teacherReview],
          // If approved, set status to published
          ...(approved ? { status: 'published' } : {}),
        },
      })

      // Create an activity record for this review
      try {
        await payload.create({
          collection: 'activities',
          data: {
            userId: user.id,
            activityType: 'article-review',
            details: {
              articleId: article.id,
              articleTitle: article.title,
              rating,
              comment,
              approved,
            },
            createdAt: new Date().toISOString(),
          },
        })
        console.log('Created activity record for review')
      } catch (activityError) {
        console.error('Error creating activity record:', activityError)
        // Continue even if activity creation fails
      }

      // Check if there's a redirect URL in the query parameters
      const url = new URL(req.url)
      const redirectUrl = url.searchParams.get('redirect')

      if (redirectUrl) {
        // Return a redirect response
        return NextResponse.redirect(new URL(redirectUrl, req.url))
      } else {
        // Return a success response
        return NextResponse.json({
          success: true,
          message: 'Review submitted successfully',
          data: {
            article: updatedArticle,
          },
        })
      }
    } catch (articleError) {
      console.error('Error processing article:', articleError)
      return NextResponse.json({ error: 'Error processing article' }, { status: 500 })
    }
  } catch (error) {
    console.error('Error reviewing article:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
