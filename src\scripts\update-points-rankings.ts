import { getPayload } from 'payload'
import { updatePointsAndRankings } from '../utils/points'
import config from '../payload.config'

/**
 * <PERSON>ript to update points and top rankings
 * Run this with npm run ts-node src/scripts/update-points-rankings.ts
 */
const updatePoints = async () => {
  try {
    console.log('Starting points and rankings update...')
    
    // Initialize Payload
    const payload = await getPayload({ config })
    
    // Update points and rankings
    await updatePointsAndRankings({ payload })
    
    console.log('Points and rankings update completed successfully!')
    process.exit(0)
  } catch (error) {
    console.error('Error updating points and rankings:', error)
    process.exit(1)
  }
}

updatePoints() 