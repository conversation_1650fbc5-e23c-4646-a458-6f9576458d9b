import type { CollectionConfig, FilterOptionsProps, Where } from 'payload' // {{change 1}} Import Where and FilterOptionsProps
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { newsAccess } from '../access'
import { getPayload } from 'payload'

export const News: CollectionConfig = {
  slug: 'news',
  admin: {
    useAsTitle: 'title',
  },
  access: {
    read: newsAccess,
    create: newsAccess,
    update: newsAccess,
    delete: newsAccess,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'content',
      type: 'richText',
      editor: lexicalEditor({}),
      required: true,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'This will be used in the URL',
      },
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Featured image for the news post (displayed at the top)',
      },
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'views',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Number of times this news item has been viewed',
        position: 'sidebar',
      },
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      // {{change 2}} Update filterOptions signature to return Promise<Where>
      filterOptions: async ({ req }: FilterOptionsProps<any>): Promise<Where> => {
        const payload = req.payload
        const mentorRole = await payload.find({
          collection: 'roles',
          where: {
            slug: {
              equals: 'mentor',
            },
          },
          limit: 1,
        })

        if (mentorRole.docs.length > 0) {
          // {{change 3}} Return a valid Where object filtering by the mentor role's ID
          return {
            role: {
              equals: mentorRole.docs[0].id,
            },
          }
        } else {
          // {{change 4}} Return a Where object that filters out all users if mentor role not found
          return {
            id: {
              equals: 'non-existent-id', // Use a value that will never match a valid user ID
            },
          }
        }
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Published', value: 'published' },
      ],
    },
  ],
  hooks: {
    // Set publishedAt when status changes to published
    beforeChange: [
      async ({ data, originalDoc }) => {
        // If status changed from draft to published and no publishedAt date
        if (originalDoc?.status === 'draft' && data.status === 'published' && !data.publishedAt) {
          // Set publishedAt to current date
          data.publishedAt = new Date().toISOString()
        }

        return data
      },
    ],
    // Update mentor rankings after news changes
    afterChange: [
      async ({ doc, req }) => {
        // Only update stats for published news
        if (doc.status === 'published') {
          try {
            const { payload } = req

            // Get all news posts with populated author
            const news = await payload.find({
              collection: 'news',
              where: {
                status: {
                  equals: 'published',
                },
              },
              depth: 1, // Populate relationships
              pagination: false,
            })

            // Count published news per mentor
            const mentorCounts: Record<string, { count: number; name: string }> = {}

            news.docs.forEach((newsItem) => {
              if (newsItem.author) {
                const mentorId =
                  typeof newsItem.author === 'object' ? newsItem.author.id : newsItem.author

                if (!mentorCounts[mentorId]) {
                  mentorCounts[mentorId] = {
                    count: 0,
                    name:
                      typeof newsItem.author === 'object'
                        ? newsItem.author.email
                        : 'Unknown Mentor',
                  }
                }

                mentorCounts[mentorId].count++
              }
            })

            // Convert to array and sort
            const rankings = Object.entries(mentorCounts)
              .map(([id, mentorData]) => ({
                id,
                name: mentorData.name,
                count: mentorData.count,
              }))
              .sort((a, b) => b.count - a.count)

            // Find existing mentor ranking or create new one
            const existingStats = await payload.find({
              collection: 'statistics',
              where: {
                type: {
                  equals: 'mentorRanking',
                },
              },
            })

            if (existingStats.docs.length > 0) {
              // Update existing
              await payload.update({
                collection: 'statistics',
                id: existingStats.docs[0].id,
                data: {
                  data: rankings,
                },
              })
            } else {
              // Create new
              await payload.create({
                collection: 'statistics',
                data: {
                  name: 'Mentor Rankings',
                  type: 'mentorRanking',
                  data: rankings,
                },
              })
            }
          } catch (error) {
            console.error('Error updating mentor rankings:', error)
          }
        }
      },
    ],
  },
}
