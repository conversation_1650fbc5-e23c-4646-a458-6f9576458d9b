'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export type ThemeColor = 'cyan' | 'blue' | 'purple' | 'green' | 'amber' | 'pink'

export interface ThemeContextType {
  darkMode: boolean
  themeColor: ThemeColor
  setDarkMode: (darkMode: boolean) => void
  setThemeColor: (color: ThemeColor) => void
  toggleDarkMode: () => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

interface ThemeProviderProps {
  children: ReactNode
  userId?: string
}

export function ThemeProvider({ children, userId }: ThemeProviderProps) {
  // Initialize state with empty values to prevent hydration mismatch
  const [darkMode, setDarkMode] = useState(false)
  const [themeColor, setThemeColor] = useState<ThemeColor>('cyan')
  const [isInitialized, setIsInitialized] = useState(false)

  // Function to apply theme to document
  const applyTheme = (isDark: boolean, color: ThemeColor) => {
    // Apply dark mode class
    if (isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
    
    // Apply theme color data attribute
    document.documentElement.setAttribute('data-theme', color)
  }

  // Initial load of theme from localStorage and API
  useEffect(() => {
    // This prevents running during server-side rendering
    if (typeof window === 'undefined') return
    
    // Synchronously apply theme from localStorage first to prevent flash
    const savedDarkMode = localStorage.getItem('darkMode') === 'true'
    const savedThemeColor = localStorage.getItem('themeColor') as ThemeColor || 'cyan'
    
    // Set state and apply theme
    setDarkMode(savedDarkMode)
    setThemeColor(savedThemeColor)
    applyTheme(savedDarkMode, savedThemeColor)
    
    // Mark as initialized
    setIsInitialized(true)
    
    // Now fetch from API if we have a userId
    if (userId) {
      fetch(`/api/user/preferences?userId=${userId}`, {
        cache: 'no-store',  // Don't cache this request
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      })
        .then(response => response.ok ? response.json() : null)
        .then(data => {
          if (data && data.theme) {
            const { darkMode: apiDarkMode, themeColor: apiThemeColor } = data.theme
            
            // Only update if different from localStorage
            if (apiDarkMode !== savedDarkMode || apiThemeColor !== savedThemeColor) {
              // Update state
              setDarkMode(apiDarkMode)
              setThemeColor(apiThemeColor || savedThemeColor)
              
              // Save to localStorage for next page load
              localStorage.setItem('darkMode', String(apiDarkMode))
              if (apiThemeColor) {
                localStorage.setItem('themeColor', apiThemeColor)
              }
              
              // Apply the theme changes
              applyTheme(apiDarkMode, apiThemeColor || savedThemeColor)
            }
          }
        })
        .catch(error => {
          console.error('Error fetching user theme preferences:', error)
        })
    }
    
    // Add event listener for storage events (theme changes in other tabs)
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'darkMode') {
        const newDarkMode = event.newValue === 'true'
        setDarkMode(newDarkMode)
        applyTheme(newDarkMode, themeColor)
      } else if (event.key === 'themeColor') {
        const newThemeColor = event.newValue as ThemeColor || 'cyan'
        setThemeColor(newThemeColor)
        applyTheme(darkMode, newThemeColor)
      }
    }
    
    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [userId]) // Only run on initial mount and when userId changes
  
  // Save theme changes to localStorage and API
  useEffect(() => {
    // Don't run during initial setup or server-side rendering
    if (!isInitialized || typeof window === 'undefined') return
    
    // Save to localStorage
    localStorage.setItem('darkMode', String(darkMode))
    localStorage.setItem('themeColor', themeColor)
    
    // Apply theme
    applyTheme(darkMode, themeColor)
    
    // Save to API if userId is available
    if (userId) {
      // Wrap the fetch call in a try/catch to prevent errors from crashing the app
      try {
        fetch('/api/user/preferences', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            theme: { darkMode, themeColor }
          }),
        }).catch(error => {
          // Already inside a try/catch, so we'll just log the error but not let it crash the app
          console.error('Error saving theme preferences:', error)
        })
      } catch (error) {
        console.error('Error in theme saving logic:', error)
      }
    }
  }, [darkMode, themeColor, userId, isInitialized])
  
  const toggleDarkMode = () => {
    setDarkMode(prevDarkMode => !prevDarkMode)
  }
  
  return (
    <ThemeContext.Provider value={{ darkMode, themeColor, setDarkMode, setThemeColor, toggleDarkMode }}>
      {children}
    </ThemeContext.Provider>
  )
} 