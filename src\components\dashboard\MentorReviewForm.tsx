'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Star, ThumbsUp, ThumbsDown, AlertTriangle, CheckCircle } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { isMediaPath } from '@/lib/media-utils'

interface MentorReviewFormProps {
  articleId: string
  reviewIndex: number
  teacherReview: {
    id?: string
    reviewer: {
      id: string
      firstName?: string
      lastName?: string
      email: string
    } | string
    rating: number
    comment: string
    approved: boolean
    createdAt?: string
  }
  redirectUrl?: string
}

export default function MentorReviewForm({
  articleId,
  reviewIndex,
  teacherReview,
  redirectUrl = '/dashboard/mentor'
}: MentorReviewFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  
  const [rating, setRating] = useState<string>('5')
  const [feedback, setFeedback] = useState('')
  const [quality, setQuality] = useState('good')
  
  // Get reviewer name
  const getReviewerName = () => {
    if (typeof teacherReview.reviewer === 'object') {
      return teacherReview.reviewer.firstName && teacherReview.reviewer.lastName
        ? `${teacherReview.reviewer.firstName} ${teacherReview.reviewer.lastName}`
        : teacherReview.reviewer.email
    }
    return 'مراجع غير معروف'
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')
    
    try {
      // Check if the article ID is a media path
      if (isMediaPath(articleId)) {
        setError('معرّف المقال غير صالح. يرجى العودة والمحاولة مرة أخرى.')
        setIsSubmitting(false)
        return
      }
      
      const response = await fetch(`/api/dashboard/mentor/rate-review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articleId,
          reviewIndex,
          rating: parseInt(rating),
          feedback,
          quality,
        }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'فشل في إرسال تقييم المراجعة')
      }
      
      setSuccess(true)
      
      // Redirect after a short delay
      setTimeout(() => {
        router.push(redirectUrl)
      }, 2000)
    } catch (err) {
      console.error('Error submitting mentor review:', err)
      setError(err instanceof Error ? err.message : 'فشل في إرسال تقييم المراجعة')
    } finally {
      setIsSubmitting(false)
    }
  }
  
  if (success) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center py-6" dir="rtl">
            <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
            <h3 className="text-xl font-semibold text-green-700">تم إرسال تقييم المراجعة</h3>
            <p className="text-gray-500 mt-2">تم إرسال تقييمك بنجاح.</p>
            <Button
              className="mt-6"
              onClick={() => router.push(redirectUrl)}
            >
              العودة إلى لوحة التحكم
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center" dir="rtl">
          <Star className="ml-2 h-5 w-5 text-yellow-500" />
          تقييم مراجعة المعلم
        </CardTitle>
      </CardHeader>
      <CardContent dir="rtl">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        <div className="mb-6 p-4 bg-gray-50 rounded-md">
          <h3 className="font-medium mb-2">مراجعة المعلم</h3>
          <div className="flex items-center mb-2">
            <p className="text-sm font-medium">المراجع:</p>
            <p className="text-sm mr-2">{getReviewerName()}</p>
          </div>
          <div className="flex items-center mb-2">
            <p className="text-sm font-medium">التقييم:</p>
            <div className="flex items-center mr-2">
              <Star className="h-4 w-4 text-yellow-500 ml-1" />
              <p className="text-sm">{teacherReview.rating}/10</p>
            </div>
          </div>
          <div className="flex items-center mb-2">
            <p className="text-sm font-medium">الحالة:</p>
            <div className="mr-2">
              {teacherReview.approved ? (
                <Badge variant="default" className="bg-green-100 text-green-800">
                  <ThumbsUp className="h-3 w-3 ml-1" />
                  تمت الموافقة
                </Badge>
              ) : (
                <Badge variant="destructive" className="bg-red-100 text-red-800">
                  <ThumbsDown className="h-3 w-3 ml-1" />
                  مرفوض
                </Badge>
              )}
            </div>
          </div>
          <div className="mt-3">
            <p className="text-sm font-medium">التعليق:</p>
            <p className="text-sm mt-1 whitespace-pre-wrap bg-white p-3 rounded border">
              {teacherReview.comment}
            </p>
          </div>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="rating">التقييم (1-10)</Label>
            <Select value={rating} onValueChange={setRating} required>
              <SelectTrigger id="rating">
                <SelectValue placeholder="اختر التقييم" />
              </SelectTrigger>
              <SelectContent>
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
                  <SelectItem key={value} value={value.toString()}>
                    {value}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="quality">جودة المراجعة</Label>
            <Select value={quality} onValueChange={setQuality} required>
              <SelectTrigger id="quality">
                <SelectValue placeholder="اختر الجودة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="excellent">ممتازة</SelectItem>
                <SelectItem value="good">جيدة</SelectItem>
                <SelectItem value="average">متوسطة</SelectItem>
                <SelectItem value="poor">ضعيفة</SelectItem>
                <SelectItem value="unacceptable">غير مقبولة</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="feedback">ملاحظات للمعلم</Label>
            <Textarea
              id="feedback"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="قدم ملاحظات بناءة للمعلم حول مراجعته..."
              rows={4}
              required
            />
          </div>
          
          <div className="flex justify-between items-center pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(redirectUrl)}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'جاري الإرسال...' : 'إرسال التقييم'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
