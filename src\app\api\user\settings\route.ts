import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

// GET endpoint to fetch user settings
export async function GET(req: NextRequest) {
  try {
    console.log('User settings GET API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    console.log('Token found:', token ? 'Yes' : 'No')

    if (!token) {
      console.log('No token found, returning 401')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Initialize Payload
      const payload = await getPayload({ config })

      // Get the user with their settings
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 0,
      })

      if (!user) {
        console.log('User not found')
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      // Extract notification settings from user
      const notificationPreferences = user.notificationPreferences || {
        emailNotifications: true,
        emailTypes: ['articleSubmissions', 'articleReviews', 'reviewEvaluations'],
      }

      // Return user settings
      return NextResponse.json({
        success: true,
        settings: {
          notifications: {
            emailNotifications: notificationPreferences.emailNotifications || false,
            articleApprovals:
              notificationPreferences.emailTypes?.includes('articleReviews') || false,
            newComments: notificationPreferences.emailTypes?.includes('reviewEvaluations') || false,
            systemUpdates:
              notificationPreferences.emailTypes?.includes('systemAnnouncements') || false,
          },
        },
      })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching user settings:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}

// POST endpoint to update user settings
export async function POST(req: NextRequest) {
  try {
    console.log('User settings POST API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    console.log('Token found:', token ? 'Yes' : 'No')

    if (!token) {
      console.log('No token found, returning 401')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      console.log('Request body:', body)

      const { notifications } = body

      if (!notifications) {
        console.log('Missing notification settings')
        return NextResponse.json({ error: 'Notification settings are required' }, { status: 400 })
      }

      // Initialize Payload
      const payload = await getPayload({ config })

      // Get the user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      if (!user) {
        console.log('User not found')
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      // Build email types array based on notification settings
      const emailTypes = []
      if (notifications.articleApprovals) emailTypes.push('articleReviews')
      if (notifications.newComments) emailTypes.push('reviewEvaluations')
      if (notifications.systemUpdates) emailTypes.push('systemAnnouncements')
      if (notifications.articleApprovals) emailTypes.push('articleSubmissions')

      // Get user role and other required fields
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
      console.log('User role:', userRole)

      // Update user notification preferences
      const updateData = {
        notificationPreferences: {
          emailNotifications: notifications.emailNotifications,
          emailTypes,
        },
      }

      // Handle role relationship properly
      try {
        // Get the role ID directly from the database
        if (user.role) {
          let roleId = null

          // If role is an object with id, use the id
          if (typeof user.role === 'object' && user.role !== null && user.role.id) {
            console.log('Using role ID from object:', user.role.id)
            updateData.role = user.role.id
          }
          // If role is a string, use it directly
          else if (typeof user.role === 'string') {
            console.log('Using role ID from string:', user.role)
            updateData.role = user.role
          }
          // If role is an object with slug, look up the ID
          else if (typeof user.role === 'object' && user.role !== null && user.role.slug) {
            console.log('Looking up role ID by slug:', user.role.slug)

            // Find the role by slug
            const roleDoc = await payload.find({
              collection: 'roles',
              where: {
                slug: {
                  equals: user.role.slug,
                },
              },
            })

            if (roleDoc.docs && roleDoc.docs.length > 0) {
              roleId = roleDoc.docs[0].id
              console.log('Found role ID by slug:', roleId)
              updateData.role = roleId
            } else {
              console.error('Role not found by slug:', user.role.slug)
            }
          }
        }
      } catch (roleError) {
        console.error('Error handling role relationship:', roleError)
      }

      // Preserve school field if it exists
      if (user.school) {
        // If school is an object with id, use the id
        if (typeof user.school === 'object' && user.school !== null && user.school.id) {
          console.log('Using school ID from object:', user.school.id)
          updateData.school = user.school.id
        }
        // If school is a string, use it directly
        else if (typeof user.school === 'string') {
          console.log('Using school ID from string:', user.school)
          updateData.school = user.school
        }
      }

      console.log('Updating user with data:', updateData)

      // Update the user
      try {
        const updatedUser = await payload.update({
          collection: 'users',
          id: userId,
          data: updateData,
        })
        console.log('User updated successfully')
      } catch (updateError) {
        console.error('Error updating user:', updateError)

        // Try a more minimal update as a fallback
        try {
          console.log('Trying minimal update as fallback')

          // Create a minimal update with only the notification preferences
          const minimalUpdate = {
            notificationPreferences: updateData.notificationPreferences,
          }

          // Always include role ID
          if (user.role) {
            if (typeof user.role === 'object' && user.role !== null && user.role.id) {
              minimalUpdate.role = user.role.id
            } else if (typeof user.role === 'string') {
              minimalUpdate.role = user.role
            } else if (typeof user.role === 'object' && user.role !== null && user.role.slug) {
              // Try to find the role by slug
              const roleDoc = await payload.find({
                collection: 'roles',
                where: {
                  slug: {
                    equals: user.role.slug,
                  },
                },
              })

              if (roleDoc.docs && roleDoc.docs.length > 0) {
                minimalUpdate.role = roleDoc.docs[0].id
              } else {
                // As a last resort, try to find any role with the same slug
                const anyRoleDoc = await payload.find({
                  collection: 'roles',
                  where: {
                    slug: {
                      equals: 'student',
                    },
                  },
                })

                if (anyRoleDoc.docs && anyRoleDoc.docs.length > 0) {
                  minimalUpdate.role = anyRoleDoc.docs[0].id
                }
              }
            }
          }

          // Include school ID if needed
          if (
            user.school &&
            typeof user.school === 'object' &&
            user.school !== null &&
            user.school.id
          ) {
            minimalUpdate.school = user.school.id
          } else if (user.school && typeof user.school === 'string') {
            minimalUpdate.school = user.school
          }

          console.log('Minimal update data:', JSON.stringify(minimalUpdate, null, 2))

          const minimalUpdatedUser = await payload.update({
            collection: 'users',
            id: userId,
            data: minimalUpdate,
          })

          console.log('User updated with minimal data')
        } catch (fallbackError) {
          console.error('Fallback update also failed:', fallbackError)

          // Final fallback: try to update directly in MongoDB
          try {
            console.log('Attempting direct MongoDB update as final fallback')

            const { db } = await connectToDatabase()

            // Create a minimal update with just the notification preferences
            const directUpdate = {
              notificationPreferences: updateData.notificationPreferences,
              updatedAt: new Date().toISOString(),
            }

            console.log('Direct MongoDB update data:', directUpdate)

            // Try different ID formats
            let updateResult = await db
              .collection('users')
              .updateOne({ id: userId }, { $set: directUpdate })

            if (updateResult.matchedCount === 0) {
              // Try with string ID
              updateResult = await db
                .collection('users')
                .updateOne({ id: userId.toString() }, { $set: directUpdate })
            }

            if (updateResult.matchedCount === 0) {
              // Try with ObjectId
              try {
                const ObjectId = require('mongodb').ObjectId
                if (ObjectId.isValid(userId)) {
                  updateResult = await db
                    .collection('users')
                    .updateOne({ _id: new ObjectId(userId) }, { $set: directUpdate })
                }
              } catch (err) {
                console.log('Error with ObjectId:', err)
              }
            }

            if (updateResult.matchedCount > 0) {
              console.log('Direct MongoDB update successful')
              return NextResponse.json({
                success: true,
                message: 'Settings updated successfully',
                note: 'Used direct database update as fallback',
              })
            } else {
              console.error('Direct MongoDB update failed')
              throw new Error('No matching user found in MongoDB')
            }
          } catch (directError) {
            console.error('Direct MongoDB update failed:', directError)
            return NextResponse.json({ error: 'Failed to update settings' }, { status: 500 })
          }
        }
      }

      console.log('User updated successfully')
      return NextResponse.json({
        success: true,
        message: 'Settings updated successfully',
      })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating user settings:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
