import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const global = url.searchParams.get('global') === 'true'
      const schoolId = url.searchParams.get('schoolId')
      const days = parseInt(url.searchParams.get('days') || '30')
      const startDate = url.searchParams.get('startDate')
      const endDate = url.searchParams.get('endDate')

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only super-admins can access this endpoint' },
          { status: 403 },
        )
      }

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Build date range filter
        let dateFilter = {}

        if (startDate && endDate) {
          dateFilter = {
            createdAt: {
              $gte: new Date(startDate),
              $lte: new Date(endDate),
            },
          }
        } else {
          // Default to last X days
          const pastDate = new Date()
          pastDate.setDate(pastDate.getDate() - days)
          
          dateFilter = {
            createdAt: {
              $gte: pastDate,
            },
          }
        }

        // Build main query filter
        let filter: any = { ...dateFilter }
        
        // Helper to safely try ObjectId conversion
        function safeObjectId(val: string) {
          try {
            return new ObjectId(val)
          } catch {
            return null
          }
        }

        // If not global and schoolId provided, filter by schoolId
        if (!global && schoolId) {
          const schoolObjId = safeObjectId(schoolId)
          filter.$or = [
            { school: schoolId },
            { 'school.id': schoolId },
            { schoolId: schoolId },
            ...(schoolObjId ? [ { school: schoolObjId } ] : []),
            ...(schoolObjId ? [ { schoolId: schoolObjId } ] : []),
            ...(schoolObjId ? [ { 'school.id': schoolObjId } ] : []),
          ]
        }

        // If ?raw=true, return all activities for the schoolId
        if (url.searchParams.get('raw') === 'true') {
          const activities = await db.collection('activities').find(filter).sort({ createdAt: -1 }).limit(200).toArray()
          return NextResponse.json({ activities })
        }

        // Get activities grouped by date
        const activities = await db.collection('activities')
          .aggregate([
            { $match: filter },
            {
              $group: {
                _id: {
                  date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
                  schoolId: { $ifNull: ['$school.id', { $ifNull: ['$school', '$schoolId'] }] },
                },
                count: { $sum: 1 },
                schoolName: { $first: { $ifNull: ['$school.name', '$schoolName'] } },
              }
            },
            {
              $project: {
                _id: 0,
                date: '$_id.date',
                schoolId: '$_id.schoolId',
                schoolName: '$schoolName',
                count: 1,
              }
            },
            { $sort: { date: 1 } }
          ])
          .toArray()

        return NextResponse.json({ activities })

      } catch (mongoError) {
        console.error('Error fetching activities from MongoDB, falling back to Payload:', mongoError)
        
        // Fallback to Payload CMS if MongoDB fails
        const query: any = {
          createdAt: {
            greater_than: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
          },
        }
        
        if (!global && schoolId) {
          query.school = {
            equals: schoolId,
          }
        }
        
        const activities = await payload.find({
          collection: 'activities',
          where: query,
          limit: 1000,
        })
        
        // Transform and group activities by date
        const groupedActivities = activities.docs.reduce((acc: any[], activity) => {
          const date = new Date(activity.createdAt).toISOString().split('T')[0]
          const schoolValue = typeof activity.school === 'object' ? activity.school?.id : activity.school
          
          const existingEntry = acc.find(
            a => a.date === date && a.schoolId === schoolValue
          )
          
          if (existingEntry) {
            existingEntry.count++
          } else {
            acc.push({
              date,
              schoolId: schoolValue,
              schoolName: typeof activity.school === 'object' 
                ? activity.school?.name 
                : `School ${schoolValue}`,
              count: 1,
            })
          }
          
          return acc
        }, [])
        
        return NextResponse.json({ activities: groupedActivities })
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching activities:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 