import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    console.log('Student approvals API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    console.log('Token found in cookies:', !!token)

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })
      console.log('Payload instance obtained')

      // Get the teacher's school
      const teacher = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1, // Don't populate relationships to avoid ObjectId errors
      })
      console.log('Teacher found in database:', !!teacher)

      if (!teacher || !teacher.school) {
        return NextResponse.json({ error: 'Teacher not associated with a school' }, { status: 400 })
      }

      const schoolId = typeof teacher.school === 'object' ? teacher.school.id : teacher.school
      console.log('Teacher school ID:', schoolId)

      // Verify the school exists
      try {
        const school = await payload.findByID({
          collection: 'schools',
          id: schoolId,
        })

        if (!school) {
          console.error(`School with ID ${schoolId} not found`)
          return NextResponse.json({ error: 'Associated school not found' }, { status: 404 })
        }

        console.log(`Teacher from school: ${school.name}`)
      } catch (schoolError) {
        console.error('Error finding school:', schoolError)
        return NextResponse.json({ error: 'Error finding associated school' }, { status: 500 })
      }

      // First, get the student role ID
      const studentRole = await payload.find({
        collection: 'roles',
        where: {
          slug: {
            equals: 'student',
          },
        },
      })

      if (!studentRole || studentRole.docs.length === 0) {
        console.error('Student role not found')
        return NextResponse.json({ error: 'Student role not found' }, { status: 404 })
      }

      const studentRoleId = studentRole.docs[0].id
      console.log('Student role ID:', studentRoleId)

      // Try MongoDB first with mixed format support
      let allStudents
      try {
        const { db } = await connectToDatabase()
        const { ObjectId } = await import('mongodb')

        // Query with mixed format support for school field
        const mongoQuery = {
          $or: [
            { school: new ObjectId(schoolId) },
            { school: schoolId },
            { 'school.id': schoolId },
            { 'school._id': new ObjectId(schoolId) },
          ],
        }

        console.log('Student Approvals API - MongoDB Query:', JSON.stringify(mongoQuery, null, 2))

        const students = await db.collection('users').find(mongoQuery).toArray()

        if (students.length > 0) {
          // Convert to Payload format for consistency
          const studentsWithPopulation = []
          for (const student of students) {
            try {
              const populatedStudent = await payload.findByID({
                collection: 'users',
                id: student._id.toString(),
                depth: 2,
              })
              studentsWithPopulation.push(populatedStudent)
            } catch (error) {
              console.warn(`Could not populate student ${student._id}:`, error.message)
              // Fallback: use raw student data
              studentsWithPopulation.push({
                ...student,
                id: student._id.toString(),
              })
            }
          }

          allStudents = {
            docs: studentsWithPopulation,
            totalDocs: studentsWithPopulation.length,
          }
          console.log(`Found ${allStudents.totalDocs} total students in school via MongoDB`)
        }
      } catch (mongoError) {
        console.warn('Error fetching students from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload if MongoDB didn't work
      if (!allStudents) {
        allStudents = await payload.find({
          collection: 'users',
          where: {
            school: {
              equals: schoolId,
            },
          },
          depth: 2,
        })
        console.log(`Found ${allStudents.totalDocs} total students in school via Payload`)
      }

      // Directly check for the students we know should exist
      const expectedStudentIds = [
        '6817ce0824bc3cae11d70541', // Profile changes student
        '683ca131c3b940095eaf4415', // Pending approval student
      ]

      console.log('Directly checking for expected students:')
      for (const expectedId of expectedStudentIds) {
        try {
          const foundStudent = await payload.findByID({
            collection: 'users',
            id: expectedId,
          })

          if (foundStudent) {
            console.log(`Found expected student with ID ${expectedId}:`, {
              id: foundStudent.id,
              email: foundStudent.email || 'no-email',
              firstName: foundStudent.firstName || 'no-name',
              role:
                typeof foundStudent.role === 'object'
                  ? `object with slug=${foundStudent.role?.slug}`
                  : foundStudent.role,
              school:
                typeof foundStudent.school === 'object'
                  ? foundStudent.school?.id
                  : foundStudent.school,
              status: foundStudent.status || 'no-status',
              profileImageStatus: foundStudent.profileImageStatus || 'none',
              nameChangeStatus: foundStudent.nameChangeStatus || 'none',
            })

            // Check if the role matches
            const hasStudentRole =
              (typeof foundStudent.role === 'object' &&
                foundStudent.role !== null &&
                'slug' in foundStudent.role &&
                foundStudent.role.slug === 'student') ||
              foundStudent.role === studentRoleId

            // Check if the school matches
            const isInSchool =
              foundStudent.school === schoolId ||
              (typeof foundStudent.school === 'object' && foundStudent.school?.id === schoolId)

            console.log(
              `Student ${expectedId} role check: ${hasStudentRole}, school check: ${isInSchool}`,
            )
          } else {
            console.log(`Expected student with ID ${expectedId} not found`)
          }
        } catch (err) {
          console.error(`Error finding student with ID ${expectedId}:`, err)
        }
      }

      // Log each student's role and statuses to debug
      console.log('Examining all students:')
      allStudents.docs.forEach((student, index) => {
        console.log(`Student ${index + 1}:`, {
          id: student.id,
          email: student.email || 'no-email',
          firstName: student.firstName || 'no-name',
          role:
            typeof student.role === 'object'
              ? `object with slug=${student.role?.slug}`
              : student.role,
          status: student.status || 'no-status',
          profileImageStatus: student.profileImageStatus || 'none',
          nameChangeStatus: student.nameChangeStatus || 'none',
        })
      })

      // Filter in code rather than in query
      const pendingProfileChanges = {
        docs: allStudents.docs.filter((student) => {
          // Special handling for known students to debug
          if (expectedStudentIds.includes(student.id)) {
            console.log(`Processing expected student ${student.id} for profile changes:`, {
              id: student.id,
              firstName: student.firstName || 'no-name',
              roleType: typeof student.role,
              role: student.role,
              profileImageStatus: student.profileImageStatus,
              nameChangeStatus: student.nameChangeStatus,
            })
          }

          // Check if student has a valid role that matches student role
          const hasStudentRole =
            // When role is an object with slug property
            (typeof student.role === 'object' &&
              student.role !== null &&
              'slug' in student.role &&
              student.role.slug === 'student') ||
            // When role is a direct reference to role ID
            student.role === studentRoleId

          // Check if student has pending profile changes
          const hasPendingChanges =
            student.profileImageStatus === 'pending' || student.nameChangeStatus === 'pending'

          // For expected students, log the result
          if (expectedStudentIds.includes(student.id)) {
            console.log(`Student ${student.id} filtering result:`, {
              hasStudentRole,
              hasPendingChanges,
              shouldInclude: hasStudentRole && hasPendingChanges,
            })
          }

          return hasStudentRole && hasPendingChanges
        }),
        totalDocs: 0, // Will update after filtering
      }
      pendingProfileChanges.totalDocs = pendingProfileChanges.docs.length
      console.log(
        `Filtered ${pendingProfileChanges.totalDocs} students with pending profile changes`,
      )

      if (pendingProfileChanges.docs.length > 0) {
        console.log('Students with pending profile changes:')
        pendingProfileChanges.docs.forEach((student) => {
          console.log({
            id: student.id,
            email: student.email || 'no-email',
            firstName: student.firstName || 'no-name',
            profileImageStatus: student.profileImageStatus || 'none',
            nameChangeStatus: student.nameChangeStatus || 'none',
          })
        })
      }

      const pendingApprovals = {
        docs: allStudents.docs.filter((student) => {
          // Special handling for known students to debug
          if (expectedStudentIds.includes(student.id)) {
            console.log(`Processing expected student ${student.id} for account approval:`, {
              id: student.id,
              firstName: student.firstName || 'no-name',
              roleType: typeof student.role,
              role: student.role,
              status: student.status,
            })
          }

          // Check if student has a valid role that matches student role
          const hasStudentRole =
            // When role is an object with slug property
            (typeof student.role === 'object' &&
              student.role !== null &&
              'slug' in student.role &&
              student.role.slug === 'student') ||
            // When role is a direct reference to role ID
            student.role === studentRoleId

          // Check if student has pending status
          const isPending = student.status === 'pending'

          // For expected students, log the result
          if (expectedStudentIds.includes(student.id)) {
            console.log(`Student ${student.id} filtering result:`, {
              hasStudentRole,
              isPending,
              shouldInclude: hasStudentRole && isPending,
            })
          }

          return hasStudentRole && isPending
        }),
        totalDocs: 0, // Will update after filtering
      }
      pendingApprovals.totalDocs = pendingApprovals.docs.length
      console.log(`Filtered ${pendingApprovals.totalDocs} students with pending account approval`)

      if (pendingApprovals.docs.length > 0) {
        console.log('Students with pending account approval:')
        pendingApprovals.docs.forEach((student) => {
          console.log({
            id: student.id,
            email: student.email || 'no-email',
            firstName: student.firstName || 'no-name',
            status: student.status || 'none',
          })
        })
      }

      return NextResponse.json({
        pendingProfileChanges: pendingProfileChanges.docs.map((student) => {
          // Helper to convert image references to URLs
          const resolveImageUrl = (img: any) => {
            if (!img) return null
            // If img is already a string (URL), just return it
            if (typeof img === 'string') return img
            // If img is an object with url property
            if (typeof img === 'object' && img !== null && 'url' in img) return img.url
            // If it's a media reference, it'll be resolved during depth population
            if (typeof img === 'object' && img !== null && 'id' in img) return img
            // Otherwise, return null
            return null
          }

          return {
            id: student.id,
            firstName: student.firstName || '',
            lastName: student.lastName || '',
            email: student.email || '',
            pendingFirstName: student.pendingFirstName || student.firstName || '',
            pendingLastName: student.pendingLastName || student.lastName || '',
            profileImage: resolveImageUrl(student.profileImage),
            pendingProfileImage: resolveImageUrl(student.pendingProfileImage),
            profileImageStatus: student.profileImageStatus || 'none',
            nameChangeStatus: student.nameChangeStatus || 'none',
            status: student.status || 'pending',
          }
        }),
        pendingApprovals: pendingApprovals.docs.map((student) => ({
          id: student.id,
          firstName: student.firstName || '',
          lastName: student.lastName || '',
          email: student.email || '',
          status: student.status || 'pending',
        })),
      })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching student approvals:', error)
    return NextResponse.json({ error: 'Failed to fetch student approvals' }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  try {
    console.log('Student approval API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      console.log('Request body:', body)

      const { studentId, action, approvalType } = body

      if (!studentId || !action || !approvalType) {
        return NextResponse.json(
          { error: 'Student ID, action, and approval type are required' },
          { status: 400 },
        )
      }

      // Initialize Payload
      const payload = await getPayload({ config })

      // Check if the current user is authorized to approve students
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      // Get the user's role
      const userRoleId =
        typeof currentUser.role === 'object' ? currentUser.role?.id : currentUser.role
      console.log('User role ID:', userRoleId)

      // Look up the role
      const roleObj = await payload.findByID({
        collection: 'roles',
        id: userRoleId,
      })

      console.log('User role object:', JSON.stringify(roleObj))

      // Only teachers and mentors can approve students
      if (
        !roleObj ||
        (roleObj.slug !== 'teacher' &&
          roleObj.slug !== 'mentor' &&
          roleObj.slug !== 'school-admin' &&
          roleObj.slug !== 'super-admin')
      ) {
        console.log('User not authorized to approve students')
        return NextResponse.json(
          {
            error: 'Only teachers, mentors, and admins can approve students',
            role: roleObj?.slug || 'unknown',
          },
          { status: 403 },
        )
      }

      // Get the student
      const student = await payload.findByID({
        collection: 'users',
        id: studentId,
      })

      if (!student) {
        return NextResponse.json({ error: 'Student not found' }, { status: 404 })
      }

      // Log detailed student information to debug role issues
      console.log('Student data for approval:', {
        id: student.id,
        role: student.role,
        roleType: typeof student.role,
        roleDetails: typeof student.role === 'object' ? JSON.stringify(student.role) : student.role,
        status: student.status,
        school: student.school,
      })

      // Check if the student is from the same school as the teacher (only for teachers)
      const studentSchool = typeof student.school === 'object' ? student.school?.id : student.school
      const userSchool =
        typeof currentUser.school === 'object' ? currentUser.school?.id : currentUser.school
      const userRole = roleObj.slug

      // Only teachers are restricted to their own school
      // Mentors, school-admins, and super-admins can approve students from any school
      if (userRole === 'teacher' && studentSchool !== userSchool) {
        return NextResponse.json(
          { error: 'Teachers can only approve students from their own school' },
          { status: 403 },
        )
      }

      console.log('Creating update data for approval type:', approvalType, 'action:', action)

      // Handle different approval types
      let updateData = {}
      let activityType: 'student-approval' | 'profile-image-approval' | 'name-change-approval' =
        'student-approval'
      let notificationMessage = ''

      // Get the current role to preserve it in the update
      let studentRole
      if (typeof student.role === 'object') {
        // If role is an object with id property, use that
        if (student.role && 'id' in student.role && student.role.id) {
          studentRole = student.role.id
        }
        // If role has slug but no id, we need to look up the role ID
        else if (student.role && 'slug' in student.role && student.role.slug) {
          try {
            // Type assertion to help TypeScript
            const roleSlug = (student.role as { slug: string }).slug
            console.log('Looking up role ID for slug:', roleSlug)
            const roleResult = await payload.find({
              collection: 'roles',
              where: {
                slug: {
                  equals: roleSlug,
                },
              },
            })
            if (roleResult.docs.length > 0) {
              studentRole = roleResult.docs[0].id
              console.log('Found role ID for slug:', studentRole)
            } else {
              console.error('Could not find role with slug:', roleSlug)
            }
          } catch (error) {
            console.error('Error looking up role ID:', error)
          }
        }
      } else {
        // Role is already an ID
        studentRole = student.role
      }
      console.log('Student role for updates:', studentRole)

      if (approvalType === 'account') {
        activityType = 'student-approval'

        if (action === 'approve') {
          updateData = {
            status: 'active',
            ...(studentRole ? { role: studentRole } : {}),
          }
          notificationMessage = 'Your account has been approved'
        } else {
          updateData = {
            status: 'rejected',
            ...(studentRole ? { role: studentRole } : {}),
          }
          notificationMessage = 'Your account has been rejected'
        }
      } else if (approvalType === 'profile-image') {
        activityType = 'profile-image-approval'

        if (action === 'approve') {
          console.log(
            'Approving profile image, pendingProfileImage value:',
            typeof student.pendingProfileImage === 'object'
              ? JSON.stringify(student.pendingProfileImage)
              : student.pendingProfileImage,
          )

          updateData = {
            profileImage: student.pendingProfileImage,
            pendingProfileImage: null,
            profileImageStatus: 'approved',
            ...(studentRole ? { role: studentRole } : {}),
          }
          notificationMessage = 'Your profile image has been approved'
        } else {
          updateData = {
            pendingProfileImage: null,
            profileImageStatus: 'rejected',
            ...(studentRole ? { role: studentRole } : {}),
          }
          notificationMessage = 'Your profile image has been rejected'
        }
      } else if (approvalType === 'name-change') {
        activityType = 'name-change-approval'

        if (action === 'approve') {
          console.log(
            'Approving name change, pendingFirstName:',
            student.pendingFirstName,
            'pendingLastName:',
            student.pendingLastName,
          )

          updateData = {
            firstName: student.pendingFirstName,
            lastName: student.pendingLastName,
            pendingFirstName: null,
            pendingLastName: null,
            nameChangeStatus: 'approved',
            ...(studentRole ? { role: studentRole } : {}),
          }
          notificationMessage = 'Your name change has been approved'
        } else {
          updateData = {
            pendingFirstName: null,
            pendingLastName: null,
            nameChangeStatus: 'rejected',
            ...(studentRole ? { role: studentRole } : {}),
          }
          notificationMessage = 'Your name change has been rejected'
        }
      } else {
        return NextResponse.json({ error: 'Invalid approval type' }, { status: 400 })
      }

      console.log('Final updateData:', JSON.stringify(updateData))

      // Update the student
      try {
        const updatedStudent = await payload.update({
          collection: 'users',
          id: studentId,
          data: updateData,
        })
        console.log('Student update successful')
      } catch (error) {
        const updateError = error as Error
        console.error('Error updating student:', updateError)

        // More detailed error logging to help debug the validation issue
        if (updateError.name === 'ValidationError') {
          console.error('Validation errors:', JSON.stringify(updateError, null, 2))

          // If it's a Payload Validation error, it might have additional details
          if ('data' in updateError && typeof updateError.data === 'object') {
            console.error('Validation error details:', updateError.data)
          }
        }

        return NextResponse.json(
          {
            error: 'Failed to update student',
            message: updateError.message,
            details: updateError.name || 'Unknown error',
          },
          { status: 500 },
        )
      }

      // Get today's activities to check if we've reached the daily limit
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      const todayApprovals = await payload.find({
        collection: 'activities',
        where: {
          userId: { equals: userId },
          activityType: { equals: activityType },
          createdAt: {
            greater_than_equal: today.toISOString(),
            less_than: tomorrow.toISOString(),
          },
        },
      })

      // Determine points based on daily limit (10 points each, 2/day required)
      const approvalCount = todayApprovals.totalDocs
      const pointsToAward = action === 'approve' ? (approvalCount < 2 ? 10 : 3) : 0 // Full points for first 2, reduced after

      // Create an activity record
      const activity = await payload.create({
        collection: 'activities',
        data: {
          userId: userId,
          targetUserId: studentId,
          activityType: activityType,
          details: {
            action: action,
            studentName: `${student.firstName} ${student.lastName}`,
            studentEmail: student.email,
            approvalType: approvalType,
            isRequired: approvalCount < 2, // First 2 are required for daily goals
          },
          school: userSchool,
          points: pointsToAward,
        },
      })

      // Award points to the teacher for approving a student
      if (action === 'approve') {
        try {
          const currentPoints = currentUser.points || 0

          // Only update points if we don't need to include firstName/lastName
          // or if they have valid values (not empty strings)
          if (currentUser.firstName && currentUser.lastName) {
            await payload.update({
              collection: 'users',
              id: userId,
              data: {
                points: currentPoints + pointsToAward,
                firstName: currentUser.firstName,
                lastName: currentUser.lastName,
              },
            })
          } else {
            // Skip firstName/lastName fields if they're not present
            await payload.update({
              collection: 'users',
              id: userId,
              data: {
                points: currentPoints + pointsToAward,
              },
            })
          }

          console.log('Teacher points updated successfully')
        } catch (updateError) {
          console.error('Error updating teacher points:', updateError)
          // Continue even if points update fails
        }
      }

      // Notify the student about the approval/rejection
      const notification = await payload.create({
        collection: 'notifications',
        data: {
          user: studentId,
          message: notificationMessage,
          type: action === 'approve' ? 'success' : 'info',
          read: false,
          details: {
            activityId: activity.id,
            actionType: action,
            approvalType: approvalType,
          },
        },
      })

      // Update the activity with a reference to the notification
      try {
        // Get the current details and make sure they are an object we can spread
        let currentDetails = {}
        if (activity.details && typeof activity.details === 'object') {
          currentDetails = { ...activity.details }
        }

        // Add notification ID to details
        const updatedDetails = {
          ...currentDetails,
          notificationId: notification.id,
          action: action,
          approvalType: approvalType,
        }

        await payload.update({
          collection: 'activities',
          id: activity.id,
          data: {
            details: updatedDetails,
          },
        })
      } catch (error) {
        console.error('Error updating activity with notification reference:', error)
        // Continue even if update fails
      }

      return NextResponse.json({
        success: true,
        message: `Student ${approvalType} ${action === 'approve' ? 'approved' : 'rejected'} successfully`,
        points: pointsToAward,
        dailyApprovals: approvalCount + 1,
        dailyGoal: 2,
      })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error approving/rejecting student:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
