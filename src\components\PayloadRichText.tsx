'use client'

import React, { ReactNode } from 'react'
import Link from 'next/link'
import Image from 'next/image'

interface PayloadRichTextProps {
  content: any
  className?: string
}

export const PayloadRichText: React.FC<PayloadRichTextProps> = ({ content, className = '' }) => {
  // For debugging
  console.log('PayloadRichText content:', content);
  console.log('PayloadRichText content type:', typeof content);
  
  // If content is null or undefined, return null
  if (!content) {
    console.log('PayloadRichText: content is null or undefined');
    return (
      <div className="rich-text-empty p-4 border border-dashed border-gray-300 rounded my-4">
        <p className="text-muted-foreground">No content available</p>
        <small className="text-xs text-gray-400">Debug: Content is null or undefined</small>
      </div>
    );
  }

  // Direct rendering for string content that isn't JSON
  if (typeof content === 'string' && !content.trim().startsWith('{')) {
    return (
      <div className={`richtext-string ${className}`}>
        {content.split('\n\n').map((paragraph, i) => (
          <p key={i} className="mb-4">{paragraph}</p>
        ))}
      </div>
    );
  }
  
  // Handle JSON string content - try to parse it
  if (typeof content === 'string' && content.trim().startsWith('{')) {
    try {
      const parsedContent = JSON.parse(content);
      console.log('Parsed JSON content:', parsedContent);
      
      // If it's Lexical format
      if (parsedContent && parsedContent.root) {
        return renderLexicalFormat(parsedContent, className);
      }
      
      // If it's not Lexical but has some structure
      if (typeof parsedContent === 'object') {
        return (
          <div className={`richtext-parsed-json ${className}`}>
            {renderStructuredContent(parsedContent)}
          </div>
        );
      }
      
      // If it parsed but is a simple value
      return (
        <div className={`richtext-simple-value ${className}`}>
          <p className="mb-4">{String(parsedContent)}</p>
        </div>
      );
    } catch (e) {
      console.error('Failed to parse JSON content:', e);
      // Fall through to regular string handling
      return (
        <div className={`richtext-html ${className}`}> 
          <div dangerouslySetInnerHTML={{ __html: content }} />
          <div className="text-xs text-gray-400 mt-2">Debug: Failed to parse as JSON</div>
        </div>
      );
    }
  }

  // Handle Lexical format objects directly
  if (content && typeof content === 'object' && content.root) {
    console.log('Detected Lexical format with root');
    
    // Special case for format with root.children[0].children[0].text
    const hasTextStructure = 
      content.root && 
      content.root.children && 
      Array.isArray(content.root.children) && 
      content.root.children.length > 0 &&
      content.root.children[0].children &&
      Array.isArray(content.root.children[0].children) &&
      content.root.children[0].children.length > 0 &&
      'text' in content.root.children[0].children[0];
      
    // If it's the specific format shared by the user, extract the text
    if (hasTextStructure) {
      const text = content.root.children[0].children[0].text;
      console.log('Extracted text from Lexical format:', text);
      
      if (text && typeof text === 'string') {
        return (
          <div className={`rich-text-extracted ${className}`}>
            {text.split('\n\n').map((paragraph: string, i: number) => (
              <p key={i} className="mb-4">{paragraph}</p>
            ))}
          </div>
        );
      }
    }
    
    return renderLexicalFormat(content, className);
  }

  // If content is an array (Payload's rich text format)
  if (Array.isArray(content)) {
    console.log('Detected array content, likely Payload blocks');
    
    if (content.length === 0) {
      return (
        <div className="rich-text-empty p-4 border border-dashed border-gray-300 rounded my-4">
          <p className="text-muted-foreground">No content (empty array)</p>
        </div>
      );
    }
    
    return (
      <div className={`payload-richtext ${className}`}>
        {content.map((node, i) => renderNode(node, i))}
      </div>
    );
  }

  // If it's a Payload object with blocks property
  if (content && typeof content === 'object' && 'blocks' in content) {
    if (!Array.isArray(content.blocks) || content.blocks.length === 0) {
      return (
        <div className="rich-text-empty p-4 border border-dashed border-gray-300 rounded my-4">
          <p className="text-muted-foreground">No content blocks</p>
        </div>
      );
    }
    
    return (
      <div className={`payload-blocks ${className}`}>
        {content.blocks.map((block: any, i: number) => (
          <div key={i} className="mb-4">
            {renderNode(block, i)}
          </div>
        ))}
      </div>
    );
  }

  // For plain objects that don't match known formats
  if (content && typeof content === 'object') {
    return (
      <div className={`richtext-object ${className}`}>
        {renderStructuredContent(content)}
      </div>
    );
  }

  // Final fallback for any other type
  return (
    <div className={`richtext-fallback ${className}`}>
      <p className="mb-4">{String(content)}</p>
      <div className="text-xs text-gray-400">Debug: Fallback renderer used</div>
    </div>
  );
}

// Function to render structured content
function renderStructuredContent(content: any): React.ReactNode {
  if (!content) return null;
  
  // Handle arrays
  if (Array.isArray(content)) {
    return (
      <div className="richtext-array">
        {content.map((item, index) => (
          <div key={index} className="mb-4">
            {renderStructuredContent(item)}
          </div>
        ))}
      </div>
    );
  }
  
  // Handle objects
  if (typeof content === 'object') {
    // Special case for text content
    if ('text' in content) {
      return <p className="mb-4">{content.text}</p>;
    }
    
    // Check for children array
    if (content.children && Array.isArray(content.children)) {
      return (
        <div className="richtext-children">
          {content.children.map((child: any, index: number) => (
            <div key={index} className="mb-2">
              {renderStructuredContent(child)}
            </div>
          ))}
        </div>
      );
    }
    
    // Generic object handling
    return (
      <div className="richtext-object-fields">
        {Object.entries(content).map(([key, value], index) => {
          // Skip rendering functions, symbols, etc.
          if (
            typeof value === 'function' || 
            typeof value === 'symbol' || 
            value === null || 
            value === undefined
          ) {
            return null;
          }
          
          // For text/string values render as paragraphs
          if (typeof value === 'string' && key !== 'type') {
            return <p key={index} className="mb-4">{value}</p>;
          }
          
          // For objects, recursively render
          if (typeof value === 'object') {
            return (
              <div key={index} className="mb-4">
                {renderStructuredContent(value)}
              </div>
            );
          }
          
          return null;
        })}
      </div>
    );
  }
  
  // Simple values
  if (typeof content === 'string' || typeof content === 'number' || typeof content === 'boolean') {
    return <p className="mb-4">{String(content)}</p>;
  }
  
  return null;
}

// Helper function to render objects
function renderObject(obj: any) {
  if (!obj) return null;
  
  if (typeof obj === 'string') {
    return <p>{obj}</p>
  }
  
  if (Array.isArray(obj)) {
    return obj.map((item, i) => <div key={i}>{renderObject(item)}</div>)
  }
  
  if (typeof obj === 'object') {
    return Object.entries(obj).map(([key, value], i) => {
      if (key === 'children' && Array.isArray(value)) {
        return <div key={i}>{(value as any[]).map((child, j) => <div key={j}>{renderObject(child)}</div>)}</div>
      }
      
      if (key === 'text') {
        return <p key={i}>{value as string}</p>
      }
      
      return null;
    }).filter(Boolean);
  }
  
  return String(obj);
}

// Helper function to render Lexical format
function renderLexicalFormat(content: any, className: string) {
  try {
    const renderNode = (node: any) => {
      if (!node) return null;

      // Handle different node types
      switch (node.type) {
        case 'root':
          return (
            <>
              {node.children?.map((child: any, i: number) => (
                <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
              ))}
            </>
          )
        case 'paragraph':
          return (
            <p className="mb-4">
              {node.children?.map((child: any, i: number) => (
                <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
              ))}
            </p>
          )
        case 'heading':
          // Use dynamic heading element based on tag
          const level = node.tag || 2 // Default to h2 if tag is missing

          if (level === 1) {
            return (
              <h1 className="text-3xl font-bold mt-6 mb-4">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h1>
            )
          } else if (level === 2) {
            return (
              <h2 className="text-2xl font-bold mt-6 mb-4">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h2>
            )
          } else if (level === 3) {
            return (
              <h3 className="text-xl font-bold mt-5 mb-3">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h3>
            )
          } else if (level === 4) {
            return (
              <h4 className="text-lg font-bold mt-4 mb-2">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h4>
            )
          } else if (level === 5) {
            return (
              <h5 className="text-base font-bold mt-3 mb-2">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h5>
            )
          } else {
            return (
              <h6 className="text-xs font-bold mt-3 mb-2">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h6>
            )
          }
        case 'list':
          if (node.listType === 'bullet') {
            return (
              <ul className="list-disc mb-4 pl-5">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </ul>
            )
          } else {
            return (
              <ol className="list-decimal mb-4 pl-5">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </ol>
            )
          }
        case 'listitem':
          return (
            <li className="mb-1">
              {node.children?.map((child: any, i: number) => (
                <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
              ))}
            </li>
          )
        case 'quote':
          return (
            <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-4">
              {node.children?.map((child: any, i: number) => (
                <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
              ))}
            </blockquote>
          )
        case 'link':
          return (
            <a href={node.url} className="text-blue-600 hover:underline">
              {node.children?.map((child: any, i: number) => (
                <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
              ))}
            </a>
          )
        case 'text':
          // Handle text formatting
          let text = node.text || '';
          
          // Convert newlines in text to <br> tags
          if (text.includes('\n')) {
            const parts = text.split('\n');
            return (
              <>
                {parts.map((part: string, i: number) => (
                  <React.Fragment key={i}>
                    {i > 0 && <br />}
                    {applyTextFormatting(part, node.format)}
                  </React.Fragment>
                ))}
              </>
            );
          }
          
          return applyTextFormatting(text, node.format);
          
        default:
          // For other node types or if children exist
          if (node.children) {
            return (
              <>
                {node.children.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </>
            )
          }
          return null;
      }
    }

    return <div className={`rich-text ${className}`}>{renderNode(content.root)}</div>
  } catch (error) {
    console.error('Error rendering Lexical format:', error);
    return <div className="rich-text-error">Error rendering content</div>
  }
}

// Helper function to apply text formatting
function applyTextFormatting(text: string, format?: number): React.ReactNode {
  if (!format) return text;
  
  let formattedText: React.ReactNode = text;
  
  // Apply formatting based on bit flags
  if (format & 1) { // Bold
    formattedText = <strong>{formattedText}</strong>;
  }
  
  if (format & 2) { // Italic
    formattedText = <em>{formattedText}</em>;
  }
  
  if (format & 4) { // Underline
    formattedText = <u>{formattedText}</u>;
  }
  
  if (format & 8) { // Strikethrough
    formattedText = <s>{formattedText}</s>;
  }
  
  return formattedText;
}

// Render a Payload node based on its type
function renderNode(node: any, index: number) {
  // Handle non-objects or null nodes
  if (!node || typeof node !== 'object') {
    return <p key={index}>{String(node)}</p>
  }

  // Handle text nodes
  if (node.text) {
    let textContent = node.text

    // Apply formatting
    if (node.bold) textContent = <strong>{textContent}</strong>
    if (node.italic) textContent = <em>{textContent}</em>
    if (node.underline) textContent = <u>{textContent}</u>
    if (node.strikethrough) textContent = <s>{textContent}</s>
    if (node.code) textContent = <code className="bg-gray-100 dark:bg-gray-800 px-1 rounded">{textContent}</code>

    // Handle links
    if (node.linkType === 'internal' && node.doc) {
      return (
        <Link 
          href={`/${node.doc.relationTo}/${node.doc.value.id}`} 
          key={index}
          className="text-primary hover:underline"
        >
          {textContent}
        </Link>
      )
    }

    if (node.linkType === 'custom' && node.url) {
      return (
        <a 
          href={node.url} 
          key={index}
          target={node.newTab ? '_blank' : undefined}
          rel={node.newTab ? 'noopener noreferrer' : undefined}
          className="text-primary hover:underline"
        >
          {textContent}
        </a>
      )
    }

    return <React.Fragment key={index}>{textContent}</React.Fragment>
  }

  // Handle different block types
  switch (node.type) {
    case 'h1':
      return <h1 key={index} className="text-3xl font-bold mt-6 mb-4">{renderChildren(node)}</h1>

    case 'h2':
      return <h2 key={index} className="text-2xl font-bold mt-6 mb-4">{renderChildren(node)}</h2>

    case 'h3':
      return <h3 key={index} className="text-xl font-bold mt-5 mb-3">{renderChildren(node)}</h3>

    case 'h4':
      return <h4 key={index} className="text-lg font-bold mt-4 mb-2">{renderChildren(node)}</h4>

    case 'h5':
      return <h5 key={index} className="text-base font-bold mt-3 mb-2">{renderChildren(node)}</h5>

    case 'h6':
      return <h6 key={index} className="text-sm font-bold mt-3 mb-2">{renderChildren(node)}</h6>

    case 'paragraph':
      return <p key={index} className="mb-4">{renderChildren(node)}</p>

    case 'blockquote':
      return (
        <blockquote key={index} className="border-l-4 border-gray-300 pl-4 py-2 my-4 italic">
          {renderChildren(node)}
        </blockquote>
      )

    case 'ul':
      return <ul key={index} className="list-disc pl-6 mb-4">{renderChildren(node)}</ul>

    case 'ol':
      return <ol key={index} className="list-decimal pl-6 mb-4">{renderChildren(node)}</ol>

    case 'li':
      return <li key={index} className="mb-1">{renderChildren(node)}</li>

    case 'hr':
      return <hr key={index} className="my-6 border-gray-300" />

    case 'upload': 
      if (node.value && typeof node.value === 'object' && node.value.url) {
        const alt = node.value.alt || 'Image'
        return (
          <div key={index} className="my-6 relative">
            <Image 
              src={node.value.url}
              alt={alt}
              width={800}
              height={600}
              className="mx-auto"
              style={{ objectFit: 'contain' }}
            />
            {node.value.caption && (
              <p className="text-center text-sm text-gray-500 mt-2">{node.value.caption}</p>
            )}
          </div>
        )
      }
      return null

    case 'relationship':
      // Render relationships - depends on your data model
      return <div key={index} className="relationship-block my-4">Relationship: {node.value}</div>

    default:
      // Handle unknown types and nested blocks
      if (node.children) {
        return <div key={index}>{renderChildren(node)}</div>
      }
      
      // Render unknown format as debug info
      return (
        <div key={index} className="debug-block my-2 p-2 border border-dashed border-gray-300">
          <small>Unknown block type: {node.type || 'undefined'}</small>
          <pre className="text-xs overflow-auto">{JSON.stringify(node, null, 2)}</pre>
        </div>
      )
  }
}

// Helper function to render node children
function renderChildren(node: any) {
  if (!node.children || !Array.isArray(node.children)) {
    return null
  }
  
  return node.children.map((child: any, i: number) => renderNode(child, i))
} 