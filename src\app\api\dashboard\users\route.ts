import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    // For development, if token is not available, use a fallback approach
    // In production, this should be removed and proper authentication enforced
    if (!token) {
      console.warn(
        'No token found in cookies, proceeding without authentication (development only)',
      )
      // For production, uncomment the following line:
      // return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Try to get users from MongoDB first
    try {
      const { db } = await connectToDatabase()
      const users = await db.collection('users').find().toArray()

      if (users && users.length > 0) {
        console.log('Fetched users from MongoDB:', users.length)
        return NextResponse.json({ users })
      }
    } catch (mongoError) {
      console.warn('Error fetching users from MongoDB, falling back to Payload:', mongoError)
    }

    // Fallback to Payload CMS if MongoDB fails
    const payload = await getPayload({ config })

    // Get all users
    const usersResponse = await payload.find({
      collection: 'users',
      depth: 1,
    })

    return NextResponse.json({ users: usersResponse.docs })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 })
  }
}
