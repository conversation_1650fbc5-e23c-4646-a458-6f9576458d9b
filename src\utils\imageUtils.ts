import { ImageSource } from '@/types'

/**
 * Safely extracts a URL from various image source formats
 * @param image The image source (can be an object with url, a string URL, or null/undefined)
 * @param fallbackUrl Optional fallback URL if no valid URL can be extracted
 * @returns A valid image URL or the fallback URL
 */
export function getImageUrl(
  image: ImageSource,
  fallbackUrl: string = 'https://images.unsplash.com/photo-1510442650500-93217e634e4c?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
): string {
  // Debug the incoming image object/value
  if (!image) {
    console.log('getImageUrl: Image source is undefined or null, using external fallback URL');
    return fallbackUrl;
  }
  
  console.log('getImageUrl called with:', typeof image, Array.isArray(image) ? 'array' : image);
  
  let finalUrl = '';

  // Handle string image sources
  if (typeof image === 'string') {
    finalUrl = processStringImageSource(image, fallbackUrl);
  }
  // Handle object image sources
  else if (typeof image === 'object') {
    if ('url' in image && image.url) {
      finalUrl = processStringImageSource(image.url, fallbackUrl);
    }
    // Handle Payload CMS media format
    else if ('filename' in image && image.filename && typeof image.filename === 'string') {
      finalUrl = processMediaFilename(image.filename);
    }
  }

  if (finalUrl) {
    return finalUrl;
  }

  console.log('No valid image format found, using fallback')
  return fallbackUrl;
}

/**
 * Process a string image source to get a valid URL
 */
function processStringImageSource(imageStr: string, fallbackUrl: string): string {
  // Handle media paths directly
  if (isMediaPath(imageStr)) {
    console.log('Using media path directly:', imageStr)
    // If it's a relative path, make sure it starts with a slash
    let imagePath = imageStr;
    if (!imagePath.startsWith('http') && !imagePath.startsWith('/')) {
      imagePath = '/' + imagePath;
    }
    
    // Try to decode URL-encoded characters (like %20 for spaces)
    try {
      imagePath = decodeURIComponent(imagePath);
      console.log('Decoded image path:', imagePath);
    } catch (e) {
      console.log('Failed to decode URL, using as-is');
    }
    
    // Try alternative URL patterns if it's an API media path
    if (imagePath.includes('/api/media/file/')) {
      // Extract just the filename part
      const parts = imagePath.split('/api/media/file/');
      if (parts.length === 2) {
        const filename = parts[1];
        console.log('Extracted filename from API path:', filename);
        
        // For paths like "/api/media/file/1156104-5.jpg" - try direct media path
        if (/^\d+-\d+\.jpg$/i.test(filename)) {
          console.log('Found numeric ID format image, trying direct media path');
          const directPath = `/media/${filename}`;
          console.log('Using direct media path:', directPath);
          return directPath;
        }
        
        // Return multiple possible URLs for the browser to try
        const directMediaPath = `/media/${filename}`;
        console.log('Also trying direct media path:', directMediaPath);
        
        // We'll stick with the original path as our best guess
        return imagePath;
      }
    }
    
    return imagePath;
  }

  // Check if it's a valid URL
  try {
    new URL(imageStr)
    console.log('Using valid string image URL:', imageStr)
    return imageStr
  } catch (e) {
    // If it's not a valid URL, it might be an ID reference
    console.log('String is not a valid URL, might be an ID reference:', imageStr)

    // If it looks like a MongoDB ID, try to construct a media URL
    if (/^[0-9a-fA-F]{24}$/.test(imageStr)) {
      const mediaUrl = `/api/media/file/${imageStr}`
      console.log('Constructed media URL from ID:', mediaUrl)
      return mediaUrl
    }

    return fallbackUrl
  }
}

/**
 * Process a media filename to get a valid URL
 */
function processMediaFilename(filename: string): string {
  let mediaUrl = `/media/${filename}`;
  
  // Try to decode URL-encoded characters
  try {
    mediaUrl = decodeURIComponent(mediaUrl);
    console.log('Decoded media URL from filename:', mediaUrl);
  } catch (e) {
    console.log('Failed to decode URL from filename, using as-is');
  }
  
  console.log('Constructed media URL from filename:', mediaUrl)
  return mediaUrl
}

/**
 * Gets the alt text for an image
 * @param image The image source
 * @param defaultAlt Default alt text if none is available
 * @returns Alt text for the image
 */
export function getImageAlt(image: ImageSource, defaultAlt: string = 'Image'): string {
  if (typeof image === 'object' && image && 'alt' in image && image.alt) {
    return image.alt
  }

  return defaultAlt
}

/**
 * Checks if an image URL is valid
 * @param url The URL to check
 * @returns True if the URL is valid, false otherwise
 */
export function isValidImageUrl(url: string): boolean {
  if (!url) return false

  // Check if it's a data URL
  if (url.startsWith('data:image/')) {
    return true
  }

  // Check if it's a valid URL
  try {
    new URL(url)
    return true
  } catch (e) {
    return false
  }
}

/**
 * Check if a string is a media path
 *
 * @param path The path to check
 * @returns True if the path is a media path, false otherwise
 */
export function isMediaPath(path: string): boolean {
  if (!path || typeof path !== 'string') return false

  // Check if the path starts with /api/media or /media
  // Also check for file extensions commonly used for images
  // Also check for paths that include /file/ followed by numbers and dashes
  return (
    path.startsWith('/api/media') ||
    path.startsWith('/media') ||
    path.includes('/file/') ||
    /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(path) ||
    /\/file\/\d+-\d+/.test(path) // Pattern like /file/1156104-5
  )
}
