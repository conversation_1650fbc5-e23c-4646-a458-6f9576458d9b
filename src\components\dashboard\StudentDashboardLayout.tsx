'use client'

import { useState, ReactNode, useEffect } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { NotificationProvider } from '@/contexts/NotificationContext'
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown'
import { PointsProvider } from '@/contexts/PointsContext'
import { ThemeProvider, useTheme, ThemeColor } from '@/contexts/ThemeContext'
import {
  LayoutDashboard,
  FileText,
  Award,
  UserCircle,
  Menu,
  X,
  LogOut,
  Settings,
  PenTool,
  MessageSquare,
  Check,
  Palette,
  Sun,
  Moon,
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import ThemeSelector from './ThemeSelector'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

interface StudentDashboardLayoutProps {
  children: ReactNode
}

type ColorOption = {
  value: ThemeColor
  label: string
  color: string
}

// Inner layout component that uses the theme context
function StudentDashboardLayoutInner({ children }: StudentDashboardLayoutProps) {
  const router = useRouter()
  const { darkMode, themeColor, toggleDarkMode, setThemeColor } = useTheme()
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const pathname = usePathname()
  const [themeDialogOpen, setThemeDialogOpen] = useState(false)
  const [selectedThemeColor, setSelectedThemeColor] = useState<ThemeColor>(themeColor)

  // Define the available color themes
  const colorOptions: ColorOption[] = [
    { value: 'cyan', label: 'Cyan', color: 'rgb(14, 165, 175)' },
    { value: 'blue', label: 'Blue', color: 'rgb(59, 130, 246)' },
    { value: 'purple', label: 'Purple', color: 'rgb(139, 92, 246)' },
    { value: 'green', label: 'Green', color: 'rgb(34, 197, 94)' },
    { value: 'amber', label: 'Amber', color: 'rgb(245, 158, 11)' },
    { value: 'pink', label: 'Pink', color: 'rgb(236, 72, 153)' },
  ]

  // Apply theme color when selected
  const applyThemeColor = (color: ThemeColor) => {
    setSelectedThemeColor(color)
    setThemeColor(color)
    
    // Apply theme color to document immediately
    document.documentElement.setAttribute('data-theme', color)
    
    // Also store in localStorage for immediate persistence
    localStorage.setItem('themeColor', color)
    
    setThemeDialogOpen(false)
  }

  // Update selected theme color when theme color changes
  useEffect(() => {
    setSelectedThemeColor(themeColor)
  }, [themeColor])

  const navItems = [
    {
      icon: <MessageSquare className="w-5 h-5" />,
      text: 'My Activity',
      href: '/dashboard/my-activity',
    },
    {
      icon: <Award className="w-5 h-5" />,
      text: 'Achievement',
      href: '/dashboard/achievement',
    },
    {
      icon: <MessageSquare className="w-5 h-5" />,
      text: 'My Activities',
      href: '/dashboard/my-activities',
    },
    {
      icon: <Award className="w-5 h-5" />,
      text: 'Achievements',
      href: '/dashboard/achievements',
    },
    {
      icon: <UserCircle className="w-5 h-5" />,
      text: 'My Profile',
      href: '/student-dashboard/my-profile',
    },
  ]

  const handleLogout = async () => {
    try {
      // Call the logout API
      const response = await fetch('/api/logout')

      if (response.ok) {
        // Clear any local storage or state
        localStorage.removeItem('notifications')

        // Redirect to login page
        router.push('/login')
      } else {
        console.error('Logout failed')
      }
    } catch (error) {
      console.error('Error during logout:', error)
    }
  }

  const handleCreateArticle = () => {
    router.push('/student-dashboard/my-articles/create')
  }

  const [userId, setUserId] = useState('')
  const [schoolId, setSchoolId] = useState('')

  useEffect(() => {
    // Fetch the actual user data from the API
    async function fetchUserData() {
      try {
        const response = await fetch('/api/auth/me', {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
            Expires: '0',
          },
        })

        if (response.ok) {
          const data = await response.json()
          if (data.user && data.user.id) {
            setUserId(data.user.id)
            
            // Set school ID if available
            if (data.user.school) {
              const schoolId = typeof data.user.school === 'object' 
                ? data.user.school.id 
                : data.user.school
              setSchoolId(schoolId)
            }
          }
        } else {
          console.error('Failed to fetch user data')
          // Redirect to login if not authenticated
          window.location.href = '/login'
        }
      } catch (error) {
        console.error('Error fetching user data:', error)
      }
    }

    fetchUserData()
  }, [])

  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''}`}>
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <aside
          className={`${sidebarOpen ? 'w-64' : 'w-20'} bg-white dark:bg-gray-800 h-screen transition-all duration-300 ease-in-out border-r border-border`}
        >
          <div className="flex flex-col h-full">
            <div className="flex items-center gap-2 p-4 h-16 border-b border-border">
              <LayoutDashboard className="w-8 h-8 text-primary" />
              {sidebarOpen && (
                <h1 className="text-xl font-bold dark:text-white">Student Portal</h1>
              )}
            </div>

            <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
              {navItems.map((item, index) => {
                const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`)
                return (
                  <Link
                    key={index}
                    href={item.href}
                    className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                      isActive
                        ? 'bg-primary/10 text-primary'
                        : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    {item.icon}
                    {sidebarOpen && <span>{item.text}</span>}
                  </Link>
                )
              })}
            </nav>

            <div className="p-4 border-t border-border space-y-2">
              <Button
                variant="outline"
                onClick={handleCreateArticle}
                className="w-full justify-center mb-2"
              >
                <PenTool className="w-5 h-5" />
                {sidebarOpen && <span className="ml-2">Create Article</span>}
              </Button>

              {/* Dark Mode Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleDarkMode}
                className="w-full flex items-center justify-center p-3 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
              >
                {darkMode ? <Sun className="w-5 h-5 mr-3" /> : <Moon className="w-5 h-5 mr-3" />}
                {sidebarOpen && (
                  <span>{darkMode ? 'Light Mode' : 'Dark Mode'}</span>
                )}
              </Button>

              {/* Theme Color Button - Opens theme dialog */}
              {sidebarOpen && (
                <Dialog open={themeDialogOpen} onOpenChange={setThemeDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full flex items-center justify-start p-3 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                    >
                      <Palette className="w-5 h-5 mr-3" />
                      <span>Theme Colors</span>
                      <div className="ml-auto flex items-center gap-1">
                        <div className="w-3 h-3 rounded-full bg-cyan-500" />
                        <div className="w-3 h-3 rounded-full bg-blue-500" />
                        <div className="w-3 h-3 rounded-full bg-purple-500" />
                      </div>
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Choose Theme Color</DialogTitle>
                      <DialogDescription>
                        Select a color theme for your dashboard
                      </DialogDescription>
                    </DialogHeader>
                    <div className="grid grid-cols-3 gap-4 py-4">
                      {colorOptions.map((color) => (
                        <Button
                          key={color.value}
                          variant="outline"
                          className={`flex flex-col items-center justify-center p-6 gap-2 ${
                            selectedThemeColor === color.value ? 'ring-2 ring-primary ring-offset-2' : ''
                          }`}
                          onClick={() => applyThemeColor(color.value)}
                        >
                          <div 
                            className="w-8 h-8 rounded-full" 
                            style={{ backgroundColor: color.color }}
                          />
                          <span>{color.label}</span>
                          {selectedThemeColor === color.value && (
                            <Check className="absolute top-2 right-2 h-4 w-4 text-primary" />
                          )}
                        </Button>
                      ))}
                    </div>
                    <div className="flex justify-between">
                      <Button variant="outline" onClick={() => setThemeDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={() => applyThemeColor(selectedThemeColor)}>
                        Apply Theme
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden bg-gray-50 dark:bg-gray-900">
          {/* Navbar */}
          <header className="h-16 flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-border">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="mr-4"
              >
                {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
                <span className="sr-only">Toggle sidebar</span>
              </Button>

              <div className="hidden md:block">
                <input
                  type="search"
                  placeholder="Search..."
                  className="bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 px-4 py-2 rounded-lg w-64"
                />
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* Theme Selector */}
              <ThemeSelector />
              
              {/* Notifications */}
              <NotificationDropdown />

              {/* User Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-full bg-gray-200 dark:bg-gray-700 h-8 w-8 p-0">
                    <span className="sr-only">Open user menu</span>
                    <span className="text-sm font-medium">ST</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() => router.push('/student-dashboard/my-profile')}
                    className="flex items-center"
                  >
                    <UserCircle className="mr-2 h-4 w-4" />
                    <span>My Profile</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="flex items-center">
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Logout</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>

          {/* Content */}
          <main className="flex-1 overflow-auto p-6">{children}</main>
        </div>
      </div>
    </div>
  )
}

// Outer layout component that provides the theme context
export default function StudentDashboardLayout({ children }: StudentDashboardLayoutProps) {
  const [userId, setUserId] = useState('')
  const [schoolId, setSchoolId] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Fetch the actual user data from the API
    async function fetchUserData() {
      setIsLoading(true)
      try {
        // Use a cache-busting approach to ensure fresh data
        const response = await fetch('/api/auth/me', {
          credentials: 'include',
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
        })

        if (response.ok) {
          const data = await response.json()
          if (data.user && data.user.id) {
            setUserId(data.user.id)
            
            // Set school ID if available
            if (data.user.school) {
              const schoolId = typeof data.user.school === 'object' 
                ? data.user.school.id 
                : data.user.school
              setSchoolId(schoolId)
            }
          }
        } else {
          console.error('Failed to fetch user data')
          // Redirect to login if not authenticated
          window.location.href = '/login'
        }
      } catch (error) {
        console.error('Error fetching user data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [])

  // Show a minimal loading state while fetching user data
  if (isLoading) {
    return <div className="min-h-screen flex items-center justify-center">Loading dashboard...</div>
  }

  return (
    <NotificationProvider userRole="student" userId={userId} schoolId={schoolId}>
      <PointsProvider userRole="student" userId={userId}>
        <ThemeProvider userId={userId}>
          <StudentDashboardLayoutInner>{children}</StudentDashboardLayoutInner>
        </ThemeProvider>
      </PointsProvider>
    </NotificationProvider>
  )
}
