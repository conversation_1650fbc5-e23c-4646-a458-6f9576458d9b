import { ObjectId } from 'bson'

/**
 * Utility functions for handling IDs in different formats
 */

/**
 * Normalize an ID to a string format for comparison
 * Handles ObjectId, Buffer, string, and other formats
 *
 * @param id The ID to normalize
 * @returns The normalized ID as a string
 */
export function normalizeId(id: any): string {
  if (!id) return ''

  // If it's already a string, return it
  if (typeof id === 'string') return id

  // If it's an ObjectId, convert to string
  if (id instanceof ObjectId) return id.toString()

  // If it's a Buffer, convert to hex string
  if (Buffer.isBuffer(id)) return id.toString('hex')

  // If it has a toString method, use it
  if (typeof id.toString === 'function') return id.toString()

  // Last resort, try JSON.stringify
  try {
    return JSON.stringify(id)
  } catch (e) {
    return ''
  }
}

/**
 * Compare two IDs for equality, regardless of their format
 *
 * @param id1 First ID to compare
 * @param id2 Second ID to compare
 * @returns True if the IDs are equal, false otherwise
 */
export function compareIds(id1: any, id2: any): boolean {
  // Normalize both IDs to strings
  const normalizedId1 = normalizeId(id1)
  const normalizedId2 = normalizeId(id2)

  // If either ID is empty, they're not equal
  if (!normalizedId1 || !normalizedId2) return false

  // Compare the normalized strings
  return normalizedId1 === normalizedId2
}

/**
 * Extract the ID from an object or return the ID itself
 *
 * @param obj Object or ID
 * @returns The extracted ID
 */
export function extractId(obj: any): any {
  if (!obj) return null

  // If it's an object with an id property, return that
  if (typeof obj === 'object' && obj !== null) {
    if (obj.id) return obj.id
    if (obj._id) return obj._id
    if (obj.$oid) return obj.$oid // Handle MongoDB $oid format
  }

  // Otherwise, return the object itself (assuming it's an ID)
  return obj
}

/**
 * Check if a user is the author of an article
 *
 * @param userId The user ID
 * @param article The article object
 * @returns True if the user is the author, false otherwise
 */
export function isUserAuthor(userId: any, article: any): boolean {
  if (!userId || !article) return false

  // Get the author from the article
  const author = article.author

  // If there's no author, return false
  if (!author) return false

  // Extract the author ID
  const authorId = extractId(author)

  // Compare the IDs
  return compareIds(userId, authorId)
}

/**
 * Check if a string is a media path
 *
 * @param path The string to check
 * @returns True if the string is a media path, false otherwise
 */
function isMediaPath(path: string): boolean {
  if (!path || typeof path !== 'string') return false

  return (
    path.includes('/api/media') ||
    path.includes('/media') ||
    path.includes('/file/') ||
    /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(path)
  )
}

/**
 * Get a normalized article ID that works across different formats
 * Checks for id, slug, _id in that order
 *
 * @param article The article object
 * @returns The normalized article ID as a string, or empty string if not found
 */
export function getArticleId(article: any): string {
  if (!article) return ''

  // Check for slug field first (preferred for URLs)
  if (article.slug && !isMediaPath(article.slug)) {
    return article.slug
  }

  // Check for id field
  if (article.id && !isMediaPath(article.id)) {
    return normalizeId(article.id)
  }

  // Check for _id field (MongoDB format)
  if (article._id && !isMediaPath(article._id)) {
    // If _id is an object with $oid (MongoDB extended JSON format)
    if (typeof article._id === 'object' && article._id.$oid) {
      return article._id.$oid
    }
    return normalizeId(article._id)
  }

  // No valid ID found
  return ''
}
