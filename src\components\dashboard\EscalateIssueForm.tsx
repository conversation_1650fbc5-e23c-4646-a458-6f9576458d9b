'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AlertTriangle, CheckCircle, AlertCircle, Trash2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog'

interface Content {
  id: string
  title: string
  type: 'article' | 'news' | 'comment' | 'user'
  author?: string
}

interface EscalateIssueFormProps {
  schoolId?: string
  issue?: any
  onCancel?: () => void
  onEscalateSuccess?: () => void
}

export default function EscalateIssueForm({ schoolId, issue, onCancel, onEscalateSuccess }: EscalateIssueFormProps) {
  const { toast } = useToast()
  const [contentType, setContentType] = useState<string>(issue ? issue.targetType || 'article' : 'article')
  const [content, setContent] = useState<Content[]>([])
  const [filteredContent, setFilteredContent] = useState<Content[]>([])
  const [selectedContent, setSelectedContent] = useState<string>(issue ? issue.id : '')
  const [reason, setReason] = useState<string>(issue && issue.description ? issue.description : '')
  const [details, setDetails] = useState<string>('')
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleteSuccess, setDeleteSuccess] = useState('')

  // Function to fetch content
  const fetchContent = async () => {
    try {
      setIsLoading(true)
      
      // Fetch content based on selected type
      const response = await fetch(
        `/api/dashboard/school-admin/content?schoolId=${schoolId}&type=${contentType}`
      )
      
      if (!response.ok) {
        throw new Error('فشل في جلب المحتوى')
      }
      
      const data = await response.json()
      
      // Format content data
      const formattedContent = data.content.map((item: any) => ({
        id: item.id,
        title: item.title || (contentType === 'user' ? `${item.firstName} ${item.lastName}` : `${contentType} #${item.id}`),
        type: contentType,
        author: item.author ? (typeof item.author === 'object' ? `${item.author.firstName} ${item.author.lastName}` : item.author) : undefined,
      }))
      
      setContent(formattedContent)
      setFilteredContent(formattedContent)
      
      // Only reset selectedContent if we don't have an issue prop
      if (!issue) {
        setSelectedContent('')
      }
      
      setIsLoading(false)
    } catch (err) {
      console.error('Error fetching content:', err)
      setError('فشل في تحميل المحتوى. يرجى المحاولة مرة أخرى.')
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (schoolId) {
      fetchContent()
    }
  }, [schoolId, contentType])
  
  // Filter content based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredContent(content)
    } else {
      const query = searchQuery.toLowerCase()
      const filtered = content.filter(
        item => 
          item.title.toLowerCase().includes(query) ||
          (item.author && item.author.toLowerCase().includes(query))
      )
      setFilteredContent(filtered)
    }
  }, [searchQuery, content])
  
  const handleContentTypeChange = (value: string) => {
    setContentType(value)
    setSelectedContent('')
    setSearchQuery('')
  }
  
  const handleContentChange = (value: string) => {
    setSelectedContent(value)
  }
  
  const handleSubmit = async () => {
    if (!selectedContent && !issue) {
      setError('يرجى تحديد المحتوى للتصعيد')
      return
    }
    
    if (!reason) {
      setError('يرجى تقديم سبب للتصعيد')
      return
    }
    
    try {
      setIsSubmitting(true)
      setError('')
      setSuccess('')
      
      const response = await fetch('/api/dashboard/school-admin/escalate-issue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contentId: issue ? issue.id : selectedContent,
          contentType: issue ? issue.targetType : contentType,
          reason,
          details,
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في تصعيد المشكلة')
      }
      
      const data = await response.json()
      
      setSuccess('تم تصعيد المشكلة إلى المشرف الأعلى للمراجعة')
      toast({
        title: 'تم تصعيد المشكلة بنجاح',
        description: 'تم تصعيد المشكلة إلى المشرف الأعلى للمراجعة',
      })
      
      // Reset form
      setSelectedContent('')
      setReason('')
      setDetails('')
      
      // Call the success callback if provided
      if (onEscalateSuccess) {
        onEscalateSuccess()
      }
      
    } catch (err: any) {
      console.error('Error escalating issue:', err)
      setError(err.message || 'فشل في تصعيد المشكلة. يرجى المحاولة مرة أخرى.')
      toast({
        title: 'خطأ',
        description: err.message || 'فشل في تصعيد المشكلة. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  const handleDeleteContent = async () => {
    if (!selectedContent || !['article', 'news'].includes(contentType)) {
      setError('يرجى تحديد مقال أو خبر للإزالة')
      return
    }
    
    try {
      setIsDeleting(true)
      setError('')
      setDeleteSuccess('')
      
      const response = await fetch('/api/dashboard/school-admin/delete-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contentId: selectedContent,
          contentType,
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في إزالة المحتوى')
      }
      
      const data = await response.json()
      
      setDeleteSuccess(`تمت إزالة ${contentType === 'article' ? 'المقال' : 'الخبر'} بنجاح`)
      toast({
        title: 'تمت إزالة المحتوى بنجاح',
        description: `تمت إزالة ${contentType === 'article' ? 'المقال' : 'الخبر'} من مدرستك`,
      })
      
      // Reset form and close dialog
      setSelectedContent('')
      setShowDeleteDialog(false)
      
      // Refresh the content list
      fetchContent()
      
    } catch (err: any) {
      console.error('Error removing content:', err)
      setError(err.message || 'فشل في إزالة المحتوى. يرجى المحاولة مرة أخرى.')
      toast({
        title: 'خطأ',
        description: err.message || 'فشل في إزالة المحتوى. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false)
    }
  }
  
  return (
    <div dir="rtl">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">تصعيد المشكلة إلى المشرف الأعلى</h2>
        <p className="text-sm text-gray-500">
          استخدم هذا النموذج فقط للمشكلات الخطيرة التي لا يمكنك حلها كمشرف مدرسة وتتطلب تدخلاً على مستوى أعلى.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 text-red-500 p-4 rounded-md mb-4 flex items-center">
          <AlertCircle className="h-5 w-5 ml-2" />
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-50 text-green-500 p-4 rounded-md mb-4 flex items-center">
          <CheckCircle className="h-5 w-5 ml-2" />
          {success}
        </div>
      )}
      
      <div className="space-y-4">
        {!issue && (
          <>
            <div className="space-y-2">
              <Label htmlFor="content-type">نوع المحتوى</Label>
              <Select value={contentType} onValueChange={handleContentTypeChange}>
                <SelectTrigger id="content-type">
                  <SelectValue placeholder="اختر نوع المحتوى" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="article">مقال</SelectItem>
                  <SelectItem value="news">خبر</SelectItem>
                  <SelectItem value="comment">تعليق</SelectItem>
                  <SelectItem value="user">مستخدم</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="content-search">البحث عن المحتوى</Label>
              <div className="relative">
                <Input
                  id="content-search"
                  placeholder="البحث بالعنوان أو المؤلف..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="content-select">تحديد المحتوى</Label>
              <Select value={selectedContent} onValueChange={handleContentChange}>
                <SelectTrigger id="content-select">
                  <SelectValue placeholder="اختر المحتوى للتصعيد" />
                </SelectTrigger>
                <SelectContent>
                  {filteredContent.length > 0 ? (
                    filteredContent.map((item) => (
                      <SelectItem key={item.id} value={item.id}>
                        {item.title} {item.author ? `(${item.author})` : ''}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-content" disabled>
                      {isLoading ? 'جاري التحميل...' : 'لا يوجد محتوى متاح'}
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
          </>
        )}
        
        <div className="space-y-2">
          <Label htmlFor="reason">سبب التصعيد</Label>
          <Select value={reason} onValueChange={setReason}>
            <SelectTrigger id="reason">
              <SelectValue placeholder="اختر سبب التصعيد" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="inappropriate-content">محتوى غير لائق</SelectItem>
              <SelectItem value="copyright-violation">انتهاك حقوق الملكية</SelectItem>
              <SelectItem value="misinformation">معلومات مضللة</SelectItem>
              <SelectItem value="harassment">تحرش أو تنمر</SelectItem>
              <SelectItem value="spam">محتوى مزعج أو غير مرغوب فيه</SelectItem>
              <SelectItem value="other">سبب آخر</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="details">تفاصيل إضافية</Label>
          <Textarea
            id="details"
            placeholder="يرجى تقديم معلومات إضافية حول سبب تصعيد هذه المشكلة..."
            value={details}
            onChange={(e) => setDetails(e.target.value)}
            rows={4}
          />
        </div>
      </div>
      
      <div className="flex justify-between mt-6">
        {onCancel ? (
          <Button variant="outline" onClick={onCancel}>
            إلغاء
          </Button>
        ) : (
          <div></div>
        )}
        
        <div className="flex gap-2">
          {!issue && ['article', 'news'].includes(contentType) && selectedContent && (
            <Button
              variant="outline"
              className="text-red-500 border-red-200 hover:bg-red-50"
              onClick={() => setShowDeleteDialog(true)}
              disabled={isSubmitting || isDeleting}
            >
              <Trash2 className="h-4 w-4 ml-2" />
              إزالة المحتوى
            </Button>
          )}
          
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || (!selectedContent && !issue) || !reason}
          >
            {isSubmitting ? 'جاري التصعيد...' : 'تصعيد المشكلة'}
          </Button>
        </div>
      </div>
      
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد إزالة المحتوى</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في إزالة هذا المحتوى؟ لا يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>
          
          {deleteSuccess && (
            <div className="bg-green-50 text-green-500 p-4 rounded-md flex items-center">
              <CheckCircle className="h-5 w-5 ml-2" />
              {deleteSuccess}
            </div>
          )}
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isDeleting}
            >
              إلغاء
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteContent}
              disabled={isDeleting}
            >
              {isDeleting ? 'جاري الإزالة...' : 'تأكيد الإزالة'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
