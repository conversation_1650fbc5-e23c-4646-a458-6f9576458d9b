import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

// GET mentor dashboard data
export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user role - handle both string ID and populated object cases
      let roleSlug: string
      if (typeof user.role === 'string') {
        const role = await payload.findByID({
          collection: 'roles',
          id: user.role,
          depth: 0,
        })
        roleSlug = role?.slug || ''
      } else if (user.role && typeof user.role === 'object') {
        roleSlug = user.role.slug || ''
      } else {
        return NextResponse.json({ error: 'Invalid user role configuration' }, { status: 400 })
      }

      // Check allowed roles
      if (!['mentor', 'super-admin', 'school-admin'].includes(roleSlug)) {
        return NextResponse.json(
          { error: 'Forbidden', message: 'Only mentors can access this dashboard' },
          { status: 403 },
        )
      }

      // Get school ID - handle both string ID and populated object cases
      let schoolId: string | undefined
      if (typeof user.school === 'string') {
        schoolId = user.school
      } else if (user.school && typeof user.school === 'object') {
        schoolId = user.school.id
      }

      // Only require school for non-super-admins
      if (roleSlug !== 'super-admin' && !schoolId) {
        return NextResponse.json(
          { error: 'Mentor must be associated with a school' },
          { status: 400 },
        )
      }

      // Get URL parameters
      const url = new URL(req.url)
      const limit = parseInt(url.searchParams.get('limit') || '5')

      // Try MongoDB first with mixed format support
      let teachers
      try {
        const { db } = await connectToDatabase()
        const { ObjectId } = await import('mongodb')

        // Get teacher role from MongoDB
        const teacherRoleDoc = await db.collection('roles').findOne({ slug: 'teacher' })

        if (teacherRoleDoc && schoolId) {
          // Query with mixed format support
          const teachersQuery = {
            $and: [
              {
                $or: [{ role: teacherRoleDoc._id }, { role: teacherRoleDoc._id.toString() }],
              },
              {
                $or: [{ school: new ObjectId(schoolId) }, { school: schoolId }],
              },
            ],
          }

          const teacherDocs = await db
            .collection('users')
            .find(teachersQuery)
            .limit(limit)
            .toArray()

          if (teacherDocs.length > 0) {
            // Convert to Payload format
            const teachersWithPopulation = []
            for (const teacher of teacherDocs) {
              try {
                const populatedTeacher = await payload.findByID({
                  collection: 'users',
                  id: teacher._id.toString(),
                  depth: 1,
                })
                teachersWithPopulation.push(populatedTeacher)
              } catch (error) {
                console.warn(`Could not populate teacher ${teacher._id}:`, error.message)
                teachersWithPopulation.push({
                  ...teacher,
                  id: teacher._id.toString(),
                })
              }
            }

            teachers = {
              docs: teachersWithPopulation,
              totalDocs: teachersWithPopulation.length,
            }
          }
        }
      } catch (mongoError) {
        console.warn('Error fetching teachers from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload if MongoDB didn't work
      if (!teachers) {
        teachers = await payload.find({
          collection: 'users',
          where: {
            role: { equals: 'teacher' },
            school: { equals: schoolId },
          },
          limit,
        })
      }

      // Get teacher activities
      const teacherActivities = await payload.find({
        collection: 'activities',
        where: {
          schoolId: { equals: schoolId },
          userRole: { equals: 'teacher' },
        },
        limit,
        sort: '-createdAt',
      })

      // Get mentor stats
      const newsCreated = await payload.find({
        collection: 'news',
        where: {
          'author.id': { equals: userId },
        },
        limit: 0,
      })

      const totalTeacherActivities = await payload.find({
        collection: 'activities',
        where: {
          schoolId: { equals: schoolId },
          userRole: { equals: 'teacher' },
        },
        limit: 0,
      })

      const dashboardData = {
        stats: {
          totalTeachers: teachers.totalDocs,
          teacherActivities: totalTeacherActivities.totalDocs,
          newsCreated: newsCreated.totalDocs,
          points: 'points' in user ? user.points : 0,
        },
        teachers: teachers.docs,
        recentActivities: teacherActivities.docs,
      }

      return NextResponse.json(dashboardData)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching mentor dashboard data:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
