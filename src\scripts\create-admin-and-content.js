// Script to create a super admin user and add lorem content for testing
const { MongoClient, ObjectId } = require('mongodb');
const bcrypt = require('bcryptjs');
const { faker } = require('@faker-js/faker');
require('dotenv').config();

// Admin credentials - SAVE THESE!
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'Admin123!';
const ADMIN_NAME = 'Super Administrator';

async function hashPassword(password) {
  const salt = await bcrypt.genSalt(10);
  const hash = await bcrypt.hash(password, salt);
  return { salt, hash };
}

async function createAdminAndContent() {
  // Connect to MongoDB
  const client = new MongoClient(process.env.DATABASE_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const usersCollection = db.collection('users');
    const rolesCollection = db.collection('roles');
    const schoolsCollection = db.collection('schools');
    const articlesCollection = db.collection('articles');
    const newsCollection = db.collection('news');
    
    // 1. Find or create super-admin role
    let superAdminRole = await rolesCollection.findOne({ slug: 'super-admin' });
    
    if (!superAdminRole) {
      console.log('Super admin role not found. Creating it...');
      const result = await rolesCollection.insertOne({
        name: 'Super Admin',
        slug: 'super-admin',
        permissions: ['full-access'],
        description: 'Full access to all features and settings',
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      superAdminRole = await rolesCollection.findOne({ _id: result.insertedId });
      console.log(`Super admin role created with ID: ${superAdminRole._id}`);
    } else {
      console.log(`Found super admin role with ID: ${superAdminRole._id}`);
    }
    
    // 2. Create password hash
    const { salt, hash } = await hashPassword(ADMIN_PASSWORD);
    
    // 3. Create or update super admin user
    const existingAdmin = await usersCollection.findOne({ email: ADMIN_EMAIL });
    
    if (existingAdmin) {
      console.log(`Admin user ${ADMIN_EMAIL} already exists. Updating password...`);
      await usersCollection.updateOne(
        { email: ADMIN_EMAIL },
        { 
          $set: {
            name: ADMIN_NAME,
            role: superAdminRole._id,
            salt,
            hash,
            updatedAt: new Date()
          } 
        }
      );
      console.log('Admin user updated successfully');
    } else {
      console.log(`Creating new admin user ${ADMIN_EMAIL}...`);
      const result = await usersCollection.insertOne({
        email: ADMIN_EMAIL,
        name: ADMIN_NAME,
        role: superAdminRole._id,
        salt,
        hash,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log(`Admin user created with ID: ${result.insertedId}`);
    }
    
    // 4. Create other roles if they don't exist
    const roles = [
      {
        name: 'School Admin',
        slug: 'school-admin',
        permissions: ['manage-users', 'manage-content'],
        description: 'Manages teachers, mentors, and students within their school'
      },
      {
        name: 'Mentor',
        slug: 'mentor',
        permissions: ['manage-content'],
        description: 'Creates news posts and reviews teacher feedback'
      },
      {
        name: 'Teacher',
        slug: 'teacher',
        permissions: ['manage-content'],
        description: 'Reviews and approves student articles'
      },
      {
        name: 'Student',
        slug: 'student',
        permissions: [],
        description: 'Creates and submits articles for review'
      }
    ];
    
    for (const role of roles) {
      const existingRole = await rolesCollection.findOne({ slug: role.slug });
      if (!existingRole) {
        await rolesCollection.insertOne({
          ...role,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        console.log(`Created ${role.name} role`);
      }
    }
    
    // 5. Create demo schools
    const schoolNames = [
      'Washington High School',
      'Lincoln Academy',
      'Roosevelt Middle School',
      'Jefferson Elementary',
      'Kennedy Preparatory School'
    ];
    
    const schoolIds = [];
    
    for (const name of schoolNames) {
      const existingSchool = await schoolsCollection.findOne({ name });
      if (existingSchool) {
        schoolIds.push(existingSchool._id);
      } else {
        const result = await schoolsCollection.insertOne({
          name,
          address: faker.location.streetAddress() + ', ' + faker.location.city() + ', ' + faker.location.state({ abbreviated: true }) + ' ' + faker.location.zipCode(),
          createdAt: new Date(),
          updatedAt: new Date()
        });
        schoolIds.push(result.insertedId);
        console.log(`Created school: ${name}`);
      }
    }
    
    // 6. Create users for each role
    const teacherRole = await rolesCollection.findOne({ slug: 'teacher' });
    const mentorRole = await rolesCollection.findOne({ slug: 'mentor' });
    const studentRole = await rolesCollection.findOne({ slug: 'student' });
    const schoolAdminRole = await rolesCollection.findOne({ slug: 'school-admin' });
    
    const users = [
      {
        email: '<EMAIL>',
        name: 'Teacher User',
        role: teacherRole._id,
        school: schoolIds[0],
        password: 'Teacher123!'
      },
      {
        email: '<EMAIL>',
        name: 'Mentor User',
        role: mentorRole._id,
        school: schoolIds[0],
        password: 'Mentor123!'
      },
      {
        email: '<EMAIL>',
        name: 'Student User',
        role: studentRole._id,
        school: schoolIds[0],
        grade: '10',
        password: 'Student123!'
      },
      {
        email: '<EMAIL>',
        name: 'School Admin User',
        role: schoolAdminRole._id,
        school: schoolIds[0],
        password: 'SchoolAdmin123!'
      }
    ];
    
    for (const user of users) {
      const { password, ...userData } = user;
      const { salt, hash } = await hashPassword(password);
      
      const existingUser = await usersCollection.findOne({ email: userData.email });
      if (existingUser) {
        await usersCollection.updateOne(
          { email: userData.email },
          { 
            $set: {
              ...userData,
              salt,
              hash,
              updatedAt: new Date()
            } 
          }
        );
        console.log(`Updated user: ${userData.email}`);
      } else {
        await usersCollection.insertOne({
          ...userData,
          salt,
          hash,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        console.log(`Created user: ${userData.email}`);
      }
    }
    
    // 7. Create sample articles
    const studentUser = await usersCollection.findOne({ email: '<EMAIL>' });
    const teacherUser = await usersCollection.findOne({ email: '<EMAIL>' });
    
    for (let i = 0; i < 10; i++) {
      const title = faker.lorem.sentence();
      const content = {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: faker.lorem.paragraphs(3),
                }
              ],
            },
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: faker.lorem.paragraphs(2),
                }
              ],
            },
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: faker.lorem.paragraphs(1),
                }
              ],
            }
          ],
          direction: null,
          format: '',
          indent: 0,
          version: 1,
        }
      };
      
      const statuses = ['draft', 'ready-for-review', 'pending-review', 'published'];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      const article = {
        title,
        content,
        author: studentUser._id,
        status,
        createdAt: faker.date.past(),
        updatedAt: new Date()
      };
      
      // Add teacher review for published articles
      if (status === 'published') {
        article.teacherReview = [
          {
            reviewer: teacherUser._id,
            comment: faker.lorem.paragraph(),
            rating: Math.floor(Math.random() * 5) + 6, // 6-10 rating
            approved: true,
            id: new ObjectId()
          }
        ];
      }
      
      await articlesCollection.insertOne(article);
      console.log(`Created article: ${title}`);
    }
    
    // 8. Create sample news posts
    const mentorUser = await usersCollection.findOne({ email: '<EMAIL>' });
    
    for (let i = 0; i < 5; i++) {
      const title = faker.lorem.sentence();
      const content = {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: faker.lorem.paragraphs(2),
                }
              ],
            },
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: faker.lorem.paragraphs(1),
                }
              ],
            }
          ],
          direction: null,
          format: '',
          indent: 0,
          version: 1,
        }
      };
      
      const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/-$/, '');
      
      await newsCollection.insertOne({
        title,
        content,
        slug,
        author: mentorUser._id,
        status: 'published',
        publishedAt: faker.date.recent(),
        createdAt: faker.date.past(),
        updatedAt: new Date()
      });
      
      console.log(`Created news post: ${title}`);
    }
    
    console.log('\n========== ADMIN CREDENTIALS ==========');
    console.log(`Email: ${ADMIN_EMAIL}`);
    console.log(`Password: ${ADMIN_PASSWORD}`);
    console.log('=======================================\n');
    
    console.log('Content creation completed successfully!');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

createAdminAndContent().catch(console.error);
