const { MongoClient, ObjectId } = require('mongodb')

// MongoDB connection string
const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/young-reporter'

async function seedData() {
  let client

  try {
    // Connect to MongoDB
    client = new MongoClient(uri)
    await client.connect()
    console.log('Connected to MongoDB')

    const db = client.db()

    // Get a student user
    let student = await db.collection('users').findOne({ 'role.slug': 'student' })

    if (!student) {
      console.log('No student user found, checking for any user...')

      // Try to find any user
      student = await db.collection('users').findOne({})

      if (!student) {
        console.log('No users found at all, creating a test student user...')

        // Create a test student user
        const newStudent = {
          id: new ObjectId().toString(),
          firstName: 'Test',
          lastName: 'Student',
          email: '<EMAIL>',
          role: {
            id: new ObjectId().toString(),
            slug: 'student',
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }

        await db.collection('users').insertOne(newStudent)
        console.log('Created test student user')

        student = newStudent
      } else {
        console.log('Found a user, using it as student')

        // Update the user to be a student if it's not already
        if (
          !student.role ||
          (typeof student.role === 'object' && student.role.slug !== 'student')
        ) {
          await db.collection('users').updateOne(
            { _id: student._id },
            {
              $set: {
                role: {
                  id: new ObjectId().toString(),
                  slug: 'student',
                },
              },
            },
          )
          console.log('Updated user to be a student')

          // Refresh the student object
          student = await db.collection('users').findOne({ _id: student._id })
        }
      }
    }

    console.log(
      `Using student: ${student.firstName || 'Unknown'} ${student.lastName || 'User'} (${student.id || student._id})`,
    )

    // Create sample articles
    const articles = [
      {
        id: new ObjectId().toString(),
        title: 'The Impact of Climate Change on Local Wildlife',
        content: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Climate change is affecting our local wildlife in significant ways. This article explores how rising temperatures and changing weather patterns are impacting the animals in our region.',
                    version: 1,
                  },
                ],
                direction: null,
                format: '',
                indent: 0,
                version: 1,
              },
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'From birds migrating earlier to mammals changing their hibernation patterns, the effects are widespread and concerning.',
                    version: 1,
                  },
                ],
                direction: null,
                format: '',
                indent: 0,
                version: 1,
              },
            ],
            direction: null,
            format: '',
            indent: 0,
            version: 1,
          },
        },
        author: {
          id: student.id,
          firstName: student.firstName,
          lastName: student.lastName,
          email: student.email,
        },
        status: 'published',
        teacherReview: [
          {
            id: new ObjectId().toString(),
            reviewer: {
              id: new ObjectId().toString(),
              firstName: 'Jane',
              lastName: 'Smith',
            },
            comment:
              'Excellent research and well-written article. Your passion for wildlife conservation shines through.',
            rating: 9,
            approved: true,
          },
        ],
        category: 'Environment',
        tags: ['climate change', 'wildlife', 'conservation'],
        views: 42,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
        updatedAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(), // 25 days ago
      },
      {
        id: new ObjectId().toString(),
        title: 'School Sports Day: A Recap',
        content: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Our annual school sports day was a huge success this year, with record participation and some impressive performances.',
                    version: 1,
                  },
                ],
                direction: null,
                format: '',
                indent: 0,
                version: 1,
              },
            ],
            direction: null,
            format: '',
            indent: 0,
            version: 1,
          },
        },
        author: {
          id: student.id,
          firstName: student.firstName,
          lastName: student.lastName,
          email: student.email,
        },
        status: 'published',
        teacherReview: [
          {
            id: new ObjectId().toString(),
            reviewer: {
              id: new ObjectId().toString(),
              firstName: 'Robert',
              lastName: 'Johnson',
            },
            comment:
              'Great coverage of the sports day events. Your article captures the excitement and achievements well.',
            rating: 8,
            approved: true,
          },
        ],
        category: 'School Events',
        tags: ['sports', 'school events', 'athletics'],
        views: 78,
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days ago
        updatedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
      },
      {
        id: new ObjectId().toString(),
        title: 'The Benefits of Learning a Musical Instrument',
        content: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Learning to play a musical instrument offers numerous cognitive and emotional benefits for students of all ages.',
                    version: 1,
                  },
                ],
                direction: null,
                format: '',
                indent: 0,
                version: 1,
              },
            ],
            direction: null,
            format: '',
            indent: 0,
            version: 1,
          },
        },
        author: {
          id: student.id,
          firstName: student.firstName,
          lastName: student.lastName,
          email: student.email,
        },
        status: 'draft',
        category: 'Education',
        tags: ['music', 'education', 'student development'],
        views: 0,
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
      },
    ]

    // Insert articles
    console.log('Inserting sample articles...')
    for (const article of articles) {
      // Check if article already exists
      const existingArticle = await db.collection('articles').findOne({ title: article.title })
      if (existingArticle) {
        console.log(`Article "${article.title}" already exists, skipping`)
        continue
      }

      await db.collection('articles').insertOne(article)
      console.log(`Created article: ${article.title}`)
    }

    // Create sample notifications
    const notifications = [
      {
        id: new ObjectId().toString(),
        title: 'Article Published',
        message:
          'Your article "The Impact of Climate Change on Local Wildlife" has been published!',
        type: 'article_published',
        userId: student.id,
        userRole: 'student',
        read: false,
        date: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(), // 25 days ago
        createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(), // 25 days ago
      },
      {
        id: new ObjectId().toString(),
        title: 'New Review',
        message:
          'A teacher has reviewed your article "The Impact of Climate Change on Local Wildlife".',
        type: 'article_review',
        userId: student.id,
        userRole: 'student',
        read: true,
        date: new Date(Date.now() - 26 * 24 * 60 * 60 * 1000).toISOString(), // 26 days ago
        createdAt: new Date(Date.now() - 26 * 24 * 60 * 60 * 1000).toISOString(), // 26 days ago
      },
      {
        id: new ObjectId().toString(),
        title: 'Article Published',
        message: 'Your article "School Sports Day: A Recap" has been published!',
        type: 'article_published',
        userId: student.id,
        userRole: 'student',
        read: false,
        date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
        createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
      },
    ]

    // Insert notifications
    console.log('Inserting sample notifications...')
    for (const notification of notifications) {
      // Check if notification already exists
      const existingNotification = await db.collection('notifications').findOne({
        title: notification.title,
        message: notification.message,
        userId: notification.userId,
      })

      if (existingNotification) {
        console.log(`Notification "${notification.title}" already exists, skipping`)
        continue
      }

      await db.collection('notifications').insertOne(notification)
      console.log(`Created notification: ${notification.title}`)
    }

    // Update user points
    console.log('Updating user points...')
    await db.collection('users').updateOne({ id: student.id }, { $set: { points: 25 } })
    console.log(`Updated points for user ${student.id}`)

    console.log('Student data seeding completed successfully!')
  } catch (error) {
    console.error('Error seeding student data:', error)
  } finally {
    if (client) {
      await client.close()
      console.log('MongoDB connection closed')
    }
  }
}

// Run the seed function
seedData()
  .then(() => console.log('Done!'))
  .catch(console.error)
