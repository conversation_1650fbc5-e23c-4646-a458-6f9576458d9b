'use client'

import React, { useState, useEffect, useRef } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

interface DeconstructedCardCarouselProps {
  title: string
  viewAllLink: string
  children: React.ReactNode[]
  itemsPerPage?: number
}

export const DeconstructedCardCarousel: React.FC<DeconstructedCardCarouselProps> = ({
  title,
  viewAllLink,
  children,
  itemsPerPage = 4,
}) => {
  const [currentPage, setCurrentPage] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)
  const [touchStart, setTouchStart] = useState(0)
  const [touchEnd, setTouchEnd] = useState(0)
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null)
  const totalPages = Math.ceil(children.length / itemsPerPage)
  const trackRef = useRef<HTMLDivElement>(null)

  // Auto-play functionality
  useEffect(() => {
    const play = () => {
      autoPlayRef.current = setTimeout(() => {
        nextPage()
      }, 8000) // Change slide every 8 seconds
    }

    play()

    return () => {
      if (autoPlayRef.current) {
        clearTimeout(autoPlayRef.current)
      }
    }
  }, [currentPage])

  // Handle touch events for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 50) {
      // Swipe left
      nextPage()
    }

    if (touchStart - touchEnd < -50) {
      // Swipe right
      prevPage()
    }
  }

  const nextPage = () => {
    if (isAnimating || totalPages <= 1) return

    setIsAnimating(true)
    setCurrentPage((prev) => (prev === totalPages - 1 ? 0 : prev + 1))

    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }

  const prevPage = () => {
    if (isAnimating || totalPages <= 1) return

    setIsAnimating(true)
    setCurrentPage((prev) => (prev === 0 ? totalPages - 1 : prev - 1))

    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }

  const goToPage = (page: number) => {
    if (isAnimating || page === currentPage) return

    setIsAnimating(true)
    setCurrentPage(page)

    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-3xl font-bold">{title}</h2>
        <div className="flex items-center gap-4">
          {totalPages > 1 && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={prevPage}
                disabled={isAnimating}
                className="h-9 w-9 rounded-full"
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="sr-only">الصفحة السابقة</span>
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={nextPage}
                disabled={isAnimating}
                className="h-9 w-9 rounded-full"
              >
                <ChevronRight className="h-4 w-4" />
                <span className="sr-only">الصفحة التالية</span>
              </Button>
            </div>
          )}
          <Button variant="outline" asChild>
            <Link href={viewAllLink} className="flex items-center gap-2">
              عرض الكل <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>

      <div
        className="relative overflow-hidden"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div
          ref={trackRef}
          className="carousel-track"
          style={{
            display: 'flex',
            transition: 'transform 0.6s cubic-bezier(0.16, 1, 0.3, 1)',
            padding: '40px 0',
            gap: '40px',
            transform: `translateX(-${currentPage * 100}%)`,
          }}
        >
          {Array.from({ length: totalPages }).map((_, pageIndex) => (
            <div
              key={pageIndex}
              className="w-full flex-shrink-0 card-system"
              style={{ display: 'flex', gap: '40px' }}
            >
              {children.slice(pageIndex * itemsPerPage, (pageIndex + 1) * itemsPerPage)}
            </div>
          ))}
        </div>
      </div>

      {/* Pagination dots */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6 gap-2">
          {Array.from({ length: totalPages }).map((_, index) => (
            <button
              key={index}
              onClick={() => goToPage(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentPage
                  ? 'bg-primary w-6'
                  : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
              }`}
              aria-label={`انتقل إلى الصفحة ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Client component wrapper
export const ClientDeconstructedCardCarousel: React.FC<{
  title: string
  viewAllLink: string
  children: React.ReactNode
  itemsPerPage?: number
}> = ({ title, viewAllLink, children, itemsPerPage = 4 }) => {
  // Convert children to array if it's not already
  const childrenArray = React.Children.toArray(children)

  return (
    <DeconstructedCardCarousel title={title} viewAllLink={viewAllLink} itemsPerPage={itemsPerPage}>
      {childrenArray}
    </DeconstructedCardCarousel>
  )
}
