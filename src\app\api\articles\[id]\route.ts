import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { incrementViewCount } from '@/utils/viewUtils'

// GET a single article
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  // Await params before accessing its properties
  const resolvedParams = await params
  const { id } = resolvedParams
  
  // Check if this is specifically a view request
  const isViewRequest = 
    req.headers.get('X-View-Count') === 'increment' || 
    req.headers.get('Accept')?.includes('application/json') ||
    req.headers.get('Accept')?.includes('text/html');
  
  try {
    console.log('Article GET API called for ID:', id)

    // If this is a view request, we'll increment the view count directly
    if (isViewRequest) {
      // Increment view count asynchronously without requiring authentication
      incrementViewCount('articles', id).catch(error => {
        console.error('Error incrementing view count:', error)
      })
      
      // For a direct view count request, we can return success immediately
      if (req.headers.get('X-View-Count') === 'increment') {
        return NextResponse.json({ success: true, message: 'View count incremented' })
      }
    }

    // For regular requests, continue with authentication
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    console.log('Token found:', token ? 'Yes' : 'No')

    if (!token) {
      console.log('No token found, returning 401')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      console.log('Verifying token...')
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      console.log('Getting Payload instance...')
      const payload = await getPayload({ config })
      console.log('Payload instance obtained')

      // Get the current user
      console.log('Looking up user in database with ID:', userId)
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })
      console.log('User found in database:', currentUser ? 'true' : 'false')

      // Get the requested article - await params first
      const resolvedParams = await params
      const articleId = String(resolvedParams.id)
      console.log('Looking up article with ID:', articleId)

      // First check if the ID is a media path - if so, return 404 immediately
      if (
        articleId.includes('/api/media') ||
        articleId.includes('/media') ||
        articleId.includes('/file/') ||
        /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(articleId)
      ) {
        console.log('Article ID is a media path, returning 404:', articleId)
        return NextResponse.json({ error: 'Invalid article ID' }, { status: 404 })
      }

      let article

      try {
        // Import article utilities
        const { findArticleByIdOrSlug } = await import('@/lib/article-utils')

        // Find the article using our utility function
        article = await findArticleByIdOrSlug(articleId)

        // If article not found, return 404
        if (!article) {
          console.log('Article not found')
          return NextResponse.json({ error: 'Article not found' }, { status: 404 })
        }

        console.log('Article lookup result: found')
      } catch (findError) {
        console.log('Error finding article:', findError)
        return NextResponse.json({ error: 'Article not found' }, { status: 404 })
      }

      console.log('Article found:', article ? 'true' : 'false')

      // Check if user is an admin, teacher, mentor, or the article author
      const userRole =
        typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      console.log('User role:', userRole)

      const isAdmin = userRole === 'super-admin' || userRole === 'school-admin'
      const isTeacher = userRole === 'teacher'
      const isMentor = userRole === 'mentor'

      // Import the normalizeId function
      const { normalizeId } = await import('@/lib/id-utils')

      // Safely check if user is the author
      let isAuthor = false
      if (article && article.author) {
        if (typeof article.author === 'object' && article.author?.id) {
          // Normalize both IDs to strings before comparison
          const normalizedAuthorId = normalizeId(article.author.id)
          const normalizedUserId = normalizeId(userId)
          isAuthor = normalizedAuthorId === normalizedUserId
        } else if (typeof article.author === 'string') {
          // Normalize both IDs to strings before comparison
          const normalizedAuthorId = normalizeId(article.author)
          const normalizedUserId = normalizeId(userId)
          isAuthor = normalizedAuthorId === normalizedUserId
        }
      }

      console.log('Author check:', {
        hasArticle: !!article,
        hasAuthor: !!(article && article.author),
        authorType: article && article.author ? typeof article.author : 'none',
        authorId:
          article && article.author && typeof article.author === 'object'
            ? article.author.id
            : 'unknown',
        normalizedAuthorId:
          article && article.author && typeof article.author === 'object'
            ? normalizeId(article.author.id)
            : typeof article.author === 'string'
              ? normalizeId(article.author)
              : 'unknown',
        userId,
        normalizedUserId: normalizeId(userId),
        isAuthor,
      })

      console.log('Access check:', {
        isAdmin,
        isTeacher,
        isMentor,
        isAuthor,
      })

      if (!isAdmin && !isTeacher && !isMentor && !isAuthor) {
        console.log('Access denied, returning 403')
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      console.log('Access granted')

      // Check if the request is for viewing
      if (article && article.status === 'published' && isViewRequest) {
        // We need the ID of the article for incrementing the view count
        const articleId = 
          article.id || 
          (typeof article._id === 'object' ? article._id.toString() : article._id);
          
        if (articleId) {
          // Increment view count asynchronously
          incrementViewCount('articles', articleId).catch(error => {
            console.error('Error incrementing view count:', error)
          });
        }
      }

      // Return the article we already fetched with proper structure
      return NextResponse.json({ article })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching article:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// UPDATE an article (PATCH method)
export async function PATCH(req: NextRequest, context: { params: { id: string } }) {
  // Await params before accessing its properties
  const params = await context.params
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Get the article ID from params
      const articleId = String(params.id)
      console.log('Article ID from params:', articleId)

      // Import article utilities
      const { findArticleByIdOrSlug, updateArticleByIdOrSlug } = await import('@/lib/article-utils')

      // Find the article using our utility function
      let article
      try {
        article = await findArticleByIdOrSlug(articleId)

        if (!article) {
          console.log('Article not found for update')
          return NextResponse.json({ error: 'Article not found' }, { status: 404 })
        }
      } catch (findError) {
        console.error('Error finding article:', findError)
        return NextResponse.json({ error: 'Article not found' }, { status: 404 })
      }

      // Import the normalizeId function
      const { normalizeId } = await import('@/lib/id-utils')

      // Check if user is an admin, teacher, mentor, or the article author
      const role = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = role === 'super-admin' || role === 'school-admin'
      const isTeacher = role === 'teacher'
      const isMentor = role === 'mentor'

      // Safely check if user is the author with normalized IDs
      let isAuthor = false
      if (article && article.author) {
        if (typeof article.author === 'object' && article.author?.id) {
          // Normalize both IDs to strings before comparison
          const normalizedAuthorId = normalizeId(article.author.id)
          const normalizedUserId = normalizeId(userId)
          isAuthor = normalizedAuthorId === normalizedUserId
        } else if (typeof article.author === 'string') {
          // Normalize both IDs to strings before comparison
          const normalizedAuthorId = normalizeId(article.author)
          const normalizedUserId = normalizeId(userId)
          isAuthor = normalizedAuthorId === normalizedUserId
        }
      }

      if (!isAdmin && !isTeacher && !isMentor && !isAuthor) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // If user is a student (author), restrict what they can update
      if (isAuthor && !isAdmin && !isTeacher && !isMentor) {
        // Get the request body
        const body = await req.json()

        // Students can only update certain fields
        const allowedFields = [
          'title',
          'content',
          'summary',
          'status',
          'category',
          'tags',
          'featuredImage',
        ]
        const filteredBody: Record<string, unknown> = {}

        // Only include allowed fields
        for (const field of allowedFields) {
          if (field in body) {
            filteredBody[field] = body[field]
          }
        }

        // Students can update published articles but can't change the status
        // If the article is already published, keep it published
        if (article.status === 'published') {
          filteredBody.status = 'published'
          console.log('Keeping published status for student edit')
        }
        // Otherwise, students can only set status to draft or pending-review
        else if (
          filteredBody.status &&
          typeof filteredBody.status === 'string' &&
          !['draft', 'pending-review'].includes(filteredBody.status)
        ) {
          filteredBody.status = 'draft'
          console.log('Restricting status to draft for student edit')
        }

        // Make sure we don't send author or teacherReview fields
        // These fields cause validation errors
        if (filteredBody.author) delete filteredBody.author
        if (filteredBody.teacherReview) delete filteredBody.teacherReview

        console.log(
          'Updating article with filtered data:',
          JSON.stringify(filteredBody).substring(0, 200) + '...',
        )

        try {
          // Update the article using our utility function
          console.log(
            'Updating article with filtered data:',
            JSON.stringify(filteredBody).substring(0, 200) + '...',
          )

          // Try to update the article using MongoDB directly
          const updateResult = await updateArticleByIdOrSlug(articleId, filteredBody)

          if (updateResult.success) {
            console.log('Article updated successfully')
            return NextResponse.json({
              success: true,
              message: 'Article updated successfully',
              article: updateResult.article,
            })
          }

          // If MongoDB update fails, try Payload
          console.log('MongoDB update failed, trying Payload...')

          // Use the article's actual ID from the database
          // Make sure we have a valid ID
          const payloadId = article.id || articleId
          console.log('Using article ID for update:', payloadId)

          // Update the article with filtered data
          const updatedArticle = await payload.update({
            collection: 'articles',
            id: payloadId,
            data: filteredBody,
          })
          console.log('Article updated successfully via Payload')
          return NextResponse.json({
            success: true,
            message: 'Article updated successfully',
            article: updatedArticle,
          })
        } catch (updateError) {
          console.error('Error updating article:', updateError)

          // Try with even more restricted data
          const safeData: {
            title?: string
            content?: Record<string, unknown>
            status?: 'draft' | 'published' | 'ready-for-review' | 'pending-review'
            featuredImage?: string | Record<string, unknown>
          } = {}

          // Only add fields that exist in filteredBody
          if (filteredBody.title) safeData.title = String(filteredBody.title)
          if (filteredBody.content && typeof filteredBody.content === 'object') {
            safeData.content = filteredBody.content as Record<string, unknown>
          }
          if (filteredBody.status && typeof filteredBody.status === 'string') {
            // Ensure status is one of the allowed values
            const status = filteredBody.status as
              | 'draft'
              | 'published'
              | 'ready-for-review'
              | 'pending-review'
            safeData.status = status
          } else {
            safeData.status = 'draft'
          }
          // Include featuredImage if it exists
          if (filteredBody.featuredImage) {
            safeData.featuredImage = filteredBody.featuredImage as string | Record<string, unknown>
          }

          console.log('Retrying with minimal data:', safeData)

          // Try one more time with minimal data using MongoDB
          const finalUpdateResult = await updateArticleByIdOrSlug(articleId, safeData)

          if (finalUpdateResult.success) {
            console.log('Article updated successfully with minimal data via MongoDB')
            return NextResponse.json({
              success: true,
              message: 'Article updated successfully',
              article: finalUpdateResult.article,
            })
          }

          // Last resort: try Payload again with minimal data
          console.log('MongoDB update with minimal data failed, trying Payload...')

          // Use the article's actual ID from the database
          // Make sure we have a valid ID
          const payloadId = article.id || articleId
          console.log('Using article ID for minimal data update:', payloadId)

          try {
            const updatedArticle = await payload.update({
              collection: 'articles',
              id: payloadId,
              data: safeData,
            })

            console.log('Article updated successfully with minimal data via Payload')
            return NextResponse.json({
              success: true,
              message: 'Article updated with basic data only',
              article: updatedArticle,
            })
          } catch (finalError) {
            console.error('Final error updating article:', finalError)
            return NextResponse.json({ error: 'Failed to update article' }, { status: 500 })
          }
        }
      }

      // Get the request body
      const body = await req.json()

      // Make sure we don't send author or teacherReview fields
      // These fields cause validation errors
      const safeBody = { ...body }
      if (safeBody.author) delete safeBody.author
      if (safeBody.teacherReview) delete safeBody.teacherReview

      console.log(
        'Updating article with admin data:',
        JSON.stringify(safeBody).substring(0, 200) + '...',
      )

      try {
        // Try to update the article using MongoDB directly first
        console.log('Updating article with admin data via MongoDB')
        const updateResult = await updateArticleByIdOrSlug(articleId, safeBody)

        if (updateResult.success) {
          console.log('Article updated successfully by admin via MongoDB')
          return NextResponse.json({
            success: true,
            message: 'Article updated successfully',
            article: updateResult.article,
          })
        }

        // If MongoDB update fails, try Payload
        console.log('MongoDB admin update failed, trying Payload...')

        // Use the article's actual ID from the database
        // Make sure we have a valid ID
        const payloadId = article.id || articleId
        console.log('Using article ID for admin update:', payloadId)

        // Update the article
        const updatedArticle = await payload.update({
          collection: 'articles',
          id: payloadId,
          data: safeBody,
        })

        console.log('Article updated successfully by admin via Payload')
        return NextResponse.json({
          success: true,
          message: 'Article updated successfully',
          article: updatedArticle,
        })
      } catch (updateError) {
        console.error('Error updating article as admin:', updateError)

        // Try with even more restricted data
        const minimalData = {
          title: safeBody.title,
          content: safeBody.content,
          status: safeBody.status || 'draft',
          featuredImage: safeBody.featuredImage || null,
        }

        console.log('Retrying with minimal admin data:', minimalData)

        // Try one more time with minimal data using MongoDB
        const finalUpdateResult = await updateArticleByIdOrSlug(articleId, minimalData)

        if (finalUpdateResult.success) {
          console.log('Article updated successfully with minimal admin data via MongoDB')
          return NextResponse.json({
            success: true,
            message: 'Article updated successfully',
            article: finalUpdateResult.article,
          })
        }

        // Last resort: try Payload again with minimal data
        console.log('MongoDB update with minimal admin data failed, trying Payload...')

        // Use the article's actual ID from the database
        // Make sure we have a valid ID
        const payloadId2 = article.id || articleId
        console.log('Using article ID for minimal admin update:', payloadId2)

        try {
          const updatedArticle = await payload.update({
            collection: 'articles',
            id: payloadId2,
            data: minimalData,
          })

          console.log('Article updated successfully with minimal admin data via Payload')
          return NextResponse.json({
            success: true,
            message: 'Article updated with basic data only',
            article: updatedArticle,
          })
        } catch (finalError) {
          console.error('Final error updating article as admin:', finalError)
          return NextResponse.json({ error: 'Failed to update article' }, { status: 500 })
        }
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating article:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// UPDATE an article (PUT method - alias for PATCH)
export async function PUT(req: NextRequest, context: { params: { id: string } }) {
  // Reuse the PATCH method implementation
  // No need to await params here as PATCH will handle it
  return PATCH(req, context)
}

// DELETE an article
export async function DELETE(_req: NextRequest, { params }: { params: { id: string } }) {
  // Await params before accessing its properties
  const resolvedParams = await params
  // Get the article ID from params
  const { id } = resolvedParams
  const articleId = String(id)
  console.log('Attempting to delete article with ID:', articleId)
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Import article utilities
      const { findArticleByIdOrSlug } = await import('@/lib/article-utils')

      // Find the article using our utility function
      let article
      try {
        article = await findArticleByIdOrSlug(articleId)

        if (!article) {
          console.log('Article not found for deletion')
          return NextResponse.json({ error: 'Article not found' }, { status: 404 })
        }

        console.log('Found article for deletion:', article.id || article._id || articleId)
      } catch (error) {
        console.log('Error finding article when trying to delete:', error)
        return NextResponse.json({ error: 'Article not found' }, { status: 404 })
      }

      // Check if user is an admin or the article author
      const userRole =
        typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role
      const isAdmin = userRole === 'super-admin' || userRole === 'school-admin'

      // Import the normalizeId function
      const { normalizeId } = await import('@/lib/id-utils')

      // Safely check if user is the author
      let isAuthor = false
      if (article && article.author) {
        if (typeof article.author === 'object' && article.author?.id) {
          // Normalize both IDs to strings before comparison
          const normalizedAuthorId = normalizeId(article.author.id)
          const normalizedUserId = normalizeId(userId)
          isAuthor = normalizedAuthorId === normalizedUserId
        } else if (typeof article.author === 'string') {
          // Normalize both IDs to strings before comparison
          const normalizedAuthorId = normalizeId(article.author)
          const normalizedUserId = normalizeId(userId)
          isAuthor = normalizedAuthorId === normalizedUserId
        }
      }

      console.log('Author check for deletion:', {
        hasArticle: !!article,
        hasAuthor: !!(article && article.author),
        authorType: article && article.author ? typeof article.author : 'none',
        authorId:
          article && article.author && typeof article.author === 'object'
            ? article.author.id
            : 'unknown',
        normalizedAuthorId:
          article && article.author && typeof article.author === 'object'
            ? normalizeId(article.author.id)
            : typeof article.author === 'string'
              ? normalizeId(article.author)
              : 'unknown',
        userId,
        normalizedUserId: normalizeId(userId),
        isAuthor,
      })

      // Only admins or the author can delete the article
      if (!isAdmin && !isAuthor) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // If the article is published, only admins can delete it
      if (article && article.status === 'published' && !isAdmin) {
        return NextResponse.json(
          {
            error: 'Published articles can only be deleted by administrators',
          },
          { status: 403 },
        )
      }

      console.log('Deleting article with ID:', articleId)

      // Import the delete utility
      const { deleteArticleByIdOrSlug } = await import('@/lib/article-utils')

      // Delete the article using our utility function
      const deleteResult = await deleteArticleByIdOrSlug(articleId)

      if (!deleteResult.success) {
        return NextResponse.json({ error: deleteResult.message }, { status: 500 })
      }

      console.log('Article deleted successfully')

      return NextResponse.json({ success: true, message: 'Article deleted successfully' })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error deleting article:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
