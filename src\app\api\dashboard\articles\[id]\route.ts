import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { createSuccessResponse, createErrorResponse, isDevelopment } from '@/utils/apiUtils'
import { isValidObjectId } from '@/utils/mongoUtils'
import { deleteArticleByIdOrSlug } from '@/lib/article-utils'
import { ObjectId } from 'mongodb'

// Helper function to ensure consistent image path format
function ensureProperImageFormat(article: any): any {
  if (!article) return article;
  
  // Make a copy to avoid mutating the original
  const formattedArticle = { ...article };
  
  // Check if featuredImage exists and is not already a proper path
  if (formattedArticle.featuredImage) {
    if (typeof formattedArticle.featuredImage === 'string') {
      // If it's just an ID and not already a path
      if (!formattedArticle.featuredImage.includes('/api/media/file/') && 
          /^[0-9a-fA-F]{24}$/.test(formattedArticle.featuredImage)) {
        formattedArticle.featuredImage = `/api/media/file/${formattedArticle.featuredImage}`;
        console.log('Converted featuredImage ID to path:', formattedArticle.featuredImage);
      }
    } else if (typeof formattedArticle.featuredImage === 'object' && formattedArticle.featuredImage !== null) {
      // If it's an object with id or filename
      if (formattedArticle.featuredImage.filename) {
        formattedArticle.featuredImage = `/api/media/file/${formattedArticle.featuredImage.filename}`;
      } else if (formattedArticle.featuredImage.id) {
        formattedArticle.featuredImage = `/api/media/file/${formattedArticle.featuredImage.id}`;
      }
      console.log('Converted featuredImage object to path:', formattedArticle.featuredImage);
    }
  }
  
  return formattedArticle;
}

export async function GET(_req: NextRequest, context: { params: any }) {
  // In Next.js App Router, we need to properly handle params
  // We must await context.params before accessing its properties
  const params = await context.params
  const { id } = params
  const articleIdOrSlug = String(id)
  console.log('Looking for article with ID or slug:', articleIdOrSlug)

  // Check if the ID is a media path - if so, return 404 immediately
  if (
    articleIdOrSlug.includes('/api/media') ||
    articleIdOrSlug.includes('/media') ||
    articleIdOrSlug.includes('/file/') ||
    /\.(jpg|jpeg|png|gif|webp|svg|bmp|tiff)$/i.test(articleIdOrSlug)
  ) {
    console.log('Article ID is a media path, returning 404:', articleIdOrSlug)
    return createErrorResponse('Invalid article ID', 404)
  }

  // No need to validate format - we'll try to find by slug first, then ID

  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return createErrorResponse('Unauthorized', 401)
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return createErrorResponse('Unauthorized', 401)
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user (for authentication purposes)
      let _user
      try {
        _user = await payload.findByID({
          collection: 'users',
          id: userId,
          depth: 0,
        })

        if (!_user) {
          console.error('User not found')
          return NextResponse.json(
            {
              success: false,
              error: 'User not found',
            },
            { status: 404 },
          )
        }

        // Log the user for debugging
        console.log('Current user:', _user.email)
      } catch (userError) {
        console.error('Error finding user:', userError)
        return NextResponse.json(
          {
            success: false,
            error: 'Error finding user',
          },
          { status: 500 },
        )
      }

      // Use our utility functions to handle different ID formats
      let article

      try {
        // Log database connection info
        console.log('Database connection info:')
        console.log('- Database name:', payload.db.name)
        console.log('- Collections:', Object.keys(payload.collections))

        // Try the same approach as the article page
        try {
          console.log('Trying the same approach as the article page...')
          const { db } = await import('@/lib/mongodb').then((mod) => mod.connectToDatabase())
          const { ObjectId } = await import('mongodb')

          // Try multiple query approaches
          let mongoArticle

          // First try without ObjectId conversion
          mongoArticle = await db.collection('articles').findOne({
            $or: [{ slug: articleIdOrSlug }, { id: articleIdOrSlug }],
          })

          // If not found, try with ObjectId conversion
          if (!mongoArticle && isValidObjectId(articleIdOrSlug)) {
            try {
              mongoArticle = await db.collection('articles').findOne({
                _id: new ObjectId(articleIdOrSlug),
              })
            } catch (idError) {
              console.error('Error converting to ObjectId:', idError)
            }
          }

          if (mongoArticle) {
            console.log('Article found using article page approach!')
            console.log('Article ID:', mongoArticle._id)
            console.log('Article title:', mongoArticle.title)
            console.log('Article slug:', mongoArticle.slug)

            // Use this article if found
            article = mongoArticle
            return createSuccessResponse({ article: ensureProperImageFormat(article) })
          } else {
            console.log('No article found using article page approach')
          }
        } catch (mongoError) {
          console.error('Error with article page approach:', mongoError)
        }

        // Log all articles in the database for debugging
        try {
          const allArticles = await payload.find({
            collection: 'articles',
            limit: 10, // Limit to 10 articles to avoid huge logs
          })

          console.log('Found', allArticles.totalDocs, 'total articles in database')
          console.log('Sample articles:')
          allArticles.docs.forEach((a, index) => {
            console.log(`Article ${index + 1}:`, {
              id: a.id,
              title: a.title,
              slug: a.slug,
              status: a.status,
            })
          })
        } catch (error) {
          console.error('Error listing all articles:', error)
        }

        // First try to find by slug (prioritize slug-based lookups)
        try {
          console.log('Trying to find article by slug:', articleIdOrSlug)
          const articles = await payload.find({
            collection: 'articles',
            where: {
              slug: {
                equals: articleIdOrSlug,
              },
            },
            depth: 2,
            limit: 1,
          })

          if (articles.docs.length > 0) {
            article = articles.docs[0]
            console.log('Found article by slug')
            return createSuccessResponse({ article: ensureProperImageFormat(article) })
          } else {
            console.log('No article found with slug:', articleIdOrSlug)
          }
        } catch (error) {
          console.log('Error finding article by slug:', error)
          // Continue to next method
        }

        // If not found by slug, try by MongoDB ObjectID if it's a valid format
        if (!article && isValidObjectId(articleIdOrSlug)) {
          try {
            console.log('Trying to find article by MongoDB ObjectID:', articleIdOrSlug)

            // Try direct MongoDB query first
            try {
              console.log('Attempting direct MongoDB query...')
              const { db } = await import('@/lib/mongodb').then((mod) => mod.connectToDatabase())

              // Need to convert string ID to MongoDB ObjectId
              const { ObjectId } = await import('mongodb')

              // Try multiple query approaches
              const mongoArticle = await db.collection('articles').findOne({
                $or: [
                  { _id: new ObjectId(articleIdOrSlug) },
                  { id: articleIdOrSlug },
                  { slug: articleIdOrSlug },
                ],
              })

              if (mongoArticle) {
                console.log('Found article directly in MongoDB:', mongoArticle._id)
                console.log('Article title:', mongoArticle.title)
                article = mongoArticle;
                return createSuccessResponse({ article: ensureProperImageFormat(article) })
              } else {
                console.log('No article found directly in MongoDB with ID:', articleIdOrSlug)
              }
            } catch (mongoError) {
              console.error('Error with direct MongoDB query:', mongoError)
            }

            // Try to find the article using find() first to avoid the NotFound error
            console.log('Trying Payload find() with ID:', articleIdOrSlug)
            const articles = await payload.find({
              collection: 'articles',
              where: {
                id: {
                  equals: articleIdOrSlug,
                },
              },
              depth: 2,
              limit: 1,
            })

            if (articles.docs.length > 0) {
              article = articles.docs[0]
              console.log('Found article by MongoDB ObjectID using find()')
            } else {
              console.log(
                'No article found with MongoDB ObjectID using Payload find():',
                articleIdOrSlug,
              )
              article = null
            }
          } catch (error) {
            console.log('Error finding by MongoDB ObjectID:', error)
            // Set article to null explicitly to ensure we continue to other methods
            article = null
            // Don't throw here, just continue to other methods
            // This allows us to try other lookup methods
          }
        }

        // If not found by ObjectID, try by numeric ID
        if (!article && /^\d+$/.test(articleIdOrSlug)) {
          try {
            const articles = await payload.find({
              collection: 'articles',
              where: {
                id: {
                  equals: articleIdOrSlug,
                },
              },
              depth: 2,
              limit: 1,
            })

            if (articles.docs.length > 0) {
              article = articles.docs[0]
              console.log('Found article by numeric ID')
            } else {
              console.log('No article found with numeric ID:', articleIdOrSlug)
            }
          } catch (error) {
            console.log('Error finding article by numeric ID:', error)
            // Continue to next method
          }
        }

        // If still not found, try by title (more flexible search)
        if (!article) {
          try {
            // Try a more flexible search that includes title
            const articles = await payload.find({
              collection: 'articles',
              where: {
                title: {
                  contains: articleIdOrSlug,
                },
              },
              depth: 2,
              limit: 1,
            })

            if (articles.docs.length > 0) {
              article = articles.docs[0]
              console.log('Found article by title search')
            } else {
              console.log('No article found with title search for:', articleIdOrSlug)
            }
          } catch (error) {
            console.log('Error finding article by title search:', error)
            // Continue to next method
          }
        }

        // If we found the article, get the author info separately
        if (article && article.author) {
          try {
            const authorId = typeof article.author === 'object' ? article.author.id : article.author
            console.log('Fetching author with ID:', authorId)

            const author = await payload.findByID({
              collection: 'users',
              id: authorId,
              depth: 0,
            })

            // Create a proper author object that matches the User type
            if (author) {
              article.author = author
              console.log('Author found:', author.email)
            } else {
              console.log('Author not found for ID:', authorId)
              // Keep the original author reference
            }
          } catch (authorError) {
            console.error('Error fetching author:', authorError)
            // Keep the original author reference
          }
        }
      } catch (findError) {
        console.log('Error finding article by ID:', findError)

        // If that fails, try one last attempt with a more general query
        try {
          console.log('Attempting fallback lookup for article with ID or slug:', articleIdOrSlug)

          // Try a more general query that might catch the article
          // This is a last-resort attempt with a very broad search
          const articlesResult = await payload.find({
            collection: 'articles',
            where: {
              or: [
                // Try by ID
                {
                  id: {
                    equals: articleIdOrSlug,
                  },
                },
                // Try by slug
                {
                  slug: {
                    equals: articleIdOrSlug,
                  },
                },
                // Try by title (partial match)
                {
                  title: {
                    contains: articleIdOrSlug,
                  },
                },
              ],
            },
            depth: 0,
            limit: 1,
          })

          if (articlesResult.docs.length > 0) {
            article = articlesResult.docs[0]
            console.log('Found article in fallback lookup by slug')

            // Get author info
            if (article.author) {
              try {
                const authorId =
                  typeof article.author === 'object' ? article.author.id : article.author
                console.log('Fetching author in fallback with ID:', authorId)

                const author = await payload.findByID({
                  collection: 'users',
                  id: authorId,
                  depth: 0,
                })

                // Create a proper author object that matches the User type
                if (author) {
                  article.author = author
                  console.log('Author found in fallback:', author.email)
                } else {
                  console.log('Author not found in fallback for ID:', authorId)
                }
              } catch (authorError) {
                console.error('Error fetching author in fallback:', authorError)
              }
            }
          } else {
            console.log('No article found in fallback lookup')
          }
        } catch (secondFindError) {
          console.error('Error in fallback article lookup:', secondFindError)
        }

        // Check if the article's author belongs to the same school as the teacher
        if (
          article &&
          typeof article.author === 'object' &&
          article.author &&
          _user &&
          typeof _user === 'object' &&
          _user.role === 'teacher' &&
          _user.school &&
          article.author.school
        ) {
          const authorSchoolId =
            typeof article.author.school === 'object'
              ? article.author.school.id
              : article.author.school
          const teacherSchoolId = typeof _user.school === 'object' ? _user.school.id : _user.school

          if (authorSchoolId !== teacherSchoolId) {
            console.log('Unauthorized: Article author is not in the same school as the teacher')
            return createErrorResponse('Unauthorized', 403)
          }
        }
      }

      // If article is not found, create a mock article for testing in development mode only
      if (!article) {
        // Use the imported isDevelopment function
        const isDevMode = isDevelopment()

        if (isDevMode) {
          console.log('Article not found, creating mock article for testing (DEV MODE)')

          // Get a student user for the author
          let author
          try {
            const students = await payload.find({
              collection: 'users',
              where: {
                'role.slug': {
                  equals: 'student',
                },
              },
              limit: 1,
            })

            if (students.docs.length > 0) {
              author = students.docs[0]
            } else {
              // If no student found, use the current user
              author = _user
            }
          } catch (error) {
            console.error('Error finding student user:', error)
            author = _user
          }

          // Create a mock article with all required fields
          article = {
            id: articleIdOrSlug,
            title: 'Test Article for ID ' + articleIdOrSlug,
            slug: 'test-article-' + articleIdOrSlug,
            content: {
              root: {
                children: [
                  {
                    children: [
                      {
                        text:
                          'This is a test article created for testing purposes. The article with ID ' +
                          articleIdOrSlug +
                          ' was not found in the database.',
                      },
                    ],
                    type: 'paragraph',
                  },
                  {
                    children: [
                      {
                        text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.',
                      },
                    ],
                    type: 'paragraph',
                  },
                ],
                direction: 'ltr',
                format: 'left',
                indent: 0,
                type: 'root',
                version: 1,
              },
            },
            summary: 'This is a test article summary for development purposes.',
            author: author || {
              id: '123456789012345678901234',
              firstName: 'Test',
              lastName: 'Student',
              email: '<EMAIL>',
              role: {
                slug: 'student',
              },
              school: _user?.school || null,
            },
            authorAlias: 'Student_' + Math.random().toString(36).substring(2, 8),
            status: 'published',
            views: Math.floor(Math.random() * 100),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            featuredImage: {
              id: 'mock-image-id',
              url: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?q=80&w=1170&auto=format&fit=crop',
              width: 1170,
              height: 780,
              alt: 'Test Featured Image',
              filename: 'test-image.jpg',
              mimeType: 'image/jpeg',
              filesize: 123456,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            teacherReview: [
              {
                id: 'mock-review-id',
                reviewer: _user || {
                  id: '123456789012345678901235',
                  firstName: 'Test',
                  lastName: 'Teacher',
                  email: '<EMAIL>',
                  role: {
                    slug: 'teacher',
                  },
                },
                comment: 'Great work! This is a test review.',
                rating: 8,
                approved: true,
              },
            ],
          }
        } else {
          // In production, return a 404 error
          console.log('Article not found in production mode')
          return NextResponse.json(
            {
              success: false,
              error: 'Article not found',
            },
            { status: 404 },
          )
        }
      }

      // Skip views count for now until the database schema is updated
      console.log('Skipping view count increment until database schema is updated')

      // Filter teacher reviews based on user role
      if (article && article.teacherReview && Array.isArray(article.teacherReview)) {
        const userRole = typeof _user.role === 'object' ? _user.role?.slug : _user.role

        if (userRole === 'teacher') {
          // Teachers can only see their own reviews
          article.teacherReview = article.teacherReview.filter(
            (review) =>
              (typeof review.reviewer === 'object' && review.reviewer?.id === userId) ||
              review.reviewer === userId,
          )
        } else if (
          userRole === 'mentor' ||
          userRole === 'school-admin' ||
          userRole === 'super-admin'
        ) {
          // Mentors and admins can see all reviews
          // Populate reviewer information for each review
          try {
            for (let i = 0; i < article.teacherReview.length; i++) {
              const review = article.teacherReview[i]
              const reviewerId =
                typeof review.reviewer === 'object' ? review.reviewer.id : review.reviewer

              if (reviewerId && typeof reviewerId === 'string') {
                try {
                  const reviewer = await payload.findByID({
                    collection: 'users',
                    id: reviewerId,
                    depth: 1,
                  })

                  if (reviewer) {
                    // Replace the reviewer ID with the reviewer object
                    article.teacherReview[i].reviewer = reviewer
                  }
                } catch (reviewerError) {
                  console.error('Error fetching reviewer:', reviewerError)
                  // Keep the original reviewer reference
                }
              }
            }
          } catch (reviewError) {
            console.error('Error processing reviews:', reviewError)
          }
        }
      }

      return createSuccessResponse({ article: ensureProperImageFormat(article) })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
        },
        { status: 401 },
      )
    }
  } catch (error) {
    console.error('Error fetching article:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal Server Error',
      },
      { status: 500 },
    )
  }
}

export async function DELETE(req: NextRequest, context: { params: any }) {
  const params = await context.params
  const { id } = params
  const articleIdOrSlug = String(id)

  // Get the token from cookies
  const cookieStore = await cookies()
  const token = cookieStore.get('payload-token')?.value

  if (!token) {
    return createErrorResponse('Unauthorized', 401)
  }

  let decoded, userId, userRole, user
  try {
    decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
    userId = typeof decoded === 'object' ? decoded.id : null
    if (!userId) {
      return createErrorResponse('Unauthorized', 401)
    }
    const payload = await getPayload({ config })
    user = await payload.findByID({ collection: 'users', id: userId, depth: 2 })
    if (!user) {
      return createErrorResponse('User not found', 404)
    }
    
    console.log('Found user:', {
      id: user.id,
      email: user.email,
      role: user.role
    });
    
    // Extract role information
    const rawRole = user.role;
    console.log('Raw role value:', rawRole);
    
    // Try to resolve role if it's an ID
    if (rawRole && typeof rawRole === 'string' && rawRole.length === 24) {
      try {
        console.log('Attempting to fetch role with ID:', rawRole);
        const roleDoc = await payload.findByID({ 
          collection: 'roles', 
          id: rawRole 
        });
        
        console.log('Fetched role:', roleDoc);
        if (roleDoc && roleDoc.slug) {
          userRole = roleDoc.slug;
          console.log('Successfully resolved role slug:', userRole);
        } else {
          console.log('Role document found but no slug:', roleDoc);
          userRole = rawRole; // Fallback to ID
        }
      } catch (roleError) {
        console.error('Error fetching role:', roleError);
        userRole = rawRole; // Fallback to ID
      }
    } else if (typeof rawRole === 'object' && rawRole !== null && 'slug' in rawRole) {
      // If role is already populated as an object
      userRole = rawRole.slug;
      console.log('Role is already an object with slug:', userRole);
    } else {
      // Use whatever we have
      userRole = rawRole;
      console.log('Using raw role value:', userRole);
    }
  } catch (err) {
    console.error('Auth error:', err);
    return createErrorResponse('Unauthorized', 401)
  }

  // Find the article to check permissions
  const payload = await getPayload({ config })
  let article
  try {
    const articles = await payload.find({
      collection: 'articles',
      where: {
        or: [
          { id: { equals: articleIdOrSlug } },
          { slug: { equals: articleIdOrSlug } },
        ],
      },
      depth: 0,
      limit: 1,
    })
    article = articles.docs[0]
    if (!article) {
      return createErrorResponse('Article not found', 404)
    }
  } catch (err) {
    return createErrorResponse('Error finding article', 500)
  }

  // Only super-admin, school-admin, or the article's author can delete
  const articleAuthorId = typeof article.author === 'object' ? 
    (article.author ? article.author.id : null) : 
    article.author;
  
  console.log('DELETE debug:', { 
    userId, 
    userRole,
    roleType: typeof userRole,
    articleAuthor: article.author,
    articleAuthorId,
    authorType: typeof article.author,
    userIdType: typeof userId
  });
  
  // Helper function to safely compare IDs that might be in different formats
  const compareIds = (id1: string | null, id2: string | null): boolean => {
    if (!id1 || !id2) return false;
    // Normalize IDs by trimming and converting to string
    const normalizedId1 = String(id1).trim();
    const normalizedId2 = String(id2).trim();
    return normalizedId1 === normalizedId2;
  };
  
  // Check for admin role by slug or by direct payload API check
  let isAdmin = false;
  
  // First try checking role as object
  if (typeof userRole === 'object' && userRole !== null) {
    isAdmin = (userRole as any).slug === 'super-admin' || (userRole as any).slug === 'school-admin';
    console.log('Admin check from role object:', { isAdmin, roleSlug: (userRole as any).slug });
  } 
  // Then try checking role as string
  else if (typeof userRole === 'string') {
    isAdmin = userRole === 'super-admin' || userRole === 'school-admin';
    console.log('Admin check from role string:', { isAdmin, roleString: userRole });
  }
  
  // Alternative check using role directly from user object
  const userRoleFromUser = user.role;
  let isAdminFromUser = false;
  
  if (typeof userRoleFromUser === 'object' && userRoleFromUser !== null) {
    if ('slug' in userRoleFromUser && userRoleFromUser.slug) {
      isAdminFromUser = userRoleFromUser.slug === 'super-admin' || userRoleFromUser.slug === 'school-admin';
      console.log('Admin check from user role object:', { isAdminFromUser, roleSlug: userRoleFromUser.slug });
    } else {
      console.log('User role object has no slug:', userRoleFromUser);
    }
  } else if (typeof userRoleFromUser === 'string') {
    // Try to directly fetch the role first
    try {
      const roleDoc = await payload.findByID({ 
        collection: 'roles', 
        id: userRoleFromUser,
        depth: 1
      });
      
      if (roleDoc && roleDoc.slug) {
        isAdminFromUser = roleDoc.slug === 'super-admin' || roleDoc.slug === 'school-admin';
        console.log('Admin check from fetched role:', { isAdminFromUser, roleSlug: roleDoc.slug });
      } else {
        console.log('Fetched role has no slug:', roleDoc);
      }
    } catch (error) {
      console.error('Error fetching role for admin check:', error);
    }
  }
  
  // Combine both checks
  isAdmin = isAdmin || isAdminFromUser;
  
  // If still not clearly an admin, try the access control functions as an intermediate check
  if (!isAdmin) {
    try {
      // Create a mock request for access checks
      const mockReq = {
        user: {
          ...user,
          collection: 'users'
        },
        payload,
        method: 'DELETE'
      } as any;
      
      // Try importing and using the access control functions directly
      const { isSuperAdmin, isSchoolAdmin } = await import('@/access');
      const superAdminCheck = await isSuperAdmin({ req: mockReq });
      const schoolAdminCheck = await isSchoolAdmin({ req: mockReq });
      
      console.log('Direct admin checks:', { superAdminCheck, schoolAdminCheck });
      
      // Update isAdmin based on direct checks
      const directCheckResult = !!superAdminCheck || !!schoolAdminCheck;
      isAdmin = isAdmin || directCheckResult;
      
      if (directCheckResult) {
        console.log('Admin status granted through direct access check');
      }
    } catch (accessCheckError) {
      console.error('Error during direct admin check:', accessCheckError);
    }
  }
  
  // If still not clearly an admin, try direct MongoDB query as absolute last resort
  if (!isAdmin) {
    try {
      console.log('Attempting direct MongoDB query for role...');
      const { db } = await import('@/lib/mongodb').then((mod) => mod.connectToDatabase());
      const { ObjectId } = await import('mongodb');
      
      // First get user with populated role
      const dbUser = await db.collection('users').findOne({ _id: new ObjectId(userId) });
      console.log('User from MongoDB:', { id: dbUser?._id, email: dbUser?.email, role: dbUser?.role });
      
      if (dbUser && dbUser.role) {
        // Try to get role document
        const roleId = typeof dbUser.role === 'object' && dbUser.role !== null ? 
          (dbUser.role.$oid || dbUser.role._id || dbUser.role.id) : 
          dbUser.role;
          
        console.log('Role ID from MongoDB user:', roleId);
        
        if (roleId && typeof roleId === 'string') {
          try {
            const roleDoc = await db.collection('roles').findOne({ 
              $or: [
                { _id: new ObjectId(roleId) },
                { id: roleId }
              ] 
            });
            
            if (roleDoc) {
              console.log('Role from MongoDB:', roleDoc);
              if (roleDoc.slug === 'super-admin' || roleDoc.slug === 'school-admin') {
                isAdmin = true;
                console.log('Admin status granted through direct MongoDB query');
              }
            }
          } catch (roleQueryError) {
            console.error('Error querying role from MongoDB:', roleQueryError);
          }
        }
      }
    } catch (mongoError) {
      console.error('Error with direct MongoDB query:', mongoError);
    }
  }
  
  const isAuthor = compareIds(articleAuthorId, userId);
  
  console.log('Final access check result:', { isAdmin, isAuthor });
  
  if (!isAdmin && !isAuthor) {
    return createErrorResponse('Forbidden: You do not have permission to delete this article', 403)
  }

  // Delete the article
  const result = await deleteArticleByIdOrSlug(articleIdOrSlug)
  if (result.success) {
    return createSuccessResponse({ message: result.message })
  } else {
    return createErrorResponse(result.message || 'Failed to delete article', 500)
  }
}
