import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { NotificationProvider } from '@/components/NotificationProvider'
import { ResponsiveLayout } from '@/components/ResponsiveLayout'
import { ClientNotificationsPage } from './client-page'

export default async function NotificationsPage() {
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // Redirect if not logged in
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">Please Log In</h1>
          <p className="mb-8">You need to be logged in to view notifications.</p>
          <Link
            href="/login"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition"
          >
            Log In
          </Link>
        </div>
      </div>
    )
  }

  // We'll use the client component to fetch and display notifications

  return (
    <ResponsiveLayout>
      {/* Notifications Header */}
      <div className="bg-blue-700 text-white py-12 px-4">
        <div className="container mx-auto">
          <h1 className="text-4xl font-bold mb-4">Your Notifications</h1>
          <p className="text-xl">Stay updated with important events and activities</p>
        </div>
      </div>

      {/* Notifications List */}
      <NotificationProvider>
        <ClientNotificationsPage />
      </NotificationProvider>
    </ResponsiveLayout>
  )
}
