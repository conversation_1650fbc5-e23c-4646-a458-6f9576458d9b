import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

// GET news for teachers
export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      // Verify user is a teacher
      const role = typeof user.role === 'object' ? user.role?.slug : user.role

      if (role !== 'teacher') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const limit = parseInt(url.searchParams.get('limit') || '10')
      const page = parseInt(url.searchParams.get('page') || '1')

      // Get news items
      const newsItems = await payload.find({
        collection: 'news',
        where: {
          status: { equals: 'published' },
        },
        sort: '-publishedDate',
        limit,
        page,
        depth: 2, // Populate relationships
      })

      return NextResponse.json(newsItems)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching news for teacher:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
