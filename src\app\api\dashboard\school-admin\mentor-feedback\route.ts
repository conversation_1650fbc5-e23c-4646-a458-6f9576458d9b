import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the token and get the user ID
    const { userId } = await verifyJWT(token)

    if (!userId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get URL parameters
    const url = new URL(req.url)
    const querySchoolId = url.searchParams.get('schoolId')
    const limit = parseInt(url.searchParams.get('limit') || '10')

    // Initialize Payload
    const payload = await getPayload({ config })

    // Get the current user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })

    // Verify user is a school admin
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'school-admin' && role !== 'super-admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get school ID
    const schoolId =
      querySchoolId || (typeof user.school === 'object' ? user.school?.id : user.school)

    if (!schoolId) {
      return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
    }

    // Get real mentor feedback from mentorReviews collection
    console.log('Fetching mentor feedback for school:', schoolId)

    try {
      const db = payload.db.connection.db
      if (db) {
        const mentorReviewsCollection = db.collection('mentorReviews')

        // Get mentor reviews for this school
        const mentorReviews = await mentorReviewsCollection
          .find({
            school: new ObjectId(schoolId),
          })
          .sort({ createdAt: -1 })
          .limit(limit)
          .toArray()

        console.log(`Found ${mentorReviews.length} mentor reviews for school ${schoolId}`)

        // Get user and article data for each review
        const mentorFeedback = []

        for (const review of mentorReviews) {
          try {
            // Get mentor info
            const mentor = await payload.findByID({
              collection: 'users',
              id: review.mentor.toString(),
            })

            // Get teacher info
            const teacher = await payload.findByID({
              collection: 'users',
              id: review.teacher.toString(),
            })

            // Get article info
            const article = await payload.findByID({
              collection: 'articles',
              id: review.article.toString(),
            })

            // Determine sentiment based on evaluation result
            let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral'
            if (review.evaluationResult === 'approve') {
              sentiment = 'positive'
            } else if (review.evaluationResult === 'reject') {
              sentiment = 'negative'
            }

            mentorFeedback.push({
              id: review._id.toString(),
              mentorId: review.mentor.toString(),
              mentorName:
                `${mentor.firstName || ''} ${mentor.lastName || ''}`.trim() || 'موجه غير معروف',
              teacherId: review.teacher.toString(),
              teacherName:
                `${teacher.firstName || ''} ${teacher.lastName || ''}`.trim() || 'معلم غير معروف',
              articleId: review.article.toString(),
              articleTitle: article.title || 'مقال غير معروف',
              sentiment,
              summary: review.comment || 'لا يوجد تعليق',
              createdAt: review.createdAt || review.timestamp,
            })
          } catch (error) {
            console.error('Error processing mentor review:', error)
            // Continue with next review if one fails
          }
        }

        console.log(`Processed ${mentorFeedback.length} mentor feedback items`)
        return NextResponse.json({ mentorFeedback })
      }
    } catch (mongoError) {
      console.error('Error fetching mentor reviews from MongoDB:', mongoError)
    }

    // Fallback: return empty array if no data found
    return NextResponse.json({ mentorFeedback: [] })
  } catch (error) {
    console.error('Error fetching mentor feedback:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
