{"name": "young-reporter", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rmdir /s /q .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "seed": "cross-env NODE_OPTIONS=--no-deprecation node src/scripts/seed.mjs", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "test:security": "vitest run src/lib/security/__tests__/basic.test.ts", "test:security:all": "vitest run src/lib/security/__tests__", "security:setup": "node src/scripts/create-security-collections.js", "validate:dashboard": "cross-env NODE_OPTIONS=--no-deprecation ts-node src/scripts/validate-dashboard.ts", "test:mentor-dashboard": "cross-env NODE_OPTIONS=--no-deprecation node src/scripts/test-mentor-dashboard.js", "validate:super-admin": "cross-env NODE_OPTIONS=--no-deprecation node src/scripts/validate-dashboard.js --role super-admin", "generate-points": "ts-node src/scripts/generate-initial-points.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.797.0", "@formatjs/intl-localematcher": "^0.6.1", "@hookform/resolvers": "^5.0.1", "@lexical/code": "^0.28.0", "@lexical/link": "^0.28.0", "@lexical/react": "^0.28.0", "@lexical/rich-text": "^0.28.0", "@noble/ed25519": "^2.2.3", "@payloadcms/db-mongodb": "3.39.1", "@payloadcms/next": "3.39.1", "@payloadcms/payload-cloud": "3.39.1", "@payloadcms/richtext-lexical": "3.39.1", "@payloadcms/storage-s3": "3.39.1", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.4", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.8", "aws4": "^1.13.2", "axios": "^1.9.0", "bson": "^6.7.0", "chalk": "^4.1.2", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "commander": "^11.1.0", "cross-env": "^7.0.3", "csv-stringify": "^6.5.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "driver.js": "^1.3.6", "framer-motion": "^12.15.0", "graphql": "^16.11.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.503.0", "mongodb": "^6.16.0", "negotiator": "^1.0.0", "next": "15.3.0", "next-i18next": "^15.4.2", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "ora": "^8.2.0", "payload": "3.39.1", "pdfkit": "^0.17.1", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.2", "react-quill": "^2.0.0", "recharts": "^2.15.3", "shadcn-ui": "^0.9.5", "sharp": "0.32.6", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "yargs": "^17.7.2", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@faker-js/faker": "^9.7.0", "@tailwindcss/line-clamp": "^0.4.4", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/negotiator": "^0.6.4", "@types/node": "^22.5.4", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.13.9", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "@types/yargs": "^17.0.33", "autoprefixer": "^10.4.14", "bcryptjs": "^3.0.2", "copy-webpack-plugin": "^13.0.0", "eslint": "^9.16.0", "eslint-config-next": "15.3.0", "postcss": "^8.4.24", "prettier": "^3.4.2", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "5.7.3", "vitest": "^1.6.1"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}