'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { formatDate } from '@/lib/date-utils'
import {
  LayoutDashboard,
  Users,
  FileText,
  Newspaper,
  School,
  Award,
  Settings,
  MessageSquare,
  BarChart,
  Download,
  Flag,
} from 'lucide-react'

import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { CrossSchoolStatistics } from '@/app/dashboard/super-admin/components/cross-school-statistics'
import { GlobalActivityHeatmap } from '@/app/dashboard/super-admin/components/global-activity-heatmap'
import { ContentManager } from '@/app/dashboard/super-admin/components/content-manager'
import { SchoolsManager } from '@/app/dashboard/super-admin/components/schools-manager'
import { FeedbackManager } from '@/app/dashboard/super-admin/components/feedback-manager'
import { ReportsExporter } from '@/app/dashboard/super-admin/components/reports-exporter'
import TeacherReviewsOverview from '@/components/dashboard/TeacherReviewsOverview'
import { RoleDistributionChart } from '@/app/dashboard/super-admin/components/role-distribution-chart'
import { RawAuditLogs } from '@/app/dashboard/super-admin/components/raw-audit-logs'
import { ViewStatsCard } from '@/components/dashboard/ViewStatsCard'
import ReportedIssuesTable from '@/components/dashboard/ReportedIssuesTable'

interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  role?:
    | {
        id: string
        name: string
        slug: string
      }
    | string
  school?:
    | {
        id: string
        name: string
      }
    | string
}

interface Stats {
  users: number
  articles: number
  news: number
  schools: number
  activities: number
}

interface RecentActivity {
  id: string
  userId: string
  userName: string
  userRole: string
  schoolId?: string
  schoolName?: string
  activityType: string
  description: string
  date: string
  targetId?: string
  targetType?: string
}

interface School {
  id: string
  name: string
  address: string
  phone: string
  website: string
  createdAt: string
}

interface AdminDashboardProps {
  user: User | null
}

export default function AdminDashboard({ user }: AdminDashboardProps) {
  const router = useRouter()
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)
  const [stats, setStats] = useState<Stats>({
    users: 0,
    articles: 0,
    news: 0,
    schools: 0,
    activities: 0,
  })
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [recentSchools, setRecentSchools] = useState<School[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('overview')
  const [usersData, setUsersData] = useState<any[]>([])
  const [articlesData, setArticlesData] = useState<any[]>([])
  const [activitiesData, setActivitiesData] = useState<any[]>([])

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true)
        // Fetch stats
        const usersResponse = await fetch('/api/users?limit=1') // Just get counts, not full data
        const articlesResponse = await fetch('/api/articles?limit=1')
        const newsResponse = await fetch('/api/news?limit=1')
        const schoolsResponse = await fetch('/api/schools?limit=1')
        const activitiesResponse = await fetch('/api/activities?limit=1')

        // Get the data from the initial responses
        const usersStatsData = usersResponse.ok
          ? await usersResponse.json()
          : { totalDocs: 0, docs: [] }
        const articlesStatsData = articlesResponse.ok
          ? await articlesResponse.json()
          : { totalDocs: 0, docs: [] }
        const newsStatsData = newsResponse.ok ? await newsResponse.json() : { totalDocs: 0 }
        const schoolsStatsData = schoolsResponse.ok
          ? await schoolsResponse.json()
          : { totalDocs: 0, docs: [] }
        const activitiesStatsData = activitiesResponse.ok
          ? await activitiesResponse.json()
          : { totalDocs: 0, docs: [] }

        // Also fetch specific data for the tabs
        const [usersDataResponse, articlesDataResponse, activitiesDataResponse] = await Promise.all(
          [
            fetch('/api/users?limit=10'), // Get a few users for the Users tab
            fetch('/api/articles?limit=10'), // Get a few articles for the Articles tab
            fetch('/api/activities?limit=20'), // Get more activities for the Activities tab
          ],
        )

        // Parse tab data responses
        const usersData = usersDataResponse.ok ? await usersDataResponse.json() : { docs: [] }
        const articlesData = articlesDataResponse.ok
          ? await articlesDataResponse.json()
          : { docs: [] }
        const activitiesData = activitiesDataResponse.ok
          ? await activitiesDataResponse.json()
          : { docs: [] }

        // Store the data for use in tabs
        setUsersData(usersData.docs || [])
        setArticlesData(articlesData.docs || [])
        setActivitiesData(activitiesData.docs || [])

        setStats({
          users: usersStatsData.totalDocs || 0,
          articles: articlesStatsData.totalDocs || 0,
          news: newsStatsData.totalDocs || 0,
          schools: schoolsStatsData.totalDocs || 0,
          activities: activitiesStatsData.totalDocs || 0,
        })

        // Set recent activities
        setRecentActivities(activitiesData.docs?.slice(0, 5) || [])

        // Set recent schools
        setRecentSchools(schoolsStatsData.docs || [])

        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError('فشل تحميل بيانات لوحة التحكم. يرجى المحاولة مرة أخرى.')
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const statCards = [
    {
      title: 'المستخدمون',
      value: stats.users,
      icon: <Users className="w-6 h-6" />,
      color: 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300',
      link: '/dashboard/users',
    },
    {
      title: 'المقالات',
      value: stats.articles,
      icon: <FileText className="w-6 h-6" />,
      color: 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300',
      link: '/dashboard/articles',
    },
    {
      title: 'الأخبار',
      value: stats.news,
      icon: <Newspaper className="w-6 h-6" />,
      color: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-300',
      link: '/dashboard/news',
    },
    {
      title: 'المدارس',
      value: stats.schools,
      icon: <School className="w-6 h-6" />,
      color: 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300',
      link: '/dashboard/schools',
    },
    {
      title: 'الأنشطة',
      value: stats.activities,
      icon: <MessageSquare className="w-6 h-6" />,
      color: 'bg-cyan-100 dark:bg-cyan-900 text-cyan-600 dark:text-cyan-300',
      link: '/dashboard/activities',
    },
  ]

  const generateReport = async () => {
    try {
      setIsGeneratingReport(true)

      // Simulate API call to generate report
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Create a sample report with current date
      const now = new Date()
      const reportDate = now.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })

      // Create a CSV-like string with some sample data
      const reportData =
        `منصة الصحفي الشاب - تقرير النشاط - ${reportDate}\n\n` +
        `إجمالي المستخدمين,${stats.users}\n` +
        `إجمالي المقالات,${stats.articles}\n` +
        `إجمالي الأخبار,${stats.news}\n` +
        `إجمالي المدارس,${stats.schools}\n` +
        `إجمالي الأنشطة,${stats.activities}\n\n` +
        `الأنشطة الأخيرة:\n` +
        `التاريخ,المستخدم,الدور,المدرسة,نوع النشاط,الوصف\n` +
        `${now.toISOString().split('T')[0]},جون سميث,مدرس,مدرسة لينكولن الثانوية,مراجعة المقالات,راجع مقال "تأثيرات التغير المناخي"\n` +
        `${now.toISOString().split('T')[0]},سارة جونسون,موجه,مدرسة لينكولن الثانوية,مراجعة المدرسين,راجع نشاط المعلم\n` +
        `${now.toISOString().split('T')[0]},مايكل براون,طالب,مدرسة واشنطن الإعدادية,تقديم مقال,قدم مقالاً جديداً "الرياضات المحلية"\n`

      // Create a blob and download link
      const blob = new Blob([reportData], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.setAttribute('hidden', '')
      a.setAttribute('href', url)
      a.setAttribute('download', `young-reporter-report-${now.toISOString().split('T')[0]}.csv`)
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)

      setIsGeneratingReport(false)
    } catch (err) {
      console.error('Error generating report:', err)
      setIsGeneratingReport(false)
      alert('فشل إنشاء التقرير. يرجى المحاولة مرة أخرى.')
    }
  }

  const customizeFeedbackManager = () => {
    // Extract user role
    const role = typeof user?.role === 'object' ? user?.role?.slug : user?.role

    // Extract user's school if applicable
    let schoolId = ''
    if (role === 'school-admin' || role === 'teacher') {
      if (typeof user?.school === 'object') {
        schoolId = user?.school?.id || ''
      } else {
        schoolId = user?.school || ''
      }
    }

    return <FeedbackManager apiEndpoint="/api/dashboard/feedback" schoolId={schoolId} />
  }

  if (isLoading) {
    return (
      <div dir="rtl" className="animate-pulse space-y-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-80 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-80 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div
        dir="rtl"
        className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4"
      >
        {error}
      </div>
    )
  }

  return (
    <div dir="rtl" className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">لوحة تحكم المدير الأعلى</h2>
        <div className="flex items-center space-x-2">
          <Button
            onClick={() => setActiveTab('reports')}
            variant="outline"
            className="hidden md:flex"
          >
            <Flag className="ml-2 h-4 w-4" />
            عرض التقارير
          </Button>
          <Button onClick={generateReport} disabled={isGeneratingReport}>
            {isGeneratingReport ? (
              <>جارٍ الإنشاء...</>
            ) : (
              <>
                <Download className="ml-2 h-4 w-4" />
                إنشاء تقرير
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} dir="rtl">
        <TabsList className="flex flex-wrap gap-2 mb-8 border-b pb-3 pt-1 px-1">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="statistics">إحصائيات المدارس</TabsTrigger>
          <TabsTrigger value="activity-heatmap">خريطة الأنشطة</TabsTrigger>
          <TabsTrigger value="content">المحتوى</TabsTrigger>
          <TabsTrigger value="schools">المدارس</TabsTrigger>
          <TabsTrigger value="users">المستخدمون</TabsTrigger>
          <TabsTrigger value="articles">المقالات</TabsTrigger>
          <TabsTrigger value="activities">الأنشطة</TabsTrigger>
          <TabsTrigger value="feedback">التعليقات</TabsTrigger>
          <TabsTrigger value="reviews">مراجعات المعلمين والموجهين</TabsTrigger>
          <TabsTrigger value="reports">التقارير</TabsTrigger>
          <TabsTrigger value="reports-exporter">مصدر التقارير</TabsTrigger>
          <TabsTrigger value="roles">توزيع الأدوار</TabsTrigger>
          <TabsTrigger value="audit-logs">سجلات التدقيق</TabsTrigger>
        </TabsList>

        <div className="pt-4">
          <TabsContent value="overview" className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {statCards.map((stat, index) => (
                <Card key={index} className="p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className={`p-3 rounded-lg ${stat.color}`}>{stat.icon}</div>
                    <div className="text-right">
                      <h3 className="text-2xl font-bold dark:text-white">{stat.value}</h3>
                      <p className="text-gray-500 dark:text-gray-400">{stat.title}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-primary hover:text-primary/80"
                      onClick={() => router.push(stat.link)}
                    >
                      عرض التفاصيل
                    </Button>
                  </div>
                </Card>
              ))}
            </div>

            {/* View Statistics Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <ViewStatsCard type="news" title="الأخبار الأكثر مشاهدة" limit={5} />
              <ViewStatsCard type="articles" title="المقالات الأكثر مشاهدة" limit={5} />
            </div>

            {/* Table and Tasks Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold dark:text-white">الأنشطة الأخيرة</h2>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => router.push('/dashboard/activities')}
                    >
                      <MessageSquare className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>المستخدم</TableHead>
                      <TableHead>الدور</TableHead>
                      <TableHead>النشاط</TableHead>
                      <TableHead>التاريخ</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentActivities.length > 0 ? (
                      recentActivities.map((activity) => (
                        <TableRow key={activity.id}>
                          <TableCell className="font-medium">
                            {activity.userName || 'غير معروف'}
                          </TableCell>
                          <TableCell>
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                activity.userRole === 'teacher'
                                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                                  : activity.userRole === 'student'
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                    : activity.userRole === 'mentor'
                                      ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
                                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
                              }`}
                            >
                              {activity.userRole === 'teacher'
                                ? 'مدرس'
                                : activity.userRole === 'student'
                                  ? 'طالب'
                                  : activity.userRole === 'mentor'
                                    ? 'موجه'
                                    : activity.userRole === 'super-admin'
                                      ? 'مدير أعلى'
                                      : activity.userRole === 'school-admin'
                                        ? 'مدير مدرسة'
                                        : activity.userRole || 'غير معروف'}
                            </span>
                          </TableCell>
                          <TableCell>{activity.description || 'لا يوجد وصف'}</TableCell>
                          <TableCell>{formatDate(activity.date, undefined, 'غير معروف')}</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                          لم يتم العثور على أنشطة
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </Card>

              {/* Schools */}
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold dark:text-white">المدارس الأخيرة</h2>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => router.push('/dashboard/schools')}
                    >
                      <School className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الاسم</TableHead>
                      <TableHead>العنوان</TableHead>
                      <TableHead>جهة الاتصال</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentSchools.length > 0 ? (
                      recentSchools.map((school) => (
                        <TableRow key={school.id}>
                          <TableCell className="font-medium">{school.name}</TableCell>
                          <TableCell>{school.address}</TableCell>
                          <TableCell>
                            <div>{school.phone}</div>
                            <div className="text-xs text-blue-600 dark:text-blue-400">
                              <a href={school.website} target="_blank" rel="noopener noreferrer">
                                {school.website}
                              </a>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={3} className="text-center py-4 text-gray-500">
                          لم يتم العثور على مدارس
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="statistics">
            <CrossSchoolStatistics />
          </TabsContent>
          <TabsContent value="activity-heatmap">
            <GlobalActivityHeatmap />
          </TabsContent>
          <TabsContent value="content">
            <ContentManager />
          </TabsContent>
          <TabsContent value="schools">
            <SchoolsManager />
          </TabsContent>
          <TabsContent value="users">
            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>إدارة المستخدمين</CardTitle>
                </CardHeader>
                <CardContent>
                  {usersData.length > 0 ? (
                    <>
                      <div className="mb-4">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>الاسم</TableHead>
                              <TableHead>البريد الإلكتروني</TableHead>
                              <TableHead>الدور</TableHead>
                              <TableHead>المدرسة</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {usersData.map((user) => (
                              <TableRow key={user.id}>
                                <TableCell>
                                  {user.firstName} {user.lastName}
                                </TableCell>
                                <TableCell>{user.email}</TableCell>
                                <TableCell>
                                  {typeof user.role === 'object'
                                    ? user.role.slug === 'teacher'
                                      ? 'مدرس'
                                      : user.role.slug === 'student'
                                        ? 'طالب'
                                        : user.role.slug === 'mentor'
                                          ? 'موجه'
                                          : user.role.slug === 'super-admin'
                                            ? 'مدير أعلى'
                                            : user.role.slug === 'school-admin'
                                              ? 'مدير مدرسة'
                                              : user.role.name
                                    : user.role === 'teacher'
                                      ? 'مدرس'
                                      : user.role === 'student'
                                        ? 'طالب'
                                        : user.role === 'mentor'
                                          ? 'موجه'
                                          : user.role === 'super-admin'
                                            ? 'مدير أعلى'
                                            : user.role === 'school-admin'
                                              ? 'مدير مدرسة'
                                              : user.role}
                                </TableCell>
                                <TableCell>
                                  {typeof user.school === 'object' ? user.school.name : ''}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                      <Button onClick={() => router.push('/dashboard/users')}>
                        عرض جميع المستخدمين
                      </Button>
                    </>
                  ) : (
                    <>
                      <p>
                        سيتم عرض مكون إدارة المستخدمين هنا. يمكنك إدارة جميع المستخدمين عبر المنصة.
                      </p>
                      <Button className="mt-4" onClick={() => router.push('/dashboard/users')}>
                        فتح مدير المستخدمين الكامل
                      </Button>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="articles">
            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>إدارة المقالات</CardTitle>
                </CardHeader>
                <CardContent>
                  {articlesData.length > 0 ? (
                    <>
                      <div className="mb-4">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>العنوان</TableHead>
                              <TableHead>الكاتب</TableHead>
                              <TableHead>الحالة</TableHead>
                              <TableHead>التاريخ</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {articlesData.map((article) => (
                              <TableRow key={article.id}>
                                <TableCell>{article.title}</TableCell>
                                <TableCell>
                                  {typeof article.author === 'object'
                                    ? `${article.author.firstName} ${article.author.lastName}`
                                    : article.author}
                                </TableCell>
                                <TableCell>
                                  {article.status === 'published'
                                    ? 'منشور'
                                    : article.status === 'draft'
                                      ? 'مسودة'
                                      : article.status === 'pending-review'
                                        ? 'قيد المراجعة'
                                        : article.status}
                                </TableCell>
                                <TableCell>
                                  {new Date(article.createdAt).toLocaleDateString('ar-EG')}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                      <Button onClick={() => router.push('/dashboard/articles')}>
                        عرض جميع المقالات
                      </Button>
                    </>
                  ) : (
                    <>
                      <p>سيتم عرض مكون إدارة المقالات هنا. يمكنك إدارة جميع المقالات عبر المنصة.</p>
                      <Button className="mt-4" onClick={() => router.push('/dashboard/articles')}>
                        فتح مدير المقالات الكامل
                      </Button>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="activities">
            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>نظرة عامة على الأنشطة</CardTitle>
                </CardHeader>
                <CardContent>
                  {activitiesData.length > 0 ? (
                    <>
                      <div className="mb-4">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>المستخدم</TableHead>
                              <TableHead>النشاط</TableHead>
                              <TableHead>التاريخ</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {activitiesData.map((activity) => (
                              <TableRow key={activity.id}>
                                <TableCell>{activity.userName || 'غير معروف'}</TableCell>
                                <TableCell>{activity.description || 'لا يوجد وصف'}</TableCell>
                                <TableCell>
                                  {new Date(activity.date || activity.createdAt).toLocaleDateString(
                                    'ar-EG',
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                      <Button onClick={() => router.push('/dashboard/activities')}>
                        عرض جميع الأنشطة
                      </Button>
                    </>
                  ) : (
                    <>
                      <p>
                        سيتم عرض مكون نظرة عامة على الأنشطة هنا. يمكنك عرض جميع الأنشطة عبر المنصة.
                      </p>
                      <Button className="mt-4" onClick={() => router.push('/dashboard/activities')}>
                        فتح عرض الأنشطة الكامل
                      </Button>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="feedback">
            <FeedbackManager />
          </TabsContent>
          <TabsContent value="reviews" className="space-y-6">
            <TeacherReviewsOverview />
          </TabsContent>
          <TabsContent value="reports" className="space-y-4">
            <div className="grid gap-4">
              <Card className="col-span-4">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Flag className="ml-2 h-5 w-5" />
                    المشكلات المبلغ عنها
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ReportedIssuesTable schoolId="" userRole="super-admin" />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="reports-exporter">
            <ReportsExporter />
          </TabsContent>
          <TabsContent value="roles">
            <RoleDistributionChart />
          </TabsContent>
          <TabsContent value="audit-logs">
            <RawAuditLogs />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  )
}
