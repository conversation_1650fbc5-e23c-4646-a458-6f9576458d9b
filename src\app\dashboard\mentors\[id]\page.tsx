"use client"

import { useEffect, useState } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { formatDistanceToNow } from 'date-fns'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function MentorDetailsPage() {
  const { id } = useParams()
  const router = useRouter()
  const [mentor, setMentor] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchMentor() {
      setLoading(true)
      const res = await fetch(`/api/dashboard/mentors/${id}`)
      if (res.ok) {
        const data = await res.json()
        setMentor(data)
      } else {
        setMentor(null)
      }
      setLoading(false)
    }
    if (id) fetchMentor()
  }, [id])

  if (loading) return <div>Loading...</div>
  if (!mentor) return <div><PERSON>tor not found or you do not have access.</div>

  return (
    <DashboardLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Mentor Details</h1>
        <Button asChild variant="outline">
          <a href="/dashboard/mentors">Back to Mentor Management</a>
        </Button>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>{mentor.name || 'Unknown'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-2"><b>Email:</b> {mentor.email || 'Unknown'}</div>
          <div className="mb-2"><b>School:</b> {mentor.school || 'Unknown School'}</div>
          <div className="mb-2"><b>Reviews:</b> {mentor.reviewCount ?? '0'}</div>
          <div className="mb-2"><b>Average Rating:</b> {mentor.averageRating?.toFixed(2) ?? '0.00'}</div>
          <div className="mb-2"><b>Last Activity:</b> {mentor.lastActivity ? formatDistanceToNow(new Date(mentor.lastActivity), { addSuffix: true }) : 'Unknown'}</div>
        </CardContent>
      </Card>
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
        </CardHeader>
        <CardContent>
          {mentor.activities && mentor.activities.length > 0 ? (
            <ul className="space-y-2">
              {mentor.activities.map((activity: any) => (
                <li key={activity._id} className="border-b pb-2">
                  <div><b>Type:</b> {activity.activityType || 'Unknown'}</div>
                  <div><b>Date:</b> {activity.date ? formatDistanceToNow(new Date(activity.date), { addSuffix: true }) : 'Unknown'}</div>
                  {activity.details && (
                    <div><b>Details:</b> {typeof activity.details === 'object' ? JSON.stringify(activity.details) : String(activity.details)}</div>
                  )}
                </li>
              ))}
            </ul>
          ) : (
            <div>No recent activities found.</div>
          )}
        </CardContent>
      </Card>
    </DashboardLayout>
  )
} 