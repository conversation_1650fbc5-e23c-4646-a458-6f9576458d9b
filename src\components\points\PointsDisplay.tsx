'use client'

import { usePoints } from '@/contexts/PointsContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Award, Calendar, CheckCircle, XCircle } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

export function PointsDisplay() {
  const { 
    points, 
    getRank, 
    getNextRankPoints,
    getTasksCompleted
  } = usePoints()
  
  const rank = getRank()
  const nextRankPoints = getNextRankPoints()
  const { daily, weekly } = getTasksCompleted()
  
  // Calculate progress to next rank
  const progress = Math.min(Math.round((points / nextRankPoints) * 100), 100)
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Your Points</CardTitle>
            <CardDescription>Track your progress and achievements</CardDescription>
          </div>
          <Badge className="bg-purple-100 text-purple-800 border-purple-300 flex items-center gap-1">
            <Award className="h-3.5 w-3.5" />
            {rank}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">{points} points</span>
              <span className="text-sm text-muted-foreground">Next rank: {nextRankPoints} points</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center gap-2 p-2 border rounded-md">
              <div className={`p-1 rounded-full ${daily ? 'bg-green-100' : 'bg-red-100'}`}>
                {daily ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
              </div>
              <div>
                <div className="text-sm font-medium">Daily Tasks</div>
                <div className="text-xs text-muted-foreground">
                  {daily ? 'Completed' : 'Incomplete'}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2 p-2 border rounded-md">
              <div className={`p-1 rounded-full ${weekly ? 'bg-green-100' : 'bg-red-100'}`}>
                {weekly ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
              </div>
              <div>
                <div className="text-sm font-medium">Weekly Tasks</div>
                <div className="text-xs text-muted-foreground">
                  {weekly ? 'Completed' : 'Incomplete'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
