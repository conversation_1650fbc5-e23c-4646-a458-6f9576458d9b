import type { CollectionConfig } from 'payload'
import { isSuperAdmin, isSchoolAdmin, canManageSchoolUsers } from '../access'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
  },
  access: {
    // Read access: users can read their own profile, super admins can read all, school admins can read users from their school
    read: ({ req, id }) => {
      // Not logged in
      if (!req.user) return false

      // Super admins can read all users
      if (
        req.user.role === 'super-admin' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'super-admin')
      ) {
        return true
      }

      // School admins can read users from their school
      if (
        req.user.role === 'school-admin' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'school-admin')
      ) {
        // If no ID, this is a list operation - only show users from their school
        if (!id) {
          return {
            school: {
              equals: req.user.school,
            },
          }
        }
      }

      // Users can read their own profile
      if (id === req.user.id) return true

      // Default deny
      return false
    },
    // Create access: only super admins and school admins (for their school)
    create: canManageSchoolUsers,
    // Update access: users can update their own profile, super admins can update all, school admins can update users from their school
    update: async ({ req, id }) => {
      // Not logged in
      if (!req.user) return false

      // Super admins can update all users
      const isSuperAdminUser = await isSuperAdmin({ req })
      if (isSuperAdminUser) return true

      // School admins can update users from their school
      const isSchoolAdminUser = await isSchoolAdmin({ req })
      if (isSchoolAdminUser && id) {
        try {
          const user = await req.payload.findByID({
            collection: 'users',
            id,
          })

          // Check if user belongs to the admin's school
          if (
            user.school === req.user.school ||
            (typeof user.school === 'object' &&
              typeof req.user.school === 'object' &&
              user.school!.id === req.user.school!.id)
          ) {
            return true
          }
        } catch (error) {
          console.error('Error checking user school:', error)
          return false
        }
      }

      // Users can update their own profile
      if (id === req.user.id) return true

      // Default deny
      return false
    },
    // Delete access: only super admins and school admins (for their school)
    delete: canManageSchoolUsers,
  },
  auth: true,
  fields: [
    // Email added by default
    {
      name: 'firstName',
      type: 'text',
      required: true,
      label: 'First Name',
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
      label: 'Last Name',
    },
    {
      name: 'pendingFirstName',
      type: 'text',
      required: false,
      label: 'Pending First Name',
      admin: {
        description: 'First name waiting for approval',
      },
    },
    {
      name: 'pendingLastName',
      type: 'text',
      required: false,
      label: 'Pending Last Name',
      admin: {
        description: 'Last name waiting for approval',
      },
    },
    {
      name: 'nameChangeStatus',
      type: 'select',
      options: [
        { label: 'None', value: 'none' },
        { label: 'Pending', value: 'pending' },
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' },
      ],
      defaultValue: 'none',
      admin: {
        description: 'Status of name change request',
      },
    },
    {
      name: 'profileImage',
      type: 'relationship',
      relationTo: 'media',
      required: false,
      label: 'Profile Image',
    },
    {
      name: 'pendingProfileImage',
      type: 'relationship',
      relationTo: 'media',
      required: false,
      label: 'Pending Profile Image',
      admin: {
        description: 'Profile image waiting for approval',
      },
    },
    {
      name: 'profileImageStatus',
      type: 'select',
      options: [
        { label: 'None', value: 'none' },
        { label: 'Approved', value: 'approved' },
        { label: 'Pending', value: 'pending' },
        { label: 'Rejected', value: 'rejected' },
      ],
      defaultValue: 'none',
      admin: {
        description: 'Status of the profile image',
      },
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Active', value: 'active' },
        { label: 'Suspended', value: 'suspended' },
      ],
      defaultValue: 'pending',
      admin: {
        description: 'Account status',
      },
    },
    {
      name: 'role',
      type: 'relationship',
      relationTo: 'roles',
      required: true,
    },
    {
      name: 'school',
      type: 'relationship',
      relationTo: 'schools',
      required: false, // Changed from true to false to make it not required
      admin: {
        condition: (data) => {
          // School is not shown for super-admin
          if (data?.role) {
            // If role is an object with a slug property
            if (typeof data.role === 'object' && data.role !== null && 'slug' in data.role) {
              return data.role.slug !== 'super-admin'
            }

            // For now, check if the role ID doesn't contain 'super-admin'
            return !String(data.role).includes('super-admin')
          }
          return true
        },
      },
    },
    {
      name: 'points',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Total points earned by the user',
        position: 'sidebar',
      },
    },
    {
      name: 'rank',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: "User's current rank among peers with the same role",
        position: 'sidebar',
      },
    },
    {
      name: 'pointsActivities',
      type: 'array',
      admin: {
        description: 'History of point-earning activities',
      },
      fields: [
        {
          name: 'type',
          type: 'select',
          required: true,
          options: [
            { label: 'Article Created', value: 'article_created' },
            { label: 'Article Published', value: 'article_published' },
            { label: 'Review Submitted', value: 'review_submitted' },
            { label: 'Achievement Earned', value: 'achievement_earned' },
            { label: 'Rating Received', value: 'rating_received' },
            { label: 'Other', value: 'other' },
          ],
        },
        {
          name: 'points',
          type: 'number',
          required: true,
          min: 0,
        },
        {
          name: 'description',
          type: 'text',
          required: true,
        },
        {
          name: 'reference',
          type: 'json',
          admin: {
            description: 'Reference to related entity (article, review, etc.)',
          },
        },
        {
          name: 'timestamp',
          type: 'date',
          required: true,
          defaultValue: () => new Date(),
        },
      ],
    },
    {
      name: 'approvedBy',
      type: 'relationship',
      relationTo: 'users',
      required: false,
      admin: {
        description: 'The teacher or admin who approved this user',
        condition: (data) => {
          // Only show for students
          if (data?.role) {
            // If role is an object with a slug property
            if (typeof data.role === 'object' && data.role !== null && 'slug' in data.role) {
              return data.role.slug === 'student'
            }

            // For now, just check if the role ID contains 'student'
            return String(data.role).includes('student')
          }
          return false
        },
      },
    },
    {
      name: 'grade',
      type: 'select',
      options: [
        { label: '1', value: '1' },
        { label: '2', value: '2' },
        { label: '3', value: '3' },
        { label: '4', value: '4' },
        { label: '5', value: '5' },
        { label: '6', value: '6' },
        { label: '7', value: '7' },
        { label: '8', value: '8' },
        { label: '9', value: '9' },
        { label: '10', value: '10' },
        { label: '11', value: '11' },
        { label: '12', value: '12' },
      ],
      admin: {
        condition: (data) => {
          // Only show grade field for students
          if (data?.role) {
            // If role is an object with a slug property
            if (typeof data.role === 'object' && data.role !== null && 'slug' in data.role) {
              return data.role.slug === 'student'
            }

            // For now, just check if the role ID contains 'student'
            // This is a fallback and not ideal
            return String(data.role).includes('student')
          }
          return false
        },
      },
      // We'll handle validation in the hooks instead
      required: false,
    },
    {
      name: 'notificationPreferences',
      type: 'group',
      label: 'Notification Preferences',
      fields: [
        {
          name: 'emailNotifications',
          type: 'checkbox',
          label: 'Receive Email Notifications',
          defaultValue: true,
        },
        {
          name: 'emailTypes',
          type: 'select',
          label: 'Email Notification Types',
          hasMany: true,
          options: [
            { label: 'Article Submissions', value: 'articleSubmissions' },
            { label: 'Article Reviews', value: 'articleReviews' },
            { label: 'Review Evaluations', value: 'reviewEvaluations' },
            { label: 'System Announcements', value: 'systemAnnouncements' },
          ],
          defaultValue: ['articleSubmissions', 'articleReviews', 'reviewEvaluations'],
          admin: {
            condition: (_data, siblingData) => siblingData.emailNotifications,
          },
        },
      ],
    },
    {
      name: 'theme',
      type: 'group',
      label: 'Theme Preferences',
      fields: [
        {
          name: 'darkMode',
          type: 'checkbox',
          label: 'Dark Mode',
          defaultValue: false,
        },
        {
          name: 'themeColor',
          type: 'select',
          label: 'Theme Color',
          options: [
            { label: 'Cyan', value: 'cyan' },
            { label: 'Blue', value: 'blue' },
            { label: 'Purple', value: 'purple' },
            { label: 'Green', value: 'green' },
            { label: 'Amber', value: 'amber' },
            { label: 'Pink', value: 'pink' },
          ],
          defaultValue: 'cyan',
        },
      ],
    },
    {
      name: 'preferences',
      type: 'group',
      label: 'User Preferences',
      admin: {
        condition: (data) => {
          // Only show for teachers, mentors, school admins, and super admins
          if (data?.role) {
            if (typeof data.role === 'object' && data.role !== null && 'slug' in data.role) {
              return ['teacher', 'mentor', 'school-admin', 'super-admin'].includes(data.role.slug)
            }
            return false
          }
          return false
        },
      },
      fields: [
        {
          name: 'showCompletedTasks',
          type: 'checkbox',
          label: 'Show Completed Tasks',
          defaultValue: true,
        },
        {
          name: 'autoApproveStudents',
          type: 'checkbox',
          label: 'Auto-Approve Students',
          defaultValue: false,
          admin: {
            description: 'Automatically approve new students from your school',
          },
        },
        {
          name: 'reviewNotificationThreshold',
          type: 'number',
          label: 'Review Notification Threshold',
          defaultValue: 5,
          min: 1,
          max: 20,
          admin: {
            description: 'Number of pending reviews before receiving a notification',
          },
        },
      ],
    },
    {
      name: 'stats',
      type: 'group',
      label: 'User Statistics',
      admin: {
        condition: (data) => {
          // Only show for teachers, mentors, school admins, and super admins
          if (data?.role) {
            if (typeof data.role === 'object' && data.role !== null && 'slug' in data.role) {
              return ['teacher', 'mentor', 'school-admin', 'super-admin'].includes(data.role.slug)
            }
            return false
          }
          return false
        },
      },
      fields: [
        {
          name: 'reviewsRated',
          type: 'number',
          label: 'Reviews Rated',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'totalMentorRating',
          type: 'number',
          label: 'Total Mentor Rating',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'averageMentorRating',
          type: 'number',
          label: 'Average Mentor Rating',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'positiveRatings',
          type: 'number',
          label: 'Positive Ratings',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'negativeRatings',
          type: 'number',
          label: 'Negative Ratings',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'totalRatings',
          type: 'number',
          label: 'Total Ratings',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'approvalRate',
          type: 'number',
          label: 'Approval Rate',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'positiveReviewCount',
          type: 'number',
          label: 'Positive Review Count',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'negativeReviewCount',
          type: 'number',
          label: 'Negative Review Count',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      async ({ data, req, operation }) => {
        if (operation === 'create' && !data.stats) {
          data.stats = {
            reviewsRated: 0,
            totalMentorRating: 0,
            averageMentorRating: 0,
            positiveRatings: 0,
            negativeRatings: 0,
            totalRatings: 0,
            approvalRate: 0,
            positiveReviewCount: 0,
            negativeReviewCount: 0,
          }
        }

        if (operation === 'create' && !data.pointsActivities) {
          data.pointsActivities = []
        }

        if (operation === 'create' && !data.points) {
          data.points = 0
        }

        if (operation === 'create' && !data.rank) {
          data.rank = 0
        }

        return data
      },
    ],
    beforeValidate: [
      // Set default role to student for new users and validate fields
      async ({ data, req, operation }) => {
        if (!data) return {}

        // Only run validations for create and update operations
        if (operation !== 'create' && operation !== 'update') {
          return data
        }

        // Set default role for new users
        if (operation === 'create' && !data.role) {
          const payload = req.payload
          const studentRole = await payload.find({
            collection: 'roles',
            where: {
              slug: { equals: 'student' },
            },
          })

          if (studentRole.docs && studentRole.docs.length > 0) {
            data.role = studentRole.docs[0].id
          }
        }

        // Validate grade field for students
        if (data.role) {
          const isStudent =
            (typeof data.role === 'object' &&
              data.role !== null &&
              'slug' in data.role &&
              data.role.slug === 'student') ||
            String(data.role).includes('student')

          if (isStudent && !data.grade) {
            throw new Error('Grade is required for students')
          }
        }

        // Validate school field for non-super-admins
        if (data.role) {
          // Check if the role is super-admin
          let isSuperAdmin = false

          // If role is an object with a slug property
          if (typeof data.role === 'object' && data.role !== null && 'slug' in data.role) {
            isSuperAdmin = data.role.slug === 'super-admin'
          } else {
            // If we have a string ID, we need to look up the role
            try {
              if (req.payload) {
                const roleDoc = await req.payload.findByID({
                  collection: 'roles',
                  id: data.role as string,
                })

                if (roleDoc && roleDoc.slug === 'super-admin') {
                  isSuperAdmin = true
                }
              }
            } catch (error) {
              console.error('Error checking role:', error)
              // Continue with the validation, assuming not super-admin
            }
          }

          // School is required for non-super-admins
          if (!isSuperAdmin && !data.school) {
            throw new Error('School is required for this role')
          }

          // For super-admins, school is optional
          // No validation needed as we've made the field not required
        }

        return data
      },
    ],
  },
}
