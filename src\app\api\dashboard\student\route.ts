import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

// Define types for review structure
interface Review {
  reviewer: string | any;
  comment: string;
  rating: number;
  approved?: boolean | null;
  id?: string | null;
  createdAt?: string | Date;
}

// Extended User interface with points and rank
interface ExtendedUser extends Record<string, any> {
  points?: number;
  rank?: number;
  role?: {
    slug?: string;
  } | string;
}

// GET student dashboard data
export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Initialize payload
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      }) as ExtendedUser

      // Check if the user exists and is a student
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'student') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get all articles by the student
      const articles = await payload.find({
        collection: 'articles',
        where: {
          author: {
            equals: userId,
          },
        },
        sort: '-createdAt',
        limit: 10,
        depth: 2,
      })

      // Get published articles
      const publishedArticles = await payload.find({
        collection: 'articles',
        where: {
          author: {
            equals: userId,
          },
          status: {
            equals: 'published',
          },
        },
      })

      // Get draft articles
      const draftArticles = await payload.find({
        collection: 'articles',
        where: {
          author: {
            equals: userId,
          },
          status: {
            equals: 'draft',
          },
        },
      })

      // Get pending articles
      const pendingArticles = await payload.find({
        collection: 'articles',
        where: {
          author: {
            equals: userId,
          },
          status: {
            equals: 'pending-review',
          },
        },
      })

      // Collect recent reviews
      const limit = 5
      const recentReviews = []

      for (const article of articles.docs) {
        if (article.teacherReview && Array.isArray(article.teacherReview)) {
          for (const review of article.teacherReview as Review[]) {
            recentReviews.push({
              articleId: article.id,
              articleTitle: article.title,
              review,
            })
          }
        }
      }

      // Sort reviews by date
      recentReviews.sort((a, b) => {
        if (!a.review.createdAt || !b.review.createdAt) return 0
        const dateA = new Date(a.review.createdAt)
        const dateB = new Date(b.review.createdAt)
        return dateB.getTime() - dateA.getTime()
      })

      // Get all students for total count
      const allStudents = await payload.find({
        collection: 'users',
        where: {
          'role.slug': {
            equals: 'student',
          },
        },
      })
      
      // Use points and rank from the user document
      const userPoints = user.points || 0
      const userRank = user.rank || allStudents.docs.length

      // Return the response with points and rank data
      return NextResponse.json({
        stats: {
          totalArticles: articles.totalDocs,
          publishedArticles: publishedArticles.totalDocs,
          draftArticles: draftArticles.totalDocs,
          pendingArticles: pendingArticles.totalDocs,
          points: userPoints,
          rank: userRank,
          totalStudents: allStudents.docs.length,
        },
        recentArticles: articles.docs,
        recentReviews: recentReviews.slice(0, limit),
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching dashboard data:', error)
    return NextResponse.json(
      { error: 'Error fetching dashboard data' },
      { status: 500 }
    )
  }
}
