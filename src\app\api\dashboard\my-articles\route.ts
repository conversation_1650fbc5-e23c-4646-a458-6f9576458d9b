import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import {
  isMediaPathError,
  logMediaPathError,
  safeMediaQuery,
  filterMediaPathArticles,
  safePayloadResponse,
} from '@/lib/media-utils'
import { getUserArticles } from '@/lib/article-utils'
import { ObjectId } from 'mongodb'
// Remove direct-db-access import since we're not using it anymore

export async function GET(req: NextRequest) {
  try {
    console.log('My Articles API called')

    // Get the token from cookies
    const cookieStore = await cookies();
    const token = cookieStore.get('payload-token')?.value

    console.log('Token found:', token ? 'Yes' : 'No')

    if (!token) {
      console.log('No token found, returning 401')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the payload instance
    const payload = await getPayload({ config })

    // Get the current user with better error handling
    let user
    try {
      // Use jwt.verify instead of payload.verifyToken
      console.log('Verifying token with JWT...')
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token decoded:', decoded ? 'success' : 'failed')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the user from the database
      console.log('Looking up user in database with ID:', userId)
      user = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      console.log('User found in database:', user ? 'true' : 'false')

      if (!user || !user.id) {
        console.log('User not found in database, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Try to get articles from MongoDB using our improved getUserArticles utility
    try {
      console.log('Connecting to MongoDB...')
      const { db } = await connectToDatabase()
      console.log('MongoDB connection successful')

      // Alternative approach - query all articles and then filter
      console.log('Getting all articles and then filtering...')
      const allArticles = await db.collection('articles').find({}).toArray()
      console.log(`Found ${allArticles.length} total articles in the database`)
      
      // Log a few random articles for debugging
      if (allArticles.length > 0) {
        const sampleSize = Math.min(3, allArticles.length)
        console.log(`Showing ${sampleSize} sample articles:`)
        
        for (let i = 0; i < sampleSize; i++) {
          const randomIndex = Math.floor(Math.random() * allArticles.length)
          const article = allArticles[randomIndex]
          console.log(`Sample article ${i+1}:`, {
            _id: article._id,
            title: article.title,
            author: typeof article.author === 'object' 
              ? JSON.stringify(article.author) 
              : article.author,
            status: article.status
          })
        }
      }
      
      // ────────────────────────────────────────────
      // NORMALIZED FILTER FOR USER ARTICLES
      // ────────────────────────────────────────────
      const userArticles = allArticles.filter((article) => {
        if (!article || article.author == null) return false

        // 1) If author is a plain JavaScript string, use it directly:
        if (typeof article.author === 'string') {
          return article.author === user.id || article.author === user.id.toString()
        }

        // 2) If author is an ObjectId instance (e.g., from Mongo's driver):
        if (article.author instanceof ObjectId) {
          return article.author.toString() === user.id
        }

        // 3) If author is an object with a nested "$oid" field:
        //    e.g. { $oid: "6817ce0824bc3cae11d70541" }
        if (
          typeof article.author === 'object' &&
          typeof (article.author as any).$oid === 'string'
        ) {
          return (article.author as any).$oid === user.id
        }

        // 4) If author is an object with an "id" field (Payload shape):
        //    e.g. { id: "6817ce0824bc3cae11d70541", … }
        if (
          typeof article.author === 'object' &&
          typeof (article.author as any).id === 'string'
        ) {
          return (article.author as any).id === user.id
        }

        return false
      })
      
      console.log(`Found ${userArticles.length} articles for user ${user.id} by direct filtering`)
      
      if (userArticles.length > 0) {
        // Apply media path filtering
        const safeArticles = filterMediaPathArticles(userArticles)
        console.log(`After media filtering: ${safeArticles.length} articles`)
        
        if (safeArticles.length > 0) {
          // Log article info for debugging
          console.log("Articles found by direct filtering:")
          safeArticles.slice(0, 5).forEach((article, index) => {
            console.log(`Article ${index + 1}:`, {
              _id: article._id,
              title: article.title,
              status: article.status
            })
          })
          
          return NextResponse.json({ 
            articles: safeArticles,
            debug: {
              count: safeArticles.length,
              statuses: safeArticles.reduce((acc: Record<string, number>, article: any) => {
                const status = article.status || 'unknown'
                acc[status] = (acc[status] || 0) + 1
                return acc
              }, {})
            }
          })
        }
      }

      // Fall back to the regular getUserArticles approach
      console.log('Querying MongoDB for articles with author ID:', user.id)
      const articles = await getUserArticles(user.id)
      console.log('MongoDB articles query result count:', articles ? articles.length : 0)

      if (articles && articles.length > 0) {
        // Apply one final media path filter to ensure no problematic articles
        const safeArticles = filterMediaPathArticles(articles)
        console.log('Fetched my articles from MongoDB:', safeArticles.length)

        if (safeArticles.length > 0) {
          return NextResponse.json({ articles: safeArticles })
        } else {
          console.log('No safe articles found after filtering, falling back to Payload')
        }
      } else {
        console.log('No articles found in MongoDB, falling back to Payload')
      }
    } catch (mongoError) {
      // Check if this is a media path error
      if (isMediaPathError(mongoError)) {
        logMediaPathError(mongoError, 'dashboard/my-articles')
      } else {
        console.warn(
          'Error fetching my articles from MongoDB, falling back to Payload:',
          mongoError,
        )
      }
    }

    // Fallback to Payload CMS if MongoDB fails
    // Get articles authored by the current user
    try {
      console.log('Falling back to Payload CMS for articles')
      // Try different query formats to handle different author structures
      let articlesResponse
      let articles: any[] = []

      // Try multiple approaches to get articles, with robust error handling

      // Approach 1: Try with author.id
      try {
        console.log('Trying Payload query with author.id')
        const response = await payload.find({
          collection: 'articles',
          where: {
            'author.id': {
              equals: user.id,
            },
          },
          depth: 1,
        })

        // Apply safe filtering to remove media paths
        const safeResponse = safePayloadResponse(response)
        if (safeResponse && safeResponse.docs && safeResponse.docs.length > 0) {
          console.log(`Found ${safeResponse.docs.length} articles with author.id query`)
          articles = [...articles, ...safeResponse.docs]
        }
      } catch (error) {
        if (isMediaPathError(error)) {
          logMediaPathError(error, 'Payload author.id query')
        } else {
          console.error('Error with author.id query:', error)
        }
      }

      // Approach 2: Try with author field
      try {
        console.log('Trying Payload query with author field')
        const response = await payload.find({
          collection: 'articles',
          where: {
            author: {
              equals: user.id,
            },
          },
          depth: 1,
        })

        // Apply safe filtering to remove media paths
        const safeResponse = safePayloadResponse(response)
        if (safeResponse && safeResponse.docs && safeResponse.docs.length > 0) {
          console.log(`Found ${safeResponse.docs.length} articles with author query`)

          // Add only articles that aren't already in the list
          const newArticles = safeResponse.docs.filter(
            (newArticle: { id: any }) =>
              !articles.some((existingArticle) => existingArticle.id === newArticle.id),
          )

          articles = [...articles, ...newArticles]
        }
      } catch (error) {
        if (isMediaPathError(error)) {
          logMediaPathError(error, 'Payload author query')
        } else {
          console.error('Error with author query:', error)
        }
      }

      // Approach 3: Try with a generic query and manual filtering
      if (articles.length === 0) {
        try {
          console.log('Trying generic Payload query with manual filtering')
          const response = await payload.find({
            collection: 'articles',
            depth: 1,
            limit: 100, // Increase limit to get more articles
          })

          // Apply safe filtering to remove media paths
          const safeResponse = safePayloadResponse(response)

          if (safeResponse && safeResponse.docs && safeResponse.docs.length > 0) {
            // Filter manually to find articles by this user
            const userArticles = safeResponse.docs.filter((article: { 
              author?: string | { id?: string, $oid?: string } | any 
            }) => {
              if (!article) return false

              // Check author field
              if (article.author) {
                if (typeof article.author === 'object' && article.author.id) {
                  return article.author.id === user.id || article.author.id === user.id.toString()
                } else if (typeof article.author === 'string') {
                  return article.author === user.id || article.author === user.id.toString()
                } else if (typeof article.author === 'object' && article.author.$oid) {
                  // Handle MongoDB ObjectId format
                  return article.author.$oid === user.id || article.author.$oid === user.id.toString()
                }
              }

              return false
            })

            console.log(
              `Found ${userArticles.length} articles with generic query and manual filtering`,
            )
            articles = [...articles, ...userArticles]
          }
        } catch (error) {
          if (isMediaPathError(error)) {
            logMediaPathError(error, 'Payload generic query')
          } else {
            console.error('Error with generic query:', error)
          }
        }
      }

      // Final filtering to ensure no duplicates and no media paths
      const uniqueArticles = Array.from(
        new Map(articles.map((article) => [article.id, article])).values(),
      )

      // Apply one final media path filter
      const safeArticles = filterMediaPathArticles(uniqueArticles)

      console.log('Final article count after all queries:', safeArticles.length)
      
      // Enhanced debugging information
      if (safeArticles.length > 0) {
        console.log('Articles being returned (first 5):')
        safeArticles.slice(0, 5).forEach((article, index) => {
          console.log(`Article ${index + 1}:`, {
            id: article.id,
            _id: article._id,
            title: article.title,
            status: article.status,
            author: typeof article.author === 'object' ? 'object' : article.author,
            createdAt: article.createdAt
          })
        })
      } else {
        console.log('No articles found to return')
      }

      // If we found articles, return them
      if (safeArticles.length > 0) {
        return NextResponse.json({ 
          articles: safeArticles,
          debug: {
            count: safeArticles.length,
            statuses: safeArticles.reduce((acc: Record<string, number>, article: any) => {
              const status = article.status || 'unknown'
              acc[status] = (acc[status] || 0) + 1
              return acc
            }, {})
          }
        })
      }

      // Last resort: Return empty array with a 200 status code
      console.log('No articles found with any method, returning empty array')
      return NextResponse.json({ articles: [] })
    } catch (payloadError) {
      // Check if this is a media path error
      if (isMediaPathError(payloadError)) {
        logMediaPathError(payloadError, 'dashboard/my-articles Payload')
      } else {
        console.error('Error fetching articles from Payload:', payloadError)
      }

      // Always return empty array instead of error to prevent UI issues
      return NextResponse.json({ articles: [] })
    }
  } catch (error) {
    console.error('Error fetching my articles:', error)

    // Always return empty array instead of error to prevent UI issues
    return NextResponse.json({ articles: [] })
  }
}
