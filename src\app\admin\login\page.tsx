'use client'

import { useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

export default function AdminLoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirect = searchParams.get('redirect') || '/admin'

  useEffect(() => {
    // Redirect to our custom admin auth endpoint
    window.location.href = `/api/admin-auth?redirect=${encodeURIComponent(redirect)}`
  }, [redirect])

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="p-8 bg-white rounded shadow-md">
        <h1 className="text-2xl font-bold mb-4">Redirecting to Admin...</h1>
        <p>Please wait while we authenticate you.</p>
      </div>
    </div>
  )
}
