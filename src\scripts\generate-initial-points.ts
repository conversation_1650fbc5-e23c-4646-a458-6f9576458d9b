// CommonJS version
const { getPayload } = require('payload')
const { updatePointsAndRankings } = require('../utils/points')
const config = require('../payload.config')

/**
 * Script to generate initial points and rankings
 * Run this with node src/scripts/generate-initial-points.ts
 * 
 * This will:
 * 1. Calculate points for all users based on their activities
 * 2. Update the statistics collection with leaderboards
 */
const generateInitialPoints = async () => {
  try {
    console.log('Starting initial points and rankings generation...')
    
    // Initialize Payload
    const payload = await getPayload({ config })
    
    // Update points and rankings
    await updatePointsAndRankings({ payload })
    
    console.log('Initial points and rankings generated successfully!')
    process.exit(0)
  } catch (error) {
    console.error('Error generating initial points and rankings:', error)
    process.exit(1)
  }
}

generateInitialPoints() 