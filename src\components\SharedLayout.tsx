'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import { Footer } from '@/components/Footer'
import { NotificationProvider } from '@/contexts/NotificationContext'
import { ResponsiveNavigation } from '@/components/ResponsiveNavigation'

interface SharedLayoutProps {
  children: React.ReactNode
  user: any
  adminRoute: string
}

export const SharedLayout: React.FC<SharedLayoutProps> = ({ children, user, adminRoute }) => {
  // Determine user role and ID for notifications
  const userRole = user ? (typeof user.role === 'object' ? user.role?.slug : user.role) : ''
  const userId = user?.id || ''
  const pathname = usePathname()

  // Check if we're on the news page
  const isNewsPage = pathname === '/news' || pathname?.startsWith('/news/')

  // Debug user object
  console.log('User in SharedLayout:', user)

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <NotificationProvider userRole={userRole} userId={userId}>
        <ResponsiveNavigation user={user} adminRoute={adminRoute} forceBlackText={isNewsPage} />
        <main dir="ltr" className="flex-grow">
          {children}
        </main>
        <Footer />
      </NotificationProvider>
    </div>
  )
}
