'use client'

import { useEffect, useState } from 'react'
import { 
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Skeleton } from '@/components/ui/skeleton'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from '@/components/ui/use-toast'
import { AlertTriangle, CheckCircle, Save, Settings, Info } from 'lucide-react'

interface SystemConfig {
  id: string
  key: string
  value: string | boolean | number
  description: string
  category: string
}

export function SystemConfigurations() {
  const [loading, setLoading] = useState(true)
  const [configs, setConfigs] = useState<SystemConfig[]>([])
  const [error, setError] = useState<string | null>(null)
  const [saving, setSaving] = useState(false)
  const [configsDirty, setConfigsDirty] = useState(false)
  const [activeTab, setActiveTab] = useState<string | null>(null)

  useEffect(() => {
    const fetchConfigs = async () => {
      try {
        setLoading(true)
        // Try to fetch from actual API first
        let data: SystemConfig[] = [];
        
        try {
          const response = await fetch('/api/dashboard/super-admin/system-configs')
          if (response.ok) {
            const result = await response.json()
            data = result.configs || result || []
          }
        } catch (apiError) {
          console.warn('Error fetching from API, using sample data', apiError)
        }
        
        // If API doesn't return data, use sample data
        if (data.length === 0) {
          data = [
            {
              id: '1',
              key: 'user_registration_enabled',
              value: true,
              description: 'Allow new users to register on the platform',
              category: 'access'
            },
            {
              id: '2',
              key: 'max_login_attempts',
              value: 5,
              description: 'Maximum number of failed login attempts before lockout',
              category: 'security'
            },
            {
              id: '3',
              key: 'default_article_visibility',
              value: 'school',
              description: 'Default visibility setting for new articles',
              category: 'content'
            },
            {
              id: '4',
              key: 'enable_notifications',
              value: true,
              description: 'Enable system-wide notifications',
              category: 'notifications'
            },
            {
              id: '5',
              key: 'student_article_requires_approval',
              value: true,
              description: 'Require teacher approval for student articles',
              category: 'content'
            },
            {
              id: '6',
              key: 'system_maintenance_mode',
              value: false,
              description: 'Put system in maintenance mode (only super admins can access)',
              category: 'system'
            }
          ]
        }
        
        setConfigs(data)
        
        // Set the default active tab to the first category found
        const categories = Array.from(new Set(data.map(config => config.category)));
        if (categories.length > 0 && !activeTab) {
          setActiveTab(categories[0]);
        }
        
        setLoading(false)
      } catch (error) {
        console.error('Error fetching system configurations:', error)
        setError('Failed to load system configurations')
        setLoading(false)
      }
    }

    fetchConfigs()
  }, [activeTab])

  const handleConfigChange = (id: string, newValue: string | boolean | number) => {
    setConfigs(configs.map(config => 
      config.id === id ? { ...config, value: newValue } : config
    ))
    setConfigsDirty(true)
  }

  const handleSaveConfigs = async () => {
    try {
      setSaving(true)
      
      // This will be replaced with an actual API call when implemented
      // const response = await fetch('/api/dashboard/super-admin/system-configs', {
      //   method: 'PUT',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({ configs }),
      // })

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800))
      
      toast({
        title: "Settings saved",
        description: "System configurations have been updated",
        duration: 3000,
      })

      setConfigsDirty(false)
      setSaving(false)
    } catch (error) {
      console.error('Error saving system configurations:', error)
      toast({
        title: "Error saving settings",
        description: "There was a problem updating the system configurations",
        variant: "destructive",
      })
      setSaving(false)
    }
  }

  // Group configs by category
  const configsByCategory = configs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = []
    }
    acc[config.category].push(config)
    return acc
  }, {} as Record<string, SystemConfig[]>)

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'access':
        return <Settings className="w-4 h-4 mr-2" />
      case 'security':
        return <AlertTriangle className="w-4 h-4 mr-2" />
      case 'content':
        return <CheckCircle className="w-4 h-4 mr-2" />
      case 'notifications':
        return <Info className="w-4 h-4 mr-2" />
      default:
        return <Settings className="w-4 h-4 mr-2" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-[400px] w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 border border-red-200 bg-red-50 rounded-md text-red-700 flex items-center">
        <AlertTriangle className="h-5 w-5 mr-2" />
        Error: {error}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          Configure system-wide settings and features
        </div>
        
        <Button 
          onClick={handleSaveConfigs} 
          disabled={!configsDirty || saving}
          variant={configsDirty ? "default" : "outline"}
        >
          {saving ? (
            <>
              <span className="animate-spin mr-2">⏳</span> Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" /> Save Changes
            </>
          )}
        </Button>
      </div>

      <Tabs value={activeTab || Object.keys(configsByCategory)[0]} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          {Object.keys(configsByCategory).map(category => (
            <TabsTrigger key={category} value={category} className="capitalize flex items-center">
              {getCategoryIcon(category)}
              {category}
            </TabsTrigger>
          ))}
        </TabsList>

        {Object.entries(configsByCategory).map(([category, categoryConfigs]) => (
          <TabsContent key={category} value={category} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {categoryConfigs.map(config => (
                <Card key={config.id}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">{config.key.split('_').map(word => 
                      word.charAt(0).toUpperCase() + word.slice(1)
                    ).join(' ')}</CardTitle>
                    <CardDescription>{config.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {typeof config.value === 'boolean' ? (
                      <div className="flex items-center space-x-2">
                        <Switch 
                          id={`switch-${config.id}`}
                          checked={config.value} 
                          onCheckedChange={(checked) => handleConfigChange(config.id, checked)}
                        />
                        <Label htmlFor={`switch-${config.id}`} className="font-medium">
                          {config.value ? 'Enabled' : 'Disabled'}
                        </Label>
                      </div>
                    ) : typeof config.value === 'number' ? (
                      <Input 
                        type="number" 
                        value={config.value}
                        onChange={(e) => handleConfigChange(config.id, Number(e.target.value))}
                      />
                    ) : (
                      <Input 
                        type="text" 
                        value={config.value as string}
                        onChange={(e) => handleConfigChange(config.id, e.target.value)}
                      />
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
      
      {configsDirty && (
        <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded text-amber-700 text-sm flex items-center">
          <AlertTriangle className="h-4 w-4 mr-2" />
          You have unsaved changes. Click "Save Changes" to apply them.
        </div>
      )}
    </div>
  )
} 