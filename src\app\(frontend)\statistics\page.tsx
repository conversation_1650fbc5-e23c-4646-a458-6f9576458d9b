import React from 'react'

import { PageLayout } from '@/components/PageLayout'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { ClientRankingsCarousel } from './components/ClientRankingsCarousel'

import './styles/statistics-carousel.css'

// Define types for our statistics data
interface SummaryStats {
  totalArticles: number
  publishedArticles: number
  totalStudents: number
  totalTeachers: number
  totalSchools: number
  totalMentors: number
}

// Update RankingItem interface to include points and type
interface RankingItem {
  id: string
  name: string
  anonymizedName?: string
  count?: number
  articleCount?: number
  averageRating?: number
  reviewCount?: number
  points?: number
  type?: 'student' | 'teacher' | 'school' | 'mentor'
  rank?: number
  image?: string
}

// This interface represents the structure of our API response
interface StatisticsApiResponse {
  statistics: any[]
  platformStats: {
    totalUsers: number
    totalArticles: number
    totalNews: number
    totalSchools: number
    usersByRole: {
      students: number
      teachers: number
      mentors: number
      admins: number
    }
    articlesByStatus: {
      published: number
      pendingReview: number
      draft: number
    }
    leaderboards: {
      students: RankingItem[]
      teachers: RankingItem[]
      mentors: RankingItem[]
    }
  }
}

export default async function StatisticsPage() {
  console.log('Rendering StatisticsPage component')

  // Default summary stats
  let summary: SummaryStats = {
    totalArticles: 0,
    publishedArticles: 0,
    totalStudents: 0,
    totalTeachers: 0,
    totalSchools: 0,
    totalMentors: 0,
  }

  // Default empty arrays for rankings
  let schoolData: RankingItem[] = []
  let studentData: RankingItem[] = []
  let teacherData: RankingItem[] = []
  let mentorData: RankingItem[] = []

  try {
    // Fetch data from our statistics API
    console.log('Fetching data from statistics API')
    const response = await fetch('/api/statistics', {
      next: { revalidate: 60 }, // Revalidate every minute
    })

    if (!response.ok) {
      throw new Error(`Statistics API responded with status: ${response.status}`)
    }

    const data: StatisticsApiResponse = await response.json()
    console.log(
      'Statistics API data received:',
      JSON.stringify({
        totalUsers: data.platformStats?.totalUsers,
        totalArticles: data.platformStats?.totalArticles,
        leaderboardCounts: {
          students: data.platformStats?.leaderboards?.students?.length || 0,
          teachers: data.platformStats?.leaderboards?.teachers?.length || 0,
          mentors: data.platformStats?.leaderboards?.mentors?.length || 0,
        },
      }),
    )

    // Extract summary data from the API response
    if (data.platformStats) {
      summary = {
        totalArticles: data.platformStats.totalArticles || 0,
        publishedArticles: data.platformStats.articlesByStatus?.published || 0,
        totalStudents: data.platformStats.usersByRole?.students || 0,
        totalTeachers: data.platformStats.usersByRole?.teachers || 0,
        totalSchools: data.platformStats.totalSchools || 0,
        totalMentors: data.platformStats.usersByRole?.mentors || 0,
      }
    }

    // Extract leaderboard data
    if (data.platformStats?.leaderboards) {
      studentData = (data.platformStats.leaderboards.students || []).map((student) => ({
        ...student,
        type: 'student' as const,
      }))

      teacherData = (data.platformStats.leaderboards.teachers || []).map((teacher) => ({
        ...teacher,
        type: 'teacher' as const,
      }))

      mentorData = (data.platformStats.leaderboards.mentors || []).map((mentor) => ({
        ...mentor,
        type: 'mentor' as const,
      }))

      // Log each array to see what we have
      console.log(
        'Student data sample:',
        studentData.length > 0 ? JSON.stringify(studentData[0]) : 'No student data',
      )
      console.log(
        'Teacher data sample:',
        teacherData.length > 0 ? JSON.stringify(teacherData[0]) : 'No teacher data',
      )
      console.log(
        'Mentor data sample:',
        mentorData.length > 0 ? JSON.stringify(mentorData[0]) : 'No mentor data',
      )
    }

    // Find school rankings in statistics collection
    const schoolRankingDoc = data.statistics?.find((stat) => stat.type === 'schoolRanking')
    if (schoolRankingDoc && Array.isArray(schoolRankingDoc.data)) {
      schoolData = schoolRankingDoc.data
    }
  } catch (error) {
    console.error('Error fetching statistics data:', error)
    // We'll continue with default empty data
  }

  return (
    <PageLayout
      bgImage="bg-[url(https://images.unsplash.com/flagged/photo-1578928534298-9747fc52ec97?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)]"
      title="لوحات المتصدرين"
      subtitle="شاهد أفضل المدارس والطلاب والمعلمين والموجهين أداءً"
      bgColor="bg-amber-700"
    >
      {/* Statistics Content */}
      <div className="py-12 px-4 bg-muted/30">
        <div className="container mx-auto">
          {/* Summary Statistics */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold mb-8 text-center">نظرة عامة على المنصة</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Total Articles */}
              <Card className="text-center">
                <CardHeader>
                  <CardTitle className="text-lg text-muted-foreground">إجمالي المقالات</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-4xl font-bold text-primary">{summary.totalArticles}</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    {summary.publishedArticles} منشور
                  </p>
                </CardContent>
              </Card>

              {/* Total Students */}
              <Card className="text-center">
                <CardHeader>
                  <CardTitle className="text-lg text-muted-foreground">المراسلون الطلاب</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-4xl font-bold text-green-600">{summary.totalStudents}</p>
                  <p className="text-sm text-muted-foreground mt-2">صحفيون شباب نشطون</p>
                </CardContent>
              </Card>

              {/* Total Teachers */}
              <Card className="text-center">
                <CardHeader>
                  <CardTitle className="text-lg text-muted-foreground">المعلمون الموجهون</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-4xl font-bold text-purple-600">{summary.totalTeachers}</p>
                  <p className="text-sm text-muted-foreground mt-2">يدعمون طلابنا</p>
                </CardContent>
              </Card>

              {/* Total Schools */}
              <Card className="text-center">
                <CardHeader>
                  <CardTitle className="text-lg text-muted-foreground">المدارس المشاركة</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-4xl font-bold text-amber-600">{summary.totalSchools}</p>
                  <p className="text-sm text-muted-foreground mt-2">عبر البلاد</p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Rankings Carousel */}
          <ClientRankingsCarousel
            schoolData={schoolData}
            studentData={studentData}
            teacherData={teacherData}
            mentorData={mentorData}
          />
        </div>
      </div>
    </PageLayout>
  )
}
