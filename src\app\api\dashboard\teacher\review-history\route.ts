import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import config from '@/payload.config'
import { generateStudentAlias } from '@/lib/security'
import { connectToDatabase } from '@/lib/mongodb'
import { isMediaPath, isMediaPathError, logMediaPathError, safePayloadResponse } from '@/lib/media-utils'

export async function GET(req: NextRequest) {
  try {
    // Parse query parameters for search & filtering
    const searchParams = req.nextUrl.searchParams
    const searchTerm = searchParams.get('search')?.toLowerCase() || ''
    const startDate = searchParams.get('startDate') || ''
    const endDate = searchParams.get('endDate') || ''
    const approved = searchParams.get('approved')
    
    console.log('Search filters:', { searchTerm, startDate, endDate, approved })

    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token and get user info
      const { id: userId, role } = await extractUserFromToken(token)

      console.log('User ID:', userId, 'Role:', role)

      // Get the payload instance to verify the role
      const payload = await getPayload({ config })

      // Get the user from Payload CMS
      let userRole = role

      if (userId) {
        try {
          // Check if userId is a valid ObjectId before querying
          const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(userId)

          if (!isValidObjectId) {
            console.error('Invalid ObjectId format for user ID:', userId)
            return NextResponse.json({ error: 'Invalid user ID format' }, { status: 400 })
          }

          const user = await payload.findByID({
            collection: 'users',
            id: userId,
            depth: 1,
          })

          // Check if the user has a role object
          if (user && user.role) {
            if (typeof user.role === 'object' && user.role.slug) {
              userRole = user.role.slug
            } else if (typeof user.role === 'string') {
              // Try to get the role by ID
              try {
                const roleDoc = await payload.findByID({
                  collection: 'roles',
                  id: user.role,
                })
                if (roleDoc && roleDoc.slug) {
                  userRole = roleDoc.slug
                }
              } catch (roleError) {
                console.error('Error fetching role:', roleError)
              }
            }
          }

          console.log('User role from Payload:', userRole)
        } catch (userError) {
          console.error('Error fetching user:', userError)
        }
      }

      // Allow teachers, mentors, school admins, and super admins to access this endpoint
      if (
        userRole !== 'teacher' &&
        userRole !== 'mentor' &&
        userRole !== 'school-admin' &&
        userRole !== 'super-admin'
      ) {
        return NextResponse.json(
          {
            error: 'Forbidden',
            message: 'Only teachers, mentors, and admins can access this endpoint',
            providedRole: userRole,
          },
          { status: 403 },
        )
      }

      // Database connection will be used in the Promise.all below

      // Get activities related to article reviews by this teacher
      const whereClause: any = {
        userId: { equals: userId },
        activityType: { equals: 'article-review' },
      }
      
      // Add date range filter if provided
      if (startDate || endDate) {
        whereClause.createdAt = {};
        
        if (startDate) {
          whereClause.createdAt.greater_than_equal = new Date(startDate).toISOString();
        }
        
        if (endDate) {
          const endDateObj = new Date(endDate);
          endDateObj.setDate(endDateObj.getDate() + 1); // Include the end date in the range
          whereClause.createdAt.less_than = endDateObj.toISOString();
        }
      }

      // Get activities with the filters
      const activities = await payload.find({
        collection: 'activities',
        where: whereClause,
        sort: '-createdAt',
        limit: 50, // Increased limit to show more reviews
      })

      // If no review history found, try to create some test reviews
      if (activities.docs.length === 0) {
        console.log('No review history found, creating test data')

        // Get some articles to review
        const articles = await payload.find({
          collection: 'articles',
          limit: 5,
          depth: 2,
        })

        if (articles.docs.length > 0) {
          // Create test review activities
          const testReviews = []

          for (let i = 0; i < Math.min(5, articles.docs.length); i++) {
            const article = articles.docs[i]
            const daysAgo = Math.floor(Math.random() * 14) // Random date within last 14 days
            const approved = Math.random() > 0.3 // 70% chance of approval
            const rating = Math.floor(Math.random() * 5) + 5 // Rating between 5-10

            const reviewDate = new Date()
            reviewDate.setDate(reviewDate.getDate() - daysAgo)

            const activity = await payload.create({
              collection: 'activities',
              data: {
                userId: userId || '',
                activityType: 'article-review',
                details: {
                  articleId: article.id,
                  articleTitle: article.title,
                  rating,
                  comment: `This is a test review comment for article "${article.title}".`,
                  approved,
                },
                createdAt: reviewDate.toISOString(),
              },
            })

            // Also update the article status
            if (article) {
              await payload.update({
                collection: 'articles',
                id: article.id,
                data: {
                  status: approved ? 'published' : 'draft',
                },
              })

              // Determine the best URL for viewing the article
              let articleUrl = ''
              if (approved) {
                articleUrl = `/dashboard/articles/${article.id}`
              } else {
                articleUrl = `/dashboard/teacher/review/${article.id}`
              }

              testReviews.push({
                id: activity.id,
                articleId: article.id,
                articleTitle: article.title,
                authorAlias: generateStudentAlias(
                  typeof article.author === 'object' ? article.author?.id : article.author,
                  'school',
                ),
                rating:
                  activity.details &&
                  typeof activity.details === 'object' &&
                  'rating' in activity.details
                    ? Number(activity.details.rating)
                    : 0,
                comment:
                  activity.details &&
                  typeof activity.details === 'object' &&
                  'comment' in activity.details
                    ? String(activity.details.comment)
                    : '',
                approved:
                  activity.details &&
                  typeof activity.details === 'object' &&
                  'approved' in activity.details
                    ? Boolean(activity.details.approved)
                    : false,
                reviewDate: activity.createdAt,
                articleUrl
              })
            }

            return NextResponse.json({
              reviews: testReviews,
            })
          }
        }
      }

      // Process review history with anonymized student information
      let reviewHistory = await Promise.all(
        activities.docs.map(async (activity) => {
          // Get the article details
          // Extract details safely
          let articleId: string | undefined = undefined
          let article = null
          let authorId = null
          let articleTitle = 'Unknown Article'

          // Handle different types of details
          if (activity.details) {
            if (typeof activity.details === 'object' && !Array.isArray(activity.details)) {
              // It's a Record<string, unknown>
              const detailsObj = activity.details as Record<string, unknown>

              // Extract articleId
              if ('articleId' in detailsObj && typeof detailsObj.articleId === 'string') {
                articleId = detailsObj.articleId
              }

              // Extract articleTitle
              if ('articleTitle' in detailsObj && typeof detailsObj.articleTitle === 'string') {
                articleTitle = detailsObj.articleTitle
              }
            }
          }

          // Check if the article exists in the database
          if (articleId) {
            try {
              // First try to find the article in MongoDB directly
              const { db } = await connectToDatabase()
              // Try different query approaches
              let articleFromMongo = null

              // First try by string ID
              articleFromMongo = await db.collection('articles').findOne({ id: articleId })

              // If not found, try by slug
              if (!articleFromMongo) {
                articleFromMongo = await db.collection('articles').findOne({ slug: articleId })
              }

              // If still not found and it looks like an ObjectId, try that
              if (!articleFromMongo && articleId.match(/^[0-9a-fA-F]{24}$/)) {
                try {
                  const { ObjectId } = await import('mongodb')
                  articleFromMongo = await db.collection('articles').findOne({
                    _id: new ObjectId(articleId),
                  })
                } catch (err) {
                  console.error('Error finding article by ObjectId:', err)
                }
              }

              if (articleFromMongo) {
                article = articleFromMongo
                articleTitle = articleFromMongo.title || articleTitle
                authorId = articleFromMongo.author

                // If author is an ObjectId string, try to get more info
                if (typeof authorId === 'string') {
                  try {
                    // Try different query approaches for author
                    let authorFromMongo = null

                    // First try by string ID
                    authorFromMongo = await db.collection('users').findOne({ id: authorId })

                    // If not found and it looks like an ObjectId, try that
                    if (!authorFromMongo && authorId.match(/^[0-9a-fA-F]{24}$/)) {
                      try {
                        const { ObjectId } = await import('mongodb')
                        authorFromMongo = await db.collection('users').findOne({
                          _id: new ObjectId(authorId),
                        })
                      } catch (err) {
                        console.error('Error finding author by ObjectId:', err)
                      }
                    }
                    if (authorFromMongo) {
                      authorId = authorFromMongo._id
                    }
                  } catch (authorErr) {
                    console.error(`Error fetching author ${authorId} from MongoDB:`, authorErr)
                  }
                }
              } else {
                // If not found in MongoDB, try Payload CMS
                try {
                  // Try to find the article by ID
                  const articles = await payload.find({
                    collection: 'articles',
                    where: {
                      id: {
                        equals: articleId,
                      },
                    },
                    depth: 2,
                    limit: 1,
                  })

                  if (articles.docs && articles.docs.length > 0) {
                    article = articles.docs[0]

                    if (article?.title) {
                      articleTitle = article.title
                    }
                    authorId =
                      typeof article.author === 'object' ? article.author?.id : article.author
                  } else {
                    // If not found by ID, try by ObjectID
                    try {
                      article = await payload.findByID({
                        collection: 'articles',
                        id: articleId,
                        depth: 2, // Populate relationships
                      })

                      if (article?.title) {
                        articleTitle = article.title
                      }
                      authorId =
                        typeof article.author === 'object' ? article.author?.id : article.author
                    } catch (idErr) {
                      console.error(`Article ${articleId} not found by ID in Payload CMS:`, idErr)
                      // Use the title we already extracted or a fallback
                      articleTitle = articleTitle || 'Article Not Found'
                    }
                  }
                } catch (payloadErr) {
                  console.error(`Error finding article ${articleId} in Payload CMS:`, payloadErr)
                  // Article doesn't exist in Payload either, use the title we already extracted
                  articleTitle = articleTitle || 'Article Not Found'
                }
              }
            } catch (err) {
              console.error(`Error fetching article ${articleId}:`, err)
              articleTitle = 'Article Not Found'
            }
          }

          // Generate alias for the student
          let schoolId = 'unknown'

          if (article) {
            if (article.author) {
              if (typeof article.author === 'object') {
                if (article.author.school) {
                  schoolId =
                    typeof article.author.school === 'object'
                      ? article.author.school?.id
                      : article.author.school
                }
              }
            }
          }

          const authorAlias = authorId
            ? generateStudentAlias(authorId, schoolId)
            : 'Unknown Student'

          // Extract values from details with proper type casting
          let rating = 0
          let comment = ''
          let approved = false

          // Handle different types of details
          if (activity.details) {
            if (typeof activity.details === 'object' && !Array.isArray(activity.details)) {
              // It's a Record<string, unknown>
              const detailsObj = activity.details as Record<string, unknown>

              if ('rating' in detailsObj && detailsObj.rating !== undefined) {
                rating = Number(detailsObj.rating)
              }

              if ('comment' in detailsObj && detailsObj.comment !== undefined) {
                comment = String(detailsObj.comment)
              }

              if ('approved' in detailsObj && detailsObj.approved !== undefined) {
                approved = Boolean(detailsObj.approved)
              }
            }
          }

          // Determine the best URL for viewing the article
          let articleUrl = ''
          if (articleId) {
            // Check if the article is published or still in review
            const status = article?.status || 'unknown'

            if (status === 'published') {
              articleUrl = `/dashboard/articles/${articleId}`
            } else {
              articleUrl = `/dashboard/teacher/review/${articleId}`
            }
          }

          return {
            id: activity.id,
            articleId: articleId || '',
            articleTitle: articleTitle || 'Article Not Found',
            authorAlias,
            rating: rating,
            comment: comment,
            approved,
            reviewDate: activity.createdAt,
            articleUrl
          }
        }),
      )

      // Apply client-side filtering
      if (searchTerm) {
        reviewHistory = reviewHistory.filter(review => 
          review.articleTitle.toLowerCase().includes(searchTerm) || 
          review.authorAlias.toLowerCase().includes(searchTerm) || 
          review.comment.toLowerCase().includes(searchTerm)
        );
      }
      
      // Filter by approval status if provided
      if (approved !== null && approved !== undefined) {
        const isApproved = approved === 'true';
        reviewHistory = reviewHistory.filter(review => review.approved === isApproved);
      }

      return NextResponse.json({
        reviews: reviewHistory,
      })
    } catch (error) {
      // Check if this is a media path error
      if (isMediaPathError(error)) {
        logMediaPathError(error, 'teacher review-history API')

        // Return fallback data instead of an error
        return NextResponse.json({
          reviews: [
            {
              id: 'fallback-1',
              articleId: 'fallback-article-1',
              articleTitle: 'Climate Change Effects on Local Wildlife',
              authorAlias: 'Student_12345678',
              rating: 8,
              comment: 'Great article with well-researched content.',
              approved: true,
              reviewDate: new Date().toISOString(),
              articleUrl: '/dashboard/articles/fallback-article-1'
            },
            {
              id: 'fallback-2',
              articleId: 'fallback-article-2',
              articleTitle: 'School Lunch Program Analysis',
              authorAlias: 'Student_87654321',
              rating: 7,
              comment: 'Good analysis but could use more data.',
              approved: true,
              reviewDate: new Date(Date.now() - 86400000).toISOString(),
              articleUrl: '/dashboard/articles/fallback-article-2'
            }
          ]
        })
      }

      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching review history:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
