import React from 'react'

interface RichTextProps {
  content: any
  className?: string
}

export const RichText: React.FC<RichTextProps> = ({ content, className = '' }) => {
  // For debugging
  console.log('RichText content:', content);
  
  // If content is null or undefined, return null
  if (!content) {
    console.log('RichText: content is null or undefined');
    return <div className="rich-text-empty">No content available</div>;
  }

  // If content is a string, try to parse it as JSON first (for Lexical format)
  if (typeof content === 'string') {
    try {
      // Try to parse as JSON
      const parsedContent = JSON.parse(content)
      if (parsedContent && parsedContent.root) {
        // It's a Lexical JSON object
        content = parsedContent
      } else {
        // It's a regular string, render with markdown-like formatting
        return (
          <div className={`rich-text-content ${className}`}>
            {content.split('\n\n').map((paragraph, i) => (
              <p
                key={i}
                className="mb-4"
                dangerouslySetInnerHTML={{
                  __html: paragraph
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/__(.*?)__/g, '<u>$1</u>')
                    .replace(
                      /`(.*?)`/g,
                      '<code class="bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-1 py-0.5 rounded text-sm">$1</code>',
                    )
                    .replace(
                      /\[(.*?)\]\((.*?)\)/g,
                      '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-cyan-600 hover:underline">$1</a>',
                    ),
                }}
              />
            ))}
          </div>
        )
      }
    } catch (e) {
      console.log('RichText: not valid JSON, rendering as HTML', e);
      // Not JSON, render as HTML
      return <div className={`rich-text-html ${className}`} dangerouslySetInnerHTML={{ __html: content }} />
    }
  }

  // Handle Payload CMS rich text format directly
  if (content && typeof content === 'object') {
    // Check for Payload CMS format (serialized rich text)
    if ('text' in content || 'children' in content || 'blocks' in content) {
      console.log('Detected Payload CMS format');
      return renderPayloadFormat(content, className);
    }

    // For Lexical editor, content is an object with a root property
    if (content.root) {
      console.log('Detected Lexical format with root');
      return renderLexicalFormat(content, className);
    }
  }

  // Fallback for Payload CMS rich text format
  if (content && Array.isArray(content)) {
    console.log('Detected array content, likely Payload blocks');
    return (
      <div className={`rich-text ${className}`}>
        {content.map((block, i) => (
          <div key={i} className="mb-4">
            {block.text || (block.children && renderPayloadFormat(block, ""))}
          </div>
        ))}
      </div>
    );
  }

  // Fallback for unknown content format
  console.log('Using fallback renderer for content');
  return (
    <div className={`rich-text-fallback ${className}`}>
      {typeof content === 'string' 
        ? content 
        : JSON.stringify(content, null, 2)}
    </div>
  );
}

// Helper function to render Lexical format
function renderLexicalFormat(content: any, className: string) {
  try {
    const renderNode = (node: any) => {
      if (!node) return null;

      // Handle different node types
      switch (node.type) {
        case 'paragraph':
          return (
            <p className="mb-4">
              {node.children?.map((child: any, i: number) => (
                <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
              ))}
            </p>
          )
        case 'heading':
          // Use dynamic heading element based on tag
          const level = node.tag || 2 // Default to h2 if tag is missing

          if (level === 1) {
            return (
              <h1 className="text-2xl font-bold mb-4">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h1>
            )
          } else if (level === 2) {
            return (
              <h2 className="text-xl font-bold mb-4">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h2>
            )
          } else if (level === 3) {
            return (
              <h3 className="text-lg font-bold mb-4">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h3>
            )
          } else if (level === 4) {
            return (
              <h4 className="text-base font-bold mb-4">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h4>
            )
          } else if (level === 5) {
            return (
              <h5 className="text-sm font-bold mb-4">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h5>
            )
          } else {
            return (
              <h6 className="text-xs font-bold mb-4">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </h6>
            )
          }
        case 'list': // Handle both ordered and unordered lists
          if (node.listType === 'bullet') {
            return (
              <ul className="list-disc mb-4 pl-5">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </ul>
            )
          } else {
            return (
              <ol className="list-decimal mb-4 pl-5">
                {node.children?.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </ol>
            )
          }
        case 'listitem':
          return (
            <li className="mb-1">
              {node.children?.map((child: any, i: number) => (
                <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
              ))}
            </li>
          )
        case 'quote':
          return (
            <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-4">
              {node.children?.map((child: any, i: number) => (
                <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
              ))}
            </blockquote>
          )
        case 'link':
          return (
            <a href={node.url} className="text-blue-600 hover:underline">
              {node.children?.map((child: any, i: number) => (
                <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
              ))}
            </a>
          )
        case 'text':
          let text = node.text || '';

          // Apply formatting
          if (node.format & 1) {
            // Bold
            text = <strong>{text}</strong>
          }
          if (node.format & 2) {
            // Italic
            text = <em>{text}</em>
          }
          if (node.format & 4) {
            // Underline
            text = <u>{text}</u>
          }
          if (node.format & 8) {
            // Strikethrough
            text = <s>{text}</s>
          }

          return text;
        default:
          // For other node types or if children exist
          if (node.children) {
            return (
              <>
                {node.children.map((child: any, i: number) => (
                  <React.Fragment key={i}>{renderNode(child)}</React.Fragment>
                ))}
              </>
            )
          }
          return null;
      }
    }

    return <div className={`rich-text ${className}`}>{renderNode(content.root)}</div>
  } catch (error) {
    console.error('Error rendering Lexical format:', error);
    return <div className="rich-text-error">Error rendering content</div>
  }
}

// Helper function to render Payload CMS format
function renderPayloadFormat(content: any, className: string) {
  try {
    if (!content) return null;

    // If it's just text
    if (typeof content === 'string') {
      return <p className="mb-4">{content}</p>;
    }

    // If it has direct text content
    if (content.text) {
      let text = content.text;
      
      // Apply any formatting if available
      if (content.bold) text = <strong>{text}</strong>;
      if (content.italic) text = <em>{text}</em>;
      if (content.underline) text = <u>{text}</u>;
      if (content.strikethrough) text = <s>{text}</s>;
      
      if (content.linkType && content.url) {
        return <a href={content.url} className="text-blue-600 hover:underline">{text}</a>;
      }
      
      return <span>{text}</span>;
    }

    // If it has children nodes
    if (content.children && Array.isArray(content.children)) {
      // Figure out the element type
      let Element: any = 'p';
      let className = 'mb-4';
      
      if (content.type === 'h1') {
        Element = 'h1';
        className = 'text-2xl font-bold mb-4';
      } else if (content.type === 'h2') {
        Element = 'h2';
        className = 'text-xl font-bold mb-4';
      } else if (content.type === 'h3') {
        Element = 'h3';
        className = 'text-lg font-bold mb-4';
      } else if (content.type === 'h4') {
        Element = 'h4';
        className = 'text-base font-bold mb-4';
      } else if (content.type === 'h5') {
        Element = 'h5';
        className = 'text-sm font-bold mb-4';
      } else if (content.type === 'h6') {
        Element = 'h6';
        className = 'text-xs font-bold mb-4';
      } else if (content.type === 'ul') {
        Element = 'ul';
        className = 'list-disc mb-4 pl-5';
      } else if (content.type === 'ol') {
        Element = 'ol';
        className = 'list-decimal mb-4 pl-5';
      } else if (content.type === 'li') {
        Element = 'li';
        className = 'mb-1';
      } else if (content.type === 'blockquote') {
        Element = 'blockquote';
        className = 'border-l-4 border-gray-300 pl-4 italic mb-4';
      }

      return (
        <Element className={className}>
          {content.children.map((child: any, i: number) => (
            <React.Fragment key={i}>
              {renderPayloadFormat(child, "")}
            </React.Fragment>
          ))}
        </Element>
      );
    }

    // Handle blocks content
    if (content.blocks && Array.isArray(content.blocks)) {
      return (
        <div className={`rich-text-blocks ${className}`}>
          {content.blocks.map((block: any, i: number) => (
            <div key={i} className="mb-4">
              {renderPayloadFormat(block, "")}
            </div>
          ))}
        </div>
      );
    }

    // Simple object with no recognizable format
    return <div className="rich-text-unknown">{JSON.stringify(content)}</div>;
  } catch (error) {
    console.error('Error rendering Payload format:', error);
    return <div className="rich-text-error">Error rendering content</div>
  }
}
