import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'
import { connectToDatabase } from '@/lib/mongodb'

export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the token and get the user ID
    const { userId } = await verifyJWT(token)

    if (!userId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get request body
    const body = await req.json()
    const { id } = body

    if (!id) {
      return NextResponse.json({ error: 'Request ID is required' }, { status: 400 })
    }

    // Try to update in MongoDB first
    try {
      const { db } = await connectToDatabase()

      // Get the user from MongoDB
      const mongoUser = await db.collection('users').findOne({ _id: userId })

      if (mongoUser) {
        // Verify user is a school admin
        const role = typeof mongoUser.role === 'object' ? mongoUser.role?.slug : mongoUser.role
        if (role !== 'school-admin' && role !== 'super-admin') {
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
        }

        // Get school ID
        const schoolId =
          typeof mongoUser.school === 'object' ? mongoUser.school?.id : mongoUser.school

        if (!schoolId) {
          return NextResponse.json(
            { error: 'School admin must be associated with a school' },
            { status: 400 },
          )
        }

        // Get the approval request
        const approvalRequest = await db.collection('approvalRequests').findOne({ _id: id })

        if (!approvalRequest) {
          return NextResponse.json({ error: 'Approval request not found' }, { status: 404 })
        }

        // Verify the request belongs to the school
        if (approvalRequest.schoolId !== schoolId) {
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
        }

        // Update the approval request
        await db.collection('approvalRequests').updateOne(
          { _id: id },
          {
            $set: {
              status: 'approved',
              approvedBy: userId,
              approvedAt: new Date().toISOString(),
            },
          },
        )

        // Process the approval based on the request type
        if (approvalRequest.type === 'article') {
          // Update article status
          await db.collection('articles').updateOne(
            { _id: approvalRequest.data.articleId },
            {
              $set: {
                status: 'published',
                publishedAt: new Date().toISOString(),
              },
            },
          )
        } else if (approvalRequest.type === 'user') {
          // Update user status
          await db.collection('users').updateOne(
            { _id: approvalRequest.data.userId },
            {
              $set: {
                status: 'active',
                approvedBy: userId,
                approvedAt: new Date().toISOString(),
              },
            },
          )
        }

        return NextResponse.json({ success: true })
      }
    } catch (mongoError) {
      console.warn('Error approving request in MongoDB, falling back to Payload:', mongoError)
    }

    // Fallback to Payload CMS if MongoDB fails
    const payload = await getPayload({ config })

    // Get the current user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })

    // Verify user is a school admin
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'school-admin' && role !== 'super-admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get school ID
    const schoolId = typeof user.school === 'object' ? user.school?.id : user.school

    if (!schoolId) {
      return NextResponse.json(
        { error: 'School admin must be associated with a school' },
        { status: 400 },
      )
    }

    // Try to check if the approvalRequests collection exists
    let approvalRequest = null

    try {
      // Use any to bypass TypeScript checking for custom collections
      const payloadAny = payload as any

      // Try to access the collection schema
      const schema = payloadAny.collections?.approvalRequests?.config?.fields
      const hasApprovalRequests = !!schema

      if (hasApprovalRequests) {
        // Get the approval request
        approvalRequest = await payloadAny.findByID({
          collection: 'approvalRequests',
          id,
        })
      } else {
        console.log('approvalRequests collection does not exist in Payload CMS')
        return NextResponse.json(
          {
            error: 'Approval system is not configured. Please contact the administrator.',
          },
          { status: 404 },
        )
      }
    } catch (error) {
      console.error('Error checking for approvalRequests collection:', error)
      return NextResponse.json(
        {
          error: 'Error accessing approval system. Please contact the administrator.',
        },
        { status: 500 },
      )
    }

    if (!approvalRequest) {
      return NextResponse.json({ error: 'Approval request not found' }, { status: 404 })
    }

    // Verify the request belongs to the school
    if (approvalRequest.schoolId !== schoolId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    try {
      // Update the approval request
      const payloadAny = payload as any
      await payloadAny.update({
        collection: 'approvalRequests',
        id,
        data: {
          status: 'approved',
          approvedBy: userId,
          approvedAt: new Date().toISOString(),
        },
      })
    } catch (error) {
      console.error('Error updating approval request:', error)
      // Continue with the process even if updating the approval request fails
      // This allows us to still update the article or user status
    }

    // Process the approval based on the request type
    if (approvalRequest.type === 'article') {
      // Update article status
      await payload.update({
        collection: 'articles',
        id: approvalRequest.data.articleId,
        data: {
          status: 'published',
          publishedAt: new Date().toISOString(),
        },
      })
    } else if (approvalRequest.type === 'user') {
      // Update user status
      await payload.update({
        collection: 'users',
        id: approvalRequest.data.userId,
        data: {
          status: 'active',
          approvedBy: userId,
          approvedAt: new Date().toISOString(),
        },
      })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error approving request:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
