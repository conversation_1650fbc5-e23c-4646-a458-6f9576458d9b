'use client'

import React, { useState, useEffect, useRef } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { TypewriterText } from './TypewriterText'

interface HeroSlide {
  title: string
  description: string
  imageUrl: string
  buttonText: string
  buttonLink: string
  secondaryButtonText?: string
  secondaryButtonLink?: string
}

interface HeroCarouselProps {
  slides: HeroSlide[]
}

export const HeroCarousel: React.FC<HeroCarouselProps> = ({ slides }) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)
  const [typingComplete, setTypingComplete] = useState(false)
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null)

  // Reset typing state when slide changes
  useEffect(() => {
    setTypingComplete(false)
  }, [currentIndex])

  // Auto-play functionality
  useEffect(() => {
    // Only start the timer when typing is complete
    if (typingComplete) {
      const play = () => {
        autoPlayRef.current = setTimeout(() => {
          nextSlide()
        }, 4000) // Change slide 4 seconds after typing completes
      }

      play()

      return () => {
        if (autoPlayRef.current) {
          clearTimeout(autoPlayRef.current)
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentIndex, typingComplete])

  const nextSlide = () => {
    if (isAnimating) return

    setIsAnimating(true)
    setCurrentIndex((prevIndex) => (prevIndex === slides.length - 1 ? 0 : prevIndex + 1))

    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }

  const prevSlide = () => {
    if (isAnimating) return

    setIsAnimating(true)
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? slides.length - 1 : prevIndex - 1))

    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }

  const goToSlide = (index: number) => {
    if (isAnimating || index === currentIndex) return

    setIsAnimating(true)
    setCurrentIndex(index)

    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }

  const handleTypingComplete = () => {
    setTypingComplete(true)
  }

  return (
    <div className="relative w-full h-[100vh] overflow-hidden">
      {/* Slides */}
      {slides.map((slide, index) => (
        <div
          key={index}
          className={`absolute inset-0 w-full h-full transition-opacity duration-1000 ease-in-out ${
            index === currentIndex ? 'opacity-100 z-10' : 'opacity-0 z-0'
          }`}
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${slide.imageUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        >
          <div className="absolute inset-0 flex flex-col items-center justify-center text-white p-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 animate-fadeIn">
                <TypewriterText text={slide.title} speed={40} delay={300} />
              </h1>
              <p className="text-lg md:text-xl mb-8 max-w-2xl mx-auto animate-fadeIn animation-delay-200">
                <TypewriterText
                  text={slide.description}
                  speed={20}
                  delay={1000}
                  onComplete={handleTypingComplete}
                />
              </p>
              {/* <div className="flex gap-4 justify-center">
                <Button asChild size="lg" className="animate-fadeIn animation-delay-400">
                  <Link href={slide.buttonLink}>{slide.buttonText}</Link>
                </Button>

                {slide.secondaryButtonText && slide.secondaryButtonLink && (
                  <Button
                    asChild
                    size="lg"
                    variant="outline"
                    className="animate-fadeIn animation-delay-500 bg-white/10 backdrop-blur-sm hover:bg-white/20"
                  >
                    <Link href={slide.secondaryButtonLink}>{slide.secondaryButtonText}</Link>
                  </Button>
                )}
              </div> */}
            </div>
          </div>
        </div>
      ))}

      {/* Navigation Arrows */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute left-4 top-1/2 -translate-y-1/2 z-20 text-white hover:bg-black/20 h-12 w-12 rounded-full"
        onClick={prevSlide}
        aria-label="الشريحة السابقة"
      >
        <ChevronLeft className="h-8 w-8" />
      </Button>

      <Button
        variant="ghost"
        size="icon"
        className="absolute right-4 top-1/2 -translate-y-1/2 z-20 text-white hover:bg-black/20 h-12 w-12 rounded-full"
        onClick={nextSlide}
        aria-label="الشريحة التالية"
      >
        <ChevronRight className="h-8 w-8" />
      </Button>

      {/* Indicators */}
      <div className="absolute bottom-6 left-0 right-0 z-20 flex justify-center space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentIndex ? 'bg-white w-8' : 'bg-white/50 hover:bg-white/80'
            }`}
            aria-label={`انتقل إلى الشريحة ${index + 1}`}
          />
        ))}
      </div>
    </div>
  )
}
