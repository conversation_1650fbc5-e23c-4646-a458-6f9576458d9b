import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

import config from '@/payload.config'

// GET: Fetch reports for school admin
export async function GET(req: NextRequest) {
  try {
    console.log('Reports API called');
    
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    // Also check for token in Authorization header
    const authHeader = req.headers.get('Authorization')
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null

    // Use token from cookie or header
    const finalToken = token || headerToken

    if (!finalToken) {
      console.log('No token found');
      return NextResponse.json(
        { success: false, error: 'Unauthorized: No token provided' },
        { status: 401 }
      )
    }

    // Verify token to get user info - with detailed logging
    let userId: string | null = null;
    let userRole: string | null = null;
    let decodedToken: any = null;
    
    try {
      const JWT_SECRET = process.env.PAYLOAD_SECRET || 'default-secret'
      decodedToken = jwt.verify(finalToken, JWT_SECRET)
      console.log('JWT decoded:', JSON.stringify(decodedToken, null, 2));
      
      // Extract userId
      userId = typeof decodedToken === 'object' ? (decodedToken.id || decodedToken.sub || null) : null;
      
      // Very flexible role extraction logic
      if (typeof decodedToken === 'object') {
        if (decodedToken.role) {
          if (typeof decodedToken.role === 'object') {
            // Handle role as object with different possible structures
            userRole = decodedToken.role.value || decodedToken.role.slug || 
                       decodedToken.role.name || decodedToken.role.role || null;
          } else {
            // Handle role as string
            userRole = decodedToken.role;
          }
        } else if (decodedToken.roles && Array.isArray(decodedToken.roles) && decodedToken.roles.length > 0) {
          // Handle roles as array
          const role = decodedToken.roles[0];
          if (typeof role === 'object') {
            userRole = role.value || role.slug || role.name || role.role || null;
          } else {
            userRole = role;
          }
        }
        
        // Additional fallback checks for more token formats
        if (!userRole && decodedToken.collection === 'users' && decodedToken.doc) {
          if (typeof decodedToken.doc === 'object' && decodedToken.doc.role) {
            if (typeof decodedToken.doc.role === 'object') {
              userRole = decodedToken.doc.role.slug || decodedToken.doc.role.value || null;
            } else {
              userRole = decodedToken.doc.role;
            }
          }
        }
      }
      
      console.log('Extracted user info:', { userId, userRole });
      
      // For development, allow override with query params
      const url = new URL(req.url);
      const debugRole = url.searchParams.get('debugRole');
      if (debugRole && process.env.NODE_ENV === 'development') {
        console.log('Using debug role override:', debugRole);
        userRole = debugRole;
      }
      
    } catch (err: any) {
      console.error('Error verifying token:', err)
      return NextResponse.json(
        { success: false, error: 'Unauthorized: Invalid token', details: err.message },
        { status: 401 }
      )
    }

    // Initialize PayloadCMS early to use for role lookup
    const payload = await getPayload({ config })
    
    // If no role in token, try to get it from the database
    if (!userRole && userId) {
      try {
        console.log('No role in token, fetching from database for user ID:', userId);
        const userFromDB = await payload.findByID({
          collection: 'users',
          id: userId,
        });
        
        console.log('User from DB:', userFromDB ? JSON.stringify({
          id: userFromDB.id,
          role: userFromDB.role
        }) : 'null');
        
        if (userFromDB && userFromDB.role) {
          if (typeof userFromDB.role === 'object') {
            userRole = userFromDB.role.slug || 
                      ((userFromDB.role as any).value ? String((userFromDB.role as any).value) : null);
          } else {
            userRole = String(userFromDB.role);
          }
          console.log('Role from database:', userRole);
        }
      } catch (err) {
        console.error('Error fetching user from database:', err);
      }
    }

    // Only allow school admin and super admin
    if (!userRole) {
      console.log('No role found in token or database');
      return NextResponse.json(
        { 
          success: false, 
          error: 'Unauthorized: No role found in token or database', 
          tokenData: decodedToken ? JSON.stringify(decodedToken) : 'none' 
        },
        { status: 403 }
      )
    }
    
    // Check if the role is allowed - very flexible check
    const allowedRoles = ['school-admin', 'super-admin', 'admin', 'schoolAdmin', 'superAdmin', 'teacher', 'school_admin'];
    const normalizedRole = userRole.toLowerCase();
    const isAllowed = allowedRoles.some(role => 
      normalizedRole.includes(role.toLowerCase()) || 
      role.toLowerCase().includes(normalizedRole)
    );
    
    // For debugging, log normalized role and check result
    console.log('Role check:', {
      normalizedRole,
      allowedRoles,
      isAllowed,
      checkResults: allowedRoles.map(role => ({
        role,
        normalizedRoleIncludesRole: normalizedRole.includes(role.toLowerCase()),
        roleIncludesNormalizedRole: role.toLowerCase().includes(normalizedRole)
      }))
    });
    
    if (!isAllowed) {
      console.log('Role not allowed:', userRole);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Unauthorized: Insufficient permissions', 
          role: userRole,
          normalizedRole,
          allowedRoles: allowedRoles
        },
        { status: 403 }
      )
    }

    // Build query based on user role
    let query = {}
    let schoolId = null;

    // For school admin type roles - get their school
    if (normalizedRole.includes('school') && userId) {
      try {
        // Get the school admin details to find their schoolId
        const currentUser = await payload.findByID({
          collection: 'users',
          id: userId, // userId will never be null here because of the condition above
        })
        
        console.log('Found user:', currentUser ? 
          JSON.stringify({
            id: currentUser.id,
            role: currentUser.role,
            school: currentUser.school
          }) : 'null');

        if (!currentUser || !currentUser.school) {
          return NextResponse.json(
            { success: false, error: 'School information not found for this admin' },
            { status: 404 }
          )
        }

        schoolId = typeof currentUser.school === 'object' 
          ? currentUser.school.id 
          : currentUser.school

        console.log('School admin school ID:', schoolId)
        
        // Don't filter by school for super admins
        if (!normalizedRole.includes('super')) {
          // Filter reports by the authorSchool field 
          query = {
            authorSchool: {
              equals: schoolId
            }
          }
        }
      } catch (err: any) {
        console.error('Error fetching school admin details:', err)
        return NextResponse.json(
          { success: false, error: 'Error fetching school information', details: err.message },
          { status: 500 }
        )
      }
    }

    console.log('Fetching reports with query:', JSON.stringify(query))

    // Fetch reports from PayloadCMS
    // Note: We'll cast the type as any for now since TS is having issues with the collection type
    const reportsResponse = await payload.find({
      collection: 'reports' as any,
      where: query,
      sort: '-createdAt',
      limit: 100,
      depth: 0, // No need to populate relationships for the list view
    })
    
    console.log(`Found ${reportsResponse.docs.length} reports in PayloadCMS collection`);
    
    // Also check MongoDB for legacy reports
    let mongoReports: any[] = [];
    try {
      const { db } = await connectToDatabase();
      
      // Build MongoDB query based on role
      let mongoQuery: any = {};
      if (normalizedRole.includes('school') && schoolId && !normalizedRole.includes('super')) {
        // Check for direct authorSchool field first
        try {
          // Direct authorSchool query
          const directReports = await db.collection('article_reports').find({
            authorSchool: schoolId
          }).limit(100).toArray();
          
          console.log(`Found ${directReports.length} reports with direct authorSchool match`);
          mongoReports = directReports;
          
          if (directReports.length === 0) {
            // Try with ObjectId
            try {
              const objIdReports = await db.collection('article_reports').find({
                'authorSchool.$oid': schoolId
              }).limit(100).toArray();
              
              console.log(`Found ${objIdReports.length} reports with authorSchool.$oid match`);
              mongoReports = objIdReports;
            } catch (e) {
              console.error('Error searching by authorSchool.$oid', e);
            }
          }
          
          if (mongoReports.length === 0) {
            // If no reports found, try more complex query by finding users of that school
            console.log('No reports found with direct school match, trying author lookup');
            
            // For school admin, we need to find authors from their school
            // This is a complex query that requires multiple steps
            const schoolUsers = await db.collection('users').find({
              school: schoolId
            }).toArray();
            
            // Extract user IDs with type assertion
            const schoolUserIds: string[] = [];
            schoolUsers.forEach(user => {
              if (user && user._id) {
                if (typeof user._id === 'string') {
                  schoolUserIds.push(user._id);
                } else if (user._id.toString) {
                  schoolUserIds.push(user._id.toString());
                }
              }
            });
            
            console.log(`Found ${schoolUserIds.length} users in school ${schoolId}`);
            
            // Then filter reports by these author IDs
            if (schoolUserIds.length > 0) {
              mongoQuery = { 
                author: { 
                  $in: [
                    ...schoolUserIds,
                    ...schoolUserIds.map(id => {
                      try { return new ObjectId(id); } catch(e) { return id; }
                    })
                  ]
                }
              };
              
              const authorReports = await db.collection('article_reports').find(mongoQuery).limit(100).toArray();
              console.log(`Found ${authorReports.length} reports by author ID lookup`);
              mongoReports = [...mongoReports, ...authorReports];
            }
          }
        } catch (e) {
          console.error('Error in complex school query', e);
        }
      } else {
        // Super admin gets all reports
        mongoReports = await db.collection('article_reports').find({}).limit(100).toArray();
        console.log(`Super admin: Found ${mongoReports.length} reports in MongoDB`);
      }
    } catch (error: any) {
      console.error('Error fetching MongoDB reports:', error);
    }
    
    // Combine reports from PayloadCMS and MongoDB
    const allReports = [
      ...reportsResponse.docs,
      ...mongoReports
    ];

    console.log(`Combined ${allReports.length} total reports`);
    
    // Format reports for frontend (using any to bypass strict typing)
    const formattedReports = allReports.map((report: any) => {
      try {
        // For MongoDB legacy records without id field but with _id
        const id = report.id || (report._id ? (typeof report._id === 'string' ? report._id : report._id.toString()) : null);
        
        return {
          id: id,
          articleId: report.article || (report.articleId ? report.articleId : null), // This is the ID reference to the article
          articleTitle: report.articleTitle || 'Untitled Report',
          authorId: report.author, // This is the ID reference to the author
          authorSchool: report.authorSchool, // School ID for reference
          reportedBy: report.reportedBy, // This is the ID reference to the user who reported
          reason: report.reason || 'No reason provided',
          status: report.status || 'pending',
          resolved: report.resolved || false,
          createdAt: report.createdAt || new Date().toISOString(),
        };
      } catch (e) {
        console.error('Error formatting report', e, report);
        return null;
      }
    }).filter(report => report !== null); // Filter out any invalid reports

    return NextResponse.json({
      success: true,
      reports: formattedReports,
    })
  } catch (error: any) {
    console.error('Error fetching reports:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message, stack: error.stack },
      { status: 500 }
    )
  }
} 