import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      const userId = typeof decoded === 'object' ? decoded.id : null
      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const schoolId = url.searchParams.get('schoolId')

      // Get the payload instance
      const payload = await getPayload({ config })
      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })
      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()
        const query: any = {}
        if (schoolId) query.schoolId = schoolId
        const feedback = await db.collection('feedback').find(query).toArray()
        if (feedback.length > 0) {
          return NextResponse.json({
            feedback: feedback.map((item: any) => ({
              id: item._id.toString(),
              type: item.type,
              subject: item.subject,
              message: item.message,
              status: item.status,
              priority: item.priority,
              schoolId: item.schoolId,
              schoolName: item.schoolName,
              submitterId: item.submitterId,
              submitterName: item.submitterName,
              submitterRole: item.submitterRole,
              dateSubmitted: item.dateSubmitted,
              lastUpdated: item.lastUpdated,
              response: item.response,
            })),
          })
        }
      } catch (mongoError) {
        console.warn('Error fetching feedback from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails or is empty
      try {
        const where: any = {}
        if (schoolId) where['school'] = { equals: schoolId }
        const result = await payload.find({
          collection: 'systemicIssues',
          where,
          limit: 100,
        })
        if (result.docs.length > 0) {
          return NextResponse.json({
            feedback: result.docs.map((item: any) => ({
              id: item.id,
              type: item.type,
              subject: item.title,
              message: item.description,
              status: item.status,
              priority: item.severity,
              schoolId: typeof item.school === 'object' ? item.school?.id : item.school,
              schoolName: typeof item.school === 'object' ? item.school?.name : '',
              submitterId: typeof item.reporter === 'object' ? item.reporter?.id : item.reporter,
              submitterName: typeof item.reporter === 'object' ? `${item.reporter?.firstName || ''} ${item.reporter?.lastName || ''}`.trim() : '',
              submitterRole: typeof item.reporter === 'object' ? (item.reporter?.role?.slug || item.reporter?.role || '') : '',
              dateSubmitted: item.createdAt,
              lastUpdated: item.updatedAt,
              response: item.resolution,
            })),
          })
        }
      } catch (payloadError) {
        console.warn('Error fetching feedback from Payload CMS:', payloadError)
      }

      // If no data, return empty array (no mock data unless you want it)
      return NextResponse.json({ feedback: [] })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching feedback:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// Helper function to handle Payload CMS feedback updates
async function handlePayloadFeedback(payload: any, feedbackId: string, action: string, body: any) {
  try {
    const existingPayloadFeedback = await payload.findByID({
      collection: 'systemicIssues',
      id: feedbackId,
    });
    
    if (!existingPayloadFeedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }
    
    // Update the feedback in Payload CMS
    if (action === 'status') {
      await payload.update({
        collection: 'systemicIssues',
        id: feedbackId,
        data: {
          status: body.status,
        },
      });
    } else if (action === 'response') {
      await payload.update({
        collection: 'systemicIssues',
        id: feedbackId,
        data: {
          resolution: body.response,
          status: body.status,
        },
      });
    }
    
    // Get the updated feedback item
    const updatedFeedback = await payload.findByID({
      collection: 'systemicIssues',
      id: feedbackId,
    });
    
    if (!updatedFeedback) {
      return NextResponse.json({ error: 'Failed to retrieve updated feedback' }, { status: 500 });
    }
    
    // Format the response
    const formattedFeedback = {
      id: updatedFeedback.id,
      type: updatedFeedback.type,
      subject: updatedFeedback.title,
      message: updatedFeedback.description,
      status: updatedFeedback.status,
      priority: updatedFeedback.severity,
      schoolId: typeof updatedFeedback.school === 'object' ? updatedFeedback.school?.id : updatedFeedback.school,
      schoolName: typeof updatedFeedback.school === 'object' ? updatedFeedback.school?.name : '',
      submitterId: typeof updatedFeedback.reporter === 'object' ? updatedFeedback.reporter?.id : updatedFeedback.reporter,
      submitterName: typeof updatedFeedback.reporter === 'object' ? 
        `${updatedFeedback.reporter?.firstName || ''} ${updatedFeedback.reporter?.lastName || ''}`.trim() : '',
      submitterRole: typeof updatedFeedback.reporter === 'object' && updatedFeedback.reporter.role ? 
        (typeof updatedFeedback.reporter.role === 'object' ? updatedFeedback.reporter.role.slug : updatedFeedback.reporter.role) : '',
      dateSubmitted: updatedFeedback.createdAt,
      lastUpdated: updatedFeedback.updatedAt,
      response: updatedFeedback.resolution,
    };
    
    return NextResponse.json({ feedback: formattedFeedback });
  } catch (payloadError) {
    console.error('Error updating feedback in Payload CMS:', payloadError);
    return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
  }
}

// Define a dynamic route handler for feedback status and response updates
export async function PATCH(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      const userId = typeof decoded === 'object' ? decoded.id : null
      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })
      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })
      
      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Extract feedback ID from the URL path
      const url = new URL(req.url)
      const pathParts = url.pathname.split('/')
      const feedbackId = pathParts[pathParts.length - 2]
      const action = pathParts[pathParts.length - 1]

      // Parse the request body
      const body = await req.json()
      
      // Get the feedback item from MongoDB
      const { db } = await connectToDatabase()
      const feedbackCollection = db.collection('feedback')
      
      try {
        // Try to convert feedbackId to ObjectId
        let objectId: ObjectId
        try {
          objectId = new ObjectId(feedbackId)
        } catch (e) {
          // If feedbackId is not a valid ObjectId, try to find it in Payload CMS
          return handlePayloadFeedback(payload, feedbackId, action, body)
        }
        
        // Check if feedback exists in MongoDB
        const existingFeedback = await feedbackCollection.findOne({ _id: objectId })
        
        if (!existingFeedback) {
          // Try to find it in Payload CMS if not in MongoDB
          return handlePayloadFeedback(payload, feedbackId, action, body)
        }
        
        // Update fields based on action
        const updateData: Record<string, any> = {
          lastUpdated: new Date().toISOString(),
        }
        
        if (action === 'status') {
          updateData.status = body.status
        } else if (action === 'response') {
          updateData.response = body.response
          if (body.status) {
            updateData.status = body.status
          }
        } else {
          return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
        }
        
        // Update the feedback in MongoDB
        const result = await feedbackCollection.updateOne(
          { _id: objectId },
          { $set: updateData }
        )
        
        if (result.modifiedCount === 0) {
          return NextResponse.json({ error: 'Failed to update feedback' }, { status: 500 })
        }
        
        // Get the updated feedback item
        const updatedFeedback = await feedbackCollection.findOne({ _id: objectId })
        
        if (!updatedFeedback) {
          return NextResponse.json({ error: 'Failed to retrieve updated feedback' }, { status: 500 })
        }
        
        // Format the response
        const formattedFeedback = {
          id: updatedFeedback._id.toString(),
          type: updatedFeedback.type,
          subject: updatedFeedback.subject,
          message: updatedFeedback.message,
          status: updatedFeedback.status,
          priority: updatedFeedback.priority,
          schoolId: updatedFeedback.schoolId,
          schoolName: updatedFeedback.schoolName,
          submitterId: updatedFeedback.submitterId,
          submitterName: updatedFeedback.submitterName,
          submitterRole: updatedFeedback.submitterRole,
          dateSubmitted: updatedFeedback.dateSubmitted,
          lastUpdated: updatedFeedback.lastUpdated,
          response: updatedFeedback.response,
        }
        
        return NextResponse.json({ feedback: formattedFeedback })
      } catch (dbError) {
        console.error('Database error:', dbError)
        return NextResponse.json({ error: 'Database error' }, { status: 500 })
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating feedback:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 