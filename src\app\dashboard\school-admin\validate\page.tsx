'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Loader2, CheckCircle, XCircle, AlertTriangle } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import SchoolLeaderboard from '@/components/dashboard/SchoolLeaderboard'
import PendingApprovalsOverview from '@/components/dashboard/PendingApprovalsOverview'
import MentorReviewQualityScores from '@/components/dashboard/MentorReviewQualityScores'
import ResourceUtilizationMetrics from '@/components/dashboard/ResourceUtilizationMetrics'
import StudentDataTable from '@/components/dashboard/StudentDataTable'
import TeacherPerformanceMetrics from '@/components/dashboard/TeacherPerformanceMetrics'
import MentorFeedbackSummaries from '@/components/dashboard/MentorFeedbackSummaries'

export default function SchoolAdminValidationPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [user, setUser] = useState<any>(null)
  const [schoolId, setSchoolId] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('dashboard')
  const [validationResults, setValidationResults] = useState<Record<string, boolean>>({})
  const [isValidating, setIsValidating] = useState(false)

  useEffect(() => {
    async function fetchUserData() {
      try {
        const response = await fetch('/api/auth/me')
        if (!response.ok) {
          throw new Error('Failed to fetch user data')
        }

        const userData = await response.json()
        setUser(userData)

        // Get school ID
        if (userData.school) {
          const schoolId =
            typeof userData.school === 'object' ? userData.school.id : userData.school
          setSchoolId(schoolId)
        }

        setIsLoading(false)
      } catch (error) {
        console.error('Error fetching user data:', error)
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [])

  const runValidation = async () => {
    setIsValidating(true)

    // Simulate validation checks
    const results: Record<string, boolean> = {}

    // Check school-specific leaderboard
    try {
      const leaderboardResponse = await fetch(
        `/api/dashboard/school-admin/leaderboard?schoolId=${schoolId}`,
      )
      results['school-specific-leaderboard'] = leaderboardResponse.ok
    } catch (error) {
      results['school-specific-leaderboard'] = false
    }

    // Check pending approvals overview
    try {
      const approvalsResponse = await fetch(
        `/api/dashboard/school-admin/pending-approvals?schoolId=${schoolId}`,
      )
      results['pending-approvals-overview'] = approvalsResponse.ok
    } catch (error) {
      results['pending-approvals-overview'] = false
    }

    // Check mentor review quality scores
    try {
      const mentorReviewsResponse = await fetch(
        `/api/dashboard/school-admin/mentor-reviews?schoolId=${schoolId}`,
      )
      results['mentor-review-quality-scores'] = mentorReviewsResponse.ok
    } catch (error) {
      results['mentor-review-quality-scores'] = false
    }

    // Check resource utilization metrics
    try {
      const resourceMetricsResponse = await fetch(
        `/api/dashboard/school-admin/resource-metrics?schoolId=${schoolId}`,
      )
      results['resource-utilization-metrics'] = resourceMetricsResponse.ok
    } catch (error) {
      results['resource-utilization-metrics'] = false
    }

    // Check students data visibility
    try {
      const studentsResponse = await fetch(
        `/api/dashboard/school-admin/students?schoolId=${schoolId}`,
      )
      results['students-data-visibility'] = studentsResponse.ok
    } catch (error) {
      results['students-data-visibility'] = false
    }

    // Check teacher performance metrics
    try {
      const teacherPerformanceResponse = await fetch(
        `/api/dashboard/school-admin/teacher-performance?schoolId=${schoolId}`,
      )
      results['teacher-performance-metrics'] = teacherPerformanceResponse.ok
    } catch (error) {
      results['teacher-performance-metrics'] = false
    }

    // Check mentor feedback summaries
    try {
      const mentorFeedbackResponse = await fetch(
        `/api/dashboard/school-admin/mentor-feedback?schoolId=${schoolId}`,
      )
      results['mentor-feedback-summaries'] = mentorFeedbackResponse.ok
    } catch (error) {
      results['mentor-feedback-summaries'] = false
    }

    setValidationResults(results)
    setIsValidating(false)

    // Show toast with validation results
    const successCount = Object.values(results).filter(Boolean).length
    const totalChecks = Object.keys(results).length

    toast({
      title: `Validation Complete: ${successCount}/${totalChecks} checks passed`,
      description: `${successCount} out of ${totalChecks} validation checks passed successfully.`,
      variant: successCount === totalChecks ? 'default' : 'destructive',
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading...</span>
      </div>
    )
  }

  if (!user || (user.role !== 'school-admin' && user.role !== 'super-admin')) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
            <h2 className="text-xl font-bold mb-2">Access Denied</h2>
            <p className="text-center text-muted-foreground mb-4">
              You need school admin privileges to access this page.
            </p>
            <Button onClick={() => router.push('/dashboard')}>Return to Dashboard</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">School Admin Dashboard Validation</h1>
          <p className="text-gray-500">Validate school admin dashboard features and permissions</p>
        </div>
        <Button onClick={runValidation} disabled={isValidating}>
          {isValidating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Validating...
            </>
          ) : (
            'Run Validation'
          )}
        </Button>
      </div>

      {Object.keys(validationResults).length > 0 && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Validation Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(validationResults).map(([key, passed]) => (
                <div key={key} className="flex items-center p-3 border rounded-md">
                  {passed ? (
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <span className={passed ? 'text-green-700' : 'text-red-700'}>
                    {key
                      .split('-')
                      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                      .join(' ')}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="mb-4">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="visibility">Data Visibility</TabsTrigger>
          <TabsTrigger value="actions">Admin Actions</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <SchoolLeaderboard schoolId={schoolId} />
            <PendingApprovalsOverview schoolId={schoolId} />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <MentorReviewQualityScores schoolId={schoolId} />
            <ResourceUtilizationMetrics schoolId={schoolId} />
          </div>
        </TabsContent>

        <TabsContent value="visibility" className="space-y-6">
          <StudentDataTable schoolId={schoolId} />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TeacherPerformanceMetrics schoolId={schoolId} />
            <MentorFeedbackSummaries schoolId={schoolId} />
          </div>
        </TabsContent>

        <TabsContent value="actions" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Assign Mentors/Teachers</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 mb-4">
                  Test assigning a mentor or teacher to a class or student group.
                </p>
                <Button className="w-full">Test Assignment</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Escalate Content Issues</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 mb-4">
                  Flag an Article for super-admin review and verify notification.
                </p>
                <Button className="w-full">Test Escalation</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Generate School Reports</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 mb-4">
                  Export a report limited to school-specific data.
                </p>
                <Button className="w-full">Generate Report</Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
