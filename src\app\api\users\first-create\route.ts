import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    const payload = await getPayload({
      config,
    })

    const { email, password } = await req.json()

    if (!email || !password) {
      return NextResponse.json(
        { message: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Check if there are any existing users
    const existingUsers = await payload.find({
      collection: 'users',
      limit: 1,
    })

    if (existingUsers.docs.length > 0) {
      return NextResponse.json(
        { message: 'First user already exists' },
        { status: 400 }
      )
    }

    // Create a role for the admin
    let superAdminRole
    try {
      // Check if the role already exists
      const existingRoles = await payload.find({
        collection: 'roles',
        where: {
          slug: {
            equals: 'super-admin',
          },
        },
      })

      if (existingRoles.docs.length > 0) {
        superAdminRole = existingRoles.docs[0]
      } else {
        // Create the role
        superAdminRole = await payload.create({
          collection: 'roles',
          data: {
            name: 'Super Admin',
            slug: 'super-admin',
            description: 'Full access to all features and settings',
          },
        })
      }
    } catch (error) {
      console.error('Error creating role:', error)
      return NextResponse.json(
        { message: 'Failed to create role' },
        { status: 500 }
      )
    }

    // Create a school
    let demoSchool
    try {
      // Check if the school already exists
      const existingSchools = await payload.find({
        collection: 'schools',
        where: {
          name: {
            equals: 'Demo High School',
          },
        },
      })

      if (existingSchools.docs.length > 0) {
        demoSchool = existingSchools.docs[0]
      } else {
        // Create the school
        demoSchool = await payload.create({
          collection: 'schools',
          data: {
            name: 'Demo High School',
            address: '123 Education Lane, Learning City, LC 12345',
          },
        })
      }
    } catch (error) {
      console.error('Error creating school:', error)
      return NextResponse.json(
        { message: 'Failed to create school' },
        { status: 500 }
      )
    }

    // Create the admin user
    try {
      const user = await payload.create({
        collection: 'users',
        data: {
          email,
          password,
          name: 'Admin User',
          role: superAdminRole.id,
          school: demoSchool.id,
        },
      })

      // Update the school with the admin
      await payload.update({
        collection: 'schools',
        id: demoSchool.id,
        data: {
          admin: user.id,
        },
      })

      return NextResponse.json({ success: true })
    } catch (error) {
      console.error('Error creating user:', error)
      return NextResponse.json(
        { message: 'Failed to create user' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Server error:', error)
    return NextResponse.json(
      { message: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
