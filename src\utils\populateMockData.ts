import { Payload } from 'payload';
import { addUserPoints, updatePointsAndRankings } from './points';

/**
 * Populates the database with test data
 * This includes:
 * - Adding random points to users
 * - Creating sample statistics
 */
export const populateMockData = async (payload: Payload): Promise<boolean> => {
  try {
    console.log('Starting to populate mock data...');
    
    // Get users by role
    const students = await payload.find({
      collection: 'users',
      where: {
        'role.slug': {
          equals: 'student'
        }
      },
      limit: 100,
    });
    
    const teachers = await payload.find({
      collection: 'users',
      where: {
        'role.slug': {
          equals: 'teacher'
        }
      },
      limit: 100,
    });
    
    const mentors = await payload.find({
      collection: 'users',
      where: {
        'role.slug': {
          equals: 'mentor'
        }
      },
      limit: 100,
    });
    
    console.log(`Found ${students.docs.length} students, ${teachers.docs.length} teachers, and ${mentors.docs.length} mentors`);
    
    // Add random points to each user
    const addPointsPromises = [];
    
    // Add points to students
    for (const student of students.docs) {
      const pointsToAdd = Math.floor(Math.random() * 500);
      addPointsPromises.push(
        addUserPoints({
          payload,
          userId: student.id,
          type: 'article_created',
          points: pointsToAdd,
          description: 'Test points for student'
        })
      );
    }
    
    // Add points to teachers
    for (const teacher of teachers.docs) {
      const pointsToAdd = Math.floor(Math.random() * 800);
      addPointsPromises.push(
        addUserPoints({
          payload,
          userId: teacher.id,
          type: 'review_submitted',
          points: pointsToAdd,
          description: 'Test points for teacher'
        })
      );
    }
    
    // Add points to mentors
    for (const mentor of mentors.docs) {
      const pointsToAdd = Math.floor(Math.random() * 1000);
      addPointsPromises.push(
        addUserPoints({
          payload,
          userId: mentor.id,
          type: 'achievement_earned',
          points: pointsToAdd,
          description: 'Test points for mentor'
        })
      );
    }
    
    await Promise.all(addPointsPromises);
    console.log('Added points to all users');
    
    // Update rankings
    await updatePointsAndRankings({ payload });
    console.log('Updated rankings');
    
    // Check if we need to create summary statistics
    const statDoc = await payload.find({
      collection: 'statistics',
      where: {
        type: {
          equals: 'summary'
        }
      }
    });
    
    if (statDoc.docs.length === 0) {
      // Create a summary statistics document
      await payload.create({
        collection: 'statistics',
        data: {
          name: 'Platform Summary',
          type: 'summary',
          data: {
            totalUsers: students.docs.length + teachers.docs.length + mentors.docs.length,
            activeUsers: Math.floor((students.docs.length + teachers.docs.length + mentors.docs.length) * 0.8),
            totalArticles: Math.floor(students.docs.length * 3),
            totalNews: Math.floor(mentors.docs.length * 4),
            averagePointsPerUser: 250,
          },
          lastUpdated: new Date().toISOString()
        }
      });
      console.log('Created summary statistics document');
    }
    
    console.log('Mock data population completed successfully');
    return true;
  } catch (error) {
    console.error('Error populating mock data:', error);
    return false;
  }
}; 