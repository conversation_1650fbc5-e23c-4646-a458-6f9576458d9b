// A simple script to test user creation with super-admin role
const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

async function testUserCreation() {
  // Connect to MongoDB
  const client = new MongoClient(process.env.DATABASE_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const usersCollection = db.collection('users');
    const rolesCollection = db.collection('roles');
    
    // Find super-admin role
    const superAdminRole = await rolesCollection.findOne({ slug: 'super-admin' });
    
    if (!superAdminRole) {
      console.log('Super admin role not found. Creating it...');
      const result = await rolesCollection.insertOne({
        name: 'Super Admin',
        slug: 'super-admin',
        permissions: ['full-access'],
        description: 'Full access to all features and settings',
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      console.log(`Super admin role created with ID: ${result.insertedId}`);
      superAdminRoleId = result.insertedId;
    } else {
      console.log(`Found super admin role with ID: ${superAdminRole._id}`);
      superAdminRoleId = superAdminRole._id;
    }
    
    // Create a test super admin user without a school
    const testUser = {
      email: '<EMAIL>',
      name: 'Test Admin',
      role: superAdminRoleId,
      // No school field
      createdAt: new Date(),
      updatedAt: new Date(),
      // Add a simple password hash (this is just for testing)
      hash: 'somehash',
      salt: 'somesalt'
    };
    
    const existingUser = await usersCollection.findOne({ email: testUser.email });
    
    if (existingUser) {
      console.log(`User ${testUser.email} already exists. Updating...`);
      await usersCollection.updateOne(
        { email: testUser.email },
        { $set: testUser }
      );
      console.log('User updated successfully');
    } else {
      console.log(`Creating new user ${testUser.email}...`);
      const result = await usersCollection.insertOne(testUser);
      console.log(`User created with ID: ${result.insertedId}`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

testUserCreation().catch(console.error);
