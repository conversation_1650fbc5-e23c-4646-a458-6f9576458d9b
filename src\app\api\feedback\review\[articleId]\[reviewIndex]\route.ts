import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { ObjectId } from 'mongodb'

import config from '@/payload.config'

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ articleId: string; reviewIndex: string }> },
) {
  try {
    const resolvedParams = await params
    const { articleId, reviewIndex } = resolvedParams
    const reviewIdx = parseInt(reviewIndex, 10)

    const payload = await getPayload({
      config,
    })

    // Get the current user using cookies instead of payload.auth
    const { cookies } = await import('next/headers')
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json(
        { error: 'You must be logged in to review feedback' },
        { status: 401 },
      )
    }

    // Extract user from token
    const { extractUserFromToken } = await import('@/lib/security')
    const user = await extractUserFromToken(token)

    if (!user) {
      return NextResponse.json({ error: 'Invalid authentication token' }, { status: 401 })
    }

    // Check if user is a mentor
    const isMentor =
      typeof user.role === 'object' && user.role !== null
        ? (user.role as any).slug === 'mentor'
        : user.role === 'mentor'

    if (!isMentor) {
      return NextResponse.json(
        { error: 'Only mentors can review teacher feedback' },
        { status: 403 },
      )
    }

    // Get the article
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
    })

    // Check if article is published
    if (article.status !== 'published') {
      return NextResponse.json(
        { error: 'Only published articles can have feedback reviewed' },
        { status: 400 },
      )
    }

    // Check if the review index is valid
    if (!article.teacherReview || !article.teacherReview[reviewIdx]) {
      return NextResponse.json({ error: 'Invalid review index' }, { status: 400 })
    }

    // Check if this mentor has already reviewed this specific teacher review
    const currentReview = article.teacherReview[reviewIdx] as any
    if (currentReview.mentorReviewedBy && currentReview.mentorReviewedBy === user.id) {
      return NextResponse.json(
        { error: 'You have already reviewed this teacher feedback' },
        { status: 400 },
      )
    }

    const formData = await req.formData()
    const rating = parseInt(formData.get('rating') as string, 10)
    const comment = formData.get('comment') as string

    // Validate inputs
    if (!rating || !comment) {
      return NextResponse.json({ error: 'Rating and comment are required' }, { status: 400 })
    }

    if (rating < 1 || rating > 10) {
      return NextResponse.json({ error: 'Rating must be between 1 and 10' }, { status: 400 })
    }

    // Update the teacher review with mentor feedback
    const updatedTeacherReview = [...article.teacherReview]
    updatedTeacherReview[reviewIdx] = {
      ...updatedTeacherReview[reviewIdx],
      // Use a different approach since mentorFeedback might not be in the type
      mentorRating: rating,
      mentorComment: comment,
      mentorReviewedAt: new Date().toISOString(),
      mentorReviewedBy: user.id,
    } as any

    // Update the article using direct MongoDB to bypass validation issues
    // This approach is proven to work from the teacher dashboard implementation
    try {
      console.log('Using direct MongoDB update to bypass validation issues')
      const mongo = payload.db.collections.articles

      if (mongo) {
        await mongo.updateOne(
          { _id: new ObjectId(articleId) },
          {
            $set: {
              [`teacherReview.${reviewIdx}.mentorRating`]: rating,
              [`teacherReview.${reviewIdx}.mentorComment`]: comment,
              [`teacherReview.${reviewIdx}.mentorReviewedAt`]: new Date().toISOString(),
              [`teacherReview.${reviewIdx}.mentorReviewedBy`]: user.id,
            },
          },
        )
        console.log('Direct MongoDB update succeeded')
      } else {
        throw new Error('MongoDB collection not available')
      }
    } catch (mongoError) {
      console.error('MongoDB direct update failed, trying Payload update:', mongoError)

      // Fallback to Payload update with minimal data
      await payload.update({
        collection: 'articles',
        id: articleId,
        data: {
          teacherReview: updatedTeacherReview,
        },
      })
    }

    // Create a record in mentor-reviews collection using direct MongoDB
    const evaluationResult = rating >= 5 ? 'approve' : 'reject'
    const teacherId =
      typeof updatedTeacherReview[reviewIdx].reviewer === 'object'
        ? updatedTeacherReview[reviewIdx].reviewer.id
        : updatedTeacherReview[reviewIdx].reviewer

    try {
      console.log('Creating mentor review record using direct MongoDB - v2')
      // Access the raw MongoDB collection
      const db = payload.db.connection.db
      if (!db) {
        throw new Error('Database connection not available')
      }
      const mentorReviewsCollection = db.collection('mentorReviews')

      await mentorReviewsCollection.insertOne({
        mentor: new ObjectId(user.id || ''),
        article: new ObjectId(articleId),
        teacherReviewIndex: reviewIdx,
        teacher: new ObjectId(teacherId),
        evaluationResult: evaluationResult,
        comment: comment,
        timestamp: new Date(),
        school: typeof user.school === 'string' ? new ObjectId(user.school) : undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      console.log('Direct MongoDB mentor review creation succeeded')
    } catch (mongoError) {
      console.error('MongoDB mentor review creation failed, skipping:', mongoError)
      // Continue without creating the mentor review record if it fails
      // The main functionality (updating the article) already succeeded
    }

    // Award points to the mentor (5 points per evaluation)
    try {
      // Use direct user update instead of the points utility to avoid import issues
      const currentUser = await payload.findByID({
        collection: 'users',
        id: user.id || '',
      })

      const currentPoints = currentUser.points || 0
      const newTotalPoints = currentPoints + 5

      await payload.update({
        collection: 'users',
        id: user.id || '',
        data: {
          points: newTotalPoints,
          pointsActivities: [
            ...(currentUser.pointsActivities || []),
            {
              type: 'other',
              points: 5,
              description: `Completed review for teacher feedback on article "${article.title || 'Untitled'}"`,
              reference: {
                collection: 'articles',
                id: articleId,
                reviewIndex: reviewIdx,
              },
              timestamp: new Date().toISOString(),
            } as any,
          ],
        },
      })
      console.log('Mentor points awarded successfully: 5 points')

      // Create activity record for mentor review
      try {
        await payload.create({
          collection: 'activities',
          data: {
            userId: user.id || '',
            activityType: 'article-review',
            details: {
              action: 'mentor_review',
              articleId: articleId,
              articleTitle: article.title || 'Untitled',
              teacherReviewIndex: reviewIdx,
              rating: rating,
              comment: comment,
            },
            school:
              typeof user.school === 'string'
                ? user.school
                : typeof user.school === 'object' && user.school
                  ? (user.school as any).id
                  : undefined,
            points: 5,
          },
        })
        console.log('Activity record created for mentor review')
      } catch (activityError) {
        console.error('Error creating activity record:', activityError)
      }

      // Create notification for mentor
      try {
        await payload.create({
          collection: 'notifications',
          data: {
            user: user.id || '',
            message: `لقد ربحت 5 نقاط لمراجعة تقييم المعلم على مقال "${article.title || 'بدون عنوان'}"`,
            type: 'success',
            read: false,
            details: {
              pointsActivity: 'mentor_review_completed',
              articleId: articleId,
              pointsChange: 5,
            },
          },
        })
        console.log('Notification created for mentor')
      } catch (notificationError) {
        console.error('Error creating notification:', notificationError)
      }
    } catch (pointsError) {
      console.error('Error awarding points to mentor:', pointsError)
      // Continue without failing the entire operation
    }

    // Update teacher review quality counts and adjust points if thresholds are met
    const teacher = await payload.findByID({
      collection: 'users',
      id: teacherId,
    })
    const currentPositiveCount = (teacher.stats && (teacher.stats as any).positiveReviewCount) || 0
    const currentNegativeCount = (teacher.stats && (teacher.stats as any).negativeReviewCount) || 0
    const teacherStatsUpdate =
      evaluationResult === 'approve'
        ? { positiveReviewCount: currentPositiveCount + 1 }
        : { negativeReviewCount: currentNegativeCount + 1 }
    await payload.update({
      collection: 'users',
      id: teacherId,
      data: {
        stats: {
          ...(teacher.stats || {}),
          ...(teacherStatsUpdate as any),
        },
      },
    })

    // Check if thresholds are met for teacher points adjustment
    // Teachers get +10 points for every 10 good reviews and -10 points for every 10 bad reviews
    const newPositiveCount =
      evaluationResult === 'approve' ? currentPositiveCount + 1 : currentPositiveCount
    const newNegativeCount =
      evaluationResult === 'reject' ? currentNegativeCount + 1 : currentNegativeCount

    if (newPositiveCount > 0 && newPositiveCount % 10 === 0) {
      try {
        // Award 10 points to teacher for positive reviews
        const currentTeacherPoints = teacher.points || 0
        const newTeacherPoints = currentTeacherPoints + 10

        await payload.update({
          collection: 'users',
          id: teacherId,
          data: {
            points: newTeacherPoints,
            pointsActivities: [
              ...(teacher.pointsActivities || []),
              {
                type: 'other',
                points: 10,
                description: `Earned 10 points for receiving ${newPositiveCount} positive mentor reviews`,
                reference: {
                  collection: 'users',
                  id: teacherId,
                  milestone: newPositiveCount,
                },
                timestamp: new Date().toISOString(),
              } as any,
            ],
          },
        })
        console.log(`Teacher ${teacherId} awarded 10 points for positive reviews`)
      } catch (pointsError) {
        console.error('Error awarding positive points to teacher:', pointsError)
      }
    } else if (newNegativeCount > 0 && newNegativeCount % 10 === 0) {
      try {
        // Deduct 10 points from teacher for negative reviews
        const currentTeacherPoints = teacher.points || 0
        const newTeacherPoints = Math.max(0, currentTeacherPoints - 10) // Don't go below 0

        await payload.update({
          collection: 'users',
          id: teacherId,
          data: {
            points: newTeacherPoints,
            pointsActivities: [
              ...(teacher.pointsActivities || []),
              {
                type: 'other',
                points: -10,
                description: `Lost 10 points for receiving ${newNegativeCount} negative mentor reviews`,
                reference: {
                  collection: 'users',
                  id: teacherId,
                  milestone: newNegativeCount,
                },
                timestamp: new Date().toISOString(),
              } as any,
            ],
          },
        })
        console.log(`Teacher ${teacherId} lost 10 points for negative reviews`)
      } catch (pointsError) {
        console.error('Error deducting points from teacher:', pointsError)
      }
    }

    // Check if this is an API call (from modal) or form submission
    const url = new URL(req.url)
    const isApiCall = url.searchParams.get('api') === 'true'

    if (isApiCall) {
      // Return JSON response for API calls
      return NextResponse.json({
        success: true,
        message: 'Teacher review feedback submitted successfully',
      })
    } else {
      // Redirect for form submissions
      const baseUrl = req.headers.get('origin') || 'http://localhost:3000'
      return NextResponse.redirect(new URL('/dashboard/mentor', baseUrl))
    }
  } catch (error) {
    console.error('Error reviewing feedback:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
