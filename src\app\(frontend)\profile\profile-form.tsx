'use client'

import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

import { updateProfile } from './actions'

interface ProfileFormProps {
  user: any
}

export const ProfileForm: React.FC<ProfileFormProps> = ({ user }) => {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: user.name || '',
    email: user.email || '',
    emailNotifications: user.notificationPreferences?.emailNotifications !== false,
    emailTypes: user.notificationPreferences?.emailTypes || [
      'articleSubmissions',
      'articleReviews',
      'reviewEvaluations',
    ],
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement
      setFormData({
        ...formData,
        [name]: checkbox.checked,
      })
    } else if (name === 'emailTypes') {
      const select = e.target as HTMLSelectElement
      const options = Array.from(select.options)
        .filter(option => option.selected)
        .map(option => option.value)
      
      setFormData({
        ...formData,
        emailTypes: options,
      })
    } else {
      setFormData({
        ...formData,
        [name]: value,
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setMessage('')
    setError('')

    try {
      const result = await updateProfile({
        name: formData.name,
        email: formData.email,
        notificationPreferences: {
          emailNotifications: formData.emailNotifications,
          emailTypes: formData.emailTypes,
        },
      })

      if (result.success) {
        setMessage('Profile updated successfully')
        router.refresh()
      } else {
        setError(result.error || 'Failed to update profile')
      }
    } catch (err) {
      setError('An error occurred while updating your profile')
      console.error(err)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h2 className="text-2xl font-bold mb-6">Personal Information</h2>
      
      {message && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {message}
        </div>
      )}
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
            required
          />
        </div>
        
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
            required
          />
        </div>
      </div>
      
      <div className="border-t border-gray-200 pt-6 mt-6">
        <h2 className="text-2xl font-bold mb-6">Notification Preferences</h2>
        
        <div className="space-y-4">
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                name="emailNotifications"
                checked={formData.emailNotifications}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600"
              />
              <span className="ml-2 text-gray-700">Receive email notifications</span>
            </label>
          </div>
          
          {formData.emailNotifications && (
            <div>
              <label htmlFor="emailTypes" className="block text-sm font-medium text-gray-700 mb-1">
                Email notification types
              </label>
              <select
                id="emailTypes"
                name="emailTypes"
                multiple
                value={formData.emailTypes}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded h-32"
              >
                <option value="articleSubmissions">Article Submissions</option>
                <option value="articleReviews">Article Reviews</option>
                <option value="reviewEvaluations">Review Evaluations</option>
                <option value="systemAnnouncements">System Announcements</option>
              </select>
              <p className="text-sm text-gray-500 mt-1">
                Hold Ctrl (or Cmd on Mac) to select multiple options
              </p>
            </div>
          )}
        </div>
      </div>
      
      <div className="border-t border-gray-200 pt-6 mt-6">
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition disabled:opacity-50"
          >
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </form>
  )
}
