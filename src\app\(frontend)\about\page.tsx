import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import Image from 'next/image'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { PageLayout } from '@/components/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { BookOpen, Users, School } from 'lucide-react'

export default async function AboutPage() {
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  // Authenticate but we don't need the user object for this page
  await payload.auth({ headers })

  return (
    <PageLayout
      bgImage="bg-[url(https://images.unsplash.com/photo-1633360821222-7e8df83639fb?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)]"
      title="عن المراسل الشاب"
      subtitle="تعرف على مهمتنا وكيف نمكن الصحفيين الطلاب"
      bgColor="bg-violet-700"
    >
      {/* About Content */}
      <div className="py-12 px-4 bg-muted/30 p-8 max-w-7xl mx-auto">
        <div className="container mx-auto">
          <div className="mb-12 overflow-hidden rounded-lg  ">
            <div className="flex flex-col md:flex-row">
              {/* Text Side */}
              <div className="w-full md:w-1/2 p-8 flex flex-col justify-center py-12">
                <h2 className="text-5xl  font-bold mb-6 text-primary">مهمتنا</h2>
                <p className="text-lg mb-4">
                  المراسل الشاب مكرس لتمكين الصحفيين الطلاب من خلال توفير منصة لهم لمشاركة أصواتهم
                  وتطوير مهاراتهم في الكتابة والحصول على تعليقات قيمة من المعلمين والموجهين ذوي
                  الخبرة.
                </p>
                <p className="text-lg mb-4">
                  نؤمن أن كل طالب لديه منظور فريد وقصة يرويها. مهمتنا هي رعاية هذه الأصوات ومساعدتها
                  في الوصول إلى جمهور أوسع.
                </p>
                <p className="text-lg">
                  من خلال منصتنا، نهدف إلى إنشاء الجيل القادم من الصحفيين المتفكرين والماهرين
                  والواثقين الذين يمكنهم إحداث تأثير إيجابي على مجتمعاتهم وما بعدها.
                </p>
              </div>

              {/* Image Side */}
              <div className="w-full md:w-1/2 relative min-h-[300px] md:min-h-0">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent z-10"></div>
                <Image
                  src="/media/2 (4) (2) - Copy.png"
                  alt="صحفيون شباب يعملون معاً"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                  priority
                  className="object-contain"
                  style={{ objectPosition: 'center' }}
                />
              </div>
            </div>
          </div>

          <h2 className="text-3xl font-bold mb-8 text-center">كيف يعمل</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <Card className="border-t-4 border-primary overflow-hidden transition-all duration-200 hover:shadow-lg">
              <CardHeader className="text-center pb-2">
                <BookOpen className="w-12 h-12 mx-auto text-primary mb-4" />
                <CardTitle>للطلاب</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                  <li>اكتب وقدم مقالات حول المواضيع التي تشغف بها</li>
                  <li>احصل على تعليقات وتقييمات من المعلمين</li>
                  <li>انشر عملك وشاركه مع جمهور أوسع</li>
                  <li>ابن محفظة من الأعمال المنشورة</li>
                  <li>تنافس للحصول على أفضل المراكز في لوحة متصدري الطلاب</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-t-4 border-green-500 overflow-hidden transition-all duration-200 hover:shadow-lg">
              <CardHeader className="text-center pb-2">
                <Users className="w-12 h-12 mx-auto text-green-500 mb-4" />
                <CardTitle>للمعلمين</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                  <li>راجع مقالات الطلاب وقدم تعليقات بناءة</li>
                  <li>قيم المقالات واعتمدها للنشر</li>
                  <li>ساعد الطلاب على تطوير مهاراتهم في الكتابة والصحافة</li>
                  <li>تتبع تأثيرك من خلال لوحة متصدري المعلمين</li>
                  <li>تعاون مع المعلمين الآخرين لتحسين نتائج الطلاب</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-t-4 border-amber-500 overflow-hidden transition-all duration-200 hover:shadow-lg">
              <CardHeader className="text-center pb-2">
                <School className="w-12 h-12 mx-auto text-amber-500 mb-4" />
                <CardTitle>للمدارس</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                  <li>اعرض أفضل أعمال طلابك</li>
                  <li>عزز ثقافة الكتابة والصحافة</li>
                  <li>تتبع أداء مدرستك في لوحة المتصدرين</li>
                  <li>تواصل مع المدارس الأخرى وشارك أفضل الممارسات</li>
                  <li>احتفل بإنجازات الطلاب وتقدمهم</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <Card className="mb-12">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold mb-6">فريقنا</h2>
              <p className="text-lg mb-6">
                تأسس المراسل الشاب من قبل فريق من المعلمين والصحفيين والتقنيين الذين يؤمنون بقوة
                أصوات الطلاب. تم تصميم منصتنا لخلق بيئة داعمة حيث يمكن للطلاب تطوير مهاراتهم في
                الكتابة والحصول على تعليقات بناءة وبناء الثقة في قدراتهم.
              </p>
            </CardContent>
          </Card>

          <Card className="mb-12">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold mb-6">شارك معنا</h2>
              <p className="text-lg mb-6">
                سواء كنت طالباً تتطلع لمشاركة صوتك، أو معلماً يريد توجيه الكتاب الشباب، أو مدير
                مدرسة مهتم بجلب المراسل الشاب إلى مؤسستك، نحن نحب أن نسمع منك. تواصل معنا لتعرف
                المزيد حول كيفية مشاركتك.
              </p>

              <div className="mt-8 text-center">
                <Button asChild size="lg" className="px-8">
                  <Link href="/contact">تواصل معنا</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  )
}
