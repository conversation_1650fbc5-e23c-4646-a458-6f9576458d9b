'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { formatDistanceToNow } from 'date-fns'
import { AlertCircle } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface TeacherReview {
  id: string
  comment: string
  rating: number
  approved: boolean
  reviewDate: string
  articleId: string
  articleTitle: string
  reviewerId?: string
}

export default function FeedbackInbox() {
  const [reviews, setReviews] = useState<TeacherReview[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function fetchReviews() {
      try {
        setIsLoading(true)
        const response = await fetch('/api/feedback/inbox', {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache',
          },
        })

        if (!response.ok) {
          throw new Error('Failed to fetch feedback')
        }

        console.log('Feedback response status:', response.status)

        const data = await response.json()
        setReviews(data.reviews || [])
      } catch (err) {
        console.error('Error fetching feedback:', err)
        setError('تعذر تحميل التعليقات. يرجى المحاولة مرة أخرى لاحقًا.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchReviews()
  }, [])

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch (error) {
      return 'تاريخ غير معروف'
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>صندوق الملاحظات</CardTitle>
          <CardDescription>تعليقات المعلم على مقالاتك</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>صندوق الملاحظات</CardTitle>
          <CardDescription>تعليقات المعلم على مقالاتك</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded">
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>صندوق الملاحظات</CardTitle>
        <CardDescription>تعليقات المعلم على مقالاتك</CardDescription>
      </CardHeader>
      <CardContent>
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4 ml-2" />
          <AlertTitle>نظام الملاحظات المجهول</AlertTitle>
          <AlertDescription>
            يعرض هذا الصندوق ملاحظات مجهولة من المعلمين على مقالاتك. يتم الحفاظ على خصوصية هويات المعلمين، تمامًا كما تكون هويات الطلاب مجهولة عند نشر المقالات. هذه قناة اتصال في اتجاه واحد - لا يمكنك الرد مباشرة على هذه الملاحظات. إذا كانت لديك أسئلة، يرجى التحدث مع منسق المعلمين الخاص بك.
          </AlertDescription>
        </Alert>

        {reviews.length === 0 ? (
          <div dir="rtl" className="text-center py-8 text-gray-500 dark:text-gray-400">
            لم يتم تلقي أي ملاحظات بعد. قم بتقديم مقالات للمراجعة للحصول على الملاحظات.
          </div>
        ) : (
          <div className="space-y-4">
            {reviews.map((review) => (
              <div key={review.id} className="border rounded-lg overflow-hidden" dir="rtl">
                <div className="p-4 bg-gray-50 dark:bg-gray-800 flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">{review.articleTitle}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      تمت المراجعة {formatDate(review.reviewDate)}
                    </p>
                  </div>
                  <Badge
                    className={
                      review.approved
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                        : 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
                    }
                  >
                    {review.approved ? 'تمت الموافقة' : 'يحتاج إلى مراجعة'}
                  </Badge>
                </div>
                <div className="p-4">
                  <div className="mb-2">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      التقييم:
                    </span>{' '}
                    <span className="font-medium">{review.rating}/10</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      الملاحظات:
                    </span>
                    <p className="mt-1">{review.comment}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
