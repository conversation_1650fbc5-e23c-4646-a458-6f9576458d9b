import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Try to get statistics from MongoDB first
    try {
      const { db } = await connectToDatabase()

      // Get counts from different collections
      const totalUsers = await db.collection('users').countDocuments()
      const totalArticles = await db.collection('articles').countDocuments()
      const totalSchools = await db.collection('schools').countDocuments()

      // Get article counts by month
      const currentYear = new Date().getFullYear()
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ]

      // Get articles created in each month of the current year
      const articlesByMonth = await Promise.all(
        months.map(async (month, index) => {
          const startDate = new Date(currentYear, index, 1)
          const endDate = new Date(currentYear, index + 1, 0)

          const count = await db.collection('articles').countDocuments({
            createdAt: {
              $gte: startDate.toISOString(),
              $lte: endDate.toISOString(),
            },
          })

          return { name: month, count }
        }),
      )

      // Get user counts by role
      const userRoleCounts = await db
        .collection('users')
        .aggregate([{ $group: { _id: '$role', count: { $sum: 1 } } }])
        .toArray()

      const usersByRole = userRoleCounts.map((item) => {
        let name = item._id
        if (typeof name === 'object' && name.slug) {
          name = name.slug
        }

        // Format the role name
        switch (name) {
          case 'super-admin':
          case 'school-admin':
            name = 'Admins'
            break
          case 'teacher':
            name = 'Teachers'
            break
          case 'mentor':
            name = 'Mentors'
            break
          case 'student':
            name = 'Students'
            break
        }

        return { name, value: item.count }
      })

      if (totalUsers > 0 || totalArticles > 0 || totalSchools > 0) {
        console.log('Fetched statistics from MongoDB')
        return NextResponse.json({
          totalUsers,
          totalArticles,
          totalSchools,
          articlesByMonth,
          usersByRole,
        })
      }
    } catch (mongoError) {
      console.warn('Error fetching statistics from MongoDB, falling back to Payload:', mongoError)
    }

    // Fallback to Payload CMS if MongoDB fails
    const payload = await getPayload({ config })

    // Get counts from different collections
    const usersResponse = await payload.find({
      collection: 'users',
      limit: 0,
    })

    const articlesResponse = await payload.find({
      collection: 'articles',
      limit: 0,
    })

    const schoolsResponse = await payload.find({
      collection: 'schools',
      limit: 0,
    })

    // Mock data for article counts by month
    const articlesByMonth = [
      { name: 'Jan', count: 12 },
      { name: 'Feb', count: 19 },
      { name: 'Mar', count: 15 },
      { name: 'Apr', count: 22 },
      { name: 'May', count: 28 },
      { name: 'Jun', count: 24 },
    ]

    // Mock data for user counts by role
    const usersByRole = [
      { name: 'Students', value: 120 },
      { name: 'Teachers', value: 45 },
      { name: 'Mentors', value: 15 },
      { name: 'Admins', value: 5 },
    ]

    return NextResponse.json({
      totalUsers: usersResponse.totalDocs,
      totalArticles: articlesResponse.totalDocs,
      totalSchools: schoolsResponse.totalDocs,
      articlesByMonth,
      usersByRole,
    })
  } catch (error) {
    console.error('Error fetching statistics:', error)
    return NextResponse.json({ error: 'Failed to fetch statistics' }, { status: 500 })
  }
}
