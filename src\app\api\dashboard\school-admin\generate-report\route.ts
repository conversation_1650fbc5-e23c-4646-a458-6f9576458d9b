import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'
import { verifyJWT } from '@/lib/auth'
import { stringify } from 'csv-stringify/sync'
import jsPDF from 'jspdf'

export async function POST(req: NextRequest) {
  try {
    // Parse request body to get format preference
    const body = await req.json().catch(() => ({}))
    const format = (body.format === 'csv' ? 'csv' : 'pdf') as 'csv' | 'pdf'
    // Add new parameter to determine if this is a global report (for super-admin only)
    const isGlobalReport = body.global === true

    console.log('[Generate Report API] Requested format:', format)
    console.log('[Generate Report API] Global report:', isGlobalReport ? 'yes' : 'no')

    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token using the existing verifyJWT helper
      const { userId } = await verifyJWT(token)

      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      console.log('[Generate Report API] User ID from token:', userId)

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the user from MongoDB
        const mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })
        console.log('[Generate Report API] MongoDB user found:', mongoUser ? 'yes' : 'no')

        if (mongoUser) {
          // Extract user role - handle multiple formats
          let userRole = null
          console.log('[Generate Report API] User role raw:', mongoUser.role)

          if (mongoUser.role && typeof mongoUser.role === 'object' && mongoUser.role.slug) {
            userRole = mongoUser.role.slug
            console.log('[Generate Report API] Role from object.slug:', userRole)
          } else if (typeof mongoUser.role === 'string') {
            // Look up the role by ID
            try {
              const roleDoc = await db
                .collection('roles')
                .findOne({ _id: new ObjectId(mongoUser.role) })
              userRole = roleDoc?.slug
              console.log('[Generate Report API] Role from string lookup:', userRole)
            } catch (e) {
              console.error('[Generate Report API] Error looking up role from string:', e)
              userRole = mongoUser.role
            }
          } else if (mongoUser.role && typeof mongoUser.role === 'object') {
            // Handle ObjectId or EJSON format
            try {
              if (mongoUser.role.$oid) {
                const roleDoc = await db
                  .collection('roles')
                  .findOne({ _id: new ObjectId(mongoUser.role.$oid) })
                userRole = roleDoc?.slug
                console.log('[Generate Report API] Role from $oid lookup:', userRole)
              } else if (
                mongoUser.role instanceof ObjectId ||
                (mongoUser.role.constructor && mongoUser.role.constructor.name === 'ObjectId')
              ) {
                const roleId = mongoUser.role.toString()
                const roleDoc = await db.collection('roles').findOne({ _id: new ObjectId(roleId) })
                userRole = roleDoc?.slug
                console.log('[Generate Report API] Role from ObjectId lookup:', userRole)
              } else if (mongoUser.role._id) {
                const roleIdValue =
                  mongoUser.role._id instanceof ObjectId
                    ? mongoUser.role._id.toString()
                    : mongoUser.role._id.$oid || mongoUser.role._id
                const roleDoc = await db
                  .collection('roles')
                  .findOne({ _id: new ObjectId(roleIdValue) })
                userRole = roleDoc?.slug
                console.log('[Generate Report API] Role from _id field lookup:', userRole)
              }
            } catch (e) {
              console.error('[Generate Report API] Error in role object lookup:', e)
            }
          }

          console.log('[Generate Report API] Final user role:', userRole)

          // Check if user is a school admin or super-admin
          if (userRole !== 'school-admin' && userRole !== 'super-admin') {
            console.log('[Generate Report API] Access denied - role not allowed:', userRole)
            return NextResponse.json(
              {
                error: 'Forbidden - Only school admins and super admins can generate reports',
                role: userRole,
              },
              { status: 403 },
            )
          }

          // Check if this is a global report request from a super-admin
          const isSuperAdmin = userRole === 'super-admin'
          
          if (isGlobalReport && !isSuperAdmin) {
            console.log('[Generate Report API] Global report requested by non-super-admin')
            return NextResponse.json(
              {
                error: 'Forbidden - Only super admins can generate global reports',
                role: userRole,
              },
              { status: 403 },
            )
          }

          // For school admins or super-admins generating a school-specific report, get the school ID
          let schoolId = null;
          let school = null;
          
          if (!isGlobalReport || userRole === 'school-admin') {
            console.log('[Generate Report API] School raw:', mongoUser.school)

            // Handle different ways school might be represented
            if (!mongoUser.school) {
              console.log('[Generate Report API] No school found for user')
              if (userRole === 'school-admin') {
                return NextResponse.json(
                  { error: 'School admin must be associated with a school' },
                  { status: 400 },
                )
              }
              // For super-admin without school, we'll create a global report later
            } else if (typeof mongoUser.school === 'string') {
              schoolId = mongoUser.school
              console.log('[Generate Report API] School ID from string:', schoolId)
            } else if (typeof mongoUser.school === 'object') {
              // Use 'any' type for MongoDB data
              const schoolObj = mongoUser.school as any

              // Check if it's in MongoDB EJSON format with $oid
              if (schoolObj.$oid) {
                schoolId = schoolObj.$oid
                console.log('[Generate Report API] School ID from $oid:', schoolId)
              } else if (schoolObj._id) {
                // Handle if it's a full embedded document
                const idField = schoolObj._id as any
                schoolId =
                  typeof idField === 'object' && idField.$oid ? idField.$oid : String(idField)
                console.log('[Generate Report API] School ID from _id field:', schoolId)
              } else if (schoolObj.id) {
                // Handle Payload format
                schoolId = schoolObj.id
                console.log('[Generate Report API] School ID from id field:', schoolId)
              }
            }

            console.log('[Generate Report API] Final school ID:', schoolId)

            if (!schoolId && userRole === 'school-admin') {
              return NextResponse.json(
                { error: 'School admin must be associated with a school' },
                { status: 400 },
              )
            }
            
            // If generating a school-specific report, get the school information
            if (schoolId) {
              school = await db.collection('schools').findOne({ _id: new ObjectId(schoolId) })

              if (!school) {
                console.log('[Generate Report API] School not found with ID:', schoolId)
                return NextResponse.json({ error: 'School not found' }, { status: 404 })
              }

              console.log('[Generate Report API] Found school:', school.name)
            }
          }
          
          // For global reports (super-admin only)
          if (isGlobalReport && isSuperAdmin) {
            console.log('[Generate Report API] Generating global report for super admin')
            
            // Get all schools
            const schools = await db.collection('schools').find({}).toArray()
            console.log('[Generate Report API] Found', schools.length, 'schools')
            
            // Total counts across all schools
            let totalUsers = 0
            let totalStudents = 0
            let totalTeachers = 0
            let totalMentors = 0
            let totalArticles = 0
            let totalPublishedArticles = 0
            
            // School-specific data for the report
            const schoolsData = []
            
            // Process each school
            for (const school of schools) {
              console.log('[Generate Report API] Processing school:', school.name)
              
              const schoolObjId = new ObjectId(school._id)
              
              // Queries for different user counts - try multiple school reference formats
              const userQueries = [
                { 'school._id': schoolObjId },
                { 'school.id': school._id.toString() },
                { school: schoolObjId },
              ]
              
              // Get counts for this school
              let schoolUsersCount = 0
              let schoolStudentsCount = 0
              let schoolTeachersCount = 0
              let schoolMentorsCount = 0
              let schoolArticlesCount = 0
              let schoolPublishedArticlesCount = 0
              
              // Users count for this school
              for (const query of userQueries) {
                const count = await db.collection('users').countDocuments(query)
                schoolUsersCount += count
              }
              
              // Students count for this school
              for (const query of userQueries) {
                const studentQuery1 = { ...query, 'role.slug': 'student' }
                const count1 = await db.collection('users').countDocuments(studentQuery1)
                schoolStudentsCount += count1
                
                try {
                  const studentRole = await db.collection('roles').findOne({ slug: 'student' })
                  if (studentRole) {
                    const studentQuery2 = { ...query, role: studentRole._id }
                    const count2 = await db.collection('users').countDocuments(studentQuery2)
                    schoolStudentsCount += count2
                  }
                } catch (e) {
                  console.error('[Generate Report API] Error in student role lookup:', e)
                }
              }
              
              // Teachers count for this school
              for (const query of userQueries) {
                const teacherQuery1 = { ...query, 'role.slug': 'teacher' }
                const count1 = await db.collection('users').countDocuments(teacherQuery1)
                schoolTeachersCount += count1
                
                try {
                  const teacherRole = await db.collection('roles').findOne({ slug: 'teacher' })
                  if (teacherRole) {
                    const teacherQuery2 = { ...query, role: teacherRole._id }
                    const count2 = await db.collection('users').countDocuments(teacherQuery2)
                    schoolTeachersCount += count2
                  }
                } catch (e) {
                  console.error('[Generate Report API] Error in teacher role lookup:', e)
                }
              }
              
              // Mentors count for this school
              for (const query of userQueries) {
                const mentorQuery1 = { ...query, 'role.slug': 'mentor' }
                const count1 = await db.collection('users').countDocuments(mentorQuery1)
                schoolMentorsCount += count1
                
                try {
                  const mentorRole = await db.collection('roles').findOne({ slug: 'mentor' })
                  if (mentorRole) {
                    const mentorQuery2 = { ...query, role: mentorRole._id }
                    const count2 = await db.collection('users').countDocuments(mentorQuery2)
                    schoolMentorsCount += count2
                  }
                } catch (e) {
                  console.error('[Generate Report API] Error in mentor role lookup:', e)
                }
              }
              
              // Articles count for this school
              const articleQueries = [
                { 'author.school._id': schoolObjId },
                { 'author.school.id': school._id.toString() },
                { 'school._id': schoolObjId },
                { 'school.id': school._id.toString() },
                { school: schoolObjId },
              ]
              
              for (const query of articleQueries) {
                const count = await db.collection('articles').countDocuments(query)
                schoolArticlesCount += count
              }
              
              // Published articles count for this school
              for (const query of articleQueries) {
                const publishedQuery = { ...query, status: 'published' }
                const count = await db.collection('articles').countDocuments(publishedQuery)
                schoolPublishedArticlesCount += count
              }
              
              // Add school data to the array
              schoolsData.push({
                name: school.name,
                address: school.address || 'No address provided',
                stats: {
                  users: schoolUsersCount,
                  students: schoolStudentsCount,
                  teachers: schoolTeachersCount,
                  mentors: schoolMentorsCount,
                  articles: schoolArticlesCount,
                  publishedArticles: schoolPublishedArticlesCount,
                  publicationRate: schoolArticlesCount > 0 
                    ? Math.round((schoolPublishedArticlesCount / schoolArticlesCount) * 100)
                    : 0,
                  studentTeacherRatio: schoolTeachersCount > 0 
                    ? (schoolStudentsCount / schoolTeachersCount).toFixed(2)
                    : 'N/A',
                  articlesPerStudent: schoolStudentsCount > 0
                    ? (schoolArticlesCount / schoolStudentsCount).toFixed(2)
                    : '0',
                }
              })
              
              // Add to platform totals
              totalUsers += schoolUsersCount
              totalStudents += schoolStudentsCount
              totalTeachers += schoolTeachersCount
              totalMentors += schoolMentorsCount
              totalArticles += schoolArticlesCount
              totalPublishedArticles += schoolPublishedArticlesCount
            }
            
            // Get recent platform activities (across all schools)
            const recentActivities = await db
              .collection('activities')
              .find({})
              .sort({ createdAt: -1 })
              .limit(20)
              .toArray();
            
            // Get feedback across all schools
            const feedback = await db
              .collection('feedback')
              .find({})
              .sort({ createdAt: -1 })
              .limit(10)
              .toArray();
            
            // Log the report generation
            try {
              await db.collection('activities').insertOne({
                userId: new ObjectId(userId),
                activityType: 'login',
                details: {
                  description: 'Generated global platform report',
                  reportDate: new Date().toISOString(),
                  format,
                },
                createdAt: new Date(),
                updatedAt: new Date(),
              })
              console.log('[Generate Report API] Global activity logged successfully')
            } catch (e) {
              console.error('[Generate Report API] Error logging global activity:', e)
            }
            
            // Prepare the global report data
            const globalReportData = {
              reportDate: new Date().toISOString(),
              platformStats: {
                totalSchools: schools.length,
                users: totalUsers,
                students: totalStudents,
                teachers: totalTeachers,
                mentors: totalMentors, 
                articles: totalArticles,
                publishedArticles: totalPublishedArticles,
                publicationRate: totalArticles > 0 
                  ? Math.round((totalPublishedArticles / totalArticles) * 100)
                  : 0,
                studentTeacherRatio: totalTeachers > 0
                  ? (totalStudents / totalTeachers).toFixed(2)
                  : 'N/A',
                articlesPerStudent: totalStudents > 0
                  ? (totalArticles / totalStudents).toFixed(2)
                  : '0',
              },
              schoolsData: schoolsData,
              recentActivities: recentActivities.map((activity) => ({
                type: activity.activityType,
                description: activity.details?.description || 'No description',
                date: activity.createdAt,
                school: activity.school 
                  ? typeof activity.school === 'object'
                    ? activity.school.name || 'Unknown School'
                    : 'Unknown School'
                  : 'Platform-wide',
              })),
              feedback: feedback.map((item) => ({
                rating: item.rating,
                comment: item.comment || 'No comment',
                date: item.createdAt,
                school: item.school
                  ? typeof item.school === 'object'
                    ? item.school.name || 'Unknown School'
                    : 'Unknown School'
                  : 'Anonymous',
              })),
            }
            
            // Generate the global report based on the requested format
            if (format === 'csv') {
              // Generate Global CSV Report
              console.log('[Generate Report API] Generating global CSV report')
              
              // Create CSV data structure for global report
              const csvData = [
                ['Young Reporter Platform - Global Report', ''],
                ['Generated on', new Date().toLocaleString()],
                [''],
                ['PLATFORM STATISTICS SUMMARY', ''],
                ['Total Schools', globalReportData.platformStats.totalSchools],
                ['Total Users', globalReportData.platformStats.users],
                ['Total Students', globalReportData.platformStats.students],
                ['Total Teachers', globalReportData.platformStats.teachers],
                ['Total Mentors', globalReportData.platformStats.mentors],
                ['Total Articles', globalReportData.platformStats.articles],
                ['Total Published Articles', globalReportData.platformStats.publishedArticles],
                ['Platform Publication Rate', globalReportData.platformStats.publicationRate + '%'],
                ['Platform Student-Teacher Ratio', globalReportData.platformStats.studentTeacherRatio],
                ['Platform Articles per Student', globalReportData.platformStats.articlesPerStudent],
                [''],
                ['SCHOOL BREAKDOWN', ''],
                ['School Name', 'Address', 'Users', 'Students', 'Teachers', 'Mentors', 'Articles', 'Published', 'Publication Rate', 'Student-Teacher Ratio'],
                ...globalReportData.schoolsData.map(school => [
                  school.name,
                  school.address,
                  school.stats.users,
                  school.stats.students,
                  school.stats.teachers,
                  school.stats.mentors,
                  school.stats.articles,
                  school.stats.publishedArticles,
                  school.stats.publicationRate + '%',
                  school.stats.studentTeacherRatio
                ]),
                [''],
                ['RECENT PLATFORM ACTIVITIES', ''],
                ['Type', 'Description', 'Date', 'School'],
                ...globalReportData.recentActivities.map((activity) => [
                  activity.type,
                  activity.description,
                  new Date(activity.date).toLocaleString(),
                  activity.school
                ]),
                [''],
                ['RECENT FEEDBACK', ''],
                ['Rating', 'Comment', 'Date', 'School'],
                ...globalReportData.feedback.map((item) => [
                  item.rating,
                  item.comment,
                  new Date(item.date).toLocaleString(),
                  item.school
                ]),
                [''],
                ['Report generated by Young Reporter Platform', new Date().toISOString().split('T')[0]],
                ['© Young Reporter ' + new Date().getFullYear(), '']
              ]
              
              // Convert to CSV string
              const csvContent = stringify(csvData)
              
              // Return global CSV file
              return new NextResponse(csvContent, {
                status: 200,
                headers: {
                  'Content-Type': 'text/csv',
                  'Content-Disposition': `attachment; filename="global-platform-report-${new Date().toISOString().split('T')[0]}.csv"`,
                },
              })
            } else {
              // Generate Global PDF Report
              console.log('[Generate Report API] Generating global PDF report')
              
              try {
                // Create PDF with jsPDF
                const doc = new jsPDF({
                  orientation: 'portrait',
                  unit: 'mm',
                  format: 'a4'
                })
                
                // Set font sizes and styles
                const titleSize = 20
                const headingSize = 16
                const subtitleSize = 14
                const normalSize = 12
                const smallSize = 10
                
                // Start positions
                let y = 20
                const margin = 15
                const lineHeight = 8
                const pageWidth = 210
                const textWidth = pageWidth - (margin * 2)
                
                // Colors
                const primaryColor: [number, number, number] = [41, 128, 185] // Blue
                const secondaryColor: [number, number, number] = [44, 62, 80] // Dark Blue/Gray
                const accentColor: [number, number, number] = [231, 76, 60]   // Red
                
                // Helper function for centered text
                const centeredText = (text: string, y: number, size: number, color: [number, number, number] = [0, 0, 0]) => {
                  doc.setFontSize(size)
                  doc.setTextColor(color[0], color[1], color[2])
                  doc.text(text, pageWidth / 2, y, { align: 'center' })
                  return y + lineHeight + (size / 10)
                }
                
                // Helper function for section headings
                const sectionHeading = (text: string, y: number, size: number = headingSize) => {
                  // Add a line above the heading
                  doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2])
                  doc.setLineWidth(0.5)
                  doc.line(margin, y - 3, pageWidth - margin, y - 3)
                  
                  // Add the heading text
                  doc.setFontSize(size)
                  doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2])
                  doc.setFont('helvetica', 'bold')
                  doc.text(text, margin, y)
                  
                  // Return the new Y position
                  doc.setFont('helvetica', 'normal')
                  doc.setTextColor(0, 0, 0)
                  return y + lineHeight + 2
                }
                
                // Helper function for adding text with proper line breaks
                const addText = (text: string, y: number, size: number, indent: number = 0) => {
                  doc.setFontSize(size)
                  const lines = doc.splitTextToSize(text, textWidth - indent)
                  doc.text(lines, margin + indent, y)
                  return y + (lines.length * lineHeight) + (size / 10)
                }
                
                // Helper function for table-like data
                const addDataRow = (label: string, value: string | number, y: number, size: number = normalSize) => {
                  doc.setFontSize(size)
                  doc.setFont('helvetica', 'bold')
                  doc.text(label, margin, y)
                  doc.setFont('helvetica', 'normal')
                  doc.text(String(value), margin + 100, y)
                  return y + lineHeight
                }
                
                // Helper function to check page break and add new page if needed
                const checkPageBreak = (y: number, reserveSpace: number = 30) => {
                  if (y > 270 - reserveSpace) {
                    doc.addPage()
                    return 20
                  }
                  return y
                }
                
                // Report header
                doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2])
                doc.setFontSize(titleSize)
                doc.setFont('helvetica', 'bold')
                doc.text("Young Reporter Platform", pageWidth / 2, y, { align: 'center' })
                y += lineHeight + 2

                // Report subtitle
                y = centeredText('Global Platform Report', y, subtitleSize, secondaryColor)
                
                // Date and reference info
                doc.setFont('helvetica', 'normal')
                doc.setFontSize(normalSize)
                doc.setTextColor(0, 0, 0)
                y = centeredText(`Generated on: ${new Date().toLocaleString()}`, y, smallSize)
                y += 5
                
                // Platform statistics section
                y = sectionHeading('Platform Statistics', y)
                y = addDataRow('Total Schools:', globalReportData.platformStats.totalSchools, y)
                y = addDataRow('Total Users:', globalReportData.platformStats.users, y)
                y = addDataRow('Total Students:', globalReportData.platformStats.students, y)
                y = addDataRow('Total Teachers:', globalReportData.platformStats.teachers, y)
                y = addDataRow('Total Mentors:', globalReportData.platformStats.mentors, y)
                y = addDataRow('Total Articles:', globalReportData.platformStats.articles, y)
                y = addDataRow('Published Articles:', globalReportData.platformStats.publishedArticles, y)
                y = addDataRow('Publication Rate:', globalReportData.platformStats.publicationRate + '%', y)
                y = addDataRow('Student-Teacher Ratio:', globalReportData.platformStats.studentTeacherRatio, y)
                y = addDataRow('Articles per Student:', globalReportData.platformStats.articlesPerStudent, y)
                y += lineHeight
                
                // Add School Breakdown section
                y = checkPageBreak(y, 100) // Make sure we have enough space for the table header
                y = sectionHeading('School Breakdown', y)
                
                if (globalReportData.schoolsData.length > 0) {
                  // Table header
                  doc.setFontSize(smallSize)
                  doc.setFont('helvetica', 'bold')
                  doc.text('School Name', margin, y)
                  doc.text('Students', margin + 80, y)
                  doc.text('Teachers', margin + 110, y)
                  doc.text('Articles', margin + 140, y)
                  doc.text('Pub. Rate', margin + 170, y)
                  y += lineHeight
                  
                  // Draw header underline
                  doc.setDrawColor(200, 200, 200)
                  doc.setLineWidth(0.2)
                  doc.line(margin, y - 3, margin + 180, y - 3)
                  
                  // Table content
                  doc.setFont('helvetica', 'normal')
                  for (const school of globalReportData.schoolsData) {
                    y = checkPageBreak(y)
                    doc.setFontSize(smallSize)
                    
                    // School name may need to be truncated
                    const schoolName = school.name.length > 30 
                      ? school.name.substring(0, 27) + '...' 
                      : school.name
                    
                    doc.text(schoolName, margin, y)
                    doc.text(String(school.stats.students), margin + 80, y)
                    doc.text(String(school.stats.teachers), margin + 110, y)
                    doc.text(String(school.stats.articles), margin + 140, y)
                    doc.text(school.stats.publicationRate + '%', margin + 170, y)
                    y += lineHeight
                  }
                } else {
                  y = addText('No schools data available', y, normalSize)
                }
                y += lineHeight
                
                // Add School Details Pages - one per school
                for (const school of globalReportData.schoolsData) {
                  // Start a new page for each school
                  doc.addPage()
                  y = 20
                  
                  // School header
                  doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2])
                  doc.setFontSize(titleSize)
                  doc.setFont('helvetica', 'bold')
                  doc.text(school.name, pageWidth / 2, y, { align: 'center' })
                  y += lineHeight + 5
                  
                  // School subtitle
                  doc.setFontSize(subtitleSize)
                  doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2])
                  doc.text('School Report', pageWidth / 2, y, { align: 'center' })
                  y += lineHeight + 2
                  
                  // School info
                  doc.setFont('helvetica', 'normal')
                  doc.setFontSize(normalSize)
                  doc.setTextColor(0, 0, 0)
                  
                  // School address
                  y = sectionHeading('School Information', y)
                  doc.text('Address:', margin, y)
                  y = addText(school.address, y, normalSize, 20)
                  y += lineHeight
                  
                  // School statistics
                  y = sectionHeading('School Statistics', y)
                  y = addDataRow('Users:', school.stats.users, y)
                  y = addDataRow('Students:', school.stats.students, y)
                  y = addDataRow('Teachers:', school.stats.teachers, y)
                  y = addDataRow('Mentors:', school.stats.mentors, y)
                  y = addDataRow('Articles:', school.stats.articles, y)
                  y = addDataRow('Published Articles:', school.stats.publishedArticles, y)
                  y = addDataRow('Publication Rate:', school.stats.publicationRate + '%', y)
                  y = addDataRow('Student-Teacher Ratio:', school.stats.studentTeacherRatio, y)
                  y = addDataRow('Articles per Student:', school.stats.articlesPerStudent, y)
                }
                
                // Add Recent Activities page
                doc.addPage()
                y = 20
                
                // Activities header
                doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2])
                doc.setFontSize(titleSize)
                doc.setFont('helvetica', 'bold')
                doc.text('Platform Activities', pageWidth / 2, y, { align: 'center' })
                y += lineHeight + 5
                
                // Activities section
                y = sectionHeading('Recent Activities', y)
                
                if (globalReportData.recentActivities.length > 0) {
                  // Table header
                  doc.setFontSize(smallSize)
                  doc.setFont('helvetica', 'bold')
                  doc.text('Type', margin, y)
                  doc.text('Description', margin + 30, y)
                  doc.text('School', margin + 100, y)
                  doc.text('Date', margin + 160, y)
                  y += lineHeight
                  
                  // Draw header underline
                  doc.setDrawColor(200, 200, 200)
                  doc.setLineWidth(0.2)
                  doc.line(margin, y - 3, margin + 180, y - 3)
                  
                  // Table content
                  doc.setFont('helvetica', 'normal')
                  for (const activity of globalReportData.recentActivities) {
                    y = checkPageBreak(y)
                    
                    // Activity data
                    doc.setFontSize(smallSize)
                    doc.text(activity.type, margin, y)
                    
                    // Description - might need wrapping
                    const descLines = doc.splitTextToSize(activity.description, 65)
                    doc.text(descLines, margin + 30, y)
                    
                    // School name - might need truncating
                    const schoolName = activity.school.length > 15
                      ? activity.school.substring(0, 12) + '...'
                      : activity.school
                    
                    doc.text(schoolName, margin + 100, y)
                    
                    // Format date
                    const date = new Date(activity.date).toLocaleDateString()
                    doc.text(date, margin + 160, y)
                    
                    // Move y down based on how many lines the description took
                    y += Math.max(descLines.length, 1) * lineHeight + 2
                  }
                } else {
                  y = addText('No recent activities available', y, normalSize)
                }
                
                // Add Feedback page
                doc.addPage()
                y = 20
                
                // Feedback header
                doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2])
                doc.setFontSize(titleSize)
                doc.setFont('helvetica', 'bold')
                doc.text('Platform Feedback', pageWidth / 2, y, { align: 'center' })
                y += lineHeight + 5
                
                // Feedback section
                y = sectionHeading('Recent User Feedback', y)
                
                if (globalReportData.feedback.length > 0) {
                  for (const item of globalReportData.feedback) {
                    y = checkPageBreak(y, 30)
                    
                    // Create a feedback card
                    doc.setDrawColor(200, 200, 200)
                    doc.setFillColor(250, 250, 250)
                    doc.roundedRect(margin, y, pageWidth - (margin * 2), 25, 3, 3, 'FD')
                    
                    // Rating
                    doc.setFont('helvetica', 'bold')
                    doc.setFontSize(normalSize)
                    doc.text(`Rating: ${item.rating}/5`, margin + 5, y + 6)
                    
                    // School and date
                    doc.setFont('helvetica', 'normal')
                    doc.setFontSize(smallSize)
                    doc.text(`From: ${item.school}`, margin + 5, y + 12)
                    doc.text(`Date: ${new Date(item.date).toLocaleDateString()}`, pageWidth - margin - 5, y + 12, { align: 'right' })
                    
                    // Comment
                    doc.setFontSize(smallSize)
                    const comment = item.comment || 'No comment provided'
                    const commentLines = doc.splitTextToSize(comment, pageWidth - (margin * 2) - 10)
                    doc.text(commentLines, margin + 5, y + 18)
                    
                    // Move to next feedback item
                    y += 30
                  }
                } else {
                  y = addText('No feedback available', y, normalSize)
                }
                
                // Add footer to all pages
                const pageCount = doc.getNumberOfPages()
                for (let i = 1; i <= pageCount; i++) {
                  doc.setPage(i)
                  doc.setFontSize(smallSize)
                  doc.setTextColor(100, 100, 100)
                  
                  // Page number
                  doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin, 285, { align: 'right' })
                  
                  // Copyright
                  const now = new Date()
                  doc.text(`Report generated by Young Reporter platform. © ${now.getFullYear()}`, 
                    pageWidth / 2, 285, { align: 'center' })
                }
                
                // Generate buffer from jsPDF
                const pdfBuffer = Buffer.from(doc.output('arraybuffer'))
                
                // Return PDF file
                return new NextResponse(pdfBuffer, {
                  status: 200,
                  headers: {
                    'Content-Type': 'application/pdf',
                    'Content-Disposition': `attachment; filename="global-platform-report-${new Date().toISOString().split('T')[0]}.pdf"`,
                  }
                })
              } catch (pdfError) {
                console.error(
                  '[Generate Report API] Global PDF generation failed, falling back to CSV:',
                  pdfError
                )
                
                // Fallback to CSV generation
                console.log('[Generate Report API] Directly generating global CSV fallback')
                
                // Create basic CSV data
                const csvData = [
                  ['Young Reporter Platform - Global Report', ''],
                  ['Generated on', new Date().toLocaleString()],
                  [''],
                  ['PLATFORM STATISTICS SUMMARY', ''],
                  ['Total Schools', globalReportData.platformStats.totalSchools],
                  ['Total Users', globalReportData.platformStats.users],
                  ['Total Students', globalReportData.platformStats.students],
                  ['Total Teachers', globalReportData.platformStats.teachers],
                  ['Total Articles', globalReportData.platformStats.articles],
                  ['Published Articles', globalReportData.platformStats.publishedArticles],
                  [''],
                  ['Note: PDF generation failed. Using CSV format as fallback.', ''],
                  [''],
                  ['Report generated by Young Reporter Platform', new Date().toISOString().split('T')[0]],
                  ['© Young Reporter ' + new Date().getFullYear(), '']
                ]
                
                // Convert to CSV string
                const csvContent = stringify(csvData)
                
                // Return CSV file
                return new NextResponse(csvContent, {
                  status: 200,
                  headers: {
                    'Content-Type': 'text/csv',
                    'Content-Disposition': `attachment; filename="global-platform-report-${new Date().toISOString().split('T')[0]}.csv"`,
                  },
                })
              }
            }
          } else {
            // Continue with school-specific report (existing code)
            // ... existing code for school-specific report ...
          }
        }
      } catch (mongoError) {
        console.warn(
          '[Generate Report API] Error generating report from MongoDB, falling back to Payload:',
          mongoError,
        )
      }

      // Fallback to Payload CMS if MongoDB fails
      console.log('[Generate Report API] Falling back to Payload CMS')
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user is a school admin or super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      console.log('[Generate Report API] Payload user role:', role)

      if (role !== 'school-admin' && role !== 'super-admin') {
        console.log('[Generate Report API] Access denied - Payload role not allowed:', role)
        return NextResponse.json(
          {
            error: 'Forbidden - Only school admins and super admins can generate reports',
            role,
          },
          { status: 403 },
        )
      }

      // Get school ID
      const schoolId = typeof user.school === 'object' ? user.school?.id : user.school
      console.log('[Generate Report API] Payload school ID:', schoolId)

      if (!schoolId) {
        return NextResponse.json(
          { error: 'School admin must be associated with a school' },
          { status: 400 },
        )
      }

      // Get school information
      const school = await payload.findByID({
        collection: 'schools',
        id: schoolId,
      })

      console.log('[Generate Report API] Payload school found:', school ? 'yes' : 'no')

      // Log the report generation using Payload's schema
      try {
      await payload.create({
        collection: 'activities',
        data: {
            userId,
            activityType: 'login',
            details: {
              description: 'Generated school report',
              reportDate: new Date().toISOString(),
              format,
            },
            school: schoolId,
          },
        })
        console.log('[Generate Report API] Payload activity logged successfully')
      } catch (e) {
        console.error('[Generate Report API] Error logging Payload activity:', e)
      }

      // Prepare the report data for Payload fallback
      const reportData = {
          schoolName: school.name,
        schoolAddress: school.address || 'No address provided',
        reportDate: new Date().toISOString(),
      }

      // Generate the file based on the requested format
      if (format === 'csv') {
        // Generate simple CSV with limited data
        console.log('[Generate Report API] Generating CSV from Payload data')

        // Create simple CSV
        const csvData = [
          ['School Report', ''],
          ['Generated on', new Date().toLocaleString()],
          ['School Name', reportData.schoolName],
          ['School Address', reportData.schoolAddress],
          ['Note', 'Limited data available in this report. MongoDB connection failed.'],
        ]

        // Convert to CSV string
        const csvContent = stringify(csvData)

        // Return CSV file
        return new NextResponse(csvContent, {
          status: 200,
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="school-report-${school.name.replace(/[^a-z0-9]/gi, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.csv"`,
          },
        })
      } else {
        // Generate simple PDF with limited data
        console.log('[Generate Report API] Generating PDF from Payload data')

        try {
          // Create PDF with jsPDF
          const doc = new jsPDF({
            orientation: 'portrait', 
            unit: 'mm',
            format: 'a4'
          })
          
          // Set font sizes and styles
          const titleSize = 20
          const headingSize = 16
          const subtitleSize = 14
          const normalSize = 12
          const smallSize = 10
          
          // Start positions
          let y = 20
          const margin = 15
          const lineHeight = 8
          const pageWidth = 210
          
          // Colors
          const primaryColor: [number, number, number] = [41, 128, 185] // Blue
          const secondaryColor: [number, number, number] = [44, 62, 80] // Dark Blue/Gray
          const warningColor: [number, number, number] = [231, 76, 60]  // Red
          
          // Report header with school name
          doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2])
          doc.setFontSize(titleSize)
          doc.setFont('helvetica', 'bold')
          doc.text(reportData.schoolName, pageWidth / 2, y, { align: 'center' })
          y += lineHeight + 5
          
          // Report subtitle
          doc.setFontSize(subtitleSize)
          doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2])
          doc.text('School Performance Report', pageWidth / 2, y, { align: 'center' })
          y += lineHeight + 2
          
          // Date
          doc.setFont('helvetica', 'normal')
          doc.setFontSize(smallSize)
          doc.setTextColor(0, 0, 0)
          doc.text(`Generated on: ${new Date().toLocaleString()}`, pageWidth / 2, y, { align: 'center' })
          y += lineHeight + 10
          
          // School info
          doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2])
          doc.setLineWidth(0.5)
          doc.line(margin, y - 3, pageWidth - margin, y - 3)
          
          doc.setFontSize(headingSize)
          doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2])
          doc.text('School Information', margin, y)
          y += lineHeight + 5
          
          doc.setTextColor(0, 0, 0)
          doc.setFontSize(normalSize)
          doc.text(`Address: ${reportData.schoolAddress || 'No address provided'}`, margin, y)
          y += lineHeight * 2
          
          // Warning about limited data
          doc.setDrawColor(warningColor[0], warningColor[1], warningColor[2])
          doc.setLineWidth(0.5)
          doc.line(margin, y - 3, pageWidth - margin, y - 3)
          
          doc.setTextColor(warningColor[0], warningColor[1], warningColor[2])
          doc.setFontSize(headingSize) 
          doc.text('Limited Data Available', margin, y)
          y += lineHeight + 5
          
          doc.setFontSize(normalSize)
          doc.text('This report contains limited information due to a MongoDB connection failure.', margin, y)
          y += lineHeight
          doc.text('Only basic school profile data is available.', margin, y)
          y += lineHeight
          doc.text('For a complete report, please try again later.', margin, y)
          y += lineHeight * 3
          
          // Reset text color
          doc.setTextColor(0, 0, 0)
          
          // Explanation box
          doc.setDrawColor(200, 200, 200)
          doc.setFillColor(245, 245, 245)
          doc.roundedRect(margin, y, pageWidth - (margin*2), 40, 3, 3, 'FD')
          
          y += 10
          doc.setFontSize(subtitleSize)
          doc.text('What this means:', margin + 10, y)
          y += lineHeight + 2
          
          doc.setFontSize(normalSize)
          doc.text('• Statistics about students and teachers are unavailable', margin + 10, y)
          y += lineHeight
          doc.text('• Performance metrics cannot be calculated', margin + 10, y)
          y += lineHeight
          doc.text('• Activity history is not accessible', margin + 10, y)
          
          // Footer on all pages
          const pageCount = doc.getNumberOfPages()
          for (let i = 1; i <= pageCount; i++) {
            doc.setPage(i)
            doc.setFontSize(smallSize)
            doc.setTextColor(100, 100, 100)
            
            // Page number
            doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin, 285, { align: 'right' })
            
            // Copyright
            const now = new Date()
            doc.text(`Report generated by Young Reporter platform. © ${now.getFullYear()}`, 
              pageWidth / 2, 285, { align: 'center' })
          }
          
          // Generate buffer from jsPDF
          const pdfBuffer = Buffer.from(doc.output('arraybuffer'))
          
          // Return PDF file
          return new NextResponse(pdfBuffer, {
            status: 200,
            headers: {
              'Content-Type': 'application/pdf',
              'Content-Disposition': `attachment; filename="school-report-${school.name.replace(/[^a-z0-9]/gi, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf"`,
            }
          })
        } catch (pdfError) {
          console.error(
            '[Generate Report API] PDF generation from Payload failed, falling back to CSV:',
            pdfError
          )
          
          // Fallback to CSV - generate it directly instead of making another request
          console.log('[Generate Report API] Directly generating CSV fallback from Payload data')

          // Create CSV with enhanced but limited data
          const csvData = [
            ['School Report - Young Reporter Platform', ''],
            ['Generated on', new Date().toLocaleString()],
            ['School Name', reportData.schoolName],
            ['School Address', reportData.schoolAddress || 'No address provided'],
            [''],
            ['NOTE: LIMITED DATA AVAILABLE', ''],
            ['This report contains limited information due to a MongoDB connection failure.', ''],
            ['Only basic school profile data is available.', ''],
            ['For a complete report, please try again later.', ''],
            [''],
            ['Report generated by Young Reporter Platform', new Date().toISOString().split('T')[0]],
            ['© Young Reporter ' + new Date().getFullYear(), '']
          ]

          // Convert to CSV string
          const csvContent = stringify(csvData)

          // Return CSV file
          return new NextResponse(csvContent, {
            status: 200,
            headers: {
              'Content-Type': 'text/csv',
              'Content-Disposition': `attachment; filename="school-report-${school.name.replace(/[^a-z0-9]/gi, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.csv"`,
            },
          })
        }
      }
    } catch (error) {
      console.error('[Generate Report API] Token verification error:', error)
      return NextResponse.json(
        { error: 'Unauthorized', details: error instanceof Error ? error.message : String(error) },
        { status: 401 },
      )
    }
  } catch (error) {
    console.error('[Generate Report API] Error generating school report:', error)
    return NextResponse.json(
      {
        error: 'Internal Server Error',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}
