'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, FileText } from 'lucide-react'
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, getMonth } from 'date-fns'

// Arabic months mapping
const arabicMonths = [
  'يناير',
  'فبراير',
  'مارس',
  'أبريل',
  'مايو',
  'يونيو',
  'يوليو',
  'أغسطس',
  'سبتمبر',
  'أكتوبر',
  'نوفمبر',
  'ديسمبر'
]

// Format date with Arabic month
const formatWithArabicMonth = (date: Date, formatStr: string) => {
  if (formatStr.includes('MMMM')) {
    // Replace the month name with Arabic equivalent
    const monthIndex = getMonth(date)
    const arabicMonth = arabicMonths[monthIndex]
    
    if (formatStr === 'MMMM yyyy') {
      return `${arabicMonth} ${date.getFullYear()}`
    }
    
    if (formatStr === 'MMMM d, yyyy') {
      return `${arabicMonth} ${date.getDate()}, ${date.getFullYear()}`
    }
    
    // For other formats, use the default formatter but replace month name later
    return format(date, formatStr).replace(
      format(date, 'MMMM'),
      arabicMonth
    )
  }
  
  // For formats not containing month names, use the default formatter
  return format(date, formatStr)
}

interface Submission {
  id: string
  title: string
  authorAlias: string
  createdAt: string
}

interface SubmissionsByDate {
  [date: string]: Submission[]
}

export default function SubmissionCalendar() {
  const router = useRouter()
  const [submissions, setSubmissions] = useState<Submission[]>([])
  const [submissionsByDate, setSubmissionsByDate] = useState<SubmissionsByDate>({})
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)

  useEffect(() => {
    async function fetchSubmissions() {
      try {
        setIsLoading(true)
        const response = await fetch('/api/dashboard/teacher/submissions')

        if (!response.ok) {
          throw new Error('Failed to fetch submissions')
        }

        const data = await response.json()
        setSubmissions(data.submissions || [])

        // Group submissions by date
        const grouped = (data.submissions || []).reduce((acc: SubmissionsByDate, submission: Submission) => {
          const date = format(new Date(submission.createdAt), 'yyyy-MM-dd')
          if (!acc[date]) {
            acc[date] = []
          }
          acc[date].push(submission)
          return acc
        }, {})

        setSubmissionsByDate(grouped)
      } catch (err) {
        console.error('Error fetching submissions:', err)
        setError('تعذر تحميل بيانات التقديمات')
      } finally {
        setIsLoading(false)
      }
    }

    fetchSubmissions()
  }, [])

  const handlePreviousMonth = () => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev)
      newDate.setMonth(newDate.getMonth() - 1)
      return newDate
    })
  }

  const handleNextMonth = () => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev)
      newDate.setMonth(newDate.getMonth() + 1)
      return newDate
    })
  }

  const handleDateClick = (date: Date) => {
    setSelectedDate(isSameDay(date, selectedDate as Date) ? null : date)
  }

  const handleViewSubmission = (submissionId: string) => {
    router.push(`/dashboard/articles/review/${submissionId}`)
  }

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>تقويم تقديمات الطلاب</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-6" dir="rtl">
          <div className="text-center py-8 text-red-500">
            <p>{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Generate days for the current month
  const monthStart = startOfMonth(currentMonth)
  const monthEnd = endOfMonth(currentMonth)
  const days = eachDayOfInterval({ start: monthStart, end: monthEnd })

  // Get submissions for selected date
  const selectedDateStr = selectedDate ? format(selectedDate, 'yyyy-MM-dd') : ''
  const selectedDateSubmissions = selectedDateStr ? submissionsByDate[selectedDateStr] || [] : []

  return (
    <Card className="w-full">
      <CardHeader dir="rtl">
        <div className="flex justify-between items-center">
          <CardTitle>تقويم تقديمات الطلاب</CardTitle>
          <div className="flex space-x-2 space-x-reverse" dir="ltr">
            <Button variant="outline" size="icon" onClick={handleNextMonth}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="px-2 py-1">
              {formatWithArabicMonth(currentMonth, 'MMMM yyyy')}
            </span>
            <Button variant="outline" size="icon" onClick={handlePreviousMonth}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent dir="rtl">
        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1 mb-4">
          {['السبت', 'الجمعة', 'الخميس', 'الأربعاء', 'الثلاثاء', 'الاثنين', 'الأحد'].map(day => (
            <div key={day} className="text-center font-medium text-sm py-1">
              {day}
            </div>
          ))}

          {/* Empty cells for days before the start of the month */}
          {Array.from({ length: 6 - (monthStart.getDay() === 0 ? 6 : monthStart.getDay() - 1) }).map((_, index) => (
            <div key={`empty-start-${index}`} className="h-12 border border-gray-100 rounded-md"></div>
          ))}

          {/* Days of the month */}
          {days.map(day => {
            const dateStr = format(day, 'yyyy-MM-dd')
            const hasSubmissions = submissionsByDate[dateStr] && submissionsByDate[dateStr].length > 0
            const isSelected = selectedDate && isSameDay(day, selectedDate)

            return (
              <div
                key={dateStr}
                className={`h-12 border rounded-md flex flex-col items-center justify-center cursor-pointer transition-colors
                  ${hasSubmissions ? 'border-primary' : 'border-gray-100'}
                  ${isSelected ? 'bg-primary text-white' : 'hover:bg-gray-50'}`}
                onClick={() => handleDateClick(day)}
              >
                <div className="text-sm">{format(day, 'd')}</div>
                {hasSubmissions && (
                  <div className={`text-xs ${isSelected ? 'text-white' : 'text-primary'}`}>
                    {submissionsByDate[dateStr].length} تقديم
                  </div>
                )}
              </div>
            )
          })}

          {/* Empty cells for days after the end of the month */}
          {Array.from({ length: monthEnd.getDay() === 0 ? 0 : monthEnd.getDay() }).map((_, index) => (
            <div key={`empty-end-${index}`} className="h-12 border border-gray-100 rounded-md"></div>
          ))}
        </div>

        {/* Selected Date Submissions */}
        {selectedDate && (
          <div className="mt-4">
            <h3 className="font-medium mb-2">
              التقديمات ليوم {formatWithArabicMonth(selectedDate, 'MMMM d, yyyy')}
            </h3>
            {selectedDateSubmissions.length === 0 ? (
              <p className="text-sm text-gray-500">لا توجد تقديمات في هذا التاريخ</p>
            ) : (
              <div className="space-y-2">
                {selectedDateSubmissions.map(submission => (
                  <div key={submission.id} className="flex justify-between items-center p-2 border rounded-md">
                    <div>
                      <p className="font-medium">{submission.title}</p>
                      <p className="text-sm text-gray-500">بواسطة: {submission.authorAlias}</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewSubmission(submission.id)}
                    >
                      <FileText className="h-4 w-4 ml-1" />
                      مراجعة
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
