// This script fixes the loginAttempts field for all users
// Run with: node src/scripts/fix-login-attempts.js

require('dotenv').config();
const { MongoClient } = require('mongodb');

async function main() {
  // Connect to MongoDB
  const client = new MongoClient(process.env.DATABASE_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const usersCollection = db.collection('users');
    
    // Find all users with NaN loginAttempts
    const result = await usersCollection.updateMany(
      { $or: [
          { loginAttempts: NaN },
          { loginAttempts: { $exists: false } },
          { loginAttempts: null }
        ]
      },
      { $set: { loginAttempts: 0 } }
    );
    
    console.log(`Updated ${result.modifiedCount} users with loginAttempts = 0`);
    
    // Find all users and ensure loginAttempts is a number
    const users = await usersCollection.find({}).toArray();
    let fixedCount = 0;
    
    for (const user of users) {
      if (isNaN(user.loginAttempts)) {
        await usersCollection.updateOne(
          { _id: user._id },
          { $set: { loginAttempts: 0 } }
        );
        fixedCount++;
      }
    }
    
    console.log(`Fixed an additional ${fixedCount} users with NaN loginAttempts`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

main().catch(console.error);
