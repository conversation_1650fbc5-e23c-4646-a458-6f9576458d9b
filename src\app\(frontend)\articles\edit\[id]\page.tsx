import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { notFound, redirect } from 'next/navigation'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'

export default async function EditArticlePage({ params }: { params: { id: string } }) {
  const { id } = params
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // If not logged in, redirect to login
  if (!user) {
    redirect('/login')
  }

  // Check if user is a student
  const isStudent = typeof user.role === 'object' 
    ? user.role?.slug === 'student' 
    : user.role === 'student'

  if (!isStudent) {
    // Only students can edit articles
    redirect('/dashboard/student')
  }

  // Fetch the article
  try {
    const article = await payload.findByID({
      collection: 'articles',
      id,
    })

    // Check if the user is the author
    const isAuthor = article.author === user.id || 
      (typeof article.author === 'object' && article.author?.id === user.id)

    if (!isAuthor) {
      // Only the author can edit their own articles
      redirect('/dashboard/student')
    }

    // Check if article is editable (not published)
    if (article.status === 'published') {
      // Published articles cannot be edited
      redirect('/dashboard/student')
    }

    return (
      <div className="min-h-screen bg-gray-50">
        {/* Navigation */}
        <nav className="bg-blue-600 text-white p-4 shadow-md">
          <div className="container mx-auto flex justify-between items-center">
            <div className="text-xl font-bold">Young Reporter</div>
            <div className="flex space-x-6">
              <Link href="/">Home</Link>
              <Link href="/articles">Articles</Link>
              <Link href="/news">News</Link>
              <Link href="/statistics">Statistics</Link>
              <Link href="/about">About</Link>
              <Link href="/contact">Contact</Link>
              <Link href="/api/logout">Logout</Link>
            </div>
          </div>
        </nav>

        {/* Edit Article Header */}
        <div className="bg-blue-700 text-white py-12 px-4">
          <div className="container mx-auto">
            <h1 className="text-4xl font-bold mb-4">Edit Article</h1>
            <p className="text-xl">
              Make changes to your article
            </p>
          </div>
        </div>

        {/* Edit Article Form */}
        <div className="py-12 px-4">
          <div className="container mx-auto max-w-4xl">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="p-8">
                <form action={`/api/articles/${id}/update`} method="post" className="space-y-6">
                  <div>
                    <label htmlFor="title" className="block text-gray-700 font-bold mb-2">
                      Article Title
                    </label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      className="w-full p-3 border border-gray-300 rounded"
                      defaultValue={article.title}
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="content" className="block text-gray-700 font-bold mb-2">
                      Article Content
                    </label>
                    <textarea
                      id="content"
                      name="content"
                      rows={15}
                      className="w-full p-3 border border-gray-300 rounded"
                      defaultValue={typeof article.content === 'string' ? article.content : ''}
                      required
                    ></textarea>
                  </div>

                  <div className="flex justify-between">
                    <div className="space-x-4">
                      <button
                        type="submit"
                        name="action"
                        value="save"
                        className="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition"
                      >
                        Save Changes
                      </button>
                      {article.status === 'draft' && (
                        <button
                          type="submit"
                          name="action"
                          value="submit"
                          className="bg-green-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-green-700 transition"
                        >
                          Save & Submit for Review
                        </button>
                      )}
                    </div>
                    <Link
                      href="/dashboard/student"
                      className="text-gray-600 px-6 py-3 rounded-lg font-bold hover:text-gray-800 transition"
                    >
                      Cancel
                    </Link>
                  </div>
                </form>
              </div>
            </div>

            <div className="mt-8 bg-blue-50 p-6 rounded-lg">
              <h2 className="text-xl font-bold mb-4">Writing Tips</h2>
              <ul className="list-disc pl-5 space-y-2">
                <li>Start with a strong introduction that hooks the reader.</li>
                <li>Use clear, concise language and avoid jargon.</li>
                <li>Support your points with evidence and examples.</li>
                <li>Structure your article with a beginning, middle, and conclusion.</li>
                <li>Proofread your work before submitting for review.</li>
                <li>Consider your audience and what would interest them.</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-gray-800 text-white py-8 px-4 mt-auto">
          <div className="container mx-auto">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-4 md:mb-0">
                <h3 className="text-xl font-bold">Young Reporter</h3>
                <p>Empowering student journalists since 2023</p>
              </div>
              <div className="flex space-x-6">
                <Link href="/">Home</Link>
                <Link href="/about">About</Link>
                <Link href="/contact">Contact</Link>
                <Link href="/privacy">Privacy Policy</Link>
              </div>
            </div>
            <div className="mt-8 text-center text-gray-400">
              <p>© {new Date().getFullYear()} Young Reporter. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    )
  } catch (error) {
    console.error('Error fetching article:', error)
    return notFound()
  }
}
