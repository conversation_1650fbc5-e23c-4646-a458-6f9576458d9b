/**
 * Utility functions for handling rich text content
 */

/**
 * Extracts plain text from a Lexical rich text object
 * @param content The rich text content object
 * @returns Plain text string or a fallback message
 */
export function extractPlainText(content: any): string {
  try {
    // If content is already a string, return it
    if (typeof content === 'string') {
      return content
    }

    // If content is null or undefined, return empty string
    if (!content) {
      return ''
    }

    // If content is a Lexical object with root
    if (content.root && content.root.children) {
      // Extract text from each child node
      return content.root.children
        .map((child: any) => {
          // Handle text nodes
          if (child.type === 'text' && typeof child.text === 'string') {
            return child.text
          }

          // Handle paragraph nodes
          if (child.type === 'paragraph' && Array.isArray(child.children)) {
            return child.children.map((textNode: any) => textNode.text || '').join('')
          }

          return ''
        })
        .join(' ')
        .trim()
    }

    // If we can't parse it, convert to string
    return JSON.stringify(content)
  } catch (error) {
    console.error('Error extracting plain text from rich text:', error)
    return 'Content unavailable'
  }
}

/**
 * Converts a plain text string to a simple Lexical rich text object
 * @param text Plain text string
 * @returns Lexical rich text object
 */
export function createRichTextFromPlain(text: string): any {
  return {
    root: {
      type: 'root',
      children: [
        {
          type: 'paragraph',
          children: [
            {
              type: 'text',
              text: text || '',
              version: 1,
            },
          ],
          direction: null,
          format: '',
          indent: 0,
          version: 1,
        },
      ],
      direction: null,
      format: '',
      indent: 0,
      version: 1,
    },
  }
}

/**
 * Determines if a value is a Lexical rich text object
 * @param value The value to check
 * @returns Boolean indicating if the value is a rich text object
 */
export function isRichTextObject(value: any): boolean {
  return (
    typeof value === 'object' &&
    value !== null &&
    value.root &&
    typeof value.root === 'object' &&
    Array.isArray(value.root.children)
  )
}

/**
 * Renders rich text content as a string representation of JSX
 * @param content The rich text content object
 * @returns String representation of the rich text content
 */
export function renderRichText(content: any): string {
  if (!content) return ''

  // If content is a string, return it directly
  if (typeof content === 'string') return content

  // If content has a children array (Lexical format)
  if (content.root && content.root.children) {
    try {
      // Extract text from paragraphs
      return content.root.children
        .map((block: any) => {
          if (block.type === 'paragraph' && block.children) {
            return block.children
              .filter((child: any) => child.type === 'text')
              .map((child: any) => child.text)
              .join('')
          } else if (block.type === 'heading' && block.children) {
            return block.children
              .filter((child: any) => child.type === 'text')
              .map((child: any) => child.text)
              .join('')
          } else if (block.type === 'list' && block.children) {
            return block.children
              .map((listItem: any) => {
                if (listItem.children) {
                  return (
                    '• ' +
                    listItem.children
                      .filter((child: any) => child.type === 'text')
                      .map((child: any) => child.text)
                      .join('')
                  )
                }
                return ''
              })
              .join('\n')
          }
          return ''
        })
        .filter(Boolean)
        .join('\n\n')
    } catch (error) {
      console.error('Error rendering rich text:', error)
      return 'Error rendering content'
    }
  }

  return 'Unable to render content'
}
