import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function POST(req: NextRequest) {
  try {
    console.log('Student approval API called')

    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    console.log('Token found:', token ? 'Yes' : 'No')

    if (!token) {
      console.log('No token found, returning 401')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      console.log('Token verified successfully')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null
      console.log('User ID from token:', userId)

      if (!userId) {
        console.log('No user ID in token, returning 401')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      console.log('Request body:', body)

      const { studentId, action } = body

      if (!studentId || !action) {
        console.log('Missing required fields')
        return NextResponse.json(
          { error: 'Student ID and action are required' },
          { status: 400 },
        )
      }

      if (action !== 'approve' && action !== 'reject') {
        console.log('Invalid action')
        return NextResponse.json(
          { error: 'Action must be either "approve" or "reject"' },
          { status: 400 },
        )
      }

      // Initialize Payload
      const payload = await getPayload({ config })

      // Check if the current user is authorized to approve/reject students
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
      })

      const userRole = typeof currentUser.role === 'object' ? currentUser.role?.slug : currentUser.role

      // Only teachers, mentors, school admins, and super admins can approve/reject students
      if (!['teacher', 'mentor', 'school-admin', 'super-admin'].includes(userRole)) {
        console.log('User not authorized to approve/reject students')
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
      }

      // Get the student
      const student = await payload.findByID({
        collection: 'users',
        id: studentId,
      })

      if (!student) {
        console.log('Student not found')
        return NextResponse.json({ error: 'Student not found' }, { status: 404 })
      }

      // Check if the student is from the same school as the teacher
      const studentSchool = typeof student.school === 'object' ? student.school?.id : student.school
      const teacherSchool = typeof currentUser.school === 'object' ? currentUser.school?.id : currentUser.school

      if (userRole !== 'super-admin' && studentSchool !== teacherSchool) {
        console.log('Teacher can only approve students from their own school')
        return NextResponse.json(
          { error: 'You can only approve students from your own school' },
          { status: 403 },
        )
      }

      // Update the student based on the action
      const updateData = {
        status: action === 'approve' ? 'active' : 'suspended',
      }

      // Set the approvedBy field if approving
      if (action === 'approve') {
        updateData.approvedBy = userId
      }

      // Update the student
      const updatedStudent = await payload.update({
        collection: 'users',
        id: studentId,
        data: updateData,
      })

      // Create an activity record
      await payload.create({
        collection: 'activities',
        data: {
          userId: userId,
          targetUserId: studentId,
          activityType: 'student-approval',
          details: {
            action: action,
            studentName: `${student.firstName} ${student.lastName}`,
            studentEmail: student.email,
          },
        },
      })

      // Award points to the teacher for approving a student
      if (action === 'approve') {
        const currentPoints = currentUser.points || 0
        await payload.update({
          collection: 'users',
          id: userId,
          data: {
            points: currentPoints + 5, // 5 points for approving a student
          },
        })
      }

      console.log('Student updated successfully')
      return NextResponse.json({
        success: true,
        message: `Student ${action === 'approve' ? 'approved' : 'rejected'} successfully`,
      })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error approving/rejecting student:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
