import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import config from '@/payload.config'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({
        success: true,
        data: { news: [] }
      })
    }

    // Get the payload instance
    const payload = await getPayload({ config })

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({
          success: true,
          data: { news: [] }
        })
      }

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Get user role
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role

      // Build query based on user role
      let query = {}

      if (userRole === 'mentor') {
        // Mentors can only see their own news and published news
        query = {
          or: [
            { 'author.id': { equals: userId } },
            { status: { equals: 'published' } }
          ]
        }
      } else if (userRole === 'super-admin' || userRole === 'school-admin') {
        // Admins can see all news
        query = {}
      } else {
        // Other users can only see published news
        query = {
          status: { equals: 'published' }
        }
      }

      // Get news based on query
      const newsResponse = await payload.find({
        collection: 'news',
        where: query,
        depth: 2, // Increase depth to properly populate relationships
      })

      if (!newsResponse.docs || newsResponse.docs.length === 0) {
        console.warn('No news found in Payload CMS')
        return NextResponse.json({
          success: true,
          data: { news: [] },
        })
      }

      // Add a flag to indicate which news items are owned by the current user
      const newsWithOwnership = newsResponse.docs.map(item => {
        const isOwner = typeof item.author === 'object'
          ? item.author?.id === userId
          : item.author === userId

        return {
          ...item,
          isOwner
        }
      })

      return NextResponse.json({
        success: true,
        data: { news: newsWithOwnership },
      })
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      // Return only published news for invalid tokens
      const publicNewsResponse = await payload.find({
        collection: 'news',
        where: {
          status: { equals: 'published' }
        },
        depth: 2,
      })

      return NextResponse.json({
        success: true,
        data: { news: publicNewsResponse.docs || [] },
      })
    }
  } catch (error) {
    console.error('Error fetching news from Payload CMS:', error)
    // Return empty news array with success false instead of error status
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch news',
      data: { news: [] },
    })
  }
}
