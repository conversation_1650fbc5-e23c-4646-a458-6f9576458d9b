import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the token and get the user ID
    const { userId } = await verifyJWT(token)

    if (!userId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get URL parameters
    const url = new URL(req.url)
    const querySchoolId = url.searchParams.get('schoolId')

    // Initialize Payload
    const payload = await getPayload({ config })

    // Get the current user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })

    // Verify user is a school admin
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'school-admin' && role !== 'super-admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get school ID
    const schoolId =
      querySchoolId || (typeof user.school === 'object' ? user.school?.id : user.school)

    if (!schoolId) {
      return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
    }

    // Get school
    const school = await payload.findByID({
      collection: 'schools',
      id: schoolId,
    })

    if (!school) {
      return NextResponse.json({ error: 'School not found' }, { status: 404 })
    }

    // Get counts for resource metrics
    const usersCount = await payload.find({
      collection: 'users',
      where: {
        school: { equals: schoolId },
      },
      limit: 0,
    })

    const articlesCount = await payload.find({
      collection: 'articles',
      where: {
        'author.school': { equals: schoolId },
      },
      limit: 0,
    })

    // Try to get media count
    let mediaCount = { totalDocs: 0 }
    try {
      // Check if media collection exists
      const collections = payload.collections || {}
      const hasMediaCollection = !!collections.media

      if (hasMediaCollection) {
        // Try different query paths based on the schema
        try {
          mediaCount = await payload.find({
            collection: 'media',
            where: {
              'uploadedBy.school': { equals: schoolId },
            },
            limit: 0,
          })
        } catch (queryError) {
          // If that fails, try a simpler query
          try {
            mediaCount = await payload.find({
              collection: 'media',
              limit: 0,
            })
            // Filter results manually if needed
          } catch (simpleQueryError) {
            console.warn('Error querying media collection:', simpleQueryError)
            // Use default value
            mediaCount = { totalDocs: 0 }
          }
        }
      } else {
        console.log('Media collection does not exist')
      }
    } catch (mediaError) {
      console.warn('Error fetching media count:', mediaError)
      // Use default value
      mediaCount = { totalDocs: 0 }
    }

    // Calculate storage usage (mock data - replace with actual calculation)
    const storageUsage = Math.min(75, Math.floor(mediaCount.totalDocs * 0.5)) // 0.5 MB per media item, max 75 GB

    // Calculate API calls (mock data - replace with actual metrics)
    const apiCalls = Math.min(8000, usersCount.totalDocs * 100 + articlesCount.totalDocs * 50)

    // Calculate user sessions (mock data - replace with actual metrics)
    const userSessions = Math.min(150, Math.floor(usersCount.totalDocs * 0.3))

    // Create resource metrics
    const resourceMetrics = [
      {
        name: 'Storage Usage',
        value: storageUsage,
        maxValue: 100,
        unit: 'GB',
      },
      {
        name: 'API Calls',
        value: apiCalls,
        maxValue: 10000,
        unit: 'calls/day',
      },
      {
        name: 'User Sessions',
        value: userSessions,
        maxValue: 200,
        unit: 'concurrent',
      },
    ]

    return NextResponse.json({ resourceMetrics })
  } catch (error) {
    console.error('Error fetching resource metrics:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
