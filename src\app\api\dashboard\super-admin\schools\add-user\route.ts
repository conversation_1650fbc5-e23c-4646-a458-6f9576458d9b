import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'
import bcrypt from 'bcryptjs'
import crypto from 'crypto'

function normalizeId(id: any): string {
  if (!id) return '';
  if (typeof id === 'string') return id;
  if (typeof id === 'object' && typeof id.toHexString === 'function') return id.toHexString();
  if (typeof id === 'object' && id._id && typeof id._id.toHexString === 'function') return id._id.toHexString();
  return String(id);
}

export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      const { firstName, lastName, email, role, schoolId, grade } = body

      // Validate required fields
      if (!firstName || !lastName || !email || !role || !schoolId) {
        return NextResponse.json({ error: 'جميع الحقول مطلوبة' }, { status: 400 })
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return NextResponse.json({ error: 'صيغة البريد الإلكتروني غير صالحة' }, { status: 400 })
      }

      // Validate role
      const validRoles = ['student', 'teacher', 'mentor', 'school-admin']
      if (!validRoles.includes(role)) {
        return NextResponse.json({ error: 'نوع المستخدم غير صالح' }, { status: 400 })
      }

      // Try to use MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the admin user from MongoDB
        const adminUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })

        if (adminUser) {
          // Check if user is a super admin
          let userRole = null;
          if (adminUser.role && typeof adminUser.role === 'object' && adminUser.role.slug) {
            userRole = adminUser.role.slug;
          } else if (adminUser.role && typeof adminUser.role === 'object' && adminUser.role instanceof ObjectId) {
            // Look up the role document
            const roleDoc = await db.collection('roles').findOne({ _id: adminUser.role });
            userRole = roleDoc?.slug || null;
          } else if (typeof adminUser.role === 'string' && adminUser.role.length === 24) {
            // If it's a string ObjectId
            const roleDoc = await db.collection('roles').findOne({ _id: new ObjectId(adminUser.role) });
            userRole = roleDoc?.slug || null;
          } else {
            userRole = adminUser.role;
          }

          if (userRole !== 'super-admin') {
            return NextResponse.json({ error: 'غير مصرح به' }, { status: 403 });
          }

          // Check if email already exists
          const existingUser = await db.collection('users').findOne({ email })
          if (existingUser) {
            return NextResponse.json(
              { error: 'يوجد مستخدم بهذا البريد الإلكتروني بالفعل' },
              { status: 400 }
            )
          }

          // Generate a temporary password
          const tempPassword = crypto.randomBytes(8).toString('hex')
          // Hash the password
          const salt = await bcrypt.genSalt(10);
          const hash = await bcrypt.hash(tempPassword, salt);

          // Look up the role document by slug and use its _id
          const roleDoc = await db.collection('roles').findOne({ slug: role });
          if (!roleDoc) {
            return NextResponse.json({ error: 'نوع المستخدم غير موجود' }, { status: 400 });
          }
          const roleId = roleDoc._id;

          // Create the user in MongoDB
          const newUser = {
            firstName,
            lastName,
            email,
            role: roleId,
            school: schoolId,
            status: 'active',
            createdBy: userId,
            createdAt: new Date().toISOString(),
            salt,
            hash,
            grade: role === 'student' ? grade : undefined,
          }

          const result = await db.collection('users').insertOne(newUser)

          // Log the account creation activity
          await db.collection('activities').insertOne({
            userId: userId,
            userName: `${adminUser.firstName} ${adminUser.lastName}`,
            userRole: userRole,
            schoolId: schoolId,
            activityType: 'login',
            description: `تم إنشاء حساب ${role} لـ ${firstName} ${lastName}`,
            date: new Date().toISOString(),
            details: {
              newUserId: result.insertedId.toString(),
              newUserEmail: email,
              newUserRole: role,
            },
          })

          return NextResponse.json({
            success: true,
            message: `تم إنشاء حساب لـ ${firstName} ${lastName}`,
            userId: result.insertedId.toString(),
            tempPassword: tempPassword,
          })
        }
      } catch (mongoError) {
        console.warn('Error creating user with MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the admin user
      const adminUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a super admin
      const userRole = typeof adminUser.role === 'object' ? adminUser.role?.slug : adminUser.role
      if (userRole !== 'super-admin') {
        return NextResponse.json({ error: 'غير مصرح به' }, { status: 403 })
      }

      // Check if email already exists
      const existingUsers = await payload.find({
        collection: 'users',
        where: {
          email: {
            equals: email,
          },
        },
      })

      if (existingUsers.docs.length > 0) {
        return NextResponse.json(
          { error: 'يوجد مستخدم بهذا البريد الإلكتروني بالفعل' },
          { status: 400 }
        )
      }

      // Look up the role document by slug
      const roles = await payload.find({
        collection: 'roles',
        where: {
          slug: {
            equals: role,
          },
        },
      })

      if (roles.docs.length === 0) {
        return NextResponse.json({ error: 'نوع المستخدم غير موجود' }, { status: 400 })
      }

      const roleId = roles.docs[0].id

      // Generate a temporary password
      const tempPassword = crypto.randomBytes(8).toString('hex')

      // Create the user
      const newUser = await payload.create({
        collection: 'users',
        data: {
          firstName,
          lastName,
          email,
          password: tempPassword,
          role: roleId,
          school: schoolId,
          status: 'active',
          grade: role === 'student' ? grade : undefined,
        },
      })

      // Log activity
      await payload.create({
        collection: 'activities',
        data: {
          userId,
          school: schoolId,
          activityType: 'login',
          details: {
            newUserId: newUser.id,
            newUserEmail: email,
            newUserRole: role,
            description: `تم إنشاء حساب ${role} لـ ${firstName} ${lastName}`
          },
        },
      })

      return NextResponse.json({
        success: true,
        message: `تم إنشاء حساب لـ ${firstName} ${lastName}`,
        userId: newUser.id,
        tempPassword,
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'غير مصرح به' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json({ error: 'خطأ في النظام' }, { status: 500 })
  }
} 