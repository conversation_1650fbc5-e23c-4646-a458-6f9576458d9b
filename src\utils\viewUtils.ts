import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'
import { processViewsForPoints } from './points'

/**
 * Increments the view count for a document in either the news or articles collection
 */
export const incrementViewCount = async (collectionName: 'news' | 'articles', id: string): Promise<number> => {
  try {
    // Connect to the database
    const { db } = await connectToDatabase()

    // Check if ID is valid MongoDB ObjectId
    if (!ObjectId.isValid(id)) {
      console.error(`Invalid ObjectId: ${id}`)
      return 0
    }
    
    // Get current view count to check if this crosses a milestone
    const currentDoc = await db.collection(collectionName).findOne({ _id: new ObjectId(id) });
    const currentViews = currentDoc?.views || 0;

    // Increment the view count
    const result = await db.collection(collectionName).updateOne(
      { _id: new ObjectId(id) },
      { $inc: { views: 1 } }
    )

    if (result.modifiedCount === 0) {
      console.warn(`No document was updated in ${collectionName} collection with ID: ${id}`)
      return 0
    }
    
    // Initialize payload
    const payload = await getPayload({ config });
    
    // Get the updated document to find the author
    const updatedDoc = await db.collection(collectionName).findOne({ _id: new ObjectId(id) });
    
    if (!updatedDoc) {
      console.warn(`Document not found after update in ${collectionName} collection with ID: ${id}`);
      return currentViews + 1;
    }
    
    // Extract the author ID
    let authorId: string | null = null;
    
    if (updatedDoc.author) {
      if (typeof updatedDoc.author === 'string') {
        authorId = updatedDoc.author;
      } else if (typeof updatedDoc.author === 'object' && updatedDoc.author?._id) {
        authorId = updatedDoc.author._id.toString();
      } else if (typeof updatedDoc.author === 'object' && updatedDoc.author?.id) {
        authorId = updatedDoc.author.id;
      }
    }
    
    // Process view counts for points if we found an author
    if (authorId) {
      // Process points for view milestone
      await processViewsForPoints({
        payload,
        type: collectionName === 'articles' ? 'article' : 'news',
        itemId: id,
        userId: authorId,
        currentViews: currentViews + 1 // Include this view
      });
    }

    return currentViews + 1
  } catch (error) {
    console.error(`Error incrementing view count for ${collectionName} with ID ${id}:`, error)
    return 0
  }
}

/**
 * Gets the view count for a document in either the news or articles collection
 */
export const getViewCount = async (collectionName: 'news' | 'articles', id: string): Promise<number> => {
  try {
    // Connect to the database
    const { db } = await connectToDatabase()

    // Check if ID is valid MongoDB ObjectId
    if (!ObjectId.isValid(id)) {
      console.error(`Invalid ObjectId: ${id}`)
      return 0
    }

    // Get the document
    const doc = await db.collection(collectionName).findOne(
      { _id: new ObjectId(id) },
      { projection: { views: 1 } }
    )

    if (!doc) {
      console.warn(`No document found in ${collectionName} collection with ID: ${id}`)
      return 0
    }

    return doc.views || 0
  } catch (error) {
    console.error(`Error getting view count for ${collectionName} with ID ${id}:`, error)
    return 0
  }
}

/**
 * Update batch view counts
 */
export const updateBatchViewCounts = async (
  viewsData: { collectionName: 'news' | 'articles'; id: string; views: number }[]
): Promise<boolean> => {
  try {
    // Connect to the database
    const { db } = await connectToDatabase()

    // Process each view count update
    for (const item of viewsData) {
      // Check if ID is valid MongoDB ObjectId
      if (!ObjectId.isValid(item.id)) {
        console.error(`Invalid ObjectId: ${item.id}`)
        continue
      }

      // Update the view count
      await db.collection(item.collectionName).updateOne(
        { _id: new ObjectId(item.id) },
        { $set: { views: item.views } }
      )
    }

    return true
  } catch (error) {
    console.error('Error updating batch view counts:', error)
    return false
  }
} 