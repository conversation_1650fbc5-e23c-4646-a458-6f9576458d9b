'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  LayoutDashboard,
  Users,
  FileText,
  Newspaper,
  School,
  Award,
  Settings,
  MessageSquare,
  BarChart,
  Download,
  AlertTriangle,
  TrendingUp,
  Activity,
  Flag,
} from 'lucide-react'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { TopLeaders } from '@/components/TopLeaders'
import SchoolLeaderboard from '@/components/dashboard/SchoolLeaderboard'
import PendingApprovalsOverview from '@/components/dashboard/PendingApprovalsOverview'

import StudentDataTable from '@/components/dashboard/StudentDataTable'
import TeacherPerformanceMetrics from '@/components/dashboard/TeacherPerformanceMetrics'
import MentorFeedbackSummaries from '@/components/dashboard/MentorFeedbackSummaries'
import CreateAccountForm from '@/components/dashboard/CreateAccountForm'
import ReportedIssuesTable from '@/components/dashboard/ReportedIssuesTable'
import EscalateIssueForm from '@/components/dashboard/EscalateIssueForm'
import RecentActivities from '@/components/dashboard/RecentActivities'
import { ViewStatsCard, StatCard } from '@/components/dashboard/ViewStatsCard'

interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  role?:
    | {
        id: string
        name: string
        slug: string
      }
    | string
  school?:
    | {
        id: string
        name: string
      }
    | string
}

interface Stats {
  users: number
  students: number
  teachers: number
  mentors: number
  articles: number
  pendingApprovals: number
  resourceUsage: number
}

interface Leader {
  id: string
  name: string
  anonymizedName?: string
  score: number
  type: 'student' | 'teacher' | 'mentor'
}

interface PendingApproval {
  id: string
  type: string
  requesterId: string
  requesterName: string
  requesterRole: string
  status: string
  createdAt: string
  data: any
}

interface MentorReviewScore {
  mentorId: string
  mentorName: string
  averageRating: number
  totalReviews: number
  lastReviewDate: string
}

interface ResourceMetric {
  name: string
  value: number
  maxValue: number
  unit: string
}

interface SchoolAdminDashboardProps {
  user: User | null
}

export default function SchoolAdminDashboard({ user }: SchoolAdminDashboardProps) {
  const router = useRouter()
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState<Stats>({
    users: 0,
    students: 0,
    teachers: 0,
    mentors: 0,
    articles: 0,
    pendingApprovals: 0,
    resourceUsage: 0,
  })
  const [leaderboard, setLeaderboard] = useState<Leader[]>([])
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([])
  const [mentorReviews, setMentorReviews] = useState<MentorReviewScore[]>([])
  const [resourceMetrics, setResourceMetrics] = useState<ResourceMetric[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  // Get school ID for API calls
  const schoolId = typeof user?.school === 'object' ? user?.school?.id : user?.school || ''

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true)

        // Debug logging for user role and school ID
        console.log('School Admin Dashboard - User:', user)
        console.log(
          'School Admin Dashboard - User Role:',
          typeof user?.role === 'object' ? user?.role?.slug : user?.role,
        )
        console.log(
          'School Admin Dashboard - School ID:',
          typeof user?.school === 'object' ? user?.school?.id : user?.school,
        )

        // Fetch school admin dashboard data
        const dashboardResponse = await fetch('/api/dashboard/school-admin')

        if (!dashboardResponse.ok) {
          console.error(
            'Dashboard API response not ok:',
            dashboardResponse.status,
            await dashboardResponse.text(),
          )
          throw new Error('فشل في جلب بيانات لوحة التحكم')
        }

        const dashboardData = await dashboardResponse.json()
        console.log('School Admin Dashboard Data:', dashboardData)

        // Set stats
        setStats({
          users: dashboardData.stats.users || 0,
          students: dashboardData.stats.students || 0,
          teachers: dashboardData.stats.teachers || 0,
          mentors: dashboardData.stats.mentors || 0,
          articles: dashboardData.stats.articles || 0,
          pendingApprovals: dashboardData.stats.pendingApprovals || 0,
          resourceUsage: dashboardData.stats.resourceUsage || 0,
        })

        // Set leaderboard data
        setLeaderboard(dashboardData.leaderboard || [])

        // Set pending approvals
        setPendingApprovals(dashboardData.pendingApprovals || [])
        console.log('Pending approvals from dashboard API:', dashboardData.pendingApprovals)

        // Explicitly fetch pending approvals to debug
        try {
          console.log('Fetching pending approvals with school ID:', schoolId)

          const pendingApprovalsResponse = await fetch(
            `/api/dashboard/school-admin/pending-approvals?schoolId=${schoolId}`,
          )

          if (pendingApprovalsResponse.ok) {
            const pendingApprovalsData = await pendingApprovalsResponse.json()
            console.log('Direct pending approvals API response:', pendingApprovalsData)
            // Only update if we actually got data and the main dashboard data didn't have any
            if (
              pendingApprovalsData.pendingApprovals &&
              pendingApprovalsData.pendingApprovals.length > 0 &&
              (!dashboardData.pendingApprovals || dashboardData.pendingApprovals.length === 0)
            ) {
              setPendingApprovals(pendingApprovalsData.pendingApprovals)
            }
          } else {
            console.error(
              'Pending approvals API error:',
              pendingApprovalsResponse.status,
              await pendingApprovalsResponse.text(),
            )
          }
        } catch (approvalError) {
          console.error('Error fetching pending approvals directly:', approvalError)
        }

        // Set mentor review scores
        setMentorReviews(dashboardData.mentorReviews || [])

        // Set resource metrics
        setResourceMetrics(dashboardData.resourceMetrics || [])

        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError('فشل تحميل بيانات لوحة التحكم. يرجى المحاولة مرة أخرى.')
        setIsLoading(false)
      }
    }

    fetchData()
  }, [user])

  const generateReport = async () => {
    try {
      setIsGeneratingReport(true)

      // Call API to generate school report
      const response = await fetch('/api/dashboard/school-admin/generate-report', {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('فشل في إنشاء التقرير')
      }

      const data = await response.json()

      // Download the report
      const link = document.createElement('a')
      link.href = data.reportUrl
      link.download = 'تقرير-المدرسة.pdf'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      setIsGeneratingReport(false)
    } catch (err) {
      console.error('Error generating report:', err)
      setIsGeneratingReport(false)
    }
  }

  const statCards = [
    {
      title: 'الطلاب',
      value: stats.students,
      icon: <Users className="w-6 h-6" />,
      color: 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300',
      link: '/dashboard/students',
    },
    {
      title: 'المعلمون',
      value: stats.teachers,
      icon: <Users className="w-6 h-6" />,
      color: 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300',
      link: '/dashboard/teachers',
    },
    {
      title: 'الموجهون',
      value: stats.mentors,
      icon: <Users className="w-6 h-6" />,
      color: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-300',
      link: '/dashboard/mentors',
    },
    {
      title: 'المقالات',
      value: stats.articles,
      icon: <FileText className="w-6 h-6" />,
      color: 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300',
      link: '/dashboard/articles',
    },
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div
        dir="rtl"
        className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4"
      >
        {error}
      </div>
    )
  }

  return (
    <div dir="rtl" className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">لوحة تحكم مدير المدرسة</h2>
        <div className="flex items-center space-x-2">
          <Button
            onClick={() => setActiveTab('reports')}
            variant="outline"
            className="hidden md:flex"
          >
            <Flag className="ml-2 h-4 w-4" />
            عرض التقارير
          </Button>
          <Button onClick={generateReport} disabled={isGeneratingReport} className="hidden md:flex">
            {isGeneratingReport ? (
              <>
                <div className="ml-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
                جاري الإنشاء...
              </>
            ) : (
              <>
                <Download className="ml-2 h-4 w-4" />
                إنشاء تقرير
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} dir="rtl">
        <TabsList
          className="mb-4 overflow-x-auto whitespace-nowrap flex p-1"
          style={{ maxWidth: '100%', overflowY: 'hidden' }}
        >
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="leaderboard">قائمة المتصدرين</TabsTrigger>
          <TabsTrigger value="approvals">الموافقات المعلقة</TabsTrigger>
          <TabsTrigger value="students">الطلاب</TabsTrigger>
          <TabsTrigger value="teachers">المعلمون</TabsTrigger>
          <TabsTrigger value="activities">الأنشطة</TabsTrigger>
          <TabsTrigger value="articles">المقالات</TabsTrigger>
          <TabsTrigger value="feedback">ملخصات تعليقات الموجهين</TabsTrigger>
          <TabsTrigger value="actions">الإجراءات</TabsTrigger>
          <TabsTrigger value="reports">التقارير</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {statCards.map((stat, index) => (
              <StatCard
                key={index}
                title={stat.title}
                value={stat.value}
                icon={stat.icon}
                color={stat.color}
                link={stat.link}
              />
            ))}
          </div>

          {/* Overview Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* School Leaderboard Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="ml-2 h-5 w-5" />
                  قائمة المتصدرين في المدرسة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <TopLeaders leaders={leaderboard.slice(0, 5)} title="" showViewAll={false} />
                <Button
                  variant="outline"
                  className="w-full mt-4"
                  onClick={() => setActiveTab('leaderboard')}
                >
                  عرض القائمة الكاملة
                </Button>
              </CardContent>
            </Card>

            {/* Pending Approvals Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="ml-2 h-5 w-5" />
                  الموافقات المعلقة
                </CardTitle>
              </CardHeader>
              <CardContent>
                {pendingApprovals.length > 0 ? (
                  <div className="space-y-4">
                    {pendingApprovals.slice(0, 3).map((approval) => (
                      <div
                        key={approval.id}
                        className="flex justify-between items-center border-b pb-2"
                      >
                        <div>
                          <p className="font-medium">{approval.requesterName}</p>
                          <p className="text-sm text-gray-500">{approval.type}</p>
                        </div>
                        <Badge>{approval.status}</Badge>
                      </div>
                    ))}
                    <Button
                      variant="outline"
                      className="w-full mt-2"
                      onClick={() => setActiveTab('approvals')}
                    >
                      عرض جميع الموافقات
                    </Button>
                  </div>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">لا توجد موافقات معلقة</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recent Activities */}
          <RecentActivities schoolId={schoolId} limit={10} />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <ViewStatsCard type="news" title="الأخبار الأكثر مشاهدة" limit={5} />
            <ViewStatsCard type="articles" title="المقالات الأكثر مشاهدة" limit={5} />
          </div>
        </TabsContent>

        {/* Leaderboard Tab */}
        <TabsContent value="leaderboard" className="space-y-6">
          <SchoolLeaderboard schoolId={schoolId} />
        </TabsContent>

        {/* Pending Approvals Tab */}
        <TabsContent value="approvals" className="space-y-6">
          <PendingApprovalsOverview schoolId={schoolId} />
        </TabsContent>

        {/* Students Tab */}
        <TabsContent value="students" className="space-y-6">
          <StudentDataTable schoolId={schoolId} />
        </TabsContent>

        {/* Teachers Tab */}
        <TabsContent value="teachers" className="space-y-6">
          <TeacherPerformanceMetrics schoolId={schoolId} />
        </TabsContent>

        {/* Activities Tab */}
        <TabsContent value="activities" className="space-y-6">
          <RecentActivities schoolId={schoolId} limit={20} />
        </TabsContent>

        {/* Articles Tab */}
        <TabsContent value="articles" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>إدارة المقالات</CardTitle>
            </CardHeader>
            <CardContent>
              <p>سيتم عرض مكون إدارة المقالات هنا. يمكنك إدارة جميع المقالات الخاصة بمدرستك.</p>
              <Button className="mt-4" onClick={() => router.push('/dashboard/articles')}>
                فتح مدير المقالات الكامل
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Feedback Tab */}
        <TabsContent value="feedback" className="space-y-6">
          <MentorFeedbackSummaries schoolId={schoolId} />
        </TabsContent>

        {/* Actions Tab */}
        <TabsContent value="actions" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>إنشاء حساب جديد</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="mb-4">إنشاء حسابات للمعلمين والموجهين في مدرستك.</p>
                <CreateAccountForm schoolId={schoolId} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>تصعيد المشكلات</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="mb-4">للمشكلات التي تتطلب تدخل المدير الأعلى.</p>
                <EscalateIssueForm schoolId={schoolId} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Reported Issues Tab */}
        <TabsContent value="reports" className="space-y-4">
          <div className="grid gap-4">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Flag className="ml-2 h-5 w-5" />
                  المشكلات المبلغ عنها
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ReportedIssuesTable
                  schoolId={schoolId}
                  userRole={
                    typeof user?.role === 'object' ? user?.role?.slug : user?.role || 'school-admin'
                  }
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
