'use client'

import { useEffect, useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  <PERSON>alogFooter,
  DialogClose,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Skeleton } from '@/components/ui/skeleton'
import { toast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SearchIcon, MoreHorizontal, MessageCircleIcon, CheckIcon, XIcon, BellIcon } from 'lucide-react'

interface Feedback {
  id: string
  type: 'report' | 'suggestion' | 'issue' | 'question'
  subject: string
  message: string
  status: 'new' | 'in-progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  schoolId: string
  schoolName: string
  submitterId: string
  submitterName: string
  submitterRole: string
  dateSubmitted: string
  lastUpdated: string
  response?: string
}

interface FeedbackManagerProps {
  schoolId?: string
  apiEndpoint?: string
}

export function FeedbackManager({ schoolId, apiEndpoint = '/api/dashboard/super-admin/feedback' }: FeedbackManagerProps) {
  const [loading, setLoading] = useState(true)
  const [feedback, setFeedback] = useState<Feedback[]>([])
  const [filteredFeedback, setFilteredFeedback] = useState<Feedback[]>([])
  const [error, setError] = useState<string | null>(null)
  const [viewFeedback, setViewFeedback] = useState<Feedback | null>(null)
  const [responseText, setResponseText] = useState('')
  const [updatingStatus, setUpdatingStatus] = useState(false)
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    type: 'all',
    school: 'all',
    priority: 'all',
  })

  useEffect(() => {
    const fetchFeedback = async () => {
      try {
        setLoading(true)
        let url = apiEndpoint
        if (schoolId) url += `?schoolId=${schoolId}`
        
        const response = await fetch(url)
        
        if (!response.ok) {
          throw new Error('فشل في جلب التعليقات')
        }
        
        const data = await response.json()
        
        // Make sure we're handling the correct API response format
        const feedbackData = data.feedback || data || []
        
        // If we have no data and this is development, load sample data
        if (feedbackData.length === 0 && process.env.NODE_ENV === 'development') {
          console.log('No feedback data found, using sample data for development')
          // Sample data will go here if needed
          setFeedback(getSampleFeedback())
          setFilteredFeedback(getSampleFeedback())
        } else {
          setFeedback(feedbackData)
          setFilteredFeedback(feedbackData)
        }
        
        setLoading(false)
      } catch (error) {
        console.error('Error fetching feedback:', error)
        setError('فشل في تحميل بيانات التعليقات')
        setLoading(false)
      }
    }
    
    fetchFeedback()
  }, [schoolId, apiEndpoint])

  useEffect(() => {
    // Filter feedback based on current filters
    let filtered = [...feedback]
    
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(item => 
        item.subject.toLowerCase().includes(searchLower) || 
        item.message.toLowerCase().includes(searchLower) ||
        item.submitterName.toLowerCase().includes(searchLower) ||
        item.schoolName.toLowerCase().includes(searchLower)
      )
    }
    
    if (filters.status !== 'all') {
      filtered = filtered.filter(item => item.status === filters.status)
    }
    
    if (filters.type !== 'all') {
      filtered = filtered.filter(item => item.type === filters.type)
    }
    
    if (filters.school !== 'all') {
      filtered = filtered.filter(item => item.schoolId === filters.school)
    }
    
    if (filters.priority !== 'all') {
      filtered = filtered.filter(item => item.priority === filters.priority)
    }
    
    setFilteredFeedback(filtered)
  }, [filters, feedback])

  const handleViewFeedback = (item: Feedback) => {
    setViewFeedback(item)
    setResponseText(item.response || '')
  }

  const handleUpdateStatus = async (status: 'new' | 'in-progress' | 'resolved' | 'closed') => {
    if (!viewFeedback) return
    
    try {
      setUpdatingStatus(true)
      
      // Extract base endpoint from apiEndpoint (remove any trailing slash)
      const baseEndpoint = apiEndpoint.endsWith('/') 
        ? apiEndpoint.slice(0, -1) 
        : apiEndpoint
      
      // Make an actual API call to update the status
      const response = await fetch(`${baseEndpoint}/${viewFeedback.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      })
      
      if (!response.ok) {
        throw new Error('فشل في تحديث الحالة')
      }
      
      // Get the updated feedback item from response if available
      let updatedItem: Feedback | undefined
      try {
        const data = await response.json()
        updatedItem = data.feedback
      } catch (e) {
        // If response doesn't contain updated item, update locally
        console.warn('API did not return updated feedback item')
      }
      
      // Update feedback item in state
      const updatedFeedback = feedback.map(item => 
        item.id === viewFeedback.id 
          ? updatedItem || { ...item, status, lastUpdated: new Date().toISOString() }
          : item
      )
      
      setFeedback(updatedFeedback)
      setViewFeedback(prev => prev ? updatedItem || { ...prev, status, lastUpdated: new Date().toISOString() } : null)
      
      toast({
        title: "تم تحديث الحالة",
        description: `تم تحديث حالة التعليق إلى ${getStatusTranslation(status)}`,
      })
    } catch (error) {
      console.error('Error updating feedback status:', error)
      toast({
        title: "خطأ في تحديث الحالة",
        description: "حدثت مشكلة أثناء تحديث حالة التعليق",
        variant: "destructive",
      })
    } finally {
      setUpdatingStatus(false)
    }
  }

  const handleSendResponse = async () => {
    if (!viewFeedback || !responseText.trim()) return
    
    try {
      setUpdatingStatus(true)
      
      // Extract base endpoint from apiEndpoint (remove any trailing slash)
      const baseEndpoint = apiEndpoint.endsWith('/') 
        ? apiEndpoint.slice(0, -1) 
        : apiEndpoint
      
      // Make an actual API call to send the response
      const response = await fetch(`${baseEndpoint}/${viewFeedback.id}/response`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          response: responseText,
          status: viewFeedback.status === 'new' ? 'in-progress' : viewFeedback.status,
        }),
      })
      
      if (!response.ok) {
        throw new Error('فشل في إرسال الرد')
      }
      
      // Get the updated feedback item from response if available
      let updatedItem: Feedback | undefined
      try {
        const data = await response.json()
        updatedItem = data.feedback
      } catch (e) {
        // If response doesn't contain updated item, update locally
        console.warn('API did not return updated feedback item')
      }
      
      // Determine new status if API didn't provide an updated item
      const newStatus = viewFeedback.status === 'new' ? 'in-progress' : viewFeedback.status
      
      // Update feedback item in state
      const updatedFeedback = feedback.map(item => 
        item.id === viewFeedback.id 
          ? updatedItem || { 
              ...item, 
              response: responseText, 
              status: newStatus,
              lastUpdated: new Date().toISOString(),
            }
          : item
      )
      
      setFeedback(updatedFeedback)
      setViewFeedback(prev => prev ? updatedItem || { 
        ...prev, 
        response: responseText, 
        status: newStatus,
        lastUpdated: new Date().toISOString(),
      } : null)
      
      toast({
        title: "تم إرسال الرد",
        description: "تم إرسال ردك بنجاح",
      })
    } catch (error) {
      console.error('Error sending response:', error)
      toast({
        title: "خطأ في إرسال الرد",
        description: "حدثت مشكلة أثناء إرسال ردك",
        variant: "destructive",
      })
    } finally {
      setUpdatingStatus(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('ar-EG', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new':
        return 'bg-blue-100 text-blue-800'
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800'
      case 'resolved':
        return 'bg-green-100 text-green-800'
      case 'closed':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-gray-100 text-gray-800'
      case 'medium':
        return 'bg-blue-100 text-blue-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'urgent':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'report':
        return <BellIcon className="h-4 w-4 text-red-500" />
      case 'suggestion':
        return <MessageCircleIcon className="h-4 w-4 text-green-500" />
      case 'issue':
        return <XIcon className="h-4 w-4 text-orange-500" />
      case 'question':
        return <SearchIcon className="h-4 w-4 text-blue-500" />
      default:
        return <MessageCircleIcon className="h-4 w-4" />
    }
  }

  // Function to generate sample feedback data for development
  const getSampleFeedback = (): Feedback[] => {
    return [
      {
        id: '1',
        type: 'issue',
        subject: 'Login problems for teachers',
        message: 'Teachers at our school are having trouble logging in. The system shows an error message after entering credentials.',
        status: 'new',
        priority: 'high',
        schoolId: '1',
        schoolName: 'Lincoln High School',
        submitterId: '101',
        submitterName: 'John Smith',
        submitterRole: 'school-admin',
        dateSubmitted: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        lastUpdated: new Date(Date.now() - 86400000).toISOString(),
      },
      {
        id: '2',
        type: 'suggestion',
        subject: 'Add student achievements section',
        message: 'It would be great to have a dedicated section to showcase student achievements in journalism.',
        status: 'in-progress',
        priority: 'medium',
        schoolId: '2',
        schoolName: 'Washington Middle School',
        submitterId: '102',
        submitterName: 'Sarah Johnson',
        submitterRole: 'teacher',
        dateSubmitted: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        lastUpdated: new Date(Date.now() - 86400000).toISOString(),
        response: 'We are currently working on this feature and expect to release it in the next update.'
      },
      {
        id: '3',
        type: 'question',
        subject: 'How to assign mentors to students?',
        message: 'I cannot figure out how to assign mentors to specific students. Is there a guide available?',
        status: 'resolved',
        priority: 'low',
        schoolId: '3',
        schoolName: 'Jefferson Academy',
        submitterId: '103',
        submitterName: 'Michael Brown',
        submitterRole: 'teacher',
        dateSubmitted: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
        lastUpdated: new Date(Date.now() - 172800000).toISOString(),
        response: 'You can assign mentors from the Students page. Select a student, click Options, then "Assign Mentor". A detailed guide has been sent to your email.'
      }
    ];
  }

  const getStatusTranslation = (status: string) => {
    switch (status) {
      case 'new':
        return 'جديد'
      case 'in-progress':
        return 'قيد التقدم'
      case 'resolved':
        return 'تم الحل'
      case 'closed':
        return 'مغلق'
      default:
        return status
    }
  }

  const getPriorityTranslation = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'منخفضة'
      case 'medium':
        return 'متوسطة'
      case 'high':
        return 'عالية'
      case 'urgent':
        return 'عاجلة'
      default:
        return priority
    }
  }

  const getTypeTranslation = (type: string) => {
    switch (type) {
      case 'report':
        return 'تقرير'
      case 'suggestion':
        return 'اقتراح'
      case 'issue':
        return 'مشكلة'
      case 'question':
        return 'سؤال'
      default:
        return type
    }
  }

  if (loading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-[400px] w-full" />
      </div>
    )
  }

  if (error) {
    return <div className="text-red-500" dir="rtl">خطأ: {error}</div>
  }

  // Get unique schools for filter
  const schools = [
    { id: 'all', name: 'جميع المدارس' },
    ...Array.from(new Set(feedback.map(item => item.schoolId))).map(schoolId => {
      const schoolItem = feedback.find(item => item.schoolId === schoolId)
      return {
        id: schoolId,
        name: schoolItem?.schoolName || 'مدرسة غير معروفة',
      }
    })
  ]

  return (
    <div className="space-y-4" dir="rtl">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <SearchIcon className="w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="البحث في التعليقات..."
            className="max-w-xs"
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
          />
        </div>
        
        <div className="flex flex-wrap items-center gap-2">
          <Select 
            value={filters.status} 
            onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
          >
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="الحالة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الحالات</SelectItem>
              <SelectItem value="new">جديد</SelectItem>
              <SelectItem value="in-progress">قيد التقدم</SelectItem>
              <SelectItem value="resolved">تم الحل</SelectItem>
              <SelectItem value="closed">مغلق</SelectItem>
            </SelectContent>
          </Select>
          
          <Select 
            value={filters.type} 
            onValueChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
          >
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="النوع" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الأنواع</SelectItem>
              <SelectItem value="report">تقارير</SelectItem>
              <SelectItem value="suggestion">اقتراحات</SelectItem>
              <SelectItem value="issue">مشاكل</SelectItem>
              <SelectItem value="question">أسئلة</SelectItem>
            </SelectContent>
          </Select>
          
          <Select 
            value={filters.school} 
            onValueChange={(value) => setFilters(prev => ({ ...prev, school: value }))}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="المدرسة" />
            </SelectTrigger>
            <SelectContent>
              {schools.map(school => (
                <SelectItem key={school.id} value={school.id}>{school.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select 
            value={filters.priority} 
            onValueChange={(value) => setFilters(prev => ({ ...prev, priority: value }))}
          >
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="الأولوية" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الأولويات</SelectItem>
              <SelectItem value="urgent">عاجلة</SelectItem>
              <SelectItem value="high">عالية</SelectItem>
              <SelectItem value="medium">متوسطة</SelectItem>
              <SelectItem value="low">منخفضة</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>النوع</TableHead>
              <TableHead>الموضوع</TableHead>
              <TableHead>المدرسة</TableHead>
              <TableHead>المرسل</TableHead>
              <TableHead>الحالة</TableHead>
              <TableHead>الأولوية</TableHead>
              <TableHead>التاريخ</TableHead>
              <TableHead className="text-left">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredFeedback.length > 0 ? (
              filteredFeedback.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <div className="flex items-center">
                      {getTypeIcon(item.type)}
                      <span className="mr-2 capitalize">{getTypeTranslation(item.type)}</span>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium max-w-xs truncate" title={item.subject}>
                    {item.subject}
                  </TableCell>
                  <TableCell>{item.schoolName}</TableCell>
                  <TableCell>{item.submitterName}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className={`${getStatusColor(item.status)}`}>
                      {getStatusTranslation(item.status)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={`${getPriorityColor(item.priority)}`}>
                      {getPriorityTranslation(item.priority)}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(item.dateSubmitted)}</TableCell>
                  <TableCell className="text-left">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewFeedback(item)}
                    >
                      عرض
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4 text-muted-foreground">
                  لم يتم العثور على تعليقات مطابقة للفلاتر الخاصة بك
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      <div className="text-xs text-muted-foreground">
        عرض {filteredFeedback.length} من {feedback.length} عنصر تعليق
      </div>
      
      {/* View Feedback Dialog */}
      <Dialog open={!!viewFeedback} onOpenChange={(open) => !open && setViewFeedback(null)}>
        <DialogContent className="max-w-md" dir="rtl">
          <DialogHeader>
            <DialogTitle>{viewFeedback?.subject}</DialogTitle>
            <DialogDescription className="flex justify-between items-center">
              <span>من {viewFeedback?.submitterName} ({viewFeedback?.submitterRole})</span>
              <Badge variant="outline" className={viewFeedback ? getStatusColor(viewFeedback.status) : ''}>
                {viewFeedback ? getStatusTranslation(viewFeedback.status) : ''}
              </Badge>
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-1">الرسالة:</h4>
              <div className="text-sm border rounded-md p-3 bg-muted/50">
                {viewFeedback?.message}
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium mb-1">المدرسة:</h4>
              <div className="text-sm">{viewFeedback?.schoolName}</div>
            </div>
            
            <div className="flex justify-between">
              <div>
                <h4 className="text-sm font-medium mb-1">تاريخ الإرسال:</h4>
                <div className="text-sm">{viewFeedback ? formatDate(viewFeedback.dateSubmitted) : ''}</div>
              </div>
              <div>
                <h4 className="text-sm font-medium mb-1">الأولوية:</h4>
                <Badge variant="outline" className={viewFeedback ? getPriorityColor(viewFeedback.priority) : ''}>
                  {viewFeedback ? getPriorityTranslation(viewFeedback.priority) : ''}
                </Badge>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium mb-1">الرد:</h4>
              <Textarea
                value={responseText}
                onChange={(e) => setResponseText(e.target.value)}
                placeholder="اكتب ردك هنا..."
                className="min-h-[100px]"
              />
            </div>
            
            <div className="flex flex-col gap-2">
              <h4 className="text-sm font-medium">تحديث الحالة:</h4>
              <div className="flex flex-wrap gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  disabled={viewFeedback?.status === 'new' || updatingStatus}
                  onClick={() => handleUpdateStatus('new')} 
                  className={viewFeedback?.status === 'new' ? 'border-blue-500' : ''}
                >
                  جديد
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  disabled={viewFeedback?.status === 'in-progress' || updatingStatus}
                  onClick={() => handleUpdateStatus('in-progress')}
                  className={viewFeedback?.status === 'in-progress' ? 'border-yellow-500' : ''}
                >
                  قيد التقدم
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  disabled={viewFeedback?.status === 'resolved' || updatingStatus}
                  onClick={() => handleUpdateStatus('resolved')}
                  className={viewFeedback?.status === 'resolved' ? 'border-green-500' : ''}
                >
                  تم الحل
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  disabled={viewFeedback?.status === 'closed' || updatingStatus}
                  onClick={() => handleUpdateStatus('closed')}
                  className={viewFeedback?.status === 'closed' ? 'border-gray-500' : ''}
                >
                  مغلق
                </Button>
              </div>
            </div>
          </div>
          
          <DialogFooter className="flex justify-between">
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
            <Button 
              onClick={handleSendResponse} 
              disabled={!responseText.trim() || updatingStatus}
            >
              إرسال الرد
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 