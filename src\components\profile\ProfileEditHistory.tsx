'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { formatDistanceToNow } from 'date-fns'

interface ProfileChangeRecord {
  id: string
  type: 'name-change' | 'profile-image' | 'bio-update' | 'grade-update'
  status: 'pending' | 'approved' | 'rejected'
  requestDate: string
  responseDate?: string
  details: {
    oldValue?: string
    newValue?: string
    reason?: string
  }
}

export default function ProfileEditHistory() {
  const [history, setHistory] = useState<ProfileChangeRecord[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function fetchProfileHistory() {
      try {
        setIsLoading(true)
        const response = await fetch('/api/profile/history', {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache',
          },
        })

        console.log('Profile history response status:', response.status)

        if (!response.ok) {
          throw new Error('Failed to fetch profile history')
        }

        const data = await response.json()
        setHistory(data.history || [])
      } catch (err) {
        console.error('Error fetching profile history:', err)
        setError('Could not load profile change history')
      } finally {
        setIsLoading(false)
      }
    }

    fetchProfileHistory()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    }
  }

  const getChangeTypeLabel = (type: string) => {
    switch (type) {
      case 'name-change':
        return 'Name Change'
      case 'profile-image':
        return 'Profile Image'
      case 'bio-update':
        return 'Biography Update'
      case 'grade-update':
        return 'Grade Update'
      default:
        return 'Profile Update'
    }
  }

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch (error) {
      return 'Unknown date'
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profile Change History</CardTitle>
          <CardDescription>Record of your profile change requests</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profile Change History</CardTitle>
          <CardDescription>Record of your profile change requests</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded">
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Change History</CardTitle>
        <CardDescription>Record of your profile change requests</CardDescription>
      </CardHeader>
      <CardContent>
        {history.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            No profile changes have been requested yet
          </div>
        ) : (
          <div className="space-y-4">
            {history.map((record) => (
              <div key={record.id} className="border rounded-lg overflow-hidden">
                <div className="p-4 bg-gray-50 dark:bg-gray-800 flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">{getChangeTypeLabel(record.type)}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Requested {formatDate(record.requestDate)}
                    </p>
                  </div>
                  <Badge className={getStatusColor(record.status)}>
                    {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                  </Badge>
                </div>
                <div className="p-4">
                  <div className="space-y-2">
                    {record.details.oldValue && record.details.newValue && (
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Previous
                          </p>
                          <p className="text-sm">{record.details.oldValue}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Requested Change
                          </p>
                          <p className="text-sm">{record.details.newValue}</p>
                        </div>
                      </div>
                    )}
                    {record.responseDate && (
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {record.status === 'approved' ? 'Approved' : 'Rejected'}{' '}
                        {formatDate(record.responseDate)}
                      </p>
                    )}
                    {record.details.reason && record.status === 'rejected' && (
                      <div className="mt-2">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          Reason for rejection:
                        </p>
                        <p className="text-sm">{record.details.reason}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
