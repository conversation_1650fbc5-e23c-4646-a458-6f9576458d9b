'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface News {
  id: string
  title: string
  slug: string
}

interface ClientBreakingNewsProps {
  news: News[]
}

export function ClientBreakingNews({ news }: ClientBreakingNewsProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  
  // If no news is provided, create a placeholder
  const newsItems = news.length > 0 
    ? news 
    : [{
        id: '1',
        title: 'Loading breaking news...',
        slug: '#',
      }]

  // Auto-advance the ticker
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % newsItems.length)
    }, 5000)
    
    return () => clearInterval(interval)
  }, [newsItems.length])

  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % newsItems.length)
  }

  const goToPrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + newsItems.length) % newsItems.length)
  }

  return (
    <div className="relative flex items-center">
  
      <div className="overflow-hidden flex-grow">
        <div className="relative h-6">
          {newsItems.map((item, index) => (
            <div 
              key={item.id}
              className={`absolute inset-0 transition-opacity duration-500 truncate ${
                index === currentIndex ? 'opacity-100 z-10' : 'opacity-0 z-0'
              }`}
            >
              <Link 
                href={`/news/${item.slug}`} 
                className="text-sm uppercase font-semibold hover:text-primary transition-colors"
              >
                {item.title}
              </Link>
            </div>
          ))}
        </div>
      </div>
      
    </div>
  )
}
