import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'

// POST to upload media
export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get the payload instance
      const payload = await getPayload({ config })

      // Get the current user
      const currentUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user exists
      if (!currentUser) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 })
      }

      // Parse the form data
      const formData = await req.formData()

      // Get the file from the form data
      const file = formData.get('file') as File
      if (!file) {
        return NextResponse.json({ error: 'No file provided' }, { status: 400 })
      }

      // Get alt text
      const alt = (formData.get('alt') as string) || 'Image'

      // Convert file to buffer
      const buffer = Buffer.from(await file.arrayBuffer())

      // Upload to Payload CMS with local storage
      try {
        // Create a fresh payload instance for each upload to avoid transaction issues
        const uploadPayload = await getPayload({ config })
        
        const media = await uploadPayload.create({
          collection: 'media',
          data: {
            alt,
          },
          file: {
            data: buffer,
            mimetype: file.type,
            name: file.name,
            size: file.size,
          },
          // Override access to ensure the upload works
          overrideAccess: true,
        })

        console.log('Media uploaded successfully:', media.id)

        // Return the media object
        return NextResponse.json({
          success: true,
          url: media.url,
          id: media.id,
          filename: media.filename,
        })
      } catch (uploadError) {
        console.error('Error uploading media:', uploadError)
        return NextResponse.json({ error: 'Failed to upload media' }, { status: 500 })
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error uploading media:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
