import { Access } from 'payload'

export const isAdmin: Access = ({ req: { user } }) => {
  if (!user) return false

  // Check if user has a role
  if (!user.role) return false

  // Handle both string ID and populated object cases
  const roleSlug = typeof user.role === 'object' ? user.role.slug : user.role

  // Check if the role is admin (super-admin or school-admin)
  return roleSlug === 'super-admin' || roleSlug === 'school-admin'
}

export const isMentor: Access = ({ req: { user } }) => {
  if (!user) return false

  // Check if user has a role
  if (!user.role) return false

  // Handle both string ID and populated object cases
  const roleSlug = typeof user.role === 'object' ? user.role.slug : user.role

  // Check if the role is mentor
  return roleSlug === 'mentor'
}

export const isTeacher: Access = ({ req: { user } }) => {
  if (!user) return false

  // Check if user has a role
  if (!user.role) return false

  // Handle both string ID and populated object cases
  const roleSlug = typeof user.role === 'object' ? user.role.slug : user.role

  // Check if the role is teacher
  return roleSlug === 'teacher'
}

export const isStudent: Access = ({ req: { user } }) => {
  if (!user) return false

  // Check if user has a role
  if (!user.role) return false

  // Handle both string ID and populated object cases
  const roleSlug = typeof user.role === 'object' ? user.role.slug : user.role

  // Check if the role is student
  return roleSlug === 'student'
}

export const isAdminOrMentor: Access = ({ req: { user } }) => {
  return isAdmin({ req: { user } }) || isMentor({ req: { user } })
}

export const isAdminOrTeacher: Access = ({ req: { user } }) => {
  return isAdmin({ req: { user } }) || isTeacher({ req: { user } })
}

export const isLoggedIn: Access = ({ req: { user } }) => {
  return Boolean(user)
}
