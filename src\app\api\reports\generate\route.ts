import { NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'

export async function POST(request: Request) {
  try {
    const { db } = await connectToDatabase()
    
    // Get request body
    const body = await request.json()
    const { month, year, schoolId } = body
    
    // Validate input
    if (!month || !year) {
      return NextResponse.json(
        { error: 'Month and year are required' },
        { status: 400 }
      )
    }
    
    // Build date range for the month
    const startDate = new Date(year, month - 1, 1).toISOString()
    const endDate = new Date(year, month, 0).toISOString()
    
    // Build query
    const query: Record<string, any> = {
      date: {
        $gte: startDate,
        $lte: endDate
      }
    }
    
    // If schoolId is provided, filter by school
    if (schoolId) {
      query.schoolId = schoolId
    }
    
    // Get activities for the month
    const activities = await db.collection('activities')
      .find(query)
      .sort({ date: -1 })
      .toArray()
    
    // Generate report ID
    const reportId = `report-${year}-${month.toString().padStart(2, '0')}-${schoolId || 'all'}`
    
    // Create report summary
    const activityTypes = activities.reduce((acc: Record<string, number>, activity: any) => {
      const type = activity.activityType
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {})
    
    const userRoles = activities.reduce((acc: Record<string, number>, activity: any) => {
      const role = activity.userRole
      acc[role] = (acc[role] || 0) + 1
      return acc
    }, {})
    
    const schools = activities.reduce((acc: Record<string, number>, activity: any) => {
      if (activity.schoolId) {
        const school = activity.schoolName || activity.schoolId
        acc[school] = (acc[school] || 0) + 1
      }
      return acc
    }, {})
    
    // Create report
    const report = {
      id: reportId,
      month,
      year,
      schoolId: schoolId || 'all',
      generatedAt: new Date().toISOString(),
      totalActivities: activities.length,
      activityTypes,
      userRoles,
      schools,
      activities
    }
    
    // Save report to database
    await db.collection('reports').updateOne(
      { id: reportId },
      { $set: report },
      { upsert: true }
    )
    
    return NextResponse.json({
      success: true,
      reportId,
      message: 'Report generated successfully'
    })
  } catch (error) {
    console.error('Error generating report:', error)
    return NextResponse.json(
      { error: 'Failed to generate report' },
      { status: 500 }
    )
  }
}
