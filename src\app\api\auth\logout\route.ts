import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(req: NextRequest) {
  try {
    // Clear the payload token cookie
    const cookieStore = await cookies()
    cookieStore.delete('payload-token')

    // Redirect to login page
    return NextResponse.redirect(new URL('/login', req.url))
  } catch (error) {
    console.error('Error logging out:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
