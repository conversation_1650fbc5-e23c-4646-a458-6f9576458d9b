# Points System Integration Guide

This guide explains how to properly implement and use the points system throughout the Young Reporter application.

## Overview

The points system consists of several components:

1. **User Points**: Each user has a `points` field and a `pointsActivities` array tracking their point history
2. **School Points**: Schools earn points based on the total points of their users
3. **Rankings**: Users are ranked based on their points within their role category

## Adding Points to Users

### Using the Points Middleware

The simplest way to award points is to use the `awardPoints` function from the points middleware:

```typescript
import { awardPoints } from '@/middleware/pointsHandler'

// Example usage in an API route
const response = await awardPoints({
  payload,
  userId: targetUser.id,
  type: 'article_published',
  points: 10,
  description: 'Published an article',
  reference: { articleId: article.id },
});

// The response contains:
// - success: boolean
// - totalPoints: number (new total)
// - message: string (success/error message)
```

### Calculating Points for Common Activities

For activities with dynamic point values (like reviews and approvals), use the calculation helpers:

```typescript
import { calculateReviewPoints, calculateApprovalPoints } from '@/middleware/pointsHandler'

// For article reviews
const pointsToAward = await calculateReviewPoints({ payload, userId });

// For student approvals
const pointsToAward = await calculateApprovalPoints({ payload, userId });
```

### Direct API Usage

You can also use the direct API endpoint for adding points:

```typescript
const response = await fetch('/api/user/add-points', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include',
  body: JSON.stringify({
    targetUserId: studentId,
    type: 'achievement_earned',
    points: 15,
    description: 'Earned an achievement',
  }),
})
```

## Point Values for Common Activities

### Student Points

| Activity | Points | Notes |
|----------|--------|-------|
| Creating an article | 5 | Awarded when article is saved |
| Publishing an article | 10 | When article is approved |
| Article ratings | 5-50 | 5 points per star |
| Article views | 1 | Per 10 views |
| Achievements | Varies | Depends on achievement |

### Teacher Points

| Activity | Points | Notes |
|----------|--------|-------|
| Reviewing articles | 10 | First 3 reviews per day |
| Additional reviews | 2 | After first 3 per day |
| Approving students | 10 | First 2 approvals per day |
| Additional approvals | 2 | After first 2 per day |
| Mentor feedback | 8 | When rated positively by mentor |

### Mentor Points

| Activity | Points | Notes |
|----------|--------|-------|
| Creating news posts | 20 | Per post |
| News post views | 20 | Per 50 views |
| Reviewing teachers | 15 | Per review |

## School Points Calculation

School points are automatically calculated as the sum of all user points for that school. You can trigger a recalculation with:

```typescript
import { updateSchoolPoints } from '@/utils/points'

// Update points for a specific school
await updateSchoolPoints({ payload, schoolId });

// Or update all schools
await updateSchoolPoints({ payload });
```

Or use the API endpoint:

```typescript
await fetch('/api/update-school-points', {
  method: 'GET',
  credentials: 'include',
})
```

## User Ranks

Users are ranked based on their points within their role. Ranks are used for displaying badges and privileges:

### Student Ranks

| Rank | Title | Points Required |
|------|-------|----------------|
| 0 | Novice Reporter | 0 |
| 1 | Reporter | 100 |
| 2 | Experienced Reporter | 200 |
| 3 | Senior Reporter | 500 |
| 4 | Master Reporter | 1000 |

### Teacher Ranks

| Rank | Title | Points Required |
|------|-------|----------------|
| 0 | Novice Teacher | 0 |
| 1 | Teacher | 100 |
| 2 | Experienced Teacher | 200 |
| 3 | Senior Teacher | 500 |
| 4 | Master Teacher | 1000 |

### Mentor Ranks

| Rank | Title | Points Required |
|------|-------|----------------|
| 0 | Novice Mentor | 0 |
| 1 | Mentor | 100 |
| 2 | Experienced Mentor | 200 |
| 3 | Senior Mentor | 500 |
| 4 | Master Mentor | 1000 |

## Implementation in Different Components

### Article Reviews

When implementing article reviews, add the following code:

```typescript
// In your API route after successfully submitting a review
const pointsToAward = await calculateReviewPoints({ payload, userId });

await payload.create({
  collection: 'activities',
  data: {
    userId,
    activityType: 'article-review',
    details: {
      articleId: article.id,
      articleTitle: article.title,
      rating,
      approved,
    },
    school: userSchool,
    points: pointsToAward,
  },
});

// Add points to the user
await addUserPoints({
  payload,
  userId,
  type: 'review_submitted',
  points: pointsToAward,
  description: `Reviewed article "${article.title}"`,
  reference: { articleId: article.id },
  createActivity: false, // We already created an activity
});
```

### Student Approvals

When approving students:

```typescript
// In your API route after successfully approving a student
const pointsToAward = await calculateApprovalPoints({ payload, userId });

await payload.create({
  collection: 'activities',
  data: {
    userId,
    targetUserId: studentId,
    activityType: 'student-approval',
    details: {
      action: 'approve',
      studentName: `${student.firstName} ${student.lastName}`,
    },
    school: userSchool,
    points: pointsToAward,
  },
});

// Add points to the teacher
await addUserPoints({
  payload,
  userId,
  type: 'other',
  points: pointsToAward,
  description: `Approved student ${student.firstName} ${student.lastName}`,
  reference: { studentId },
  createActivity: false, // We already created an activity
});
```

## Troubleshooting

If points aren't being updated correctly:

1. Check that the `addUserPoints` function is being called with the correct parameters
2. Verify that the user document has the `points` and `pointsActivities` fields
3. Look for errors in the console related to point updates
4. Use the recalculation endpoint `/api/update-rankings` to fix any inconsistencies

## Points System Improvements

This document has been updated to reflect recent improvements to the points system:

1. **Arabic Notifications**: All point notifications are now in Arabic by default
2. **Error Handling**: Better error handling with rollback capabilities
3. **Duplicate Prevention**: Transaction IDs to prevent duplicate point awards
4. **Incremental Updates**: School points can now be updated incrementally for better performance
5. **Scheduled Tasks**: A scheduled task to periodically verify and fix user points

## Scheduled Task Setup

A new scheduled task has been created to ensure consistency in the points system. It performs:

1. Update of all user rankings and statistics
2. Verification and correction of user points for recently active users

To run this task manually:

```bash
npm run ts-node src/scripts/scheduled-tasks.ts
```

For automatic execution, set up a cron job to run daily:

```
0 0 * * * cd /path/to/your/app && npm run ts-node src/scripts/scheduled-tasks.ts > /path/to/logs/update-points.log 2>&1
```

## Efficient School Points Updates

The school points system now supports incremental updates. When points are awarded to a user, the school points are updated incrementally rather than recalculating the entire school total:

```typescript
await updateSchoolPoints({ 
  payload, 
  schoolId,
  incrementalUpdate: true,
  pointsChange: points 
})
```

This is significantly more efficient for large schools with many users.