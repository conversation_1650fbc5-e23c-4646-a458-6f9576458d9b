import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { populateMockData } from '@/utils/populateMockData'

/**
 * API route to populate test data for the statistics page
 * This is for development and testing purposes only
 */
export async function GET(_req: NextRequest) {
  try {
    console.log('Populate test data API called')
    
    // Get the payload instance
    const payload = await getPayload({ config })
    
    // Populate test data
    const success = await populateMockData(payload)
    
    if (success) {
      // Return success response
      return NextResponse.json({
        success: true,
        message: 'Test data populated successfully',
      })
    } else {
      return NextResponse.json(
        { error: 'Failed to populate test data' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error populating test data:', error)
    return NextResponse.json(
      { 
        error: 'Internal Server Error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
} 