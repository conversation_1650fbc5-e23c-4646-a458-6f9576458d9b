'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { FeedbackManager } from '../super-admin/components/feedback-manager'
import { Skeleton } from '@/components/ui/skeleton'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Flag, Download } from 'lucide-react'
import ReportedIssuesTable from '@/components/dashboard/ReportedIssuesTable'
import { ReportsExporter } from '@/app/dashboard/super-admin/components/reports-exporter'

export default function FeedbackPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    async function fetchUserData() {
      try {
        // Check authentication status
        const response = await fetch('/api/auth/me', {
          credentials: 'include',
        })

        if (!response.ok) {
          throw new Error('Failed to authenticate')
        }

        const data = await response.json()
        setUser(data.user)
        setIsLoading(false)
      } catch (error) {
        console.error('Error fetching user data:', error)
        setError('Failed to load user data')
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [])

  if (isLoading) {
    return (
      <DashboardLayout>
        <h1 className="text-2xl font-bold mb-4">Feedback & Reports</h1>
        <Skeleton className="h-[500px] w-full" />
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <h1 className="text-2xl font-bold mb-4">Feedback & Reports</h1>
        <div className="text-red-500 p-4 border border-red-300 rounded-md bg-red-50">
          {error}
        </div>
      </DashboardLayout>
    )
  }

  // Customize FeedbackManager based on user role
  const customizeFeedbackManager = () => {
    // Extract user role
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    
    // Extract user's school if applicable
    let schoolId = null
    if (role === 'school-admin' || role === 'teacher') {
      if (typeof user.school === 'object') {
        schoolId = user.school?.id
      } else {
        schoolId = user.school
      }
    }
    
    return (
      <FeedbackManager 
        apiEndpoint="/api/dashboard/feedback" 
        schoolId={schoolId} 
      />
    )
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-6">Feedback & Reports</h1>
        
        <Tabs defaultValue="feedback">
          <TabsList className="mb-6">
            <TabsTrigger value="feedback">Feedback</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>
          
          <TabsContent value="feedback">
            {customizeFeedbackManager()}
          </TabsContent>
          
          <TabsContent value="reports">
            <div className="grid gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Flag className="mr-2 h-5 w-5" />
                    Reported Issues
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ReportedIssuesTable schoolId="" userRole="admin" />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Download className="mr-2 h-5 w-5" />
                    Report Exports
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ReportsExporter />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
} 