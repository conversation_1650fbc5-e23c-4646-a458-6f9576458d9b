'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import AdminDashboard from '@/components/dashboard/AdminDashboard'
import SchoolAdminDashboard from '@/components/dashboard/SchoolAdminDashboard'
import TeacherDashboard from '@/components/dashboard/TeacherDashboard'
import StudentDashboard from '@/components/dashboard/StudentDashboard'
import MentorDashboard from '@/components/dashboard/MentorDashboard'

export default function DashboardPage() {
  const router = useRouter()
  const [user, setUser] = useState<any>(null)
  const [userRole, setUserRole] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function fetchUserData() {
      try {
        // Check if we're already authenticated by checking for cookies
        console.log('Checking for authentication...')

        // Check if user is authenticated
        console.log('Fetching user data from /api/auth/me')
        const authResponse = await fetch('/api/auth/me', {
          // Include credentials to ensure cookies are sent
          credentials: 'include',
          // Add cache control to prevent caching
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
            Expires: '0',
          },
        })

        console.log('Auth response status:', authResponse.status)

        if (!authResponse.ok) {
          console.log('Auth response not OK, redirecting to login')
          // Use a small delay to ensure the redirect happens
          setTimeout(() => {
            window.location.href = '/login'
          }, 100)
          return
        }

        // Parse the response data
        let authData
        try {
          authData = await authResponse.json()
          console.log('Auth data:', JSON.stringify(authData))
        } catch (jsonError) {
          console.error('Error parsing JSON response:', jsonError)
          window.location.href = '/login'
          return
        }

        if (!authData.user) {
          console.log('No user data found, redirecting to login')
          window.location.href = '/login'
          return
        }

        // If we get here, we're authenticated
        console.log('Authentication successful, user is logged in')

        setUser(authData.user)

        // Determine user role
        const role =
          typeof authData.user.role === 'object' ? authData.user.role?.slug : authData.user.role

        console.log('User role from auth data:', role)
        setUserRole(role)
        setIsLoading(false)

        // Log the authentication success
        console.log('Dashboard loaded successfully for user:', authData.user.email)
      } catch (err) {
        console.error('Error fetching user data:', err)
        setError('فشل في تحميل لوحة التحكم. يرجى المحاولة مرة أخرى.')
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [router])

  if (isLoading) {
    return (
      <DashboardLayout>
        <div dir="rtl" className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div dir="rtl" className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      </DashboardLayout>
    )
  }

  // Render the appropriate dashboard based on user role
  return (
    <DashboardLayout>
      {userRole === 'super-admin' ? (
        <AdminDashboard user={user} />
      ) : userRole === 'school-admin' ? (
        <SchoolAdminDashboard user={user} />
      ) : userRole === 'teacher' ? (
        <TeacherDashboard user={user} />
      ) : userRole === 'mentor' ? (
        <MentorDashboard user={user} />
      ) : (
        <StudentDashboard user={user} />
      )}
    </DashboardLayout>
  )
}
