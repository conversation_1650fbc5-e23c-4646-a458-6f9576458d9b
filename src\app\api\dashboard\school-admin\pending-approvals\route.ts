import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

// Type definitions
interface ApprovalRequest {
  _id: string | ObjectId;
  type: string;
  requesterId: string;
  requesterName: string;
  requesterRole: string;
  status: string;
  schoolId: string;
  createdAt: string;
  data: {
    email?: string;
    studentId?: string;
    pendingFirstName?: string;
    pendingLastName?: string;
    [key: string]: any;
  };
}

// Helper for MongoDB ObjectId conversion
const safeObjectId = (id: string) => {
  try {
    return new ObjectId(id);
  } catch (err) {
    return id;
  }
};

// Type-safe version that ensures ObjectId return type
const toObjectId = (id: string | null | undefined): ObjectId | null => {
  if (!id) return null;
  try {
    return new ObjectId(id);
  } catch (err) {
    return null;
  }
};

// Helper function to determine user's role
const getUserRoleSlug = async (db: any, user: any): Promise<string> => {
  // First check if role is directly available as a string
  if (typeof user.role === 'string') {
    // Return direct role if it's a recognized slug
    if (['student', 'teacher', 'mentor', 'school-admin', 'super-admin'].includes(user.role)) {
      return user.role;
    }
    
    // If role is an ObjectId in string form, look it up
    if (ObjectId.isValid(user.role)) {
      try {
        const role = await db.collection('roles').findOne({ _id: new ObjectId(user.role) });
        if (role && role.slug) {
          return role.slug;
        }
      } catch (error) {
        console.error('Error looking up role by ID string:', error);
      }
    }
  }
  
  // If role is an ObjectId instance, look it up
  else if (user.role instanceof ObjectId) {
    try {
      const role = await db.collection('roles').findOne({ _id: user.role });
      if (role && role.slug) {
        return role.slug;
      }
    } catch (error) {
      console.error('Error looking up role by ObjectId:', error);
    }
  }
  
  // If role is an object with slug
  else if (typeof user.role === 'object' && user.role !== null) {
    if (typeof user.role.slug === 'string') {
      return user.role.slug;
    }
    if (user.role._id) {
      try {
        const role = await db.collection('roles').findOne({ _id: user.role._id });
        if (role && role.slug) {
          return role.slug;
        }
      } catch (error) {
        console.error('Error looking up role by embedded _id:', error);
      }
    }
  }
  
  // Default to 'student' if can't determine role
  return 'student';
};

// Safe ObjectId finder
const findById = async (collection: any, id: string): Promise<any> => {
  if (!id) return null;
  
  try {
    // First try as ObjectId
    if (ObjectId.isValid(id)) {
      const result = await collection.findOne({ _id: new ObjectId(id) });
      if (result) return result;
    }
    
    // Then try as string ID
    return await collection.findOne({ 
      $or: [
        { id: id },
        { _id: id }
      ] 
    });
  } catch (error) {
    console.error('Error in findById:', error);
    return null;
  }
};

export async function GET(req: NextRequest) {
  try {
    console.log('School admin pending approvals API called')
    
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value
    console.log('Token exists:', !!token)

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the token and get the user ID
    const { userId } = await verifyJWT(token)
    console.log('User ID from token:', userId)

    if (!userId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get URL parameters
    const url = new URL(req.url)
    const querySchoolId = url.searchParams.get('schoolId')
    console.log('Query school ID:', querySchoolId)
    const limit = parseInt(url.searchParams.get('limit') || '10')

    // Try to get data from MongoDB first
    try {
      const { db } = await connectToDatabase()

      // Get the user from MongoDB
      const mongoUser = await db.collection('users').findOne({ _id: safeObjectId(userId) })
      console.log('MongoDB user found:', !!mongoUser)

      if (mongoUser) {
        // Debug the user object to see its structure
        console.log('User role property:', mongoUser.role)
        console.log('User school property:', mongoUser.school)
        
        // More comprehensive role detection 
        let userRoleSlug = null
        
        // If role is a string and looks like a direct slug
        if (typeof mongoUser.role === 'string' && 
            (mongoUser.role === 'school-admin' || mongoUser.role === 'super-admin')) {
          userRoleSlug = mongoUser.role
          console.log('User role direct string match:', userRoleSlug)
        }
        // If role is an object with slug 
        else if (typeof mongoUser.role === 'object' && 
                 mongoUser.role !== null && 
                 typeof mongoUser.role.slug === 'string') {
          userRoleSlug = mongoUser.role.slug
          console.log('User role from object slug:', userRoleSlug)
        }
        // If role is an ObjectId, look it up
        else if (mongoUser.role instanceof ObjectId || 
                (typeof mongoUser.role === 'string' && ObjectId.isValid(mongoUser.role))) {
          const roleId = mongoUser.role instanceof ObjectId ? 
                        mongoUser.role : new ObjectId(mongoUser.role)
          console.log('Looking up role by ID:', roleId)
          
          const role = await db.collection('roles').findOne({ _id: roleId })
          if (role && role.slug) {
            userRoleSlug = role.slug
            console.log('User role from lookup:', userRoleSlug)
          }
        }
        
        console.log('Final determined role slug:', userRoleSlug)

        // Verify user is a school admin
        if (userRoleSlug !== 'school-admin' && userRoleSlug !== 'super-admin') {
          console.log('User is not school-admin or super-admin, access forbidden')
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
        }

        // Get school ID with more comprehensive detection
        let schoolId = null
        
        // First try from query parameter
        if (querySchoolId) {
          schoolId = querySchoolId
          console.log('Using school ID from query:', schoolId)
        }
        // Then try from user object - as direct string
        else if (typeof mongoUser.school === 'string') {
          schoolId = mongoUser.school
          console.log('Using school ID as direct string:', schoolId)
        }
        // Try from user object - as object with id
        else if (typeof mongoUser.school === 'object' && 
                 mongoUser.school !== null && 
                 typeof mongoUser.school.id === 'string') {
          schoolId = mongoUser.school.id
          console.log('Using school ID from object.id:', schoolId)
        }
        // Try from user object - as object with _id
        else if (typeof mongoUser.school === 'object' && 
                 mongoUser.school !== null && 
                 mongoUser.school._id) {
          schoolId = mongoUser.school._id.toString()
          console.log('Using school ID from object._id:', schoolId)
        }

        console.log('Final determined school ID:', schoolId)
        
        if (!schoolId) {
          console.log('No valid school ID found')
          return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
        }

        // Check if the collection exists
        const collections = await db.listCollections({ name: 'approvalRequests' }).toArray()
        console.log('approvalRequests collection exists:', collections.length > 0)
        
        let pendingApprovals: ApprovalRequest[] = []

        if (collections.length > 0) {
          // Collection exists, fetch data
          pendingApprovals = await db
            .collection('approvalRequests')
            .find({
              status: 'pending',
              schoolId: schoolId,
            })
            .limit(limit)
            .toArray()
          
          console.log(`Found ${pendingApprovals.length} pending approvals in approvalRequests collection`)
        } else {
          console.log('approvalRequests collection does not exist in MongoDB')
        }
        
        // If no pending approvals found, check for pending students directly
        if (pendingApprovals.length === 0) {
          console.log('No approval requests found, checking for pending students directly')
          
          // Find pending students from this school with more robust query
          const pendingStudents = await db
            .collection('users')
            .find({
              status: 'pending',
              $or: [
                { 'school._id': schoolId },
                { 'school._id': toObjectId(schoolId) },
                { 'school.id': schoolId },
                { school: schoolId },
                { school: toObjectId(schoolId) }
              ]
            })
            .limit(limit)
            .toArray()
          
          console.log(`Found ${pendingStudents.length} pending students with direct query`)
          
          if (pendingStudents.length > 0) {
            // Log some details about the first few students for debugging
            pendingStudents.slice(0, 2).forEach((student, i) => {
              console.log(`Student ${i+1} info:`, {
                id: student._id.toString(),
                email: student.email,
                name: `${student.firstName || ''} ${student.lastName || ''}`.trim(),
                school: typeof student.school === 'object' ? 'object' : student.school,
                role: typeof student.role === 'object' ? student.role?.slug : student.role
              })
            })
            
            // Map students to approval format with correct role detection
            pendingApprovals = await Promise.all(pendingStudents.map(async student => {
              const roleSlug = await getUserRoleSlug(db, student);
              
              return {
                _id: student._id,
                type: 'user',
                requesterId: student._id.toString(),
                requesterName: `${student.firstName || ''} ${student.lastName || ''}`.trim() || student.email || 'Unknown Student',
                requesterRole: roleSlug,
                status: 'pending',
                schoolId: schoolId,
                createdAt: student.createdAt || new Date().toISOString(),
                data: {
                  email: student.email,
                  studentId: student._id.toString()
                }
              };
            }));
          }
          
          // Also check for profile changes with more robust query
          const profileChangeStudents = await db
            .collection('users')
            .find({
              $or: [
                { profileImageStatus: 'pending' },
                { nameChangeStatus: 'pending' }
              ],
              $and: [
                { 
                  $or: [
                    { 'school._id': schoolId },
                    { 'school._id': toObjectId(schoolId) },
                    { 'school.id': schoolId },
                    { school: schoolId },
                    { school: toObjectId(schoolId) }
                  ]
                }
              ]
            })
            .limit(limit)
            .toArray()
          
          console.log(`Found ${profileChangeStudents.length} students with pending profile changes`)
          
          if (profileChangeStudents.length > 0) {
            // Add them to the pendingApprovals array
            for (const student of profileChangeStudents) {
              const roleSlug = await getUserRoleSlug(db, student);
              
              // Add profile image approval if pending
              if (student.profileImageStatus === 'pending') {
                pendingApprovals.push({
                  _id: new ObjectId(),
                  type: 'profile-image',
                  requesterId: student._id.toString(),
                  requesterName: `${student.firstName || ''} ${student.lastName || ''}`.trim() || student.email || 'Unknown Student',
                  requesterRole: roleSlug,
                  status: 'pending',
                  schoolId: schoolId,
                  createdAt: student.updatedAt || new Date().toISOString(),
                  data: {
                    email: student.email,
                    studentId: student._id.toString()
                  }
                });
              }
              
              // Add name change approval if pending
              if (student.nameChangeStatus === 'pending') {
                pendingApprovals.push({
                  _id: new ObjectId(),
                  type: 'name-change',
                  requesterId: student._id.toString(),
                  requesterName: `${student.firstName || ''} ${student.lastName || ''}`.trim() || student.email || 'Unknown Student',
                  requesterRole: roleSlug,
                  status: 'pending',
                  schoolId: schoolId,
                  createdAt: student.updatedAt || new Date().toISOString(),
                  data: {
                    email: student.email,
                    studentId: student._id.toString(),
                    pendingFirstName: student.pendingFirstName,
                    pendingLastName: student.pendingLastName
                  }
                });
              }
            }
          }
          
          // Also check for article approvals
          try {
            const pendingArticles = await db
              .collection('articles')
              .find({
                status: 'pending-approval',
                $or: [
                  { 'school._id': schoolId },
                  { 'school._id': toObjectId(schoolId) },
                  { 'school.id': schoolId },
                  { school: schoolId },
                  { school: toObjectId(schoolId) },
                  { 'author.school._id': schoolId },
                  { 'author.school._id': toObjectId(schoolId) },
                  { 'author.school.id': schoolId },
                  { 'author.school': schoolId },
                  { 'author.school': toObjectId(schoolId) }
                ]
              })
              .limit(limit)
              .toArray()
              
            console.log(`Found ${pendingArticles.length} pending articles`)
            
            if (pendingArticles.length > 0) {
              for (const article of pendingArticles) {
                // Get author info
                const authorName = article.author && typeof article.author === 'object' 
                  ? `${article.author.firstName || ''} ${article.author.lastName || ''}`.trim() 
                  : 'Unknown Author'
                  
                const authorId = article.author 
                  ? (typeof article.author === 'object' 
                     ? (article.author.id || article.author._id || '').toString()
                     : article.author.toString())
                  : ''
                
                // Try to determine author's role directly from article if available
                let authorRole = 'student';  // Default
                
                if (article.author && typeof article.author === 'object') {
                  // If author has role info embedded
                  if (article.author.role) {
                    if (typeof article.author.role === 'string') {
                      authorRole = article.author.role;
                    } else if (typeof article.author.role === 'object' && article.author.role.slug) {
                      authorRole = article.author.role.slug;
                    }
                  }
                  
                  // If we have an author ID, try to look up their full user record
                  if (authorId) {
                    try {
                      const authorUser = await findById(db.collection('users'), authorId);
                      
                      if (authorUser) {
                        authorRole = await getUserRoleSlug(db, authorUser);
                      }
                    } catch (error) {
                      console.error('Error looking up article author:', error);
                    }
                  }
                }
                
                pendingApprovals.push({
                  _id: article._id,
                  type: 'article',
                  requesterId: authorId,
                  requesterName: authorName,
                  requesterRole: authorRole,
                  status: 'pending',
                  schoolId: schoolId,
                  createdAt: article.createdAt || new Date().toISOString(),
                  data: {
                    articleId: article._id.toString(),
                    title: article.title || 'Untitled Article'
                  }
                });
              }
            }
          } catch (articleError) {
            console.error('Error checking for pending articles:', articleError)
          }
        }

        console.log(`Returning ${pendingApprovals.length} pending approvals`)
        return NextResponse.json({
          pendingApprovals: pendingApprovals.map((approval: ApprovalRequest) => ({
            id: approval._id.toString(),
            type: approval.type,
            requesterId: approval.requesterId,
            requesterName: approval.requesterName || 'Unknown User',
            requesterRole: approval.requesterRole || 'Unknown Role',
            status: approval.status,
            createdAt: approval.createdAt,
            data: approval.data,
          })),
        })
      }
    } catch (mongoError) {
      console.warn(
        'Error fetching pending approvals from MongoDB, falling back to Payload:',
        mongoError,
      )
    }

    // Fallback to Payload CMS if MongoDB fails
    console.log('Falling back to Payload CMS')
    const payload = await getPayload({ config })

    // Get the current user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })
    console.log('Payload user found:', !!user)

    // Verify user is a school admin
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    console.log('Payload user role:', role)
    
    if (role !== 'school-admin' && role !== 'super-admin') {
      console.log('User is not school-admin or super-admin in Payload')
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get school ID
    const schoolId =
      querySchoolId || (typeof user.school === 'object' ? user.school?.id : user.school)
    console.log('Payload school ID:', schoolId)

    if (!schoolId) {
      console.log('No school ID in Payload')
      return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
    }

    // Get pending approvals
    let pendingApprovals = []
    try {
      // Try to access the collection directly
      let hasApprovalRequests = false

      try {
        // Use any to bypass TypeScript checking for custom collections
        const payloadAny = payload as any
        // Try to access the collection schema
        const schema = payloadAny.collections?.approvalRequests?.config?.fields
        hasApprovalRequests = !!schema
      } catch (error) {
        console.log('Error checking for approvalRequests collection:', error)
        hasApprovalRequests = false
      }

      if (hasApprovalRequests) {
        // Use any to bypass TypeScript checking for custom collections
        const payloadAny = payload as any
        const approvalRequests = await payloadAny.find({
          collection: 'approvalRequests',
          where: {
            status: { equals: 'pending' },
            schoolId: { equals: schoolId },
          },
          limit,
        })
        pendingApprovals = approvalRequests.docs || []
      } else {
        console.log('approvalRequests collection does not exist in Payload CMS')
        // Return empty array
        pendingApprovals = []
      }
    } catch (error) {
      console.warn('Error fetching approval requests:', error)
      // Return empty array
      pendingApprovals = []
    }

    // Format pending approvals
    const formattedPendingApprovals = pendingApprovals.map((approval) => ({
      id: approval.id,
      type: approval.type,
      requesterId: approval.requesterId,
      requesterName: approval.requesterName || 'Unknown User',
      requesterRole: approval.requesterRole || 'Unknown Role',
      status: approval.status,
      createdAt: approval.createdAt,
      data: approval.data,
    }))

    return NextResponse.json({ pendingApprovals: formattedPendingApprovals })
  } catch (error) {
    console.error('Error fetching pending approvals:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  try {
    console.log('School admin approval action called')
    
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the token and get the user ID
    const { userId } = await verifyJWT(token)

    if (!userId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }
    
    // Get request body
    const body = await req.json()
    console.log('Request body:', body)

    const { studentId, action, approvalType } = body

    if (!studentId || !action || !approvalType) {
      return NextResponse.json(
        { error: 'Student ID, action, and approval type are required' },
        { status: 400 },
      )
    }

    // Initialize Payload
    const payload = await getPayload({ config })

    // Get the current user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })

    // Verify user is a school admin or super admin
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'school-admin' && role !== 'super-admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get student
    const student = await payload.findByID({
      collection: 'users',
      id: studentId,
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Prepare update data for the student based on approval type
    let updateData = {}
    let notificationMessage = ''

    // Handle different approval types
    switch (approvalType) {
      case 'user':
        if (action === 'approve') {
          updateData = { status: 'active' }
          notificationMessage = 'Your account has been approved'
        } else {
          updateData = { status: 'rejected' }
          notificationMessage = 'Your account has been rejected'
        }
        break
      
      case 'profile-image':
        if (action === 'approve') {
          updateData = {
            profileImage: student.pendingProfileImage,
            pendingProfileImage: null,
            profileImageStatus: 'approved'
          }
          notificationMessage = 'Your profile image has been approved'
        } else {
          updateData = {
            pendingProfileImage: null,
            profileImageStatus: 'rejected'
          }
          notificationMessage = 'Your profile image has been rejected'
        }
        break
      
      case 'name-change':
        if (action === 'approve') {
          updateData = {
            firstName: student.pendingFirstName,
            lastName: student.pendingLastName,
            pendingFirstName: null,
            pendingLastName: null,
            nameChangeStatus: 'approved'
          }
          notificationMessage = 'Your name change has been approved'
        } else {
          updateData = {
            pendingFirstName: null,
            pendingLastName: null,
            nameChangeStatus: 'rejected'
          }
          notificationMessage = 'Your name change has been rejected'
        }
        break
      
      default:
        return NextResponse.json({ error: 'Invalid approval type' }, { status: 400 })
    }

    // Update the student record
    try {
      await payload.update({
        collection: 'users',
        id: studentId,
        data: updateData
      })
    } catch (error) {
      console.error('Error updating student:', error)
      return NextResponse.json({ 
        error: 'Failed to update student',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, { status: 500 })
    }

    // Create notification for the student
    try {
      await payload.create({
        collection: 'notifications',
        data: {
          user: studentId,
          message: notificationMessage,
          type: action === 'approve' ? 'success' : 'info',
          read: false
        },
      })
    } catch (error) {
      console.error('Error creating notification:', error)
      // Continue even if notification creation fails
    }

    return NextResponse.json({
      success: true,
      message: `${approvalType} request ${action === 'approve' ? 'approved' : 'rejected'} successfully`
    })
  } catch (error) {
    console.error('Error processing approval request:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
