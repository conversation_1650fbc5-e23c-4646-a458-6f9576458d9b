// Common interfaces for the application

export interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  role: Role | string
  school?: School | string
  profileImage?: Media
  points?: number
  grade?: string
  createdAt: string
  updatedAt?: string
}

export interface Role {
  id: string
  name: string
  slug: 'student' | 'teacher' | 'mentor' | 'school-admin' | 'super-admin'
}

export interface School {
  id: string
  name: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  phone?: string
  email?: string
  website?: string
  description?: string
  image?: Media | string | null
  imageUrl?: string | null
  admin?: User | string | null
  createdAt?: string
  updatedAt?: string
}

export interface Media {
  id: string
  // {{change 1}} Allow url to be string, null, or undefined to match payload-types
  url?: string | null
  // {{change 2}} Allow filename to be string, null, or undefined to match payload-types
  filename?: string | null
  // {{change 3}} Allow mimeType to be string, null, or undefined to match payload-types
  mimeType?: string | null
  filesize?: number | null
  width?: number | null
  height?: number
  alt?: string
}

// For handling different image formats in the application
export type ImageSource = Media | string | { url: string } | null | undefined

export interface Article {
  id: string
  title: string
  slug?: string
  content: any // Rich text content
  author:
    | User
    | string
    | {
        id: string
        firstName?: string
        lastName?: string
        profileImage?: ImageSource
        role?: Role | string | { slug: string }
        grade?: string
      }
  status: 'draft' | 'pending-review' | 'published' | string
  category?: string
  featuredImage?: ImageSource
  teacherReview?: ArticleReview[] | any[]
  createdAt: string
  updatedAt?: string
  numericId?: number
}

export interface ArticleReview {
  reviewer: User | string | { id: string; firstName?: string; lastName?: string }
  reviewerName?: string
  comment: string
  rating: number
  approved: boolean
  reviewDate?: string
  id?: string
}

export interface News {
  id: string
  title: string
  slug?: string
  content: any // Rich text content
  author:
    | User
    | string
    | {
        id: string
        firstName?: string
        lastName?: string
        profileImage?: ImageSource
      }
  status: 'draft' | 'published' | string
  featuredImage?: ImageSource
  image?: ImageSource // For backward compatibility
  views?: number
  createdAt: string
  updatedAt?: string
  numericId?: number
  isOwner?: boolean // Flag to indicate if the current user is the author
}

export interface Activity {
  id: string
  userId: string
  activityType: 'article-review' | 'article-creation' | 'login' | 'profile-update'
  details: Record<string, any>
  school?: School | string
  points?: number
  isRequired?: boolean
  createdAt: string
}

export interface Notification {
  id: string
  user: User | string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  read: boolean
  link?: string
  createdAt: string
}

// API response interfaces
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  status?: number
}

export interface ArticlesResponse {
  articles: Article[]
}

export interface ArticleResponse {
  article: Article
}

export interface ReviewResponse {
  message: string
  points: number
  dailyReviews: number
  dailyGoal: number
}

export interface ReportResponse {
  message: string
}

export interface UserResponse {
  user: User | null
  role?: string
  isAdmin?: boolean
  isAuthenticated?: boolean
  error?: string
}
