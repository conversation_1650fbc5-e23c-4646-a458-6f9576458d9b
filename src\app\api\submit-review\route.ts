import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'
import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    console.log('Submit review API called')

    // Get form data
    const formData = await req.formData()
    const articleId = formData.get('articleId') as string
    const rating = parseInt(formData.get('rating') as string, 10)
    const comment = formData.get('comment') as string
    const approved = formData.has('approved') && formData.get('approved') === 'true'

    console.log('Form data received:', { articleId, rating, comment, approved })

    // Get cookies directly
    const cookieHeader = req.headers.get('cookie') || ''
    console.log('Cookie header:', cookieHeader)

    // Extract the payload token manually
    const payloadTokenMatch = cookieHeader.match(/payload-token=([^;]+)/)
    const payloadToken = payloadTokenMatch ? payloadTokenMatch[1] : null

    if (!payloadToken) {
      console.log('No payload token found in cookies')
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    console.log('Payload token found')

    // Verify the token manually
    let userId
    try {
      const decoded = jwt.verify(payloadToken, process.env.PAYLOAD_SECRET || 'secret')
      userId = typeof decoded === 'object' && decoded.id ? decoded.id : null

      if (!userId) {
        console.log('Invalid token - no user ID')
        return NextResponse.json({
          success: false,
          error: 'Invalid authentication token'
        }, { status: 401 })
      }

      console.log('Token verified, user ID:', userId)
    } catch (tokenError) {
      console.error('Token verification error:', tokenError)
      return NextResponse.json({
        success: false,
        error: 'Invalid authentication token'
      }, { status: 401 })
    }

    // Initialize Payload
    const payload = await getPayload({
      config,
    })

    // Get the user directly
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })

    if (!user) {
      console.log('User not found')
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 })
    }

    console.log('User found:', user.email)

    // Check if user is a teacher
    const isTeacher = typeof user.role === 'object'
      ? user.role?.slug === 'teacher'
      : user.role === 'teacher'

    if (!isTeacher) {
      console.log('User is not a teacher:', user.role)
      return NextResponse.json({
        success: false,
        error: 'Only teachers can review articles'
      }, { status: 403 })
    }

    // Find the article
    console.log('Looking for article with ID:', articleId)

    try {
      // Try to find all articles first to see what's available
      console.log('Listing all articles to debug...')
      const allArticles = await payload.find({
        collection: 'articles',
        limit: 5,
      })

      console.log(`Found ${allArticles.totalDocs} total articles`)
      console.log('Sample articles:')
      allArticles.docs.forEach((a, i) => {
        console.log(`Article ${i+1}:`, {
          id: a.id,
          _id: a._id,
          title: a.title,
          status: a.status
        })
      })

      // First try to find the article using find() to avoid NotFound errors
      const articlesResult = await payload.find({
        collection: 'articles',
        where: {
          or: [
            { id: { equals: articleId } },
            { slug: { equals: articleId } }
          ]
        },
        limit: 1
      })

      if (articlesResult.docs.length === 0) {
        console.log('Article not found with ID:', articleId)
        console.log('Trying to find article by URL parameter ID...')

        // Try to find the article using the URL parameter ID
        const urlParamId = req.nextUrl.pathname.split('/').pop()
        console.log('URL parameter ID:', urlParamId)

        if (urlParamId && urlParamId !== 'submit-review') {
          const articlesByUrlParam = await payload.find({
            collection: 'articles',
            where: {
              or: [
                { id: { equals: urlParamId } },
                { slug: { equals: urlParamId } }
              ]
            },
            limit: 1
          })

          if (articlesByUrlParam.docs.length > 0) {
            console.log('Found article by URL parameter ID:', articlesByUrlParam.docs[0].id)
            return NextResponse.json({
              success: false,
              error: 'Article ID mismatch. Please use the correct article ID.',
              correctId: articlesByUrlParam.docs[0].id
            }, { status: 400 })
          }
        }

        return NextResponse.json({
          success: false,
          error: 'Article not found'
        }, { status: 404 })
      }

      const article = articlesResult.docs[0]
      console.log('Found article:', article.id, article.title)

      // Check if article is pending review
      console.log('Article status:', article.status)

      // For testing purposes, allow any status
      // if (article.status !== 'pending-review') {
      //   return NextResponse.json({
      //     success: false,
      //     error: 'This article is not pending review'
      //   }, { status: 400 })
      // }

      // Create the review
      const teacherReview = {
        reviewer: user.id,
        rating,
        comment,
        approved,
      }

      // Log the current teacherReview array
      console.log('Current teacherReview:', article.teacherReview || [])

      // Prepare the update data
      const updateData = {
        teacherReview: [...(article.teacherReview || []), teacherReview],
        // If approved, set status to published
        ...(approved ? { status: 'published' } : {}),
      }

      console.log('Update data:', updateData)

      // Update the article with the review
      try {
        const updatedArticle = await payload.update({
          collection: 'articles',
          id: article.id,
          data: updateData,
        })

        console.log('Article updated successfully with ID:', updatedArticle.id)

        // Return success response
        return NextResponse.json({
          success: true,
          message: 'Review submitted successfully',
          data: {
            article: updatedArticle
          }
        })
      } catch (updateError) {
        console.error('Error updating article:', updateError)
        return NextResponse.json({
          success: false,
          error: 'Failed to update article',
          details: String(updateError)
        }, { status: 500 })
      }
    } catch (articleError) {
      console.error('Error processing article:', articleError)
      return NextResponse.json({
        success: false,
        error: 'Error processing article'
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Error in submit review:', error)
    return NextResponse.json({
      success: false,
      error: 'An unexpected error occurred',
      details: String(error)
    }, { status: 500 })
  }
}
