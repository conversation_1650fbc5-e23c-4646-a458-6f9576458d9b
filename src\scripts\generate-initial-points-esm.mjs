// ESM version with dynamic imports
async function generateInitialPoints() {
  try {
    console.log('Starting initial points and rankings generation...')
    
    // Dynamically import the required modules
    const { getPayload } = await import('payload')
    
    // We'll need to use the existing payload instance instead of creating a new one with the config
    // since we can't directly import the TypeScript config file
    const payload = await getPayload({
      // Initialize with minimal config needed to connect to the database
      secret: process.env.PAYLOAD_SECRET || 'your-secret-key',
      mongoURL: process.env.MONGODB_URI || 'mongodb://localhost/young-reporter',
    })
    
    // Define the functions we need inline since the modules are in TypeScript
    // and we're using an .mjs file to ensure ESM compatibility
    
    const calculateUserPoints = async ({
      payload,
      userId,
    }) => {
      try {
        // Get user achievements and calculate points
        const userAchievements = await payload.find({
          collection: 'user-achievements',
          where: {
            user: {
              equals: userId,
            },
          },
          depth: 1,
        })
    
        let achievementPoints = 0
        userAchievements.docs.forEach((achievement) => {
          if (
            typeof achievement.achievement === 'object' &&
            achievement.achievement &&
            'points' in achievement.achievement
          ) {
            achievementPoints += achievement.achievement.points
          }
        })
    
        // Get articles by user and calculate points
        const articles = await payload.find({
          collection: 'articles',
          where: {
            author: {
              equals: userId,
            },
          },
        })
    
        let articlePoints = 0
        articles.docs.forEach((article) => {
          // Points for creating an article
          articlePoints += 5
    
          // Additional points for published articles
          if (article.status === 'published') {
            articlePoints += 15
          }
    
          // Points for teacher reviews (if they exist)
          if (article.teacherReview && Array.isArray(article.teacherReview)) {
            // Calculate average rating
            const ratings = article.teacherReview.map((review) => 
              typeof review === 'object' && review.rating ? review.rating : 0
            )
            
            if (ratings.length > 0) {
              const avgRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
              // Points based on average rating (1-10 scale)
              articlePoints += Math.round(avgRating * 2) // 2 points per rating point
            }
          }
        })
    
        // Get teacher reviews done by this user
        const reviewsCount = await payload.find({
          collection: 'articles',
          where: {
            'teacherReview.reviewer': {
              equals: userId,
            },
          },
        })
    
        // Points for reviews (10 points per review)
        const reviewPoints = reviewsCount.totalDocs * 10
    
        // Calculate total points
        return achievementPoints + articlePoints + reviewPoints
      } catch (error) {
        console.error('Error calculating user points:', error)
        return 0
      }
    }
    
    const getTopUsers = async ({
      payload,
      role = 'all',
      limit = 10,
    }) => {
      try {
        // Get all users (optionally filtered by role)
        const whereClause = role !== 'all' 
          ? { 'role.slug': { equals: role } }
          : {}
    
        const users = await payload.find({
          collection: 'users',
          where: whereClause,
          depth: 1,
        })
    
        // Calculate points for each user
        const usersWithPoints = await Promise.all(
          users.docs.map(async (user) => {
            const points = await calculateUserPoints({
              payload,
              userId: user.id,
            })
    
            // Determine user type based on role
            let type = 'student'
            if (typeof user.role === 'object' && user.role) {
              const roleSlug = user.role.slug
              if (roleSlug === 'teacher') type = 'teacher'
              else if (roleSlug === 'mentor') type = 'mentor'
              else if (roleSlug === 'school-admin') type = 'school'
            }
    
            return {
              id: user.id,
              name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
              anonymizedName: `Student ${user.id.substring(0, 4)}`,
              points,
              type,
            }
          })
        )
    
        // Sort by points (descending) and take the top N
        return usersWithPoints
          .sort((a, b) => b.points - a.points)
          .slice(0, limit)
      } catch (error) {
        console.error('Error getting top users:', error)
        return []
      }
    }
    
    const updatePointsAndRankings = async ({ 
      payload 
    }) => {
      try {
        // Get top students
        const topStudents = await getTopUsers({
          payload,
          role: 'student',
          limit: 20,
        })
    
        // Get top teachers
        const topTeachers = await getTopUsers({
          payload,
          role: 'teacher',
          limit: 10,
        })
    
        // Get top mentors
        const topMentors = await getTopUsers({
          payload,
          role: 'mentor',
          limit: 10,
        })
    
        // Find existing student ranking or create new one
        const existingStudentStats = await payload.find({
          collection: 'statistics',
          where: {
            type: {
              equals: 'studentRanking',
            },
          },
        })
    
        if (existingStudentStats.docs.length > 0) {
          // Update existing
          await payload.update({
            collection: 'statistics',
            id: existingStudentStats.docs[0].id,
            data: {
              data: topStudents,
            },
          })
        } else {
          // Create new
          await payload.create({
            collection: 'statistics',
            data: {
              name: 'Student Rankings by Points',
              type: 'studentRanking',
              data: topStudents,
            },
          })
        }
    
        // Find existing teacher ranking or create new one
        const existingTeacherStats = await payload.find({
          collection: 'statistics',
          where: {
            type: {
              equals: 'teacherRanking',
            },
          },
        })
    
        if (existingTeacherStats.docs.length > 0) {
          // Update existing
          await payload.update({
            collection: 'statistics',
            id: existingTeacherStats.docs[0].id,
            data: {
              data: topTeachers,
            },
          })
        } else {
          // Create new
          await payload.create({
            collection: 'statistics',
            data: {
              name: 'Teacher Rankings by Points',
              type: 'teacherRanking',
              data: topTeachers,
            },
          })
        }
    
        // Find existing mentor ranking or create new one
        const existingMentorStats = await payload.find({
          collection: 'statistics',
          where: {
            type: {
              equals: 'mentorRanking',
            },
          },
        })
    
        if (existingMentorStats.docs.length > 0) {
          // Update existing
          await payload.update({
            collection: 'statistics',
            id: existingMentorStats.docs[0].id,
            data: {
              data: topMentors,
            },
          })
        } else {
          // Create new
          await payload.create({
            collection: 'statistics',
            data: {
              name: 'Mentor Rankings by Points',
              type: 'mentorRanking',
              data: topMentors,
            },
          })
        }
    
        console.log('Points and rankings updated successfully')
      } catch (error) {
        console.error('Error updating points and rankings:', error)
      }
    }
    
    // Update points and rankings
    await updatePointsAndRankings({ payload })
    
    console.log('Initial points and rankings generated successfully!')
    process.exit(0)
  } catch (error) {
    console.error('Error generating initial points and rankings:', error)
    process.exit(1)
  }
}

generateInitialPoints() 