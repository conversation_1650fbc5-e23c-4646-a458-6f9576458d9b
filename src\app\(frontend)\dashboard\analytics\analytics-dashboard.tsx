'use client'

import Link from 'next/link'
import React, { useEffect, useState } from 'react'

interface AnalyticsDashboardProps {
  summary: any
  schoolData: any[]
  studentData: any[]
  recentArticles: any[]
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  summary,
  schoolData,
  studentData,
  recentArticles,
}) => {
  const [chartLoaded, setChartLoaded] = useState(false)
  const [Chart, setChart] = useState<any>(null)

  // Load Chart.js dynamically on the client side
  useEffect(() => {
    const loadChart = async () => {
      try {
        // Dynamic import of Chart.js
        const chartModule = await import('chart.js/auto')
        const reactChartModule = await import('react-chartjs-2')
        
        // Register required components
        chartModule.Chart.register(
          chartModule.CategoryScale,
          chartModule.LinearScale,
          chartModule.BarElement,
          chartModule.Title,
          chartModule.Tooltip,
          chartModule.Legend
        )
        
        setChart({
          Bar: reactChartModule.Bar,
          Doughnut: reactChartModule.Doughnut,
          Line: reactChartModule.Line,
        })
        setChartLoaded(true)
      } catch (error) {
        console.error('Failed to load Chart.js:', error)
      }
    }

    loadChart()
  }, [])

  // Prepare data for school performance chart
  const schoolChartData = {
    labels: schoolData.slice(0, 5).map(school => school.name),
    datasets: [
      {
        label: 'Articles Published',
        data: schoolData.slice(0, 5).map(school => school.articleCount),
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
    ],
  }

  // Prepare data for content distribution chart
  const contentDistributionData = {
    labels: ['Published Articles', 'Pending Review', 'Draft'],
    datasets: [
      {
        data: [
          summary.publishedArticles,
          summary.totalArticles - summary.publishedArticles,
          summary.totalArticles * 0.3, // Estimate for drafts
        ],
        backgroundColor: [
          'rgba(16, 185, 129, 0.6)', // Green
          'rgba(245, 158, 11, 0.6)', // Yellow
          'rgba(107, 114, 128, 0.6)', // Gray
        ],
        borderColor: [
          'rgba(16, 185, 129, 1)',
          'rgba(245, 158, 11, 1)',
          'rgba(107, 114, 128, 1)',
        ],
        borderWidth: 1,
      },
    ],
  }

  // Prepare data for user growth chart (simulated data)
  const userGrowthData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Students',
        data: [
          summary.totalStudents * 0.7,
          summary.totalStudents * 0.75,
          summary.totalStudents * 0.8,
          summary.totalStudents * 0.85,
          summary.totalStudents * 0.9,
          summary.totalStudents,
        ],
        borderColor: 'rgba(59, 130, 246, 1)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Teachers',
        data: [
          summary.totalTeachers * 0.8,
          summary.totalTeachers * 0.85,
          summary.totalTeachers * 0.9,
          summary.totalTeachers * 0.92,
          summary.totalTeachers * 0.95,
          summary.totalTeachers,
        ],
        borderColor: 'rgba(139, 92, 246, 1)',
        backgroundColor: 'rgba(139, 92, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
    ],
  }

  return (
    <div className="space-y-8">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Total Articles</h3>
          <p className="text-4xl font-bold text-blue-600">{summary.totalArticles}</p>
          <p className="text-sm text-gray-500 mt-2">
            {summary.publishedArticles} published articles
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Student Reporters</h3>
          <p className="text-4xl font-bold text-green-600">{summary.totalStudents}</p>
          <p className="text-sm text-gray-500 mt-2">Active young journalists</p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Teacher Mentors</h3>
          <p className="text-4xl font-bold text-purple-600">{summary.totalTeachers}</p>
          <p className="text-sm text-gray-500 mt-2">Supporting our students</p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Participating Schools</h3>
          <p className="text-4xl font-bold text-orange-600">{summary.totalSchools}</p>
          <p className="text-sm text-gray-500 mt-2">Across the country</p>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* School Performance Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Top Schools Performance</h3>
          <div className="h-80">
            {chartLoaded && Chart ? (
              <Chart.Bar
                data={schoolChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'top',
                    },
                    title: {
                      display: false,
                    },
                  },
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500">Loading chart...</p>
              </div>
            )}
          </div>
        </div>

        {/* Content Distribution Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Content Distribution</h3>
          <div className="h-80">
            {chartLoaded && Chart ? (
              <Chart.Doughnut
                data={contentDistributionData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'top',
                    },
                  },
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500">Loading chart...</p>
              </div>
            )}
          </div>
        </div>

        {/* User Growth Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4">User Growth (Last 6 Months)</h3>
          <div className="h-80">
            {chartLoaded && Chart ? (
              <Chart.Line
                data={userGrowthData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'top',
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500">Loading chart...</p>
              </div>
            )}
          </div>
        </div>

        {/* Top Students */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Top Student Authors</h3>
          <div className="space-y-4">
            {studentData.length > 0 ? (
              studentData.slice(0, 5).map((student, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between border-b border-gray-200 pb-3"
                >
                  <div className="flex items-center">
                    <span className="text-lg font-bold text-green-600 mr-3">#{index + 1}</span>
                    <div>
                      <h4 className="font-semibold">{student.name}</h4>
                      <p className="text-sm text-gray-500">
                        {student.school ? student.school.name : 'Unknown School'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">{student.articleCount} articles</p>
                    <p className="text-sm text-gray-500">{student.publishedCount} published</p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 text-center py-4">No student data available yet</p>
            )}
          </div>
        </div>
      </div>

      {/* Recent Articles */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-bold text-gray-800 mb-4">Recently Published Articles</h3>
        <div className="space-y-4">
          {recentArticles.length > 0 ? (
            recentArticles.map((article) => (
              <div
                key={article.id}
                className="flex flex-col md:flex-row justify-between border-b border-gray-200 pb-4"
              >
                <div>
                  <Link
                    href={`/articles/${article.id}`}
                    className="text-lg font-semibold text-blue-600 hover:underline"
                  >
                    {article.title}
                  </Link>
                  <p className="text-sm text-gray-500">
                    By{' '}
                    {typeof article.author === 'object'
                      ? article.author?.name || 'Unknown'
                      : 'Unknown'}
                  </p>
                </div>
                <div className="text-sm text-gray-500">
                  {new Date(article.createdAt).toLocaleDateString()}
                </div>
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center py-4">No articles published yet</p>
          )}
        </div>
        <div className="mt-6 text-center">
          <Link
            href="/articles"
            className="inline-block bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition"
          >
            View All Articles
          </Link>
        </div>
      </div>
    </div>
  )
}
