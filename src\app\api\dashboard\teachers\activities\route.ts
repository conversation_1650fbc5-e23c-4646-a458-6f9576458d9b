import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import { isMediaPath } from '@/lib/media-utils'
import config from '@/payload.config'
import { Activity as PayloadActivity, User as PayloadUser } from '@/payload-types'

// Define types for processing
interface UserStats {
  reviewsRated?: number
  totalMentorRating?: number
  averageMentorRating?: number
}

interface ProcessedUser {
  id: string
  firstName?: string
  lastName?: string
  email?: string
  profileImage?: any
  school?: any
  stats?: UserStats
}

interface ProcessedActivity {
  id: string
  userId: string
  userName?: string
  userEmail?: string
  userProfileImage?: any
  activityType: string
  details: any
  createdAt: string
  school?: any
  points?: number
  isRated: boolean
  rating?: any
  mentorFeedback?: string
  stats?: UserStats
}

export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value || ''

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized: Please log in' }, { status: 401 })
    }

    const user = await extractUserFromToken(token)

    console.log(
      'API: Teacher Activities: User extracted:',
      user ? { id: user.id, role: user.role, school: user.school } : null,
    )

    // Get query parameters early to log teacherId
    const url = new URL(request.url)
    const teacherId: string | null = url.searchParams.get('teacherId')
    console.log('API: Teacher Activities: Received teacherId:', teacherId)

    // Check if we have the minimal information to proceed
    // If the user extraction failed but we have a user ID in the token, and that user is trying to access
    // their own data (or we have a teacherId parameter), we can still proceed with the request
    if (!user || !user.id) {
      return NextResponse.json({ error: 'Unauthorized: Invalid user information' }, { status: 401 })
    }

    // Use role from the token, or assume a role based on the request
    let userRole = user.role

    // If user.role is null (token extraction issue) but we have a valid user.id
    if (user.id && userRole === null) {
      if (teacherId === user.id) {
        // If user is accessing their own data, assume they are a teacher
        userRole = 'teacher'
        console.log(`Assuming user ${user.id} is a teacher for self-access`)
      } else {
        // If user is accessing another teacher's data, assume they are a mentor/admin
        userRole = 'mentor'
        console.log(`Assuming user ${user.id} is a mentor for accessing teacher ${teacherId}`)
      }
    }

    // Regular authorization check with our possibly assumed role
    if (!['super-admin', 'school-admin', 'mentor', 'teacher'].includes(userRole as string)) {
      return NextResponse.json(
        { error: 'Unauthorized: Insufficient privileges to access this endpoint' },
        { status: 403 },
      )
    }

    // Get the school ID from the user (used for school-admin and mentor)
    const schoolId = userRole === 'super-admin' ? undefined : user.school

    // For school-admin and mentor, a school association is required
    // But if we had a token extraction issue, we might not have the school ID
    if (userRole !== 'super-admin' && userRole !== 'teacher' && !schoolId) {
      console.log('School ID missing, but proceeding with query without school filter')
      // We'll proceed but won't filter by school
    }

    // If the user is a teacher, they should only be able to access their own activities
    if (userRole === 'teacher' && teacherId && teacherId !== user.id) {
      return NextResponse.json(
        { error: 'Unauthorized: Teachers can only view their own activities' },
        { status: 403 },
      )
    }

    // Get query parameters
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const filter = url.searchParams.get('filter')
    const search = url.searchParams.get('search')
    const activityType = url.searchParams.get('activityType')

    // Define query types (simplified for problematic field)
    type FieldCondition = {
      equals?: string | number | boolean | null
      contains?: string
      // Add other operators as needed
    }

    type UserIdCondition = {
      equals: string | null // Allow string or null for user.id
    }

    type CombinedCondition = {
      [key: string]: FieldCondition | UserIdCondition
    }

    type LogicalCondition = {
      and?: QueryCondition[]
      or?: QueryCondition[]
    }

    type QueryCondition = CombinedCondition | LogicalCondition

    interface ActivitiesQuery {
      and: QueryCondition[]
      or?: QueryCondition[]
      [key: string]: any // Fallback for other properties if needed
    }

    // Build the base query for Payload
    const payloadQuery: ActivitiesQuery = {
      and: [] as QueryCondition[],
    }

    // If the user is a mentor, filter by their school in the Payload query
    if (userRole === 'mentor' && schoolId) {
      payloadQuery.and.push({
        school: {
          equals: schoolId,
        },
      })
    } else {
      // For other roles (super-admin, school-admin, teacher), filter by teacherId in the Payload query
      // School-admin access is handled by Payload's access control
      if (teacherId) {
        payloadQuery.and.push({
          userId: {
            equals: teacherId,
          },
        })
      } else if (userRole === 'teacher' && user.id) {
        // If a teacher is accessing their own page without teacherId param
        payloadQuery.and.push({
          userId: {
            equals: user.id,
          },
        })
      }
    }

    // Add filter conditions (apply to all roles in Payload query)
    if (filter && filter !== 'all') {
      payloadQuery.and.push({
        status: {
          equals: filter,
        },
      })
    }

    // Add activity type filter (apply to all roles in Payload query)
    if (activityType && activityType !== 'all') {
      payloadQuery.and.push({
        activityType: {
          equals: activityType,
        },
      })
    }

    // Add search filter (apply to all roles in Payload query)
    if (search) {
      payloadQuery.and.push({
        or: [
          { 'userId.firstName': { contains: search } },
          { 'userId.lastName': { contains: search } },
          { 'userId.email': { contains: search } },
          { 'details.articleTitle': { contains: search } },
          { 'details.studentName': { contains: search } },
        ],
      })
    }

    // Get the payload instance
    if (!config) {
      console.error('Error: Payload config is undefined.')
      return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
    }
    const payload = await getPayload({ config })

    // console.log(
    //   'API: Teacher Activities: Final Payload query:',
    //   JSON.stringify(payloadQuery, null, 2),
    // )

    // Fetch activities from Payload
    const activities = await payload.find({
      collection: 'activities',
      where: payloadQuery,
      sort: '-createdAt',
      // Do not apply pagination/limit here for mentor role if filtering by teacherId later
      page: userRole === 'mentor' && teacherId ? 1 : page,
      limit: userRole === 'mentor' && teacherId ? 1000 : limit, // Fetch more for mentor+teacher filter
      depth: 2,
    })

    // console.log(
    //   'API: Teacher Activities: Raw activities from Payload:',
    //   JSON.stringify(activities.docs, null, 2),
    // )

    let filteredActivities = activities.docs

    // If the user is a mentor and a teacherId is provided, filter the results further
    if (userRole === 'mentor' && teacherId) {
      filteredActivities = activities.docs.filter((activityDoc) => {
        // Ensure activityDoc.userId is populated and matches teacherId
        const userField = (activityDoc as any).userId
        const userId = typeof userField === 'object' ? (userField as any).id : userField
        return userId === teacherId
      })
    }

    // Process activities to include user information
    const processedActivities = filteredActivities
      .map((activityDoc) => {
        // Skip activities with media path IDs
        if (isMediaPath(activityDoc.id)) {
          return null
        }

        try {
          // Cast to known types to resolve TypeScript errors
          const activity = activityDoc as unknown as PayloadActivity

          // Handle both userId and user fields for backward compatibility
          const userField = activity.userId || (activity as any).user
          const user = typeof userField === 'object' ? (userField as unknown as PayloadUser) : null

          // Safely access details
          const details =
            typeof activity.details === 'object' && activity.details
              ? (activity.details as Record<string, any>)
              : {}

          return {
            id: activity.id,
            userId: user ? user.id : (userField as string),
            userName: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() : undefined,
            userEmail: user ? user.email : undefined,
            userProfileImage: user && user.profileImage ? user.profileImage : undefined,
            activityType: activity.activityType,
            details: details,
            createdAt: activity.createdAt,
            school: user && user.school ? user.school : undefined,
            points: activity.points,
            isRated: details.mentorRating ? true : false,
            rating: details.mentorRating,
            mentorFeedback: details.mentorFeedback,
            stats: user && user.stats ? user.stats : { reviewsRated: 0 },
          }
        } catch (err) {
          console.error('Error processing activity:', err, activityDoc)
          return null
        }
      })
      .filter(Boolean) // Remove null entries

    // Manually apply pagination after filtering for mentor+teacher case
    const paginatedActivities =
      userRole === 'mentor' && teacherId
        ? processedActivities.slice((page - 1) * limit, page * limit)
        : processedActivities

    const totalDocs =
      userRole === 'mentor' && teacherId ? filteredActivities.length : activities.totalDocs
    const totalPages =
      userRole === 'mentor' && teacherId ? Math.ceil(totalDocs / limit) : activities.totalPages

    return NextResponse.json(
      {
        activities: paginatedActivities,
        totalDocs: totalDocs,
        totalPages: totalPages,
        page: page,
        limit: limit,
      },
      { status: 200 },
    )
  } catch (error) {
    console.error('Error fetching teacher activities:', error)
    return NextResponse.json({ error: 'Failed to fetch teacher activities' }, { status: 500 })
  }
}
