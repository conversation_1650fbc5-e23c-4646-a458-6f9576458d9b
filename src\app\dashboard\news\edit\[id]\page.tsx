import { use } from 'react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import NewsEditClient from './client'

interface Params {
  id: string
}

export interface NewsEditPageProps {
  params: Promise<Params> | Params
}

export default function NewsEditPage({ params }: NewsEditPageProps) {
  // Properly unwrap params using React.use()
  const resolvedParams = use(params as Promise<Params>) as Params
  const id = resolvedParams.id

  return (
    <DashboardLayout>
      <NewsEditClient idOrSlug={id} />
    </DashboardLayout>
  )
}
