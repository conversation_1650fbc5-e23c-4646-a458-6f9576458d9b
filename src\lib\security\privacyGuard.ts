import crypto from 'crypto'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

/**
 * Privacy Guard System
 * Handles anonymization, PII protection, and privacy controls
 */

export interface AnonymizationConfig {
  pattern: string;
  salt: string;
}

export interface TeacherViewConfig {
  visibleFields: string[];
  hiddenFields: string[];
}

export interface PrivacyGuardConfig {
  articleSubmissions: {
    authorAnonymization: AnonymizationConfig;
    teacherView: TeacherViewConfig;
  };
  profileApproval: {
    approvalToken: {
      ttl: string;
      rotation: string;
    };
    changeHistory: string;
  };
}

// Default configuration
const DEFAULT_CONFIG: PrivacyGuardConfig = {
  articleSubmissions: {
    authorAnonymization: {
      pattern: 'Student_${6-digit}',
      salt: process.env.ANONYMIZATION_SALT || 'school-specific-secret',
    },
    teacherView: {
      visibleFields: ['content', 'submissionDate', 'title', 'summary', 'category', 'tags', 'featuredImage'],
      hiddenFields: ['realName', 'email', 'metadata', 'author.firstName', 'author.lastName', 'author.email'],
    },
  },
  profileApproval: {
    approvalToken: {
      ttl: '24h',
      rotation: 'hourly-batch',
    },
    changeHistory: 'encrypted-at-rest',
  },
}

/**
 * Generates a pseudonymous identifier for a student
 * @param studentId The real student ID
 * @param schoolId The school ID (used for salting)
 * @returns A pseudonymous identifier that is consistent for the same student
 */
export function generateStudentAlias(
  studentId: string | any,
  schoolId: string | any
): string {
  // Convert IDs to strings if they're objects (handling ObjectId)
  const studentIdStr = studentId && typeof studentId === 'object' && studentId.toString 
    ? studentId.toString() 
    : String(studentId);
  
  const schoolIdStr = schoolId && typeof schoolId === 'object' && schoolId.toString
    ? schoolId.toString()
    : String(schoolId);
  
  const salt = DEFAULT_CONFIG.articleSubmissions.authorAnonymization.salt + schoolIdStr
  
  // Create a deterministic but non-reversible hash
  const hash = crypto
    .createHmac('sha256', salt)
    .update(studentIdStr)
    .digest('hex')
  
  // Generate a 6-digit number from the hash
  const sixDigitNumber = parseInt(hash.substring(0, 6), 16) % 1000000
  
  // Pad with leading zeros if needed
  const paddedNumber = sixDigitNumber.toString().padStart(6, '0')
  
  // Return the formatted alias
  return `Student_${paddedNumber}`
}

/**
 * Anonymizes an article for teacher review
 * @param article The original article object
 * @param teacherSchoolId The school ID of the reviewing teacher
 * @returns An anonymized version of the article
 */
export async function anonymizeArticleForTeacher(
  article: any,
  teacherSchoolId: string
): Promise<any> {
  if (!article) return null
  
  try {
    // Deep clone the article to avoid modifying the original
    const anonymizedArticle = JSON.parse(JSON.stringify(article))
    
    // Get the student's ID and school
    let studentId = null
    let studentSchoolId = null
    
    if (typeof article.author === 'object' && article.author) {
      studentId = article.author.id
      studentSchoolId = typeof article.author.school === 'object' 
        ? article.author.school?.id 
        : article.author.school
    } else {
      // If author is just an ID, fetch the user
      const payload = await getPayload({ config })
      const student = await payload.findByID({
        collection: 'users',
        id: article.author,
        depth: 1,
      })
      
      if (student) {
        studentId = student.id
        studentSchoolId = typeof student.school === 'object' 
          ? student.school?.id 
          : student.school
      }
    }
    
    // If we couldn't determine the student or their school, return the article as is
    if (!studentId || !studentSchoolId) return anonymizedArticle
    
    // Generate the student alias
    const studentAlias = generateStudentAlias(studentId, studentSchoolId)
    
    // Replace author information with the alias
    if (typeof anonymizedArticle.author === 'object' && anonymizedArticle.author) {
      // Hide real name and other PII
      anonymizedArticle.author = {
        id: anonymizedArticle.author.id, // Keep the ID for database operations
        alias: studentAlias,
      }
    } else {
      // If author is just an ID, keep it but add an alias field
      anonymizedArticle.authorAlias = studentAlias
    }
    
    // Remove any fields that should be hidden from teachers
    const hiddenFields = DEFAULT_CONFIG.articleSubmissions.teacherView.hiddenFields
    
    for (const field of hiddenFields) {
      // Handle nested fields (e.g., author.firstName)
      if (field.includes('.')) {
        const parts = field.split('.')
        let current = anonymizedArticle
        
        // Navigate to the parent object
        for (let i = 0; i < parts.length - 1; i++) {
          if (current[parts[i]]) {
            current = current[parts[i]]
          } else {
            break
          }
        }
        
        // Delete the field if it exists
        const lastPart = parts[parts.length - 1]
        if (current && current[lastPart]) {
          delete current[lastPart]
        }
      } else if (anonymizedArticle[field]) {
        // Delete top-level fields
        delete anonymizedArticle[field]
      }
    }
    
    return anonymizedArticle
  } catch (error) {
    console.error('Error anonymizing article:', error)
    return article // Return the original article if anonymization fails
  }
}

/**
 * Anonymizes teacher reviews for student viewing
 * @param reviews Array of teacher reviews
 * @returns Anonymized reviews with teacher identities protected
 */
export function anonymizeTeacherReviews(reviews: any[]): any[] {
  if (!reviews || !Array.isArray(reviews)) return []
  
  return reviews.map(review => {
    // Create a copy of the review
    const anonymizedReview = { ...review }
    
    // Remove teacher identifiers
    delete anonymizedReview.reviewer
    delete anonymizedReview.reviewerName
    
    // Keep the review content, rating, and approval status
    return {
      comment: anonymizedReview.comment,
      rating: anonymizedReview.rating,
      approved: anonymizedReview.approved,
      reviewDate: anonymizedReview.reviewDate,
    }
  })
}

/**
 * Generates a cryptographically secure approval token
 * @param userId ID of the user requesting approval
 * @param approvalType Type of approval (e.g., 'profile-image', 'name-change')
 * @param ttl Time-to-live in seconds
 * @returns Signed approval token
 */
export function generateApprovalToken(
  userId: string,
  approvalType: string,
  ttl: number = 86400 // 24 hours by default
): string {
  const payload = {
    userId,
    approvalType,
    exp: Math.floor(Date.now() / 1000) + ttl,
    iat: Math.floor(Date.now() / 1000),
  }
  
  // Sign with EdDSA if available, otherwise fall back to HMAC
  try {
    // Use the PAYLOAD_SECRET as the signing key
    return crypto
      .createHmac('sha256', process.env.PAYLOAD_SECRET || 'secret')
      .update(JSON.stringify(payload))
      .digest('hex') + '.' + Buffer.from(JSON.stringify(payload)).toString('base64')
  } catch (error) {
    console.error('Error generating approval token:', error)
    throw new Error('Failed to generate approval token')
  }
}

/**
 * Verifies an approval token
 * @param token The approval token to verify
 * @returns The decoded payload if valid, null otherwise
 */
export function verifyApprovalToken(token: string): any | null {
  try {
    const [signature, encodedPayload] = token.split('.')
    
    if (!signature || !encodedPayload) {
      return null
    }
    
    // Decode the payload
    const payloadStr = Buffer.from(encodedPayload, 'base64').toString()
    const payload = JSON.parse(payloadStr)
    
    // Check if the token has expired
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null
    }
    
    // Verify the signature
    const expectedSignature = crypto
      .createHmac('sha256', process.env.PAYLOAD_SECRET || 'secret')
      .update(payloadStr)
      .digest('hex')
    
    if (signature !== expectedSignature) {
      return null
    }
    
    return payload
  } catch (error) {
    console.error('Error verifying approval token:', error)
    return null
  }
}

/**
 * Encrypts sensitive data for storage
 * @param data The data to encrypt
 * @returns Encrypted data
 */
export function encryptSensitiveData(data: any): string {
  const algorithm = 'aes-256-gcm'
  const key = crypto.scryptSync(
    process.env.PAYLOAD_SECRET || 'secret',
    'salt',
    32
  )
  const iv = crypto.randomBytes(16)
  const cipher = crypto.createCipheriv(algorithm, key, iv)
  
  let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex')
  encrypted += cipher.final('hex')
  
  const authTag = cipher.getAuthTag()
  
  // Return IV, encrypted data, and auth tag
  return iv.toString('hex') + ':' + encrypted + ':' + authTag.toString('hex')
}

/**
 * Decrypts sensitive data
 * @param encryptedData The encrypted data
 * @returns Decrypted data
 */
export function decryptSensitiveData(encryptedData: string): any {
  const algorithm = 'aes-256-gcm'
  const key = crypto.scryptSync(
    process.env.PAYLOAD_SECRET || 'secret',
    'salt',
    32
  )
  
  const [ivHex, encrypted, authTagHex] = encryptedData.split(':')
  
  const iv = Buffer.from(ivHex, 'hex')
  const authTag = Buffer.from(authTagHex, 'hex')
  
  const decipher = crypto.createDecipheriv(algorithm, key, iv)
  decipher.setAuthTag(authTag)
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8')
  decrypted += decipher.final('utf8')
  
  return JSON.parse(decrypted)
}

/**
 * Logs a privacy-related event for audit purposes
 * @param userId ID of the user performing the action
 * @param actionType Type of privacy action
 * @param details Additional details about the action
 */
export async function logPrivacyEvent(
  userId: string,
  actionType: string,
  details: any
): Promise<void> {
  try {
    const { db } = await connectToDatabase()
    
    await db.collection('privacyLogs').insertOne({
      userId,
      actionType,
      details: encryptSensitiveData(details),
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Error logging privacy event:', error)
  }
}
