'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Star, Eye, ThumbsUp, ThumbsDown } from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

interface TeacherReview {
  id?: string
  reviewer: {
    id: string
    firstName?: string
    lastName?: string
    email: string
  } | string
  rating: number
  comment: string
  approved: boolean
  createdAt?: string
  reviewerRole?: string
  isMentorReview?: boolean
}

interface TeacherReviewsTableProps {
  reviews: TeacherReview[]
  articleId: string
  articleTitle: string
}

export default function TeacherReviewsTable({
  reviews,
  articleId,
  articleTitle,
}: TeacherReviewsTableProps) {
  const router = useRouter()
  const [expandedReviews, setExpandedReviews] = useState<Record<string, boolean>>({})

  const toggleReviewExpansion = (reviewId: string) => {
    setExpandedReviews((prev) => ({
      ...prev,
      [reviewId]: !prev[reviewId],
    }))
  }

  const getReviewerName = (review: TeacherReview) => {
    if (typeof review.reviewer === 'object') {
      return review.reviewer.firstName && review.reviewer.lastName
        ? `${review.reviewer.firstName} ${review.reviewer.lastName}`
        : review.reviewer.email
    }
    return 'مراجع غير معروف'
  }

  const getReviewerRole = (review: TeacherReview) => {
    if (review.reviewerRole) {
      return review.reviewerRole
    }
    if (review.isMentorReview) {
      return 'موجه'
    }
    return 'معلم'
  }

  const getReviewDate = (review: TeacherReview) => {
    if (review.createdAt) {
      return new Date(review.createdAt).toLocaleDateString('ar-EG')
    }
    return 'تاريخ غير معروف'
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Star className="ml-2 h-5 w-5 text-yellow-500" />
          مراجعات المعلمين لـ "{articleTitle}"
        </CardTitle>
      </CardHeader>
      <CardContent>
        {reviews.length > 0 ? (
          <div className="overflow-x-auto" dir="rtl">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المراجع</TableHead>
                  <TableHead>التقييم</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reviews.map((review, index) => {
                  const reviewId = review.id || `review-${index}`
                  const isExpanded = expandedReviews[reviewId] || false
                  const reviewerName = getReviewerName(review)
                  const reviewerRole = getReviewerRole(review)
                  const reviewDate = getReviewDate(review)

                  return (
                    <TableRow key={reviewId}>
                      <TableCell>
                        <div className="font-medium">{reviewerName}</div>
                        <div className="text-xs text-gray-500">
                          <Badge variant={reviewerRole === 'موجه' ? 'secondary' : 'outline'}>
                            {reviewerRole}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-500 ml-1" />
                          <span>{review.rating}/10</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {review.approved ? (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            <ThumbsUp className="h-3 w-3 ml-1" />
                            تمت الموافقة
                          </Badge>
                        ) : (
                          <Badge variant="destructive" className="bg-red-100 text-red-800">
                            <ThumbsDown className="h-3 w-3 ml-1" />
                            مرفوض
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>{reviewDate}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleReviewExpansion(reviewId)}
                          >
                            {isExpanded ? 'إخفاء' : 'عرض'} التعليق
                          </Button>
                          {reviewerRole === 'معلم' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                router.push(
                                  `/dashboard/mentor/review-feedback/${articleId}/${index}`,
                                )
                              }
                            >
                              <Eye className="h-4 w-4 ml-1" />
                              تقييم
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
            {/* Expanded Review Comments */}
            {Object.entries(expandedReviews).map(([reviewId, isExpanded]) => {
              if (!isExpanded) return null
              
              const reviewIndex = parseInt(reviewId.replace('review-', ''), 10)
              const review = isNaN(reviewIndex) 
                ? reviews.find(r => r.id === reviewId)
                : reviews[reviewIndex]
                
              if (!review) return null
              
              return (
                <div key={`comment-${reviewId}`} className="mt-4 p-4 bg-gray-50 rounded-md">
                  <h3 className="font-medium mb-2">تعليق المراجعة</h3>
                  <p className="whitespace-pre-wrap">{review.comment}</p>
                </div>
              )
            })}
          </div>
        ) : (
          <p className="text-gray-500">لا توجد مراجعات متاحة لهذا المقال.</p>
        )}
      </CardContent>
    </Card>
  )
}
