'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AlertTriangle, CheckCircle } from 'lucide-react'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'

interface SystemicIssueFormProps {
  schoolId: string
}

export default function SystemicIssueForm({ schoolId }: SystemicIssueFormProps) {
  const router = useRouter()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState('')
  
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [type, setType] = useState('')
  const [severity, setSeverity] = useState('')
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    if (!title || !description || !type || !severity) {
      setError('يرجى ملء جميع الحقول المطلوبة')
      return
    }
    
    setIsSubmitting(true)
    setError('')
    
    try {
      const response = await fetch('/api/dashboard/mentor/flag-issue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          type,
          severity,
          schoolId,
        }),
      })
      
      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'فشل في إرسال المشكلة')
      }
      
      // Reset form
      setTitle('')
      setDescription('')
      setType('')
      setSeverity('')
      
      setIsSuccess(true)
      setTimeout(() => {
        setIsDialogOpen(false)
        setIsSuccess(false)
      }, 2000)
      
      // Refresh the page to show the new issue
      router.refresh()
    } catch (err) {
      console.error('Error submitting systemic issue:', err)
      setError(err instanceof Error ? err.message : 'فشل في إرسال المشكلة')
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <AlertTriangle className="ml-2 h-5 w-5 text-amber-500" />
          الإبلاغ عن مشكلات نظامية
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-500 mb-4">
          استخدم هذا النموذج للإبلاغ عن المشكلات النظامية التي تتطلب اهتمام المسؤول.
        </p>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" className="w-full">
              <AlertTriangle className="ml-2 h-4 w-4" />
              الإبلاغ عن مشكلة جديدة
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]" dir="rtl">
            <DialogHeader>
              <DialogTitle>الإبلاغ عن مشكلة نظامية</DialogTitle>
              <DialogDescription>
                الإبلاغ عن مشكلة نظامية تتطلب اهتمام المسؤول. سيكون هذا مرئياً لمسؤولي المدرسة.
              </DialogDescription>
            </DialogHeader>
            
            {isSuccess ? (
              <div className="flex flex-col items-center justify-center py-6">
                <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
                <h3 className="text-xl font-semibold text-green-700">تم الإبلاغ عن المشكلة</h3>
                <p className="text-gray-500 mt-2">تم الإبلاغ عن مشكلتك بنجاح.</p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {error}
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان المشكلة</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="عنوان موجز يصف المشكلة"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">الوصف</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="وصف مفصل للمشكلة"
                    rows={5}
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">نوع المشكلة</Label>
                    <Select value={type} onValueChange={setType} required>
                      <SelectTrigger id="type">
                        <SelectValue placeholder="اختر النوع" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="inappropriate-content">محتوى غير لائق</SelectItem>
                        <SelectItem value="review-quality">جودة المراجعة</SelectItem>
                        <SelectItem value="student-behavior">سلوك الطالب</SelectItem>
                        <SelectItem value="teacher-behavior">سلوك المعلم</SelectItem>
                        <SelectItem value="technical-issue">مشكلة تقنية</SelectItem>
                        <SelectItem value="other">أخرى</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="severity">الخطورة</Label>
                    <Select value={severity} onValueChange={setSeverity} required>
                      <SelectTrigger id="severity">
                        <SelectValue placeholder="اختر مستوى الخطورة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">منخفضة</SelectItem>
                        <SelectItem value="medium">متوسطة</SelectItem>
                        <SelectItem value="high">عالية</SelectItem>
                        <SelectItem value="critical">حرجة</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <DialogFooter className="sm:justify-start flex-row-reverse">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                    disabled={isSubmitting}
                  >
                    إلغاء
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? 'جارٍ الإرسال...' : 'إرسال المشكلة'}
                  </Button>
                </DialogFooter>
              </form>
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}
