'use client'

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Loader2, Upload } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'

interface IsolatedFileUploadProps {
  onFileUploaded: (fileId: string, fileUrl: string) => void
  onUploadStart?: () => void
  altText?: string
}

export default function IsolatedFileUpload({
  onFileUploaded,
  onUploadStart,
  altText = 'Uploaded image',
}: IsolatedFileUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      // Don't prevent default on the change event - this would interfere with file selection
      // Only prevent form submission if needed

      // Get the selected file
      const file = e.target.files?.[0]
      if (!file) return

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: 'Error',
          description: 'Please upload an image file',
          variant: 'destructive',
        })
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: 'Error',
          description: 'Image size should be less than 5MB',
          variant: 'destructive',
        })
        return
      }

      // Upload file
      setIsUploading(true)

      // Notify parent component that upload is starting
      if (onUploadStart) {
        onUploadStart()
      }

      try {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('alt', altText)

        console.log('Uploading image file:', file.name, file.type, file.size)

        const response = await fetch('/api/media', {
          method: 'POST',
          body: formData,
          credentials: 'include',
        })

        console.log('Image upload response status:', response.status)

        if (!response.ok) {
          throw new Error(`Failed to upload image: ${response.status}`)
        }

        const data = await response.json()
        console.log('Image upload response data:', data)

        if (data.id && data.url) {
          // Call the parent component's handler with the image ID and URL
          onFileUploaded(data.id, data.url)
          toast({
            title: 'Success',
            description: 'Image uploaded successfully',
          })
        } else {
          throw new Error('No image ID or URL returned')
        }
      } catch (error) {
        console.error('Error uploading image:', error)
        toast({
          title: 'Error',
          description: 'Failed to upload image. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setIsUploading(false)
        // Clear the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = ''
        }
      }
    } catch (outerError) {
      console.error('Outer error in handleFileChange:', outerError)
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      })
      setIsUploading(false)
    }
  }

  return (
    <div className="flex flex-col items-center">
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={(e) => {
          try {
            // Only prevent form submission, not the click itself
            e.preventDefault()

            // Ensure we're not inside a form that might submit
            const form = e.target.closest('form')
            if (form) {
              // Add a submit handler to the form to prevent submission
              const preventSubmit = (formEvent: Event) => {
                formEvent.preventDefault()
                return false
              }

              // Add the handler once
              form.removeEventListener('submit', preventSubmit)
              form.addEventListener('submit', preventSubmit)
            }

            // Trigger the file input click
            if (fileInputRef.current) {
              fileInputRef.current.click()
            }
          } catch (error) {
            console.error('Error in button click handler:', error)
          }
        }}
        disabled={isUploading}
      >
        {isUploading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Uploading...
          </>
        ) : (
          <>
            <Upload className="mr-2 h-4 w-4" />
            Upload Image
          </>
        )}
      </Button>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
        // Don't prevent default on click - this would prevent the file dialog from opening
        // Only prevent form submission
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          return false
        }}
      />
    </div>
  )
}
