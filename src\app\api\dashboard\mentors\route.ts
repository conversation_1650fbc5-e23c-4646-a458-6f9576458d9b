import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Try to use MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the current user to check role
        const user = await db.collection('users').findOne({ _id: new ObjectId(userId) })

        if (!user) {
          return NextResponse.json({ error: 'User not found' }, { status: 404 })
        }

        // Get user role
        let userRoleSlug: string | null = null
        console.log('User object:', user)
        console.log('User role property:', user.role)

        if (user.role instanceof ObjectId) {
          // If role is an ObjectId, use it to lookup the role
          console.log('User role is ObjectId, looking up role by ID:', user.role)
          const role = await db.collection('roles').findOne({ _id: user.role })
          console.log('Role document found:', role)
          userRoleSlug = role?.slug || null
        } else if (typeof user.role === 'string' && ObjectId.isValid(user.role)) {
          // If role is a string ID, fetch the role and use its slug
          console.log('User role is string ID, looking up role by ID:', user.role)
          const role = await db.collection('roles').findOne({ _id: new ObjectId(user.role) })
          console.log('Role document found:', role)
          userRoleSlug = role?.slug || null
        } else if (typeof user.role === 'object' && user.role !== null && 'slug' in user.role) {
          // If role is a populated object, use its slug
          console.log('User role is populated object, using slug:', user.role.slug)
          userRoleSlug = user.role.slug
        }

        console.log('Final userRoleSlug:', userRoleSlug)
        // Check if user has permission to view mentors
        if (!userRoleSlug || !['super-admin', 'school-admin'].includes(userRoleSlug)) {
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
        }

        // Get school ID for school admins
        let schoolFilter = {}
        if (userRoleSlug === 'school-admin') {
          const schoolId = typeof user.school === 'object' ? user.school._id : user.school
          if (schoolId) {
            schoolFilter = {
              school:
                typeof schoolId === 'string' && ObjectId.isValid(schoolId)
                  ? new ObjectId(schoolId)
                  : schoolId,
            }
          }
        }

        // Get all mentors
        const mentorRoleId = await db.collection('roles').findOne({ slug: 'mentor' })
        if (!mentorRoleId) {
          return NextResponse.json({ error: 'Mentor role not found' }, { status: 404 })
        }

        // Find all users with mentor role - handle both ObjectId and string formats
        const mentorQuery = {
          $and: [
            {
              $or: [{ role: mentorRoleId._id }, { role: mentorRoleId._id.toString() }],
            },
            ...(Object.keys(schoolFilter).length > 0 ? [schoolFilter] : []),
          ],
        }

        const mentors = await db.collection('users').find(mentorQuery).toArray()

        // Get schools for each mentor
        const schoolIds = mentors
          .map((mentor) => {
            if (typeof mentor.school === 'object' && mentor.school?._id) {
              return mentor.school._id
            }
            return mentor.school
          })
          .filter((id) => id)

        const schools =
          schoolIds.length > 0
            ? await db
                .collection('schools')
                .find({
                  _id: {
                    $in: schoolIds.map((id) =>
                      typeof id === 'string' && ObjectId.isValid(id) ? new ObjectId(id) : id,
                    ),
                  },
                })
                .toArray()
            : []

        // Format mentor data
        const formattedMentors = await Promise.all(
          mentors.map(async (mentor) => {
            // Get school name
            let schoolName = 'Unknown School'
            if (schoolFilter && userRoleSlug === 'school-admin') {
              // School admin: always use their own school name
              let adminSchoolId = null
              if (typeof user.school === 'object' && user.school && user.school._id) {
                adminSchoolId = user.school._id.toString()
              } else if (user.school) {
                adminSchoolId = user.school.toString()
              }
              const adminSchool = adminSchoolId
                ? schools.find((s) => s._id.toString() === adminSchoolId)
                : undefined
              if (adminSchool) schoolName = adminSchool.name
            } else {
              // Super admin: try to get mentor's school name
              const schoolId =
                typeof mentor.school === 'object' ? mentor.school?._id : mentor.school
              const school = schools.find(
                (s) =>
                  s._id.toString() ===
                  (typeof schoolId === 'object' ? schoolId.toString() : schoolId),
              )
              if (school) schoolName = school.name
            }

            // Get review stats
            const reviews = await db
              .collection('activities')
              .find({
                userId: mentor._id.toString(),
                activityType: 'teacher-review',
              })
              .toArray()

            const reviewCount = reviews.length
            const totalRating = reviews.reduce((sum, review) => {
              return sum + (review.details?.rating || 0)
            }, 0)
            const averageRating = reviewCount > 0 ? totalRating / reviewCount : 0

            // Get last activity
            const lastActivity = await db
              .collection('activities')
              .find({
                userId: mentor._id.toString(),
              })
              .sort({ date: -1 })
              .limit(1)
              .toArray()

            // Fix mentor name fallback
            let mentorName = `${mentor.firstName || ''} ${mentor.lastName || ''}`.trim()
            if (!mentorName) mentorName = 'Unknown'

            return {
              id: mentor._id.toString(),
              name: mentorName,
              email: mentor.email,
              school: schoolName,
              reviewCount,
              averageRating,
              lastActivity: lastActivity[0]?.date || mentor.updatedAt || mentor.createdAt,
            }
          }),
        )

        return NextResponse.json({ mentors: formattedMentors })
      } catch (mongoError) {
        console.warn('Error fetching mentors from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user has permission to view mentors
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (!['super-admin', 'school-admin'].includes(role)) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get school ID for school admins
      let where = {}
      if (role === 'school-admin') {
        const schoolId = typeof user.school === 'object' ? user.school?.id : user.school
        if (schoolId) {
          where = { school: { equals: schoolId } }
        }
      }

      // Get all mentors
      const mentorRole = await payload.find({
        collection: 'roles',
        where: {
          slug: {
            equals: 'mentor',
          },
        },
      })

      if (!mentorRole.docs || mentorRole.docs.length === 0) {
        return NextResponse.json({ error: 'Mentor role not found' }, { status: 404 })
      }

      // Find all users with mentor role
      const mentors = await payload.find({
        collection: 'users',
        where: {
          role: {
            equals: mentorRole.docs[0].id,
          },
          ...where,
        },
        depth: 2,
      })

      // Format mentor data
      const formattedMentors = await Promise.all(
        mentors.docs.map(async (mentor) => {
          // Get school name
          let schoolName = 'Unknown School'
          if (role === 'school-admin') {
            // School admin: always use their own school name
            const adminSchool =
              typeof user.school === 'object' && user.school ? user.school.name : null
            if (adminSchool) schoolName = adminSchool
          } else {
            // Super admin: try to get mentor's school name
            if (typeof mentor.school === 'object' && mentor.school) {
              if (mentor.school.name) schoolName = mentor.school.name
            }
          }

          // Get review stats from activities
          const reviews = await payload.find({
            collection: 'activities',
            where: {
              userId: {
                equals: mentor.id,
              },
              activityType: {
                equals: 'teacher-review',
              },
            },
          })

          const reviewCount = reviews.totalDocs
          let totalRating = 0
          reviews.docs.forEach((review) => {
            // Check if details is an object and has a rating property
            if (
              review.details &&
              typeof review.details === 'object' &&
              'rating' in review.details &&
              typeof review.details.rating === 'number'
            ) {
              totalRating += review.details.rating
            }
          })
          const averageRating = reviewCount > 0 ? totalRating / reviewCount : 0

          // Get last activity
          const lastActivity = await payload.find({
            collection: 'activities',
            where: {
              userId: {
                equals: mentor.id,
              },
            },
            sort: '-createdAt', // Use createdAt as date is not available
            limit: 1,
          })

          // Fix mentor name fallback
          let mentorName = `${mentor.firstName || ''} ${mentor.lastName || ''}`.trim()
          if (!mentorName) mentorName = 'Unknown'

          return {
            id: mentor.id,
            name: mentorName,
            email: mentor.email,
            school: schoolName,
            reviewCount,
            averageRating,
            lastActivity: lastActivity.docs[0]?.createdAt || mentor.updatedAt || mentor.createdAt, // Use createdAt
          }
        }),
      )

      return NextResponse.json({ mentors: formattedMentors })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching mentors:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
