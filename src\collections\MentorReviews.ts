import type { CollectionConfig } from 'payload'
import { isSuperAdmin, isSchoolAdmin, canManageSchoolUsers } from '../access'

export const MentorReviews: CollectionConfig = {
  slug: 'mentor-reviews',
  admin: {
    useAsTitle: 'id',
    description: 'Collection for storing mentor reviews of teacher evaluations',
  },
  access: {
    read: ({ req, id }) => {
      // Not logged in
      if (!req.user) return false

      // Super admins can read all reviews
      if (
        req.user.role === 'super-admin' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'super-admin')
      ) {
        return true
      }

      // School admins can read reviews from their school
      if (
        req.user.role === 'school-admin' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'school-admin')
      ) {
        return true
      }

      // Mentors can read their own reviews
      if (
        req.user.role === 'mentor' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'mentor')
      ) {
        return true
      }

      return false
    },
    create: ({ req }) => {
      // Only mentors can create reviews
      if (!req.user) return false
      return (
        req.user.role === 'mentor' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'mentor')
      )
    },
    update: async ({ req, id }) => {
      // Not logged in
      if (!req.user) return false

      // Super admins can update all reviews
      const isSuperAdminUser = await isSuperAdmin({ req })
      if (isSuperAdminUser) return true

      // Mentors can update reviews
      if (
        req.user.role === 'mentor' ||
        (typeof req.user.role === 'object' && req.user.role?.slug === 'mentor')
      ) {
        return true
      }

      return false
    },
    delete: canManageSchoolUsers,
  },
  fields: [
    {
      name: 'mentor',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      filterOptions: () => ({
        role: {
          equals: 'mentor',
        },
      }),
      admin: {
        description: 'The mentor who performed the review',
      },
    },
    {
      name: 'article',
      type: 'relationship',
      relationTo: 'articles',
      required: true,
      admin: {
        description: 'The article associated with the teacher review',
      },
    },
    {
      name: 'teacherReviewIndex',
      type: 'number',
      required: true,
      admin: {
        description: "Index of the teacher review within the article's teacherReview array",
      },
    },
    {
      name: 'teacher',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      filterOptions: () => ({
        role: {
          equals: 'teacher',
        },
      }),
      admin: {
        description: 'The teacher who made the original review',
      },
    },
    {
      name: 'evaluationResult',
      type: 'select',
      options: [
        { label: 'Approve', value: 'approve' },
        { label: 'Reject', value: 'reject' },
      ],
      required: true,
      admin: {
        description: "Result of the mentor's evaluation (approve or reject)",
      },
    },
    {
      name: 'comment',
      type: 'textarea',
      required: true,
      admin: {
        description: "Mentor's comment on the teacher's review",
      },
    },
    {
      name: 'timestamp',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
      admin: {
        description: 'Date and time when the review was submitted',
      },
    },
    {
      name: 'school',
      type: 'relationship',
      relationTo: 'schools',
      required: false,
      admin: {
        description: 'School associated with the review (inherited from teacher or article author)',
      },
    },
  ],
  hooks: {
    beforeChange: [
      async ({ data, req, operation }) => {
        if (operation === 'create' && data.mentor && !data.school) {
          // Automatically set school based on mentor's school if not provided
          const mentor = await req.payload.findByID({
            collection: 'users',
            id: typeof data.mentor === 'object' ? data.mentor.id : data.mentor,
          })
          if (mentor && mentor.school) {
            data.school = mentor.school
          }
        }
        return data
      },
    ],
    afterChange: [
      async ({ doc, req, operation }) => {
        // Award points to mentor when a review is created
        if (operation === 'create' && doc.mentor) {
          const { addUserPoints } = await import('../utils/points')
          const mentorId = typeof doc.mentor === 'object' ? doc.mentor.id : doc.mentor

          try {
            await addUserPoints({
              payload: req.payload,
              userId: mentorId,
              type: 'mentor_review_completed',
              points: 5,
              description: `Completed mentor review evaluation`,
              reference: {
                collection: 'mentor-reviews',
                id: doc.id,
              },
            })
          } catch (error) {
            console.error('Error awarding points to mentor:', error)
            // Don't fail the review creation if point awarding fails
          }
        }
      },
    ],
  },
}
