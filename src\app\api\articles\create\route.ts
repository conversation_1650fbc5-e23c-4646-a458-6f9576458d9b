import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    const payload = await getPayload({
      config,
    })

    // Get the current user
    const { user } = await payload.auth({ req })

    if (!user) {
      return NextResponse.json(
        { error: 'You must be logged in to create an article' },
        { status: 401 },
      )
    }

    // Check if user is a student or admin
    const userRole = typeof user.role === 'object' ? user.role?.slug : user.role

    const isStudent = userRole === 'student'
    const isAdmin = userRole === 'super-admin' || userRole === 'school-admin'

    if (!isStudent && !isAdmin) {
      return NextResponse.json(
        { error: 'Only students and admins can create articles' },
        { status: 403 },
      )
    }

    const formData = await req.formData()
    const title = formData.get('title') as string
    const content = formData.get('content') as string
    const status = formData.get('status') as string

    // Validate inputs
    if (!title || !content || !status) {
      return NextResponse.json(
        { error: 'Title, content, and status are required' },
        { status: 400 },
      )
    }

    // Validate status
    if (status !== 'draft' && status !== 'ready-for-review') {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 })
    }

    // Create the article
    const article = await payload.create({
      collection: 'articles',
      data: {
        title,
        content,
        author: user.id,
        status: status === 'ready-for-review' ? 'pending-review' : status,
      },
    })

    // Redirect to the student dashboard
    return NextResponse.redirect(new URL('/dashboard/student', req.url))
  } catch (error) {
    console.error('Error creating article:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
