'use client'

import { useEffect, useState } from 'react'
import { Bar } from 'react-chartjs-2'
import { 
  Chart as ChartJS, 
  CategoryScale, 
  LinearScale, 
  BarElement, 
  Title, 
  Tooltip, 
  Legend 
} from 'chart.js'
import { Skeleton } from '@/components/ui/skeleton'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

// Register Chart.js components
ChartJS.register(
  CategoryScale, 
  LinearScale, 
  BarElement, 
  Title, 
  Tooltip, 
  Legend
)

interface SchoolStat {
  schoolId: string
  schoolName: string
  articles: number
  news: number
  students: number
  teachers: number
  comments: number
  activity: number
}

export function CrossSchoolStatistics() {
  const [loading, setLoading] = useState(true)
  const [statistics, setStatistics] = useState<SchoolStat[]>([])
  const [error, setError] = useState<string | null>(null)
  const [metric, setMetric] = useState('articles')

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/dashboard/super-admin/statistics/cross-school')
        if (!response.ok) throw new Error('فشل في جلب الإحصائيات')
        const data = await response.json()
        setStatistics(data.statistics || data || [])
        setLoading(false)
      } catch (error) {
        console.error('Error fetching cross-school statistics:', error)
        setError('فشل في تحميل بيانات الإحصائيات')
        setLoading(false)
      }
    }

    fetchStatistics()
  }, [])

  const metricLabels = {
    articles: 'المقالات',
    news: 'الأخبار',
    students: 'الطلاب',
    teachers: 'المعلمون',
    comments: 'التعليقات',
    activity: 'النشاط'
  }

  const chartData = {
    labels: statistics.map(stat => stat.schoolName),
    datasets: [
      {
        label: metricLabels[metric as keyof typeof metricLabels],
        data: statistics.map(stat => stat[metric as keyof SchoolStat] as number),
        backgroundColor: 'rgba(0, 112, 243, 0.7)',
      },
    ],
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  }

  if (loading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-[200px] w-full" />
      </div>
    )
  }

  if (error) {
    return <div className="text-red-500">خطأ: {error}</div>
  }

  return (
    <div className="space-y-4" dir="rtl">
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          مقارنة المقاييس الرئيسية بين جميع المدارس
        </div>
        
        <Select value={metric} onValueChange={setMetric}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="اختر المقياس" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="articles">المقالات</SelectItem>
            <SelectItem value="news">الأخبار</SelectItem>
            <SelectItem value="students">الطلاب</SelectItem>
            <SelectItem value="teachers">المعلمون</SelectItem>
            <SelectItem value="comments">التعليقات</SelectItem>
            <SelectItem value="activity">النشاط</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="h-[300px]">
        <Bar data={chartData} options={chartOptions} />
      </div>
    </div>
  )
} 