'use client'

import React, { useState, useMemo, useEffect } from 'react'
import { RankingsCarousel } from './RankingsCarousel'
import '../styles/statistics-carousel.css'

interface RankingItem {
  id: string
  name: string
  anonymizedName?: string
  count?: number
  articleCount?: number
  averageRating?: number
  reviewCount?: number
  points?: number
  type?: 'student' | 'teacher' | 'school' | 'mentor'
  rank?: number
  image?: string
}

interface ClientRankingsCarouselProps {
  schoolData: RankingItem[]
  studentData: RankingItem[]
  teacherData: RankingItem[]
  mentorData: RankingItem[]
}

// Fallback images in case the dynamic IDs don't work
const fallbackImages = {
  school: [
    'https://images.pexels.com/photos/207692/pexels-photo-207692.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/256490/pexels-photo-256490.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/159490/yale-university-landscape-universities-schools-159490.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/159752/books-read-literature-159752.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=600',
  ],
  student: [
    'https://images.pexels.com/photos/3184405/pexels-photo-3184405.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184398/pexels-photo-3184398.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184396/pexels-photo-3184396.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184394/pexels-photo-3184394.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&cs=tinysrgb&w=600',
  ],
  teacher: [
    'https://images.pexels.com/photos/3184317/pexels-photo-3184317.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184319/pexels-photo-3184319.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184328/pexels-photo-3184328.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=600',
  ],
  mentor: [
    'https://images.pexels.com/photos/3184338/pexels-photo-3184338.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184325/pexels-photo-3184325.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184293/pexels-photo-3184293.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184296/pexels-photo-3184296.jpeg?auto=compress&cs=tinysrgb&w=600',
    'https://images.pexels.com/photos/3184302/pexels-photo-3184302.jpeg?auto=compress&cs=tinysrgb&w=600',
  ],
}

// Generate placeholder school data
const generatePlaceholderSchools = (): RankingItem[] => {
  const schoolNames = [
    'Westfield Academy',
    'Oakridge High School',
    'Riverside Preparatory',
    'Pinecrest School',
    'Meadowbrook Institute',
    'Greenwood College',
    'Lakeview High School',
    'Sunnydale Academy',
    'Elmwood Preparatory',
    'Brookside School',
  ]

  return schoolNames.map((name, index) => ({
    id: `school-${index}`,
    name,
    count: Math.floor(Math.random() * 50) + 10,
    image: fallbackImages.school[index % fallbackImages.school.length],
  }))
}

// Generate placeholder student data
const generatePlaceholderStudents = (): RankingItem[] => {
  const studentNames = [
    'Emma Johnson',
    'Liam Smith',
    'Olivia Williams',
    'Noah Brown',
    'Ava Jones',
    'Ethan Miller',
    'Sophia Davis',
    'Mason Wilson',
    'Isabella Taylor',
    'Lucas Anderson',
  ]

  return studentNames.map((name, index) => ({
    id: `student-${index}`,
    name,
    articleCount: Math.floor(Math.random() * 15) + 5,
    averageRating: Math.random() * 2 + 3, // Rating between 3.0 and 5.0
    points: Math.floor(Math.random() * 500) + 100, // Random points between 100-600
    type: 'student' as const,
    image: fallbackImages.student[index % fallbackImages.student.length],
  }))
}

// Generate placeholder teacher data
const generatePlaceholderTeachers = (): RankingItem[] => {
  const teacherNames = [
    'Dr. Sarah Mitchell',
    'Prof. James Wilson',
    'Ms. Rebecca Thompson',
    'Mr. David Garcia',
    'Dr. Jennifer Lee',
    'Prof. Michael Clark',
    'Ms. Elizabeth Rodriguez',
    'Mr. Robert Martinez',
    'Dr. Patricia Lewis',
    'Prof. Thomas Walker',
  ]

  return teacherNames.map((name, index) => ({
    id: `teacher-${index}`,
    name,
    reviewCount: Math.floor(Math.random() * 40) + 20,
    points: Math.floor(Math.random() * 800) + 200, // Random points between 200-1000
    type: 'teacher' as const,
    image: fallbackImages.teacher[index % fallbackImages.teacher.length],
  }))
}

// Generate placeholder mentor data
const generatePlaceholderMentors = (): RankingItem[] => {
  const mentorNames = [
    'Alexandra Chen',
    'Jonathan Parker',
    'Samantha Wright',
    'Benjamin Foster',
    'Victoria Hayes',
    'Christopher Reed',
    'Natalie Morgan',
    'Daniel Cooper',
    'Gabriella Scott',
    'Matthew Turner',
  ]

  return mentorNames.map((name, index) => ({
    id: `mentor-${index}`,
    name,
    count: Math.floor(Math.random() * 30) + 15,
    points: Math.floor(Math.random() * 600) + 150, // Random points between 150-750
    type: 'mentor' as const,
    image: fallbackImages.mentor[index % fallbackImages.mentor.length],
  }))
}

// Verify if data has real user information
const hasRealUserData = (data: RankingItem[]): boolean => {
  if (!data || data.length === 0) return false;
  
  // Check all items in array to ensure we have at least one valid item
  for (const item of data) {
    // Skip items without ID or name
    if (!item.id || !item.name) continue;
    
    // If any item has real data (not a mock ID), return true
    if (!item.id.startsWith('student-') && 
        !item.id.startsWith('teacher-') && 
        !item.id.startsWith('mentor-') && 
        !item.id.startsWith('school-')) {
      // If the item has points, it's likely real data
      if (item.points !== undefined || item.count !== undefined) {
        return true;
      }
    }
  }
  
  return false;
}

export const ClientRankingsCarousel: React.FC<ClientRankingsCarouselProps> = ({
  schoolData,
  studentData,
  teacherData,
  mentorData,
}) => {
  const [activeTab, setActiveTab] = useState<'school' | 'student' | 'teacher' | 'mentor'>('school')
  const [dataLoaded, setDataLoaded] = useState(false);

  // Log incoming data for debugging
  useEffect(() => {
    console.log('ClientRankingsCarousel received data:', {
      schoolCount: schoolData?.length || 0,
      studentCount: studentData?.length || 0,
      teacherCount: teacherData?.length || 0,
      mentorCount: mentorData?.length || 0,
    });
    
    // Set initial active tab based on which has data
    if (studentData && studentData.length > 0) {
      setActiveTab('student');
    } else if (teacherData && teacherData.length > 0) {
      setActiveTab('teacher');
    } else if (mentorData && mentorData.length > 0) {
      setActiveTab('mentor');
    }
    
    setDataLoaded(true);
  }, [schoolData, studentData, teacherData, mentorData]);

  // Generate placeholder data if real data is empty
  const finalSchoolData = useMemo(
    () => (hasRealUserData(schoolData) ? schoolData : generatePlaceholderSchools()),
    [schoolData],
  )

  const finalStudentData = useMemo(
    () => (hasRealUserData(studentData) ? studentData : generatePlaceholderStudents()),
    [studentData],
  )

  const finalTeacherData = useMemo(
    () => (hasRealUserData(teacherData) ? teacherData : generatePlaceholderTeachers()),
    [teacherData],
  )

  const finalMentorData = useMemo(
    () => (hasRealUserData(mentorData) ? mentorData : generatePlaceholderMentors()),
    [mentorData],
  )

  // Check if we're using real or placeholder data
  const isSchoolPlaceholder = !hasRealUserData(schoolData);
  const isStudentPlaceholder = !hasRealUserData(studentData);
  const isTeacherPlaceholder = !hasRealUserData(teacherData);
  const isMentorPlaceholder = !hasRealUserData(mentorData);

  return (
    <div className="py-12">
      <div className="tabs">
        <button
          className={`tab-button ${activeTab === 'school' ? 'active' : ''}`}
          onClick={() => setActiveTab('school')}
        >
          Top Schools
        </button>
        <button
          className={`tab-button ${activeTab === 'student' ? 'active' : ''}`}
          onClick={() => setActiveTab('student')}
        >
          Top Students
        </button>
        <button
          className={`tab-button ${activeTab === 'teacher' ? 'active' : ''}`}
          onClick={() => setActiveTab('teacher')}
        >
          Top Teachers
        </button>
        <button
          className={`tab-button ${activeTab === 'mentor' ? 'active' : ''}`}
          onClick={() => setActiveTab('mentor')}
        >
          Top Mentors
        </button>
      </div>

      {activeTab === 'school' && (
        <RankingsCarousel
          title="TOP SCHOOLS"
          items={finalSchoolData}
          type="school"
          isPlaceholder={isSchoolPlaceholder}
        />
      )}
      {activeTab === 'student' && (
        <RankingsCarousel
          title="TOP STUDENTS"
          items={finalStudentData}
          type="student"
          isPlaceholder={isStudentPlaceholder}
        />
      )}
      {activeTab === 'teacher' && (
        <RankingsCarousel
          title="TOP TEACHERS"
          items={finalTeacherData}
          type="teacher"
          isPlaceholder={isTeacherPlaceholder}
        />
      )}
      {activeTab === 'mentor' && (
        <RankingsCarousel
          title="TOP MENTORS"
          items={finalMentorData}
          type="mentor"
          isPlaceholder={isMentorPlaceholder}
        />
      )}
    </div>
  )
}
