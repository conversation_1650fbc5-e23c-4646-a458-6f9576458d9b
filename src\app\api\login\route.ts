import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import config from '@/payload.config'

export async function POST(req: NextRequest) {
  try {
    const payload = await getPayload({
      config,
    })

    const formData = await req.formData()
    const email = formData.get('email') as string
    const password = formData.get('password') as string

    // Validate inputs
    if (!email || !password) {
      return NextResponse.json({ error: 'Email and password are required' }, { status: 400 })
    }

    try {
      // Attempt to login
      console.log('Attempting login with email:', email)
      const result = await payload.login({
        collection: 'users',
        data: {
          email: email,
          password: password,
        },
      })

      // Set the payload token as a cookie
      const cookieStore = await cookies()
      console.log('Setting payload-token cookie with token:', result.token.substring(0, 20) + '...')
      cookieStore.set('payload-token', result.token, {
        httpOnly: true,
        path: '/',
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
        maxAge: 60 * 60 * 24 * 7, // 7 days
      })

      // Redirect to the unified dashboard for all user roles
      const redirectUrl = new URL('/dashboard', req.url)
      console.log('Redirecting to:', redirectUrl.toString())
      return NextResponse.redirect(redirectUrl, { status: 302 })
    } catch (error) {
      console.error('Login error:', error)
      return NextResponse.json({ error: 'Invalid email or password' }, { status: 401 })
    }
  } catch (error) {
    console.error('Server error:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}
