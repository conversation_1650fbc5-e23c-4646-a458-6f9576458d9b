# Dashboard Validation Tools

This directory contains tools for validating dashboard functionality across different user roles in the Young Reporter platform.

## Super-Admin Dashboard Validation

The super-admin dashboard validation tool checks for the presence and functionality of various features and actions that should be available to super-admin users.

### Features Checked

- **Global Activity Heatmap**: Validates that the heatmap visualizes activity over time across all schools.
- **Cross-School Statistics**: Confirms aggregated metrics span every school in the system.
- **System Health Monitor**: Validates CPU, memory, and uptime statistics display correctly.
- **Role Distribution Pie Chart**: Checks that the pie chart segments sum to 100% and match user counts per role.
- **Raw Audit Logs**: Verifies that the raw log feed includes entries for creation, updates, and deletions across all collections.
- **System Configurations**: Ensures settings (e.g., feature toggles, multi-tenancy flags) are visible.

### Actions Validated

- **Create/Manage Schools**: Tests creating a new school, editing its fields, and listing it in the schools index.
- **Force Publish Content**: Tests overriding status locks on Articles or News as a super-admin.
- **Export Global Reports**: Verifies that the export generates a downloadable CSV or JSON reflecting all schools.

## Usage

### Command Line

You can run the validation tool from the command line using:

```bash
# Using npm script
npm run validate:super-admin

# Or directly with Node.js
node src/scripts/validate-dashboard.js --role super-admin

# Or with TypeScript
ts-node src/scripts/validate-dashboard.ts --role super-admin
```

#### Options

- `--role <role>`: Role to validate (default: "super-admin")
- `--check <check...>`: Specific features to check (if not provided, all default checks are run)
- `--action <action...>`: Specific actions to validate (if not provided, all default actions are run)
- `--output <file>`: Output file for results in JSON format
- `--verbose`: Show detailed output including data

Examples:

```bash
# Check specific features
npm run validate:super-admin -- --check "global activity heatmap" --check "system health monitor"

# Validate specific actions
npm run validate:super-admin -- --action "force publish content"

# Save results to a file
npm run validate:super-admin -- --output validation-results.json

# Show detailed output
npm run validate:super-admin -- --verbose
```

### Web Interface

You can also access the validation tool through the web interface at:

```
/dashboard/super-admin/validate
```

This page provides a user-friendly interface for running validation checks and viewing the results.

## Extending the Validation Tool

### Adding New Checks

To add a new check, update the following files:

1. Add the check to the `DEFAULT_SUPER_ADMIN_CHECKS` array in `src/scripts/validate-dashboard.js` and `src/scripts/validate-dashboard.ts`
2. Add a validation function in `src/app/api/dashboard/super-admin/validate/route.ts`
3. Update the `SuperAdminValidation` component in `src/components/dashboard/SuperAdminValidation.tsx` to include the new check

### Adding New Actions

To add a new action, update the following files:

1. Add the action to the `DEFAULT_SUPER_ADMIN_ACTIONS` array in `src/scripts/validate-dashboard.js` and `src/scripts/validate-dashboard.ts`
2. Add a validation function in `src/app/api/dashboard/super-admin/validate/route.ts`
3. Update the `SuperAdminValidation` component in `src/components/dashboard/SuperAdminValidation.tsx` to include the new action

## Troubleshooting

If you encounter issues with the validation tool:

1. Check that you have the necessary permissions (super-admin role)
2. Ensure the API endpoint is accessible
3. Check the server logs for any errors
4. Verify that the collections being checked exist in the database

## Integration with CI/CD

You can integrate the validation tool with your CI/CD pipeline to ensure that dashboard functionality is working correctly before deployment:

```yaml
# Example GitHub Actions workflow
validate-dashboards:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
      with:
        node-version: '18'
    - run: npm ci
    - run: npm run validate:super-admin -- --output validation-results.json
    - uses: actions/upload-artifact@v3
      with:
        name: validation-results
        path: validation-results.json
```
