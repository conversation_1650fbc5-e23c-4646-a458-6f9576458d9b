'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { HardDrive, Activity, Users, AlertCircle } from 'lucide-react'
import { Progress } from '@/components/ui/progress'

interface ResourceMetric {
  name: string
  value: number
  maxValue: number
  unit: string
}

interface ResourceUtilizationMetricsProps {
  schoolId: string
}

export default function ResourceUtilizationMetrics({ schoolId }: ResourceUtilizationMetricsProps) {
  const [resourceMetrics, setResourceMetrics] = useState<ResourceMetric[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function fetchResourceMetrics() {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/dashboard/school-admin/resource-metrics?schoolId=${schoolId}`)
        
        if (!response.ok) {
          throw new Error('فشل في جلب مقاييس استخدام الموارد')
        }
        
        const data = await response.json()
        setResourceMetrics(data.resourceMetrics || [])
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching resource metrics:', err)
        setError('فشل في تحميل مقاييس استخدام الموارد')
        setIsLoading(false)
      }
    }
    
    fetchResourceMetrics()
  }, [schoolId])
  
  const getResourceIcon = (name: string) => {
    if (name.toLowerCase().includes('storage')) {
      return <HardDrive className="h-5 w-5" />
    } else if (name.toLowerCase().includes('api') || name.toLowerCase().includes('call')) {
      return <Activity className="h-5 w-5" />
    } else if (name.toLowerCase().includes('user') || name.toLowerCase().includes('session')) {
      return <Users className="h-5 w-5" />
    } else {
      return <Activity className="h-5 w-5" />
    }
  }
  
  const getProgressColor = (value: number, maxValue: number) => {
    const percentage = (value / maxValue) * 100
    if (percentage < 50) {
      return 'bg-green-500'
    } else if (percentage < 75) {
      return 'bg-yellow-500'
    } else {
      return 'bg-red-500'
    }
  }
  
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center" dir="rtl">
            <Activity className="ml-2 h-5 w-5" />
            مقاييس استخدام الموارد
          </CardTitle>
        </CardHeader>
        <CardContent dir="rtl">
          <div className="bg-red-50 text-red-500 p-4 rounded-md">
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center" dir="rtl">
          <Activity className="ml-2 h-5 w-5" />
          مقاييس استخدام الموارد
        </CardTitle>
      </CardHeader>
      <CardContent dir="rtl">
        {isLoading ? (
          <div className="space-y-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-40" />
                  <Skeleton className="h-4 w-20" />
                </div>
                <Skeleton className="h-4 w-full" />
              </div>
            ))}
          </div>
        ) : resourceMetrics.length > 0 ? (
          <div className="space-y-6">
            {resourceMetrics.map((metric, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="bg-primary/10 p-2 rounded-full ml-3">
                      {getResourceIcon(metric.name)}
                    </div>
                    <h4 className="font-medium">{metric.name}</h4>
                  </div>
                  <div className="text-sm font-medium">
                    {metric.value} / {metric.maxValue} {metric.unit}
                  </div>
                </div>
                <div className="relative pt-1">
                  <div className="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                    <div
                      style={{ width: `${(metric.value / metric.maxValue) * 100}%` }}
                      className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center ${getProgressColor(
                        metric.value,
                        metric.maxValue
                      )}`}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="h-12 w-12 text-gray-300 mb-4" />
            <p className="text-gray-500">لا تتوفر بيانات استخدام الموارد</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
