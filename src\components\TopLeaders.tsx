import Link from 'next/link'
import React from 'react'

interface Leader {
  id: string
  name: string
  anonymizedName?: string
  score: number
  type: 'student' | 'teacher' | 'school' | 'mentor'
}

interface TopLeadersProps {
  leaders: Leader[]
  title?: string
  showViewAll?: boolean
  anonymizeStudents?: boolean
}

export const TopLeaders: React.FC<TopLeadersProps> = ({
  leaders,
  title = 'Top Leaders',
  showViewAll = true,
  anonymizeStudents = false,
}) => {
  // Get icon based on leader type
  const getLeaderIcon = (type: string) => {
    switch (type) {
      case 'student':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-green-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        )
      case 'teacher':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-purple-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
            />
          </svg>
        )
      case 'school':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-orange-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        )
      case 'mentor':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-blue-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            />
          </svg>
        )
      default:
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-gray-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
            />
          </svg>
        )
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 text-center">
      <h3 className="text-xl font-bold mb-6 text-center">{title}</h3>

      {leaders.length > 0 ? (
        <ul className="space-y-4">
          {leaders.map((leader, index) => (
            <li
              key={leader.id}
              className={`flex items-center justify-between pb-3 ${
                index < leaders.length - 1 ? 'border-b border-gray-100' : ''
              }`}
            >
              <div className="flex items-center">
                <span
                  className={`text-lg font-bold mr-3 ${
                    index === 0
                      ? 'text-yellow-500'
                      : index === 1
                        ? 'text-gray-400'
                        : index === 2
                          ? 'text-amber-600'
                          : 'text-primary'
                  }`}
                >
                  #{index + 1}
                </span>
                <div className="flex items-center">
                  {getLeaderIcon(leader.type)}
                  <span className="ml-2 font-medium">
                    {anonymizeStudents && leader.type === 'student'
                      ? leader.anonymizedName || `Student ${leader.id.substring(0, 4)}`
                      : leader.name}
                  </span>
                </div>
              </div>
              <span className="font-semibold text-primary">{leader.score}</span>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-muted-foreground text-center py-4">لا يوجد قادة متاحون</p>
      )}

      {showViewAll && (
        <div className="mt-6 text-center">
          <Link
            href="/statistics"
            className="text-primary hover:text-primary/80 font-semibold inline-flex items-center"
          >
            عرض جميع التصنيفات
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 ml-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      )}
    </div>
  )
}
