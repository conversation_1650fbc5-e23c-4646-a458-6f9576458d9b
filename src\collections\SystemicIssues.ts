import { CollectionConfig } from 'payload/types'
import { isAdmin, isMentor, isAdminOrMentor } from '../access/isRole'

const SystemicIssues: CollectionConfig = {
  slug: 'systemicIssues',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'type', 'severity', 'status', 'reporter', 'createdAt'],
    group: 'Content',
  },
  access: {
    read: isAdminOrMentor,
    create: isAdminOrMentor,
    update: isAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Inappropriate Content', value: 'inappropriate-content' },
        { label: 'Review Quality', value: 'review-quality' },
        { label: 'Student Behavior', value: 'student-behavior' },
        { label: 'Teacher Behavior', value: 'teacher-behavior' },
        { label: 'Technical Issue', value: 'technical-issue' },
        { label: 'Other', value: 'other' },
      ],
    },
    {
      name: 'severity',
      type: 'select',
      required: true,
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
        { label: 'Critical', value: 'critical' },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'open',
      options: [
        { label: 'Open', value: 'open' },
        { label: 'In Progress', value: 'in-progress' },
        { label: 'Resolved', value: 'resolved' },
        { label: 'Closed', value: 'closed' },
      ],
    },
    {
      name: 'reporter',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'school',
      type: 'relationship',
      relationTo: 'schools',
      required: true,
    },
    {
      name: 'relatedTeachers',
      type: 'relationship',
      relationTo: 'users',
      hasMany: true,
      filterOptions: {
        role: {
          equals: 'teacher',
        },
      },
    },
    {
      name: 'relatedArticles',
      type: 'relationship',
      relationTo: 'articles',
      hasMany: true,
    },
    {
      name: 'adminNotes',
      type: 'textarea',
      admin: {
        condition: (data, siblingData) => {
          return data.status === 'in-progress' || data.status === 'resolved' || data.status === 'closed'
        },
      },
    },
    {
      name: 'resolution',
      type: 'textarea',
      admin: {
        condition: (data, siblingData) => {
          return data.status === 'resolved' || data.status === 'closed'
        },
      },
    },
  ],
  timestamps: true,
}

export default SystemicIssues
