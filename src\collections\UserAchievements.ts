import type { CollectionConfig } from 'payload'

export const UserAchievements: CollectionConfig = {
  slug: 'user-achievements',
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['user', 'achievement', 'earnedAt'],
  },
  access: {
    read: async ({ req }) => {
      // Users can read their own achievements
      if (!req.user) return false

      // Check if user is super-admin
      const isSuperAdmin =
        (typeof req.user.role === 'object' && req.user.role?.slug === 'super-admin') ||
        req.user.role === 'super-admin'

      if (isSuperAdmin) return true

      // Regular users can only see their own achievements
      return {
        user: {
          equals: req.user.id,
        },
      }
    },
    create: async ({ req }) => {
      if (!req.user) return false
      return (
        (typeof req.user.role === 'object' && req.user.role?.slug === 'super-admin') ||
        req.user.role === 'super-admin'
      )
    },
    update: async ({ req }) => {
      if (!req.user) return false
      return (
        (typeof req.user.role === 'object' && req.user.role?.slug === 'super-admin') ||
        req.user.role === 'super-admin'
      )
    },
    delete: async ({ req }) => {
      if (!req.user) return false
      return (
        (typeof req.user.role === 'object' && req.user.role?.slug === 'super-admin') ||
        req.user.role === 'super-admin'
      )
    },
  },
  fields: [
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'achievement',
      type: 'relationship',
      relationTo: 'achievements',
      required: true,
    },
    {
      name: 'earnedAt',
      type: 'date',
      required: true,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'metadata',
      type: 'json',
      admin: {
        description: 'Additional metadata about how the achievement was earned',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        return {
          ...data,
          earnedAt: data.earnedAt || new Date().toISOString(),
        }
      },
    ],
  },
}
