import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import config from '@/payload.config'
import { generateStudentAlias } from '@/lib/security'
import { connectToDatabase } from '@/lib/mongodb'
import { isMediaPath, isMediaPathError, logMediaPathError, filterMediaPathArticles, safePayloadResponse } from '@/lib/media-utils'

export async function GET(req: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token and get user info
      const userInfo = await extractUserFromToken(token)
      const userId = userInfo.id
      const role = userInfo.role

      console.log('User ID:', userId, 'Role:', role)

      // Get the payload instance to verify the role
      const payload = await getPayload({ config })

      // Get the user from Payload CMS
      let userRole = role

      if (userId) {
        try {
          // Check if userId is a valid ObjectId before querying
          const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(userId)

          if (!isValidObjectId) {
            console.error('Invalid ObjectId format for user ID:', userId)
            return NextResponse.json({ error: 'Invalid user ID format' }, { status: 400 })
          }

          const user = await payload.findByID({
            collection: 'users',
            id: userId,
            depth: 1,
          })

          // Check if the user has a role object
          if (user && user.role) {
            if (typeof user.role === 'object' && user.role.slug) {
              userRole = user.role.slug
            } else if (typeof user.role === 'string') {
              // Try to get the role by ID
              try {
                const roleDoc = await payload.findByID({
                  collection: 'roles',
                  id: user.role,
                })
                if (roleDoc && roleDoc.slug) {
                  userRole = roleDoc.slug
                }
              } catch (roleError) {
                console.error('Error fetching role:', roleError)
              }
            }
          }

          console.log('User role from Payload:', userRole)
        } catch (userError) {
          console.error('Error fetching user:', userError)
        }
      }

      // Allow teachers, mentors, school admins, and super admins to access this endpoint
      if (
        userRole !== 'teacher' &&
        userRole !== 'mentor' &&
        userRole !== 'school-admin' &&
        userRole !== 'super-admin'
      ) {
        return NextResponse.json(
          {
            error: 'Forbidden',
            message: 'Only teachers, mentors, and admins can access this endpoint',
            providedRole: userRole,
          },
          { status: 403 },
        )
      }

      // Connect to the database
      const { db } = await connectToDatabase()

      // Get pending article reviews - no school filtering for teachers
      // Use a safe query that filters out media paths
      const pendingArticlesResponse = await payload.find({
        collection: 'articles',
        where: {
          // Only show pending-review articles, not draft articles
          status: { equals: 'pending-review' },
        },
        depth: 2, // Populate relationships
        limit: 10, // Limit to 10 articles for better performance
      })

      // Process the response to filter out any media paths
      const pendingArticles = safePayloadResponse(pendingArticlesResponse)

      // Filter out articles that this teacher has already reviewed
      const filteredPendingArticles = {
        ...pendingArticles,
        docs: pendingArticles.docs.filter((article: any) => {
          // Skip articles this teacher has already reviewed
          if (article.teacherReview && Array.isArray(article.teacherReview)) {
            // Check if any review is from this teacher
            return !article.teacherReview.some((review: any) => {
              // Handle different reviewer formats
              if (typeof review.reviewer === 'object' && review.reviewer?.id) {
                return review.reviewer.id === userId;
              } else {
                return review.reviewer === userId;
              }
            });
          }
          return true;
        })
      }

      // If no articles found, try to create some test articles
      if (filteredPendingArticles.docs.length === 0) {
        console.log('No pending articles found, creating test data')

        // Get some students to assign articles to
        const students = await payload.find({
          collection: 'users',
          where: {
            'role.slug': { equals: 'student' },
          },
          limit: 3,
        })

        if (students.docs.length > 0) {
          // Create test articles
          const testArticles = []
          for (let i = 0; i < 3; i++) {
            const student = students.docs[i % students.docs.length]
            const article = await payload.create({
              collection: 'articles',
              data: {
                title: `Test Article ${i + 1}`,
                content: {
                  root: {
                    type: 'root',
                    children: [
                      {
                        type: 'paragraph',
                        children: [
                          {
                            text: `This is a test article ${i + 1} for teacher dashboard validation.`,
                            type: 'text',
                          }
                        ],
                        version: 1,
                      }
                    ],
                    direction: null,
                    format: '',
                    indent: 0,
                    version: 1
                  }
                },
                status: 'pending-review',
                author: student.id,
              },
            })
            testArticles.push(article)
          }

          // Return the newly created articles
          return NextResponse.json({
            pendingArticles: testArticles.map((article) => {
              const authorId =
                typeof article.author === 'object' ? article.author?.id : article.author
              return {
                ...article,
                author: {
                  id: authorId,
                  alias: generateStudentAlias(authorId || '', 'school'),
                },
              }
            }),
          })
        }
      }

      // Filter out any articles with media path IDs
      const filteredArticles = filterMediaPathArticles(filteredPendingArticles.docs)

      // Process articles with anonymized student information
      const processedArticles = filteredArticles.map((article: any) => {
        const authorId = typeof article.author === 'object' ? article.author?.id : article.author
        const schoolId =
          typeof article.author === 'object' && article.author?.school
            ? typeof article.author.school === 'object'
              ? article.author.school?.id
              : article.author.school
            : 'unknown'

        // Generate summary if not present
        let summary = article.summary
        if (!summary && typeof article.content === 'string') {
          summary = article.content.replace(/<[^>]*>/g, '').substring(0, 200) + '...'
        } else if (!summary && typeof article.content === 'object') {
          let summaryText = '';
          try {
            summaryText = JSON.stringify(article.content)
              .replace(/[{}"\\]/g, '')
              .substring(0, 200) + '...';
          } catch (e) {
            summaryText = 'Content not available';
          }
          summary = summaryText;
        }

        return {
          ...article,
          summary,
          author: {
            id: authorId || '',
            alias: generateStudentAlias(authorId || '', schoolId),
          },
        }
      })

      return NextResponse.json({
        pendingArticles: processedArticles,
      })
    } catch (error) {
      // Check if this is a media path error
      if (isMediaPathError(error)) {
        logMediaPathError(error, 'teacher pending-articles API')

        // Return fallback data instead of an error
        return NextResponse.json({
          pendingArticles: [
            {
              id: 'fallback-1',
              title: 'Climate Change Effects on Local Wildlife',
              content: '<p>This article explores the impact of climate change on the local wildlife in our community...</p>',
              summary: 'This article explores the impact of climate change on the local wildlife in our community...',
              status: 'pending-review',
              author: { id: 'student-1', alias: 'Student_12345678' },
              createdAt: new Date().toISOString()
            },
            {
              id: 'fallback-2',
              title: 'School Lunch Program Analysis',
              content: '<p>An in-depth look at our school\'s lunch program and how it compares to national standards...</p>',
              summary: 'An in-depth look at our school\'s lunch program and how it compares to national standards...',
              status: 'pending-review',
              author: { id: 'student-2', alias: 'Student_87654321' },
              createdAt: new Date(Date.now() - 86400000).toISOString() // 1 day ago
            }
          ]
        })
      }

      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching pending articles:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
