'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Users, Search, AlertCircle, FileText, Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useRouter } from 'next/navigation'
import AddUserModal from '@/components/dashboard/AddUserModal'

interface Student {
  id: string
  firstName: string
  lastName: string
  email: string
  status: string
  articlesCount: number
  points: number
  anonymizedId?: string
}

interface StudentDataTableProps {
  schoolId: string
}

export default function StudentDataTable({ schoolId }: StudentDataTableProps) {
  const router = useRouter()
  const [students, setStudents] = useState<Student[]>([])
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)

  useEffect(() => {
    fetchStudents()
  }, [schoolId])

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredStudents(students)
    } else {
      const query = searchQuery.toLowerCase()
      const filtered = students.filter(
        (student) =>
          student.id.toLowerCase().includes(query) ||
          (student.firstName || '').toLowerCase().includes(query) ||
          (student.lastName || '').toLowerCase().includes(query) ||
          (student.anonymizedId || `الطالب ${student.id.substring(0, 4)}`)
            .toLowerCase()
            .includes(query) ||
          student.email.toLowerCase().includes(query) ||
          student.status.toLowerCase().includes(query),
      )
      setFilteredStudents(filtered)
    }
  }, [searchQuery, students])

  const handleViewStudent = (id: string) => {
    router.push(`/dashboard/students/${id}`)
  }

  const handleUserAdded = () => {
    // Refresh the students list
    fetchStudents()
  }

  const fetchStudents = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/dashboard/school-admin/students?schoolId=${schoolId}`)

      if (!response.ok) {
        throw new Error('فشل في جلب بيانات الطلاب')
      }

      const data = await response.json()
      setStudents(data.students || [])
      setFilteredStudents(data.students || [])
      setIsLoading(false)
    } catch (err) {
      console.error('Error fetching student data:', err)
      setError('فشل في تحميل بيانات الطلاب')
      setIsLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <Badge className="bg-green-500">نشط</Badge>
      case 'pending':
        return <Badge className="bg-yellow-500">معلق</Badge>
      case 'suspended':
        return <Badge className="bg-red-500">موقوف</Badge>
      default:
        return <Badge className="bg-gray-500">{status}</Badge>
    }
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center" dir="rtl">
            <Users className="ml-2 h-5 w-5" />
            بيانات الطلاب
          </CardTitle>
        </CardHeader>
        <CardContent dir="rtl">
          <div className="bg-red-50 text-red-500 p-4 rounded-md">{error}</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between" dir="rtl">
          <div className="flex items-center">
            <Users className="ml-2 h-5 w-5" />
            بيانات الطلاب
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setIsAddModalOpen(true)}
              size="sm"
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة طالب
            </Button>
            <div className="relative w-64">
              <Search className="absolute right-2 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="بحث عن طلاب..."
                className="pr-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent dir="rtl">
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between border-b pb-4">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-40" />
                    <Skeleton className="h-3 w-32 mt-2" />
                  </div>
                </div>
                <Skeleton className="h-8 w-24" />
              </div>
            ))}
          </div>
        ) : filteredStudents.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right py-2 px-4">الاسم</th>
                  <th className="text-right py-2 px-4">البريد الإلكتروني</th>
                  <th className="text-center py-2 px-4">الحالة</th>
                  <th className="text-center py-2 px-4">
                    <FileText className="h-4 w-4 inline ml-1" />
                    المقالات
                  </th>
                  <th className="text-center py-2 px-4">النقاط</th>
                  <th className="text-left py-2 px-4">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredStudents.map((student) => (
                  <tr key={student.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <span className="font-medium">
                        {student.firstName && student.lastName
                          ? `${student.firstName} ${student.lastName}`
                          : student.anonymizedId || `الطالب ${student.id.substring(0, 4)}`}
                      </span>
                      <span className="text-xs text-gray-500 block">ID: {student.id}</span>
                    </td>
                    <td className="py-3 px-4 text-gray-600">{student.email}</td>
                    <td className="py-3 px-4 text-center">{getStatusBadge(student.status)}</td>
                    <td className="py-3 px-4 text-center">{student.articlesCount}</td>
                    <td className="py-3 px-4 text-center font-medium">{student.points}</td>
                    <td className="py-3 px-4 text-left">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewStudent(student.id)}
                      >
                        عرض
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="h-12 w-12 text-gray-300 mb-4" />
            <p className="text-gray-500">لم يتم العثور على بيانات طلاب</p>
            {searchQuery && (
              <Button variant="link" onClick={() => setSearchQuery('')} className="mt-2">
                مسح البحث
              </Button>
            )}
          </div>
        )}
      </CardContent>

      <AddUserModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        schoolId={schoolId}
        defaultRole="student"
        onUserAdded={handleUserAdded}
      />
    </Card>
  )
}
