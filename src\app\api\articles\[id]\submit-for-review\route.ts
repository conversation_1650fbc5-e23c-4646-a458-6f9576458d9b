import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import config from '@/payload.config'

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params
    const payload = await getPayload({
      config,
    })

    // Get the current user
    const { user } = await payload.auth({ req })

    if (!user) {
      return NextResponse.json(
        { error: 'You must be logged in to submit an article for review' },
        { status: 401 }
      )
    }

    // Get the article
    const article = await payload.findByID({
      collection: 'articles',
      id,
    })

    // Check if the user is the author
    const isAuthor = article.author === user.id || 
      (typeof article.author === 'object' && article.author?.id === user.id)

    if (!isAuthor) {
      return NextResponse.json(
        { error: 'You can only submit your own articles for review' },
        { status: 403 }
      )
    }

    // Check if article is in draft status
    if (article.status !== 'draft') {
      return NextResponse.json(
        { error: 'Only draft articles can be submitted for review' },
        { status: 400 }
      )
    }

    // Update the article status
    await payload.update({
      collection: 'articles',
      id,
      data: {
        status: 'pending-review',
      },
    })

    // Redirect to the student dashboard
    return NextResponse.redirect(new URL('/dashboard/student', req.url))
  } catch (error) {
    console.error('Error submitting article for review:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
