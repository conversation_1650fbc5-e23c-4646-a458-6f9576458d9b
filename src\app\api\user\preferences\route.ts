import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import { ObjectId } from 'mongodb'

import config from '@/payload.config'

// GET /api/user/preferences - Get user preferences
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config: await config })
    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')
    
    // Check if userId is provided
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    // Get the user's preferences
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Return the preferences
    return NextResponse.json({
      theme: user.theme || { darkMode: false, themeColor: 'cyan' },
    })
  } catch (error) {
    console.error('Error fetching user preferences:', error)
    return NextResponse.json({ error: 'Failed to fetch user preferences' }, { status: 500 })
  }
}

// POST /api/user/preferences - Update user preferences
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config: await config })
    const { userId, theme } = await request.json()

    // Check if userId is provided
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    console.log('Updating theme for user:', userId, 'Theme:', JSON.stringify(theme))

    try {
      // Directly use Payload's update function, which handles validation properly
      const updatedUser = await payload.update({
        collection: 'users',
        id: userId,
        data: { theme },
        draft: false,
      })
      
      console.log('Successfully updated theme for user:', userId)
      return NextResponse.json({ success: true, user: { id: updatedUser.id, theme: updatedUser.theme } })
    } catch (updateError) {
      console.error('Failed to update using Payload:', updateError)
      
      // Fallback to direct MongoDB update if Payload update fails
      const db = payload.db.connection;
      const usersCollection = db.collection('users');
      
      // Convert string ID to MongoDB ObjectId
      let objectId;
      try {
        objectId = new ObjectId(userId);
      } catch (e) {
        console.error('Invalid ObjectId format:', e);
        return NextResponse.json({ error: 'Invalid user ID format' }, { status: 400 });
      }
      
      // Update only the theme field directly in MongoDB
      const result = await usersCollection.updateOne(
        { _id: objectId },
        { $set: { theme } }
      );

      console.log('MongoDB update result:', result);

      // Check if the update was successful
      if (result.matchedCount === 0) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      return NextResponse.json({ success: true })
    }
  } catch (error) {
    console.error('Error updating user preferences:', error)
    return NextResponse.json({ error: `Failed to update user preferences: ${error}` }, { status: 500 })
  }
} 