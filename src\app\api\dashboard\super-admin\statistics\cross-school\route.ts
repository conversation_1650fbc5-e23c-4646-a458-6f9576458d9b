import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    if (!token) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
    const userId = typeof decoded === 'object' ? decoded.id : null
    if (!userId) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    const payload = await getPayload({ config })
    const user = await payload.findByID({ collection: 'users', id: userId, depth: 1 })
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'super-admin') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    try {
      const { db } = await connectToDatabase()
      const schools = await db.collection('schools').find({}).toArray()
      const stats = await Promise.all(schools.map(async (school: any) => {
        const schoolId = school._id?.toString() || school.id
        const articles = await db.collection('articles').countDocuments({ $or: [ { 'author.school.id': schoolId }, { 'author.school': schoolId }, { school: schoolId } ] })
        const news = await db.collection('news').countDocuments({ $or: [ { 'author.school.id': schoolId }, { 'author.school': schoolId }, { school: schoolId } ] })
        const students = await db.collection('users').countDocuments({ $or: [ { 'school.id': schoolId }, { school: schoolId } ], role: 'student' })
        const teachers = await db.collection('users').countDocuments({ $or: [ { 'school.id': schoolId }, { school: schoolId } ], role: 'teacher' })
        const comments = await db.collection('systemicIssues').countDocuments({ school: schoolId })
        const activity = await db.collection('activities').countDocuments({ $or: [ { 'school.id': schoolId }, { school: schoolId } ] })
        return {
          schoolId,
          schoolName: school.name,
          articles,
          news,
          students,
          teachers,
          comments,
          activity,
        }
      }))
      if (stats.length > 0) return NextResponse.json({ statistics: stats })
    } catch (mongoError) {
      console.warn('Error fetching cross-school stats from MongoDB, falling back to Payload:', mongoError)
    }
    // Fallback to Payload CMS
    try {
      const schools = await payload.find({ collection: 'schools', limit: 100, depth: 1 })
      const stats = await Promise.all((schools.docs || []).map(async (school: any) => {
        const schoolId = school.id
        const articles = await payload.count({ collection: 'articles', where: { 'author.school': { equals: schoolId } } })
        const news = await payload.count({ collection: 'news', where: { 'author.school': { equals: schoolId } } })
        const students = await payload.count({ collection: 'users', where: { school: { equals: schoolId }, role: { equals: 'student' } } })
        const teachers = await payload.count({ collection: 'users', where: { school: { equals: schoolId }, role: { equals: 'teacher' } } })
        const comments = await payload.count({ collection: 'systemicIssues', where: { school: { equals: schoolId } } })
        const activity = await payload.count({ collection: 'activities', where: { school: { equals: schoolId } } })
        return {
          schoolId,
          schoolName: school.name,
          articles,
          news,
          students,
          teachers,
          comments,
          activity,
        }
      }))
      if (stats.length > 0) return NextResponse.json({ statistics: stats })
    } catch (payloadError) {
      console.warn('Error fetching cross-school stats from Payload CMS:', payloadError)
    }
    // Fallback mock data
    return NextResponse.json({ statistics: [] })
  } catch (error) {
    console.error('Error fetching cross-school statistics:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 