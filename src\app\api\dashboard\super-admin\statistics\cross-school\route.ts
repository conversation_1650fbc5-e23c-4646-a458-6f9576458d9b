import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    if (!token) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
    const userId = typeof decoded === 'object' ? decoded.id : null
    if (!userId) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    const payload = await getPayload({ config })
    const user = await payload.findByID({ collection: 'users', id: userId, depth: 1 })
    const role = typeof user.role === 'object' ? user.role?.slug : user.role
    if (role !== 'super-admin') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    try {
      const { db } = await connectToDatabase()

      // First, get role IDs for student and teacher
      const studentRole = await db.collection('roles').findOne({ slug: 'student' })
      const teacherRole = await db.collection('roles').findOne({ slug: 'teacher' })

      console.log('Found roles:', {
        student: studentRole?._id,
        teacher: teacherRole?._id,
      })

      // Debug: Check what users actually look like
      const sampleUsers = await db.collection('users').find({}).limit(3).toArray()
      console.log(
        'Sample users structure:',
        sampleUsers.map((u) => ({
          id: u._id,
          role: u.role,
          school: u.school,
          email: u.email,
        })),
      )

      // Debug: Check what schools actually look like
      const sampleSchools = await db.collection('schools').find({}).limit(2).toArray()
      console.log(
        'Sample schools structure:',
        sampleSchools.map((s) => ({
          id: s._id,
          name: s.name,
        })),
      )

      const schools = await db.collection('schools').find({}).toArray()
      console.log(`Found ${schools.length} schools`)

      // Debug: Basic counts
      const totalUsers = await db.collection('users').countDocuments({})
      const totalArticles = await db.collection('articles').countDocuments({})
      const totalNews = await db.collection('news').countDocuments({})
      console.log(
        `Database totals: users=${totalUsers}, articles=${totalArticles}, news=${totalNews}`,
      )

      const stats = await Promise.all(
        schools.map(async (school: any) => {
          const schoolId = school._id?.toString() || school.id
          const schoolObjectId = new ObjectId(schoolId)
          const articles = await db.collection('articles').countDocuments({
            $or: [
              { 'author.school.id': schoolId },
              { 'author.school': schoolId },
              { school: schoolId },
            ],
          })
          const news = await db.collection('news').countDocuments({
            $or: [
              { 'author.school.id': schoolId },
              { 'author.school': schoolId },
              { school: schoolId },
            ],
          })
          // User queries with proper role handling
          let students = 0
          if (studentRole) {
            students = await db.collection('users').countDocuments({
              $and: [
                {
                  $or: [
                    { 'school.id': schoolId },
                    { school: schoolId },
                    { school: schoolObjectId },
                  ],
                },
                {
                  $or: [
                    { role: studentRole._id },
                    { role: studentRole._id.toString() },
                    { role: 'student' },
                    { 'role.slug': 'student' },
                  ],
                },
              ],
            })
          }

          let teachers = 0
          if (teacherRole) {
            teachers = await db.collection('users').countDocuments({
              $and: [
                {
                  $or: [
                    { 'school.id': schoolId },
                    { school: schoolId },
                    { school: schoolObjectId },
                  ],
                },
                {
                  $or: [
                    { role: teacherRole._id },
                    { role: teacherRole._id.toString() },
                    { role: 'teacher' },
                    { 'role.slug': 'teacher' },
                  ],
                },
              ],
            })
          }
          const comments = await db.collection('systemicIssues').countDocuments({
            $or: [{ school: schoolId }, { school: schoolObjectId }],
          })

          const activity = await db.collection('activities').countDocuments({
            $or: [{ 'school.id': schoolId }, { school: schoolId }, { school: schoolObjectId }],
          })

          console.log(
            `School ${school.name}: articles=${articles}, news=${news}, students=${students}, teachers=${teachers}`,
          )

          return {
            schoolId,
            schoolName: school.name,
            articles,
            news,
            students,
            teachers,
            comments,
            activity,
          }
        }),
      )

      console.log('Cross-school statistics:', stats)

      // Return data even if some counts are zero, so we can see the structure
      if (stats.length > 0) {
        return NextResponse.json({ statistics: stats })
      } else {
        console.log('No schools found in MongoDB, trying Payload fallback')
      }
    } catch (mongoError) {
      console.warn(
        'Error fetching cross-school stats from MongoDB, falling back to Payload:',
        mongoError,
      )
    }
    // Fallback to Payload CMS
    try {
      // First get role IDs
      const studentRoleResult = await payload.find({
        collection: 'roles',
        where: { slug: { equals: 'student' } },
        limit: 1,
      })
      const teacherRoleResult = await payload.find({
        collection: 'roles',
        where: { slug: { equals: 'teacher' } },
        limit: 1,
      })

      const studentRoleId = studentRoleResult.docs[0]?.id
      const teacherRoleId = teacherRoleResult.docs[0]?.id

      console.log('Payload roles found:', { studentRoleId, teacherRoleId })

      const schools = await payload.find({ collection: 'schools', limit: 100, depth: 1 })
      const stats = await Promise.all(
        (schools.docs || []).map(async (school: any) => {
          const schoolId = school.id
          const articles = await payload.count({
            collection: 'articles',
            where: { 'author.school': { equals: schoolId } },
          })
          const news = await payload.count({
            collection: 'news',
            where: { 'author.school': { equals: schoolId } },
          })

          let students = 0
          if (studentRoleId) {
            const studentsResult = await payload.count({
              collection: 'users',
              where: { school: { equals: schoolId }, role: { equals: studentRoleId } },
            })
            students = studentsResult.totalDocs
          }

          let teachers = 0
          if (teacherRoleId) {
            const teachersResult = await payload.count({
              collection: 'users',
              where: { school: { equals: schoolId }, role: { equals: teacherRoleId } },
            })
            teachers = teachersResult.totalDocs
          }
          const comments = await payload.count({
            collection: 'systemicIssues',
            where: { school: { equals: schoolId } },
          })
          const activity = await payload.count({
            collection: 'activities',
            where: { school: { equals: schoolId } },
          })
          return {
            schoolId,
            schoolName: school.name,
            articles,
            news,
            students,
            teachers,
            comments,
            activity,
          }
        }),
      )
      if (stats.length > 0) return NextResponse.json({ statistics: stats })
    } catch (payloadError) {
      console.warn('Error fetching cross-school stats from Payload CMS:', payloadError)
    }
    // Fallback mock data with diagnostic info
    console.log('Returning fallback data - both MongoDB and Payload failed')
    return NextResponse.json({
      statistics: [
        {
          schoolId: 'test-school-1',
          schoolName: 'Test School 1 (Fallback Data)',
          articles: 0,
          news: 0,
          students: 0,
          teachers: 0,
          comments: 0,
          activity: 0,
        },
      ],
      debug: 'Both MongoDB and Payload queries failed - check console logs',
    })
  } catch (error) {
    console.error('Error fetching cross-school statistics:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
