'use client'

import { useEffect } from 'react'

interface ViewCounterProps {
  id: string
  type: 'articles' | 'news'
}

export function ViewCounter({ id, type }: ViewCounterProps) {
  useEffect(() => {
    // Create an AbortController to cancel the request if the component unmounts
    const controller = new AbortController()
    const signal = controller.signal
    
    const incrementViewCount = async () => {
      try {
        // First try the collections API endpoint
        const collectionEndpoint = type === 'articles' 
          ? `/api/collections/articles/${id}`
          : `/api/collections/news/${id}`
        
        let success = false
        
        try {
          const response = await fetch(collectionEndpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'X-View-Count': 'increment'
            },
            signal
          })
          
          success = response.ok
          console.log(`First attempt: Incremented view count for ${type} with ID: ${id} - Success: ${success}`)
        } catch (firstError) {
          console.error(`Error on first attempt for ${type} with ID: ${id}:`, firstError)
        }
        
        // If the first attempt failed, try the direct API endpoint for the specific type
        if (!success) {
          const directEndpoint = type === 'articles' 
            ? `/api/articles/${id}`
            : `/api/news/${id}`
            
          try {
            const response = await fetch(directEndpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'X-View-Count': 'increment'
              },
              signal
            })
            
            success = response.ok
            console.log(`Second attempt: Incremented view count for ${type} with ID: ${id} - Success: ${success}`)
          } catch (secondError) {
            console.error(`Error on second attempt for ${type} with ID: ${id}:`, secondError)
          }
        }
        
        // If both attempts failed, try the analytics API as a last resort
        if (!success) {
          try {
            const analyticsEndpoint = `/api/analytics/views?type=${type}&id=${id}`
            await fetch(analyticsEndpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json'
              },
              signal
            })
            console.log(`Third attempt: Fetched analytics view for ${type} with ID: ${id}`)
          } catch (thirdError) {
            console.error(`Error on third attempt for ${type} with ID: ${id}:`, thirdError)
          }
        }
      } catch (error) {
        // Only log errors that aren't from aborting the request
        if (!(error instanceof DOMException && error.name === 'AbortError')) {
          console.error(`Error incrementing view count for ${type} with ID: ${id}:`, error)
        }
      }
    }
    
    // Only increment the view if this is a real user (check for bot user agents)
    const userAgent = navigator.userAgent.toLowerCase()
    const isBot = /bot|googlebot|crawler|spider|robot|crawling/i.test(userAgent)
    
    if (!isBot) {
      incrementViewCount()
    }
    
    // Clean up the request if the component unmounts
    return () => {
      controller.abort()
    }
  }, [id, type]) // Only run once when the component mounts
  
  // This component doesn't render anything
  return null
} 