'use client'

import { useState, useEffect } from 'react'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { ThemeSettings } from '@/components/dashboard/ThemeSettings'

export default function ThemeTestPage() {
  const [userId, setUserId] = useState<string | undefined>(undefined)
  
  useEffect(() => {
    // Simulate fetching a user ID
    const mockUserId = 'test-user-123'
    setUserId(mockUserId)
  }, [])
  
  return (
    <ThemeProvider userId={userId}>
      <div className="min-h-screen bg-background text-foreground p-8">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold mb-8 text-primary">Theme Test Page</h1>
          
          <div className="mb-8">
            <p className="mb-4">
              This page demonstrates the theme functionality. You can change the theme settings below
              and see how they affect the UI elements.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <div className="p-4 bg-card text-card-foreground rounded-lg border">
                <h3 className="text-lg font-medium mb-2">Card Component</h3>
                <p className="text-sm">This is a card component with the current theme.</p>
              </div>
              
              <div className="p-4 bg-muted text-muted-foreground rounded-lg">
                <h3 className="text-lg font-medium mb-2">Muted Component</h3>
                <p className="text-sm">This is a muted component with the current theme.</p>
              </div>
            </div>
            
            <div className="flex flex-wrap gap-4 mb-8">
              <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md">
                Primary Button
              </button>
              <button className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md">
                Secondary Button
              </button>
              <button className="px-4 py-2 bg-accent text-accent-foreground rounded-md">
                Accent Button
              </button>
              <button className="px-4 py-2 bg-destructive text-destructive-foreground rounded-md">
                Destructive Button
              </button>
            </div>
          </div>
          
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-4">Theme Settings</h2>
            <ThemeSettings />
          </div>
        </div>
      </div>
    </ThemeProvider>
  )
} 