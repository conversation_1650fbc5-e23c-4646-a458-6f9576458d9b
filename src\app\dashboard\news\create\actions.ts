'use server'

import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { getPayload } from 'payload'
import config from '@/payload.config'

export async function createNews(formData: FormData) {
  try {
    // Get the payload instance
    const payload = await getPayload({
      config,
    })

    // Get the current user from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      throw new Error('Authentication token not found')
    }

    // Get the current user
    const { user } = await payload.auth({ token })

    if (!user) {
      throw new Error('You must be logged in to create a news post')
    }

    // Check if user is a mentor or admin
    const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
    const isMentor = userRole === 'mentor'
    const isAdmin = userRole === 'super-admin' || userRole === 'school-admin'

    if (!isMentor && !isAdmin) {
      throw new Error('Only mentors and admins can create news posts')
    }

    // Get form data
    const title = formData.get('title') as string
    const slug = formData.get('slug') as string
    const content = formData.get('content') as string
    const status = formData.get('status') as string

    if (!title || !slug || !content || !status) {
      throw new Error('All fields are required')
    }

    // Validate status
    if (status !== 'draft' && status !== 'published') {
      throw new Error('Invalid status')
    }

    // Create the news post
    const newsData = {
      title,
      slug,
      content,
      author: user.id,
      status,
    }

    // If publishing now, add publishedAt date
    if (status === 'published') {
      newsData.publishedAt = new Date().toISOString()
    }

    await payload.create({
      collection: 'news',
      data: newsData,
    })

    // Redirect to the news page
    redirect('/dashboard/news')
  } catch (error) {
    console.error('Error creating news post:', error)
    throw error
  }
}
