import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'
import { writeFile } from 'fs/promises'
import path from 'path'
import { existsSync, mkdirSync } from 'fs'

// Make sure the uploads directory exists
const UPLOAD_DIR = path.join(process.cwd(), 'public/uploads')

export async function POST(req: NextRequest) {
  try {
    // Ensure upload directory exists
    if (!existsSync(UPLOAD_DIR)) {
      mkdirSync(UPLOAD_DIR, { recursive: true })
    }

    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token and get the user ID
      const { userId } = await verifyJWT(token)

      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      // Get form data (multipart/form-data) from request
      const formData = await req.formData()
      const imageFile = formData.get('image') as File | null

      if (!imageFile) {
        return NextResponse.json({ error: 'No image provided' }, { status: 400 })
      }

      console.log('[School Image API] Received file:', imageFile.name, 'type:', imageFile.type, 'size:', imageFile.size)

      // Try to use Payload CMS for file upload
      try {
        const payload = await getPayload({ config })

        // Get the current user
        const user = await payload.findByID({
          collection: 'users',
          id: userId,
          depth: 1,
        })

        // Check if user is a school admin or super admin
        let userRole = null
        if (user.role && typeof user.role === 'object' && user.role.slug) {
          userRole = user.role.slug
        } else if (typeof user.role === 'string') {
          try {
            const roleDoc = await payload.findByID({ collection: 'roles', id: user.role })
            userRole = roleDoc?.slug || user.role
          } catch {
            userRole = user.role
          }
        }

        console.log('[School Image API] User role check:', userRole)
        if (userRole !== 'school-admin' && userRole !== 'super-admin') {
          console.log('[School Image API] Permission denied: User role is not allowed:', userRole)
          return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
        }

        // Get school ID
        let schoolId = typeof user.school === 'object' ? user.school?.id : user.school
        if (!schoolId || typeof schoolId !== 'string' || schoolId.trim() === '') {
          return NextResponse.json({ error: 'School ID not found' }, { status: 400 })
        }

        // Save the file to disk first (workaround for Payload file upload issues)
        const fileName = `school_${schoolId}_${Date.now()}_${imageFile.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;
        const filePath = path.join(UPLOAD_DIR, fileName);
        const fileBuffer = Buffer.from(await imageFile.arrayBuffer());
        await writeFile(filePath, fileBuffer);
        
        const publicUrl = `/uploads/${fileName}`;
        console.log('[School Image API] Saved file to:', filePath);

        // Update the school with direct image URL instead of media relationship
        try {
          // Validate ObjectId for MongoDB operations
          if (!ObjectId.isValid(schoolId)) {
            console.error('[School Image API] Invalid school ID:', schoolId);
            return NextResponse.json({ error: 'Invalid school ID' }, { status: 400 });
          }

          // Update directly in MongoDB first
          const { db } = await connectToDatabase();
          const updateResult = await db.collection('schools').updateOne(
            { _id: new ObjectId(schoolId) },
            {
              $set: {
                imageUrl: publicUrl,
                updatedAt: new Date().toISOString(),
              },
            }
          );

          console.log('[School Image API] MongoDB update result:', updateResult);

          // Also update in Payload
          try {
            // Use type assertion to bypass TypeScript validation
            await payload.update({
              collection: 'schools',
              id: schoolId,
              data: {
                imageUrl: publicUrl,
              } as any,
            });

            // Fetch the updated school
            const updatedSchool = await payload.findByID({
              collection: 'schools',
              id: schoolId,
            });

            console.log('[School Image API] Payload update result:', updatedSchool ? 'success' : 'failed');

            return NextResponse.json({
              success: true,
              message: 'School image updated successfully',
              school: {
                id: schoolId,
                imageUrl: publicUrl,
                name: updatedSchool?.name || '',
              },
            });
          } catch (payloadError) {
            console.error('[School Image API] Payload update error:', payloadError);
            // Continue with MongoDB result since that already succeeded
            return NextResponse.json({
              success: true,
              message: 'School image updated successfully (MongoDB only)',
              school: {
                id: schoolId,
                imageUrl: publicUrl,
              },
            });
          }
        } catch (updateError) {
          console.error('Error updating school with image URL:', updateError);
          return NextResponse.json({ error: 'Failed to update school with image' }, { status: 500 });
        }
      } catch (payloadError) {
        console.error('Error uploading school image via Payload:', payloadError)
        return NextResponse.json({ error: 'Failed to upload image' }, { status: 500 })
      }
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error updating school image:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 