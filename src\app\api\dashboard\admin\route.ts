import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import jwt from 'jsonwebtoken'

import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'

// GET admin dashboard data
export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const limit = parseInt(url.searchParams.get('limit') || '5')

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the current user
        const mongoUser = await db.collection('users').findOne({ id: userId })

        if (mongoUser) {
          // Verify user is an admin
          const role = typeof mongoUser.role === 'object' ? mongoUser.role?.slug : mongoUser.role

          if (role !== 'super-admin' && role !== 'school-admin') {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get school ID if school admin
          const schoolId =
            role === 'school-admin'
              ? typeof mongoUser.school === 'object'
                ? mongoUser.school?.id
                : mongoUser.school
              : null

          // Build query filters
          const schoolFilter = schoolId ? { 'school.id': schoolId } : {}
          const authorSchoolFilter = schoolId ? { 'author.school.id': schoolId } : {}
          const schoolIdFilter = schoolId ? { id: schoolId } : {}

          // Get counts
          const usersCount = await db.collection('users').countDocuments(schoolFilter)

          const articlesCount = await db.collection('articles').countDocuments(authorSchoolFilter)

          const newsCount = await db.collection('news').countDocuments(authorSchoolFilter)

          const schoolsCount = await db.collection('schools').countDocuments(schoolIdFilter)

          // For activities, we need to check if the collection exists
          let activitiesCount = 0
          try {
            activitiesCount = await db
              .collection('activities')
              .countDocuments(schoolId ? { schoolId } : {})
          } catch (error) {
            console.warn('Activities collection may not exist:', error)
          }

          // Get recent activities
          let recentActivities: any[] = []
          try {
            recentActivities = await db
              .collection('activities')
              .find(schoolId ? { schoolId } : {})
              .sort({ createdAt: -1 })
              .limit(limit)
              .toArray()
          } catch (error) {
            console.warn('Error fetching recent activities:', error)
          }

          // Get recent schools
          const recentSchools = await db
            .collection('schools')
            .find(schoolIdFilter)
            .sort({ createdAt: -1 })
            .limit(limit)
            .toArray()

          const dashboardData = {
            stats: {
              users: usersCount,
              articles: articlesCount,
              news: newsCount,
              schools: schoolsCount,
              activities: activitiesCount,
            },
            recentActivities,
            recentSchools,
          }

          return NextResponse.json(dashboardData)
        }
      } catch (mongoError) {
        console.warn('Error fetching admin data from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user is an admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role

      if (role !== 'super-admin' && role !== 'school-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get school ID if school admin
      const schoolId =
        role === 'school-admin'
          ? typeof user.school === 'object'
            ? user.school?.id
            : user.school
          : null

      // Get counts
      const usersCount = await payload.find({
        collection: 'users',
        ...(schoolId ? { where: { school: { equals: schoolId } } } : {}),
        limit: 0,
      })

      const articlesCount = await payload.find({
        collection: 'articles',
        ...(schoolId ? { where: { 'author.school': { equals: schoolId } } } : {}),
        limit: 0,
      })

      const newsCount = await payload.find({
        collection: 'news',
        ...(schoolId ? { where: { 'author.school': { equals: schoolId } } } : {}),
        limit: 0,
      })

      const schoolsCount = await payload.find({
        collection: 'schools',
        ...(schoolId ? { where: { id: { equals: schoolId } } } : {}),
        limit: 0,
      })

      // For activities, we need to handle the type issue
      let activitiesCount = { totalDocs: 0 }
      try {
        // Use any to bypass TypeScript checking
        const payloadAny = payload as any
        activitiesCount = await payloadAny.find({
          collection: 'activities',
          ...(schoolId ? { where: { schoolId: { equals: schoolId } } } : {}),
          limit: 0,
        })
      } catch (error) {
        console.warn('Error fetching activities count:', error)
      }

      // Get recent activities
      let recentActivities = { docs: [] }
      try {
        // Use any to bypass TypeScript checking
        const payloadAny = payload as any
        recentActivities = await payloadAny.find({
          collection: 'activities',
          ...(schoolId ? { where: { schoolId: { equals: schoolId } } } : {}),
          limit,
          sort: '-createdAt',
        })
      } catch (error) {
        console.warn('Error fetching recent activities:', error)
      }

      // Get recent schools
      const recentSchools = await payload.find({
        collection: 'schools',
        ...(schoolId ? { where: { id: { equals: schoolId } } } : {}),
        limit,
        sort: '-createdAt',
      })

      const dashboardData = {
        stats: {
          users: usersCount.totalDocs,
          articles: articlesCount.totalDocs,
          news: newsCount.totalDocs,
          schools: schoolsCount.totalDocs,
          activities: activitiesCount.totalDocs,
        },
        recentActivities: recentActivities.docs,
        recentSchools: recentSchools.docs,
      }

      return NextResponse.json(dashboardData)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching admin dashboard data:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
