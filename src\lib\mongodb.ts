import { MongoClient, Db } from 'mongodb'

const MONGODB_URI =
  process.env.DATABASE_URI || 'mongodb+srv://Codic:<EMAIL>'
const MONGODB_DB = process.env.MONGODB_DB || 'young-reporter'

let cachedClient: MongoClient | null = null
let cachedDb: Db | null = null

export async function connectToDatabase(): Promise<{ client: MongoClient; db: Db }> {
  // If we have cached values, use them
  if (cachedClient && cachedDb) {
    return { client: cachedClient, db: cachedDb }
  }

  // Connect to MongoDB
  const client = await MongoClient.connect(MONGODB_URI)
  const db = client.db(MONGODB_DB)

  // Cache the client and db for reuse
  cachedClient = client
  cachedDb = db

  return { client, db }
}

// Helper function to seed the database with test data
export async function seedDatabase() {
  const { db } = await connectToDatabase()

  // Check if collections already have data
  const usersCount = await db.collection('users').countDocuments()
  const schoolsCount = await db.collection('schools').countDocuments()
  const articlesCount = await db.collection('articles').countDocuments()
  const newsCount = await db.collection('news').countDocuments()
  const activitiesCount = await db.collection('activities').countDocuments()

  // Only seed if collections are empty
  if (usersCount === 0) {
    await seedUsers(db)
  }

  if (schoolsCount === 0) {
    await seedSchools(db)
  }

  if (articlesCount === 0) {
    await seedArticles(db)
  }

  if (newsCount === 0) {
    await seedNews(db)
  }

  if (activitiesCount === 0) {
    await seedActivities(db)
  }

  console.log('Database seeded successfully')
}

async function seedUsers(db: Db) {
  const users = [
    {
      id: 'admin1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'super-admin',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'school-admin1',
      name: 'School Admin',
      email: '<EMAIL>',
      role: 'school-admin',
      schoolId: 'school1',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'mentor1',
      name: 'David Miller',
      email: '<EMAIL>',
      role: 'mentor',
      schoolId: 'school1',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'mentor2',
      name: 'Michael Brown',
      email: '<EMAIL>',
      role: 'mentor',
      schoolId: 'school2',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'teacher1',
      name: 'Robert Johnson',
      email: '<EMAIL>',
      role: 'teacher',
      schoolId: 'school1',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'teacher2',
      name: 'Sarah Williams',
      email: '<EMAIL>',
      role: 'teacher',
      schoolId: 'school1',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'teacher3',
      name: 'Emily Wilson',
      email: '<EMAIL>',
      role: 'teacher',
      schoolId: 'school2',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'student1',
      name: 'Jennifer Davis',
      email: '<EMAIL>',
      role: 'student',
      schoolId: 'school1',
      grade: '10',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'student2',
      name: 'Michael Johnson',
      email: '<EMAIL>',
      role: 'student',
      schoolId: 'school1',
      grade: '11',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'student3',
      name: 'Emily Wilson',
      email: '<EMAIL>',
      role: 'student',
      schoolId: 'school2',
      grade: '9',
      createdAt: new Date().toISOString(),
    },
  ]

  await db.collection('users').insertMany(users)
  console.log('Users seeded successfully')
}

async function seedSchools(db: Db) {
  const schools = [
    {
      id: 'school1',
      name: 'Lincoln High School',
      address: '123 Main St, Anytown, USA',
      phone: '************',
      website: 'www.lincolnhigh.edu',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'school2',
      name: 'Washington Middle School',
      address: '456 Oak Ave, Anytown, USA',
      phone: '************',
      website: 'www.washingtonms.edu',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'school3',
      name: 'Jefferson Elementary School',
      address: '789 Pine St, Anytown, USA',
      phone: '************',
      website: 'www.jeffersonelem.edu',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'school4',
      name: 'Roosevelt Academy',
      address: '321 Elm St, Anytown, USA',
      phone: '************',
      website: 'www.rooseveltacademy.edu',
      createdAt: new Date().toISOString(),
    },
  ]

  await db.collection('schools').insertMany(schools)
  console.log('Schools seeded successfully')
}

async function seedArticles(db: Db) {
  const articles = [
    {
      id: '1',
      title: 'Climate Change Effects on Local Wildlife',
      content:
        'This article explores the impact of climate change on the local wildlife in our community...',
      authorId: 'student1',
      schoolId: 'school1',
      status: 'published',
      publishDate: new Date('2023-10-15').toISOString(),
      createdAt: new Date('2023-10-10').toISOString(),
      updatedAt: new Date('2023-10-15').toISOString(),
    },
    {
      id: '2',
      title: 'School Lunch Program Analysis',
      content:
        "An in-depth look at our school's lunch program and how it compares to national standards...",
      authorId: 'student2',
      schoolId: 'school1',
      status: 'published',
      publishDate: new Date('2023-10-14').toISOString(),
      createdAt: new Date('2023-10-05').toISOString(),
      updatedAt: new Date('2023-10-14').toISOString(),
    },
    {
      id: '3',
      title: 'Local Sports Tournament Coverage',
      content:
        'Coverage of the recent inter-school sports tournament including highlights and interviews...',
      authorId: 'student3',
      schoolId: 'school2',
      status: 'published',
      publishDate: new Date('2023-10-12').toISOString(),
      createdAt: new Date('2023-10-08').toISOString(),
      updatedAt: new Date('2023-10-12').toISOString(),
    },
    {
      id: '4',
      title: 'Technology in the Classroom',
      content:
        'How technology is changing the way we learn and interact in the classroom environment...',
      authorId: 'student1',
      schoolId: 'school1',
      status: 'draft',
      createdAt: new Date('2023-10-18').toISOString(),
      updatedAt: new Date('2023-10-18').toISOString(),
    },
    {
      id: '5',
      title: 'Student Council Elections',
      content:
        'Coverage of the recent student council elections including candidate profiles and results...',
      authorId: 'student2',
      schoolId: 'school1',
      status: 'pending',
      createdAt: new Date('2023-10-20').toISOString(),
      updatedAt: new Date('2023-10-20').toISOString(),
    },
    {
      id: '6',
      title: 'School Sports Day Coverage',
      content:
        'A detailed report on the annual school sports day including event results and highlights...',
      authorId: 'student3',
      schoolId: 'school2',
      status: 'rejected',
      createdAt: new Date('2023-10-15').toISOString(),
      updatedAt: new Date('2023-10-17').toISOString(),
      rejectionReason: 'Needs more details and better structure',
    },
  ]

  await db.collection('articles').insertMany(articles)
  console.log('Articles seeded successfully')
}

async function seedNews(db: Db) {
  const news = [
    {
      id: '1',
      title: 'Young Reporter Program Launches New Website',
      content:
        'We are excited to announce the launch of our new Young Reporter website, designed to provide a better experience for students, teachers, and mentors.',
      authorId: 'admin1',
      status: 'published',
      publishDate: new Date('2023-10-15').toISOString(),
      createdAt: new Date('2023-10-15').toISOString(),
      category: 'Announcements',
    },
    {
      id: '2',
      title: 'Upcoming Journalism Workshop',
      content:
        'Join us for a virtual journalism workshop on October 25th. Learn from professional journalists and improve your reporting skills.',
      authorId: 'mentor1',
      status: 'published',
      publishDate: new Date('2023-10-14').toISOString(),
      createdAt: new Date('2023-10-14').toISOString(),
      category: 'Events',
    },
    {
      id: '3',
      title: 'Student Article of the Month',
      content:
        'Congratulations to Sarah Johnson from Lincoln High School for her outstanding article on climate change effects on local wildlife.',
      authorId: 'mentor2',
      status: 'published',
      publishDate: new Date('2023-10-12').toISOString(),
      createdAt: new Date('2023-10-12').toISOString(),
      category: 'Achievements',
    },
    {
      id: '4',
      title: 'New Mentorship Program Guidelines',
      content:
        'We have updated our mentorship program guidelines. Please review the changes before the next semester begins.',
      authorId: 'admin1',
      status: 'draft',
      createdAt: new Date('2023-10-20').toISOString(),
      category: 'Guidelines',
    },
    {
      id: '5',
      title: 'Summer Reporting Competition',
      content:
        'Start planning for our summer reporting competition. This year\'s theme will be "Local Heroes" - more details coming soon.',
      authorId: 'mentor1',
      status: 'draft',
      createdAt: new Date('2023-10-18').toISOString(),
      category: 'Competitions',
    },
  ]

  await db.collection('news').insertMany(news)
  console.log('News seeded successfully')
}

async function seedActivities(db: Db) {
  const activities = [
    {
      id: '1',
      userId: 'teacher1',
      userName: 'Robert Johnson',
      userRole: 'teacher',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'approval',
      description: 'Approved student registration for Michael Johnson',
      date: new Date('2023-10-15').toISOString(),
      targetId: 'student2',
      targetType: 'user',
    },
    {
      id: '2',
      userId: 'teacher2',
      userName: 'Sarah Williams',
      userRole: 'teacher',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'rating',
      description: 'Rated article "School Lunch Program Analysis" with 4 stars',
      date: new Date('2023-10-14').toISOString(),
      targetId: '2',
      targetType: 'article',
    },
    {
      id: '3',
      userId: 'mentor1',
      userName: 'David Miller',
      userRole: 'mentor',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'review',
      description: 'Reviewed teacher activity for Robert Johnson',
      date: new Date('2023-10-12').toISOString(),
      targetId: 'teacher1',
      targetType: 'user',
    },
    {
      id: '4',
      userId: 'student1',
      userName: 'Jennifer Davis',
      userRole: 'student',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'comment',
      description: 'Commented on article "Local Sports Tournament Coverage"',
      date: new Date('2023-10-10').toISOString(),
      targetId: '3',
      targetType: 'article',
    },
    {
      id: '5',
      userId: 'teacher1',
      userName: 'Robert Johnson',
      userRole: 'teacher',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'report',
      description: 'Reported student Emily Wilson for inappropriate content',
      date: new Date('2023-10-08').toISOString(),
      targetId: 'student3',
      targetType: 'user',
    },
    {
      id: '6',
      userId: 'mentor2',
      userName: 'Michael Brown',
      userRole: 'mentor',
      schoolId: 'school2',
      schoolName: 'Washington Middle School',
      activityType: 'news',
      description: 'Added news post "Student Council Elections"',
      date: new Date('2023-10-07').toISOString(),
      targetId: '5',
      targetType: 'news',
    },
    {
      id: '7',
      userId: 'teacher3',
      userName: 'Emily Wilson',
      userRole: 'teacher',
      schoolId: 'school2',
      schoolName: 'Washington Middle School',
      activityType: 'approval',
      description:
        'Rejected article "School Sports Day Coverage" with reason: "Needs more details"',
      date: new Date('2023-10-05').toISOString(),
      targetId: '6',
      targetType: 'article',
    },
    {
      id: '8',
      userId: 'school-admin1',
      userName: 'School Admin',
      userRole: 'school-admin',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'report',
      description: 'Generated monthly activity report',
      date: new Date('2023-10-01').toISOString(),
      targetId: 'report1',
      targetType: 'report',
    },
    {
      id: '9',
      userId: 'teacher1',
      userName: 'Robert Johnson',
      userRole: 'teacher',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'approval',
      description: 'Approved student registration for Jennifer Davis',
      date: new Date('2023-09-28').toISOString(),
      targetId: 'student1',
      targetType: 'user',
    },
    {
      id: '10',
      userId: 'mentor1',
      userName: 'David Miller',
      userRole: 'mentor',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'review',
      description: 'Reviewed teacher activity for Sarah Williams',
      date: new Date('2023-09-25').toISOString(),
      targetId: 'teacher2',
      targetType: 'user',
    },
    {
      id: '11',
      userId: 'student2',
      userName: 'Michael Johnson',
      userRole: 'student',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'comment',
      description: 'Commented on article "Climate Change Effects on Local Wildlife"',
      date: new Date('2023-09-22').toISOString(),
      targetId: '1',
      targetType: 'article',
    },
    {
      id: '12',
      userId: 'teacher2',
      userName: 'Sarah Williams',
      userRole: 'teacher',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'rating',
      description: 'Rated article "Local Sports Tournament Coverage" with 3 stars',
      date: new Date('2023-09-20').toISOString(),
      targetId: '3',
      targetType: 'article',
    },
    {
      id: '13',
      userId: 'admin1',
      userName: 'Admin User',
      userRole: 'super-admin',
      activityType: 'news',
      description: 'Added news post "Young Reporter Program Launches New Website"',
      date: new Date('2023-09-15').toISOString(),
      targetId: '1',
      targetType: 'news',
    },
    {
      id: '14',
      userId: 'school-admin1',
      userName: 'School Admin',
      userRole: 'school-admin',
      schoolId: 'school1',
      schoolName: 'Lincoln High School',
      activityType: 'approval',
      description: 'Approved new teacher Robert Johnson',
      date: new Date('2023-09-10').toISOString(),
      targetId: 'teacher1',
      targetType: 'user',
    },
    {
      id: '15',
      userId: 'admin1',
      userName: 'Admin User',
      userRole: 'super-admin',
      activityType: 'approval',
      description: 'Approved new school Lincoln High School',
      date: new Date('2023-09-05').toISOString(),
      targetId: 'school1',
      targetType: 'school',
    },
  ]

  await db.collection('activities').insertMany(activities)
  console.log('Activities seeded successfully')
}
