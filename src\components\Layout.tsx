import React from 'react'

import { Footer } from './Footer'
import { Navigation } from './Navigation'

type LayoutProps = {
  children: React.ReactNode
  user: any
  adminRoute: string
}

export const Layout: React.FC<LayoutProps> = ({ children, user, adminRoute }) => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navigation user={user} adminRoute={adminRoute} />
      <main className="flex-grow">{children}</main>
      <Footer />
    </div>
  )
}
