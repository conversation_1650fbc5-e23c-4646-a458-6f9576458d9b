import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import payloadConfig from '@/payload.config'

export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    const user = await extractUserFromToken(token)
    if (!user || !user.id) {
      return NextResponse.json({ error: 'مستخدم غير صالح' }, { status: 401 })
    }

    const payload = await getPayload({ config: await payloadConfig })

    // Get all mentors sorted by points
    const mentors = await payload.find({
      collection: 'users',
      where: {
        'role.slug': {
          equals: 'mentor',
        },
      },
      sort: '-points',
      limit: 1000, // Get all mentors
      depth: 0,
    })

    // Find the current user's rank
    let userRank = 0
    let userPoints = 0

    for (let i = 0; i < mentors.docs.length; i++) {
      if (mentors.docs[i].id === user.id) {
        userRank = i + 1
        userPoints = mentors.docs[i].points || 0
        break
      }
    }

    // If user not found in mentors list, they might not have points yet
    if (userRank === 0) {
      // Get user's current points
      const currentUser = await payload.findByID({
        collection: 'users',
        id: user.id,
        depth: 0,
      })
      userPoints = currentUser.points || 0
      userRank = mentors.docs.length + 1 // Last place
    }

    const result = {
      rank: userRank,
      points: userPoints,
      totalMentors: mentors.docs.length,
    }

    console.log('Mentor rank API result:', result)

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error getting mentor rank:', error)
    return NextResponse.json({ error: 'فشل في الحصول على ترتيب الموجه' }, { status: 500 })
  }
}
