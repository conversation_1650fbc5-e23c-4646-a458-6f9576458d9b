'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Loader2, RefreshCw } from 'lucide-react'
import ProfileImageUploadV2 from '@/components/ui/ProfileImageUploadV2'
import { toast } from '@/components/ui/use-toast'

export default function ProfilePage() {
  const router = useRouter()
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // Form state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    bio: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    profileImageId: '',
  })

  // Profile image state
  const [profileImageUrl, setProfileImageUrl] = useState('')
  const [pendingProfileImageUrl, setPendingProfileImageUrl] = useState('')
  const [profileImageStatus, setProfileImageStatus] = useState('none')

  // Name change state
  const [pendingFirstName, setPendingFirstName] = useState('')
  const [pendingLastName, setPendingLastName] = useState('')
  const [nameChangeStatus, setNameChangeStatus] = useState('none')

  // Function to fetch user data that can be called multiple times
  const fetchUserData = async () => {
    try {
      setIsLoading(true)
      // Add cache-busting timestamp to prevent caching
      const timestamp = new Date().getTime()
      const response = await fetch(`/api/auth/me?t=${timestamp}`, {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error('فشل في جلب بيانات المستخدم')
      }

      const data = await response.json()

      if (!data.user) {
        router.push('/login')
        return
      }

      setUser(data.user)
      setFormData({
        ...formData,
        firstName: data.user.firstName || '',
        lastName: data.user.lastName || '',
        email: data.user.email || '',
        bio: data.user.bio || '',
      })

      // Handle profile image
      if (data.user.profileImage) {
        const imageUrl =
          typeof data.user.profileImage === 'object'
            ? data.user.profileImage.url
            : `/api/media/${data.user.profileImage}`
        setProfileImageUrl(imageUrl)
      }

      // Handle pending profile image
      if (data.user.pendingProfileImage) {
        const pendingImageUrl =
          typeof data.user.pendingProfileImage === 'object'
            ? data.user.pendingProfileImage.url
            : `/api/media/${data.user.pendingProfileImage}`
        setPendingProfileImageUrl(pendingImageUrl)
      }

      // Set profile image status
      if (data.user.profileImageStatus) {
        setProfileImageStatus(data.user.profileImageStatus)
      }

      // Handle pending name changes
      if (data.user.pendingFirstName) {
        setPendingFirstName(data.user.pendingFirstName)
      }

      if (data.user.pendingLastName) {
        setPendingLastName(data.user.pendingLastName)
      }

      if (data.user.nameChangeStatus) {
        setNameChangeStatus(data.user.nameChangeStatus)
      }

      setIsLoading(false)
    } catch (err) {
      console.error('Error fetching user data:', err)
      setError('فشل في تحميل الملف الشخصي. يرجى المحاولة مرة أخرى.')
      setIsLoading(false)
    }
  }

  // Flag to track if we're in the middle of a file upload
  const [isUploadingFile, setIsUploadingFile] = useState(false)

  // Fetch user data on component mount and set up focus event listener
  useEffect(() => {
    fetchUserData()

    // Removing the focus event listener that was causing refreshes during image uploads
    // This was causing problems when uploading profile images
    
    // Original code:
    // const handleFocus = () => {
    //   // Skip refresh if we're in the middle of a file upload
    //   if (isUploadingFile) {
    //     console.log('Window focused during file upload, skipping refresh')
    //     return
    //   }
    //
    //   console.log('Window focused, refreshing user data...')
    //   fetchUserData()
    // }
    //
    // window.addEventListener('focus', handleFocus)
    // 
    // // Clean up event listener
    // return () => {
    //   window.removeEventListener('focus', handleFocus)
    // }

  }, []) // No dependencies needed now

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleProfileImageUpload = (imageId: string) => {
    console.log('Profile image uploaded, ID:', imageId)

    // Mark upload as complete
    setIsUploadingFile(false)

    // Store the image ID in form data
    setFormData((prev) => {
      console.log('Updating formData with profileImageId:', imageId)
      return { ...prev, profileImageId: imageId }
    })

    // Get the user role to determine if auto-approval is needed
    const userRole = typeof user?.role === 'object' ? user?.role?.slug : user?.role
    console.log('User role for image approval:', userRole)

    // For teachers, mentors, school admins, and super admins, auto-approve
    if (['teacher', 'mentor', 'school-admin', 'super-admin'].includes(userRole)) {
      setProfileImageStatus('approved')
      // The image URL will be set by the ProfileImageUploadV2 component
      toast({
        title: 'نجاح',
        description: 'تم تحديث صورة الملف الشخصي بنجاح',
      })
    } else {
      setProfileImageStatus('pending')
      toast({
        title: 'نجاح',
        description: 'تم رفع صورة الملف الشخصي وبانتظار الموافقة',
      })
    }
  }

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError('')
    setSuccess('')

    try {
      console.log('Submitting profile update...')

      // Check if profileImageId is set and log it
      if (formData.profileImageId) {
        console.log('Profile image ID included in update:', formData.profileImageId)
      }

      // First check if we're authenticated
      const authResponse = await fetch('/api/auth/me', {
        credentials: 'include',
      })

      if (!authResponse.ok) {
        console.error('Auth check failed:', await authResponse.text())
        throw new Error('فشلت المصادقة')
      }

      const authData = await authResponse.json()
      console.log('Auth check successful, user:', authData.user ? 'found' : 'not found')

      if (!authData.user) {
        throw new Error('المستخدم غير مصادق عليه')
      }

      // Now update the profile
      const updateData: {
        firstName: string
        lastName: string
        bio: string
        profileImageId?: string
      } = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        bio: formData.bio,
      }

      // Include profile image if provided
      if (formData.profileImageId) {
        updateData.profileImageId = formData.profileImageId
      }

      console.log('Sending profile update data:', updateData)

      const response = await fetch('/api/user/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(updateData),
      })

      console.log('Profile update response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('API error response:', errorText)
        throw new Error(`فشل في تحديث الملف الشخصي: ${response.status} ${errorText}`)
      }

      const data = await response.json()
      console.log('Profile update response:', data)

      setSuccess('تم تحديث الملف الشخصي بنجاح')

      // Show toast notification
      toast({
        title: 'نجاح',
        description: 'تم تحديث الملف الشخصي بنجاح',
      })

      // Refresh user data to show the updated information
      console.log('Refreshing user data after successful update...')
      setTimeout(() => {
        fetchUserData()
      }, 1000) // Small delay to ensure the server has processed the update
    } catch (err) {
      console.error('Error updating profile:', err)
      const errorMessage = 'فشل في تحديث الملف الشخصي. يرجى المحاولة مرة أخرى.'
      setError(errorMessage)

      // Show toast notification for error
      toast({
        title: 'خطأ',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError('')
    setSuccess('')

    // Validate passwords
    if (formData.newPassword !== formData.confirmPassword) {
      setError('كلمات المرور الجديدة غير متطابقة')
      setIsSaving(false)
      return
    }

    try {
      const response = await fetch('/api/profile/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          currentPassword: formData.currentPassword,
          newPassword: formData.newPassword,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'فشل في تغيير كلمة المرور')
      }

      setSuccess('تم تغيير كلمة المرور بنجاح')
      setFormData({
        ...formData,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      })
    } catch (err: unknown) {
      console.error('Error changing password:', err)
      setError(err instanceof Error ? err.message : 'فشل في تغيير كلمة المرور. يرجى المحاولة مرة أخرى.')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full" dir="rtl">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6" dir="rtl">
        <h1 className="text-2xl font-bold mb-6">الملف الشخصي</h1>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {success}
          </div>
        )}

        <div className="flex flex-col md:flex-row gap-6">
          <div className="w-full md:w-1/3">
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center">
                  {/* Isolate from form context but don't interfere with clicks */}
                  <div className="profile-image-container">
                    <ProfileImageUploadV2
                      currentImageUrl={profileImageUrl}
                      pendingImageUrl={pendingProfileImageUrl}
                      imageStatus={profileImageStatus}
                      onImageUpload={handleProfileImageUpload}
                      onUploadStart={() => setIsUploadingFile(true)}
                      onUploadError={() => setIsUploadingFile(false)}
                      firstName={user?.firstName}
                      lastName={user?.lastName}
                    />
                  </div>

                  <div className="flex items-center justify-center mt-4">
                    <h2 className="text-xl font-semibold">
                      {user?.firstName} {user?.lastName}
                    </h2>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => fetchUserData()}
                      className="mr-2"
                      title="تحديث بيانات الملف الشخصي"
                    >
                      <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                    </Button>
                  </div>
                  <p className="text-gray-500">{user?.email}</p>
                  <p className="mt-2 text-sm">
                    الدور: {typeof user?.role === 'object' ? user?.role?.slug : user?.role}
                  </p>
                  {user?.school && (
                    <p className="text-sm">
                      المدرسة: {typeof user?.school === 'object' ? user?.school?.name : 'غير محدد'}
                    </p>
                  )}
                  <p className="text-sm">النقاط: {user?.points || 0}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="w-full md:w-2/3">
            <Tabs defaultValue="profile">
              <TabsList className="mb-4">
                <TabsTrigger value="profile">معلومات الملف الشخصي</TabsTrigger>
                <TabsTrigger value="password">تغيير كلمة المرور</TabsTrigger>
              </TabsList>

              <TabsContent value="profile"
              dir='rtl'
              >
                <Card>
                  <CardHeader>
                    <CardTitle>تعديل الملف الشخصي</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleProfileSubmit} className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="firstName">الاسم الأول</Label>
                          <Input
                            id="firstName"
                            name="firstName"
                            value={formData.firstName}
                            onChange={handleInputChange}
                            required
                            className={nameChangeStatus === 'pending' ? 'border-amber-500' : ''}
                          />
                          {nameChangeStatus === 'pending' && pendingFirstName && (
                            <p className="text-xs text-amber-600 dark:text-amber-400">
                              تغيير معلق إلى: {pendingFirstName}
                            </p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="lastName">الاسم الأخير</Label>
                          <Input
                            id="lastName"
                            name="lastName"
                            value={formData.lastName}
                            onChange={handleInputChange}
                            required
                            className={nameChangeStatus === 'pending' ? 'border-amber-500' : ''}
                          />
                          {nameChangeStatus === 'pending' && pendingLastName && (
                            <p className="text-xs text-amber-600 dark:text-amber-400">
                              تغيير معلق إلى: {pendingLastName}
                            </p>
                          )}
                        </div>
                      </div>

                      {nameChangeStatus === 'pending' && (
                        <div className="bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 rounded p-3 text-sm text-amber-800 dark:text-amber-300">
                          <p>
                            طلب تغيير الاسم الخاص بك قيد انتظار الموافقة. سيتم إشعارك عند الموافقة عليه.
                          </p>
                        </div>
                      )}

                      <div className="space-y-2">
                        <Label htmlFor="email">البريد الإلكتروني</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          disabled
                        />
                        <p className="text-sm text-gray-500">لا يمكن تغيير البريد الإلكتروني</p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bio">السيرة الذاتية</Label>
                        <Input
                          id="bio"
                          name="bio"
                          value={formData.bio}
                          onChange={handleInputChange}
                          placeholder="أخبرنا عن نفسك"
                        />
                      </div>

                      <Button type="submit" disabled={isSaving}>
                        {isSaving ? (
                          <>
                            <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                            جاري الحفظ...
                          </>
                        ) : (
                          'حفظ التغييرات'
                        )}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="password">
                <Card>
                  <CardHeader>
                    <CardTitle>تغيير كلمة المرور</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <form
                     onSubmit={handlePasswordSubmit} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="currentPassword">كلمة المرور الحالية</Label>
                        <Input
                          id="currentPassword"
                          name="currentPassword"
                          type="password"
                          value={formData.currentPassword}
                          onChange={handleInputChange}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="newPassword">كلمة المرور الجديدة</Label>
                        <Input
                          id="newPassword"
                          name="newPassword"
                          type="password"
                          value={formData.newPassword}
                          onChange={handleInputChange}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword">تأكيد كلمة المرور الجديدة</Label>
                        <Input
                          id="confirmPassword"
                          name="confirmPassword"
                          type="password"
                          value={formData.confirmPassword}
                          onChange={handleInputChange}
                          required
                        />
                      </div>

                      <Button type="submit" disabled={isSaving}>
                        {isSaving ? (
                          <>
                            <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                            جاري تغيير كلمة المرور...
                          </>
                        ) : (
                          'تغيير كلمة المرور'
                        )}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
